# Project & Organization Info
sonar.projectKey=roava_rova-cba-cib-client-frontend
sonar.organization=roava
#sonar.javascript.lcov.reportPaths=./coverage/lcov.info

# Source code
sonar.sources=src
#sonar.exclusions=**/*test/*.js

# Exclude files/folders not relevant for test coverage
sonar.exclusions=\
  src/redux/**,\
  node_modules/**,\
  **/*.test.jsx,\
  src/components/page-components/auth/**,\
  src/app/auth/**,\
  src/middleware-login.ts,\
  src/middleware.ts,\
  src/middleware-signup.ts,\
  src/app/**/page.tsx,\
  __tests__/services/permissionsService.test.ts,\

# Excluding node modules folder
sonar.test.exclusions=\
  node_modules/**,\
  coverage/**,\
  **/*.test.ts,\
  **/*.spec.ts,\
  **/__mocks__/**

# LCOV Coverage Report
sonar.javascript.lcov.reportPaths=./coverage/lcov.info

# JS language settings
sonar.javascript.exclusions=node_modules/**
#sonar.language=js
#sonar.tests=./tests