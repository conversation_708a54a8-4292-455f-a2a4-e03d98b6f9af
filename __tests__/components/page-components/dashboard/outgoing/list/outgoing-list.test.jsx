import React from "react";
import { render, screen, fireEvent, act } from "@testing-library/react";
import OutgoingList from "@/components/page-components/dashboard/outgoing/list/outgoing-list";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { downloadReceipt } from "@/redux/actions/transferActions";
import { sendCatchFeedback } from "@/functions/feedback";

// Store the original NODE_ENV
const originalNodeEnv = process.env.NODE_ENV;

// Mock the dependencies with state management
let mockSearchParams = new Map([
    ["size", "20"],
    ["page", "1"],
]);

jest.mock("next/navigation", () => {
    const pushMock = jest.fn((url) => {
        // Extract params from URL and update mock state
        const urlObj = new URL(url, "http://localhost");
        const params = urlObj.searchParams;
        mockSearchParams.set("page", params.get("page") || "1");
        mockSearchParams.set("size", params.get("size") || "20");
    });

    return {
        useSearchParams: jest.fn(() => ({
            get: jest.fn((param) => mockSearchParams.get(param)),
            toString: jest.fn(() => {
                const params = new URLSearchParams();
                mockSearchParams.forEach((value, key) => {
                    params.set(key, value);
                });
                return params.toString();
            }),
        })),
        useRouter: jest.fn(() => ({
            push: pushMock,
        })),
        usePathname: jest.fn(() => "/dashboard/outgoing"),
    };
});

jest.mock("@/redux/hooks", () => ({
    useAppDispatch: jest.fn(),
    useAppSelector: jest.fn(),
}));

jest.mock("@/redux/actions/transferActions", () => ({
    downloadReceipt: jest.fn(),
}));

jest.mock("@/functions/feedback", () => ({
    sendCatchFeedback: jest.fn(),
}));

// Mock the Pagination component
jest.mock("@/components/common/pagination", () => ({
    Pagination: ({
        totalItems,
        onPageChange,
        onItemsPerPageChange,
        itemsPerPageOptions,
        totalPages,
        hasNext,
        hasPrevious,
        currentPage,
        currentItemsPerPage,
    }) => (
        <div data-testid="pagination">
            <div data-testid="total-items">Total items: {totalItems}</div>
            <div data-testid="total-pages">Total pages: {totalPages}</div>
            <div data-testid="current-page">Current page: {currentPage}</div>
            <div data-testid="current-items-per-page">Items per page: {currentItemsPerPage}</div>
            <button
                data-testid="previous-page-button"
                onClick={() => onPageChange(currentPage - 1)}
                disabled={!hasPrevious}
            >
                Previous
            </button>
            <button data-testid="next-page-button" onClick={() => onPageChange(currentPage + 1)} disabled={!hasNext}>
                Next
            </button>
            <select
                data-testid="items-per-page-select"
                value={currentItemsPerPage}
                onChange={(e) => onItemsPerPageChange(Number(e.target.value))}
            >
                {itemsPerPageOptions.map((option) => (
                    <option key={option} value={option}>
                        {option}
                    </option>
                ))}
            </select>
        </div>
    ),
}));

// Mock dynamic imports
jest.mock("next/dynamic", () => () => {
    const DynamicComponent = ({ isOpen, handleCloseDetails, transfer, activeTab, onRetry, onSendNow }) =>
        isOpen ? (
            <div data-testid="outgoing-details-modal">
                <button onClick={handleCloseDetails} data-testid="close-details-button">
                    Close
                </button>
                <button onClick={() => onRetry && onRetry("123")} data-testid="retry-button">
                    Retry
                </button>
                <button onClick={() => onSendNow && onSendNow("123")} data-testid="send-now-button">
                    Send Now
                </button>
            </div>
        ) : null;
    return DynamicComponent;
});

// Mock the DataTable component
jest.mock("@/components/common/table/DataTable", () => ({
    DataTable: ({ table, loading, emptyTabletitle, emptyTabledescription, onRowClick }) => {
        // Store the table meta functions so we can trigger them in tests
        if (table.options.meta) {
            window.tableMeta = table.options.meta;
        }

        return (
            <div data-testid="data-table">
                <div>Loading: {loading ? "true" : "false"}</div>
                {table?.getRowModel()?.rows?.length === 0 && !loading && (
                    <div data-testid="empty-state">
                        <div data-testid="empty-title">{emptyTabletitle}</div>
                        <div data-testid="empty-description">{emptyTabledescription}</div>
                    </div>
                )}
                {!table?.getRowModel()?.rows?.length === 0 && (
                    <>
                        <div data-testid="empty-title">{emptyTabletitle}</div>
                        <div data-testid="empty-description">{emptyTabledescription}</div>
                    </>
                )}
                {/* Mock table row to test row click functionality */}
                <div
                    data-testid="table-row"
                    onClick={() => onRowClick && onRowClick(mockTransfers[0])}
                    style={{ cursor: onRowClick ? "pointer" : "default" }}
                >
                    Test Row
                </div>
                <button
                    data-testid="view-details-button"
                    onClick={() => {
                        if (window.tableMeta) {
                            const transfer = mockTransfers[0];
                            window.tableMeta.setSelectedTransfer(transfer);
                            window.tableMeta.setIsDetailsOpen(true);
                        }
                    }}
                >
                    View Details
                </button>
                <button
                    data-testid="download-receipt-button"
                    onClick={() => {
                        if (window.tableMeta) {
                            const transfer = mockTransfers[0];
                            window.tableMeta.handleDownloadReceipt(transfer);
                        }
                    }}
                >
                    Download Receipt
                </button>
                <button
                    data-testid="download-receipt-null-id-button"
                    onClick={() => {
                        if (window.tableMeta) {
                            const invalidTransfer = { ...mockTransfers[0], transferScheduledId: null };
                            window.tableMeta.handleDownloadReceipt(invalidTransfer);
                        }
                    }}
                >
                    Download Receipt With Null ID
                </button>
                <button
                    data-testid="download-receipt-undefined-id-button"
                    onClick={() => {
                        if (window.tableMeta) {
                            const invalidTransfer = { ...mockTransfers[0], transferScheduledId: undefined };
                            window.tableMeta.handleDownloadReceipt(invalidTransfer);
                        }
                    }}
                >
                    Download Receipt With Undefined ID
                </button>
                <button
                    data-testid="download-receipt-zero-id-button"
                    onClick={() => {
                        if (window.tableMeta) {
                            const zeroIdTransfer = { ...mockTransfers[0], transferScheduledId: 0 };
                            window.tableMeta.handleDownloadReceipt(zeroIdTransfer);
                        }
                    }}
                >
                    Download Receipt With Zero ID
                </button>
            </div>
        );
    },
}));

// Mock the EmptyStateComponent
jest.mock("@/components/page-components/dashboard/outgoing/list/empty-state", () => ({
    __esModule: true,
    default: ({ activeTab }) => (
        <div data-testid="empty-state">
            <div>Empty state for {activeTab}</div>
        </div>
    ),
}));

// Mock transfers data
const mockTransfers = [
    {
        id: "1",
        transferScheduledId: 12345,
        date: "2023-01-01T12:00:00Z",
        counterparty: "John Doe",
        accountNumber: "**********",
        bank: "FCMB",
        narration: "Test payment",
        status: "Successful",
        amount: 10000,
        transferType: "Intra-bank",
    },
    {
        id: "2",
        transferScheduledId: 12346,
        date: "2023-01-02T12:00:00Z",
        counterparty: "Jane Smith",
        accountNumber: "**********",
        bank: "GTBank",
        narration: "Test payment 2",
        status: "Failed",
        amount: 20000,
        transferType: "Inter-bank",
    },
];

// Mock Redux state with server-side pagination metadata
const mockReduxState = {
    transfer: {
        downloadReceiptLoading: false,
        // Instant transfers pagination metadata
        transfersTotalElements: 100,
        transfersTotalPages: 5,
        transfersHasNext: true,
        transfersHasPrevious: false,
        // Scheduled transfers pagination metadata
        scheduledTransfersTotalElements: 50,
        scheduledTransfersTotalPages: 3,
        scheduledTransfersHasNext: true,
        scheduledTransfersHasPrevious: false,
        // Recurring transfers pagination metadata
        recurringTransfersTotalElements: 25,
        recurringTransfersTotalPages: 2,
        recurringTransfersHasNext: false,
        recurringTransfersHasPrevious: true,
    },
};

describe("OutgoingList Component", () => {
    // Setup before each test
    beforeEach(() => {
        // Reset mocks
        jest.clearAllMocks();

        // Reset mock search params
        mockSearchParams.clear();
        mockSearchParams.set("size", "20");
        mockSearchParams.set("page", "1");

        // Setup dispatch mock
        const mockDispatch = jest.fn();
        useAppDispatch.mockReturnValue(mockDispatch);

        // Setup Redux state mock
        useAppSelector.mockImplementation((selector) => selector(mockReduxState));

        // Setup window for table meta access
        window.tableMeta = null;

        // Mock console.log for development environment logs
        jest.spyOn(console, "log").mockImplementation(() => {});

        // Set NODE_ENV to test by default
        process.env.NODE_ENV = "test";
    });

    // Restore NODE_ENV after all tests
    afterAll(() => {
        process.env.NODE_ENV = originalNodeEnv;
    });

    test("renders data table with transfers", () => {
        render(<OutgoingList transfers={mockTransfers} activeTab="instant" />);

        expect(screen.getByTestId("data-table")).toBeInTheDocument();
        expect(screen.getByTestId("pagination")).toBeInTheDocument();
    });

    test("renders empty state when transfers array is empty", () => {
        render(<OutgoingList transfers={[]} activeTab="instant" />);

        expect(screen.getByTestId("data-table")).toBeInTheDocument();
        expect(screen.getByTestId("empty-state")).toBeInTheDocument();
        expect(screen.getByTestId("empty-title")).toHaveTextContent("No payments yet");
        expect(screen.getByTestId("empty-description")).toHaveTextContent(
            "Ready for your first payment? Click 'send money' to make your first payment."
        );
    });

    test("renders empty state for scheduled tab", () => {
        render(<OutgoingList transfers={[]} activeTab="scheduled" />);

        expect(screen.getByTestId("empty-title")).toHaveTextContent("No scheduled payments");
        expect(screen.getByTestId("empty-description")).toHaveTextContent(
            "Payments scheduled for future dates will show up here."
        );
    });

    test("renders empty state for recurring tab", () => {
        render(<OutgoingList transfers={[]} activeTab="recurring" />);

        expect(screen.getByTestId("empty-title")).toHaveTextContent("No active recurring payments");
        expect(screen.getByTestId("empty-description")).toHaveTextContent(
            "Your regular automated payments will show up here."
        );
    });

    test("renders empty state for unknown tab (default case)", () => {
        render(<OutgoingList transfers={[]} activeTab="unknown" />);

        expect(screen.getByTestId("empty-title")).toHaveTextContent("No payments found");
        expect(screen.getByTestId("empty-description")).toHaveTextContent("Start by creating a new transfer.");
    });

    test("does not render pagination when transfers array is empty", () => {
        render(<OutgoingList transfers={[]} activeTab="instant" />);

        expect(screen.queryByTestId("pagination")).not.toBeInTheDocument();
    });

    test("opens details modal when clicking view details", () => {
        render(<OutgoingList transfers={mockTransfers} activeTab="instant" />);

        // Click the view details button which triggers the table meta function
        fireEvent.click(screen.getByTestId("view-details-button"));

        // Check if the details modal is now open
        expect(screen.getByTestId("outgoing-details-modal")).toBeInTheDocument();
    });

    test("closes details modal", () => {
        render(<OutgoingList transfers={mockTransfers} activeTab="instant" />);

        // Open the modal first
        fireEvent.click(screen.getByTestId("view-details-button"));
        expect(screen.getByTestId("outgoing-details-modal")).toBeInTheDocument();

        // Close the modal
        fireEvent.click(screen.getByTestId("close-details-button"));

        // Modal should not be visible anymore
        expect(screen.queryByTestId("outgoing-details-modal")).not.toBeInTheDocument();
    });

    test("opens details modal when clicking on a table row", () => {
        render(<OutgoingList transfers={mockTransfers} activeTab="instant" />);

        // Initially, the modal should not be visible
        expect(screen.queryByTestId("outgoing-details-modal")).not.toBeInTheDocument();

        // Click on the table row
        fireEvent.click(screen.getByTestId("table-row"));

        // Check if the details modal is now open
        expect(screen.getByTestId("outgoing-details-modal")).toBeInTheDocument();
    });

    test("does not render details modal when isDetailsOpen is true but selectedTransfer is null", () => {
        const { rerender } = render(<OutgoingList transfers={mockTransfers} activeTab="instant" />);

        // Initially, modal should not be visible
        expect(screen.queryByTestId("outgoing-details-modal")).not.toBeInTheDocument();

        // This test simulates the case where isDetailsOpen might be true but selectedTransfer is null
        // In the actual component, this scenario is prevented by the conditional rendering logic
        // But we test this to ensure the conditional && operator works correctly

        // The component uses: {isDetailsOpen && selectedTransfer && (...)}
        // So even if isDetailsOpen is true, without selectedTransfer, modal won't render
        expect(screen.queryByTestId("outgoing-details-modal")).not.toBeInTheDocument();
    });

    test("handles loading state correctly", () => {
        render(<OutgoingList transfers={mockTransfers} activeTab="instant" isLoading={true} />);

        expect(screen.getByTestId("data-table")).toBeInTheDocument();
        expect(screen.getByText("Loading: true")).toBeInTheDocument();
    });

    test("handles non-loading state correctly", () => {
        render(<OutgoingList transfers={mockTransfers} activeTab="instant" isLoading={false} />);

        expect(screen.getByText("Loading: false")).toBeInTheDocument();
    });

    describe("handleDownloadReceipt function", () => {
        let handleDownloadReceipt;
        let mockDispatch;

        beforeEach(() => {
            // Create our own instance of the component to access its methods
            mockDispatch = jest.fn();
            useAppDispatch.mockReturnValue(mockDispatch);

            // Directly extract the handleDownloadReceipt function from the component instance
            const { container } = render(<OutgoingList transfers={mockTransfers} activeTab="instant" />);

            // Get the instance's handleDownloadReceipt function
            handleDownloadReceipt = window.tableMeta.handleDownloadReceipt;
        });

        test("directly invokes function with valid transferScheduledId", () => {
            const validTransfer = { ...mockTransfers[0], transferScheduledId: 12345 };

            // Directly call the function
            handleDownloadReceipt(validTransfer);

            // Verify it called the right actions
            expect(downloadReceipt).toHaveBeenCalledWith({
                transactionId: 12345,
                isUserInitiated: true,
            });
            expect(mockDispatch).toHaveBeenCalled();
        });

        test("doesn't log debug info in production environment", () => {
            // Set to production environment
            process.env.NODE_ENV = "production";

            const validTransfer = { ...mockTransfers[0] };

            // Directly call the function
            handleDownloadReceipt(validTransfer);

            // Verify no logging occurred
            expect(console.log).not.toHaveBeenCalled();
        });

        test("handles null transferScheduledId with error", () => {
            const invalidTransfer = { ...mockTransfers[0], transferScheduledId: null };

            // Directly call the function
            handleDownloadReceipt(invalidTransfer);

            // Verify error handling
            expect(sendCatchFeedback).toHaveBeenCalledWith(
                expect.objectContaining({
                    message: "No valid transaction ID found for this transfer",
                })
            );
            expect(mockDispatch).not.toHaveBeenCalled();
        });

        test("handles undefined transferScheduledId with error", () => {
            const invalidTransfer = { ...mockTransfers[0], transferScheduledId: undefined };

            // Directly call the function
            handleDownloadReceipt(invalidTransfer);

            // Verify error handling
            expect(sendCatchFeedback).toHaveBeenCalledWith(
                expect.objectContaining({
                    message: "No valid transaction ID found for this transfer",
                })
            );
            expect(mockDispatch).not.toHaveBeenCalled();
        });

        test("processes zero as a invalid transferScheduledId", () => {
            const zeroIdTransfer = { ...mockTransfers[0], transferScheduledId: 0 };

            // Set to development for logging check
            process.env.NODE_ENV = "development";

            // Directly call the function
            handleDownloadReceipt(zeroIdTransfer);

            // In JavaScript, 0 is falsy, so it should be treated as invalid
            expect(sendCatchFeedback).toHaveBeenCalledWith(
                expect.objectContaining({
                    message: "No valid transaction ID found for this transfer",
                })
            );
            expect(mockDispatch).not.toHaveBeenCalled();
        });
    });

    describe("Pagination functionality", () => {
        test("renders pagination component when transfers are provided", () => {
            render(<OutgoingList transfers={mockTransfers} activeTab="instant" />);

            expect(screen.getByTestId("pagination")).toBeInTheDocument();
            // Server-side pagination uses metadata from Redux state
            expect(screen.getByTestId("total-items")).toHaveTextContent("Total items: 100");
            expect(screen.getByTestId("total-pages")).toHaveTextContent("Total pages: 5");
        });

        test("uses server-side pagination metadata for different tabs", () => {
            // Test instant tab (default)
            const { unmount } = render(<OutgoingList transfers={mockTransfers} activeTab="instant" />);
            expect(screen.getByTestId("total-pages")).toHaveTextContent("Total pages: 5");
            expect(screen.getByTestId("total-items")).toHaveTextContent("Total items: 100");
            unmount();

            // Test scheduled tab
            render(<OutgoingList transfers={mockTransfers} activeTab="scheduled" />);
            expect(screen.getByTestId("total-pages")).toHaveTextContent("Total pages: 3");
            expect(screen.getByTestId("total-items")).toHaveTextContent("Total items: 50");
            expect(screen.getByTestId("current-page")).toHaveTextContent("Current page: 1");
            expect(screen.getByTestId("current-items-per-page")).toHaveTextContent("Items per page: 20");
        });

        test("uses server-side pagination metadata for recurring tab", () => {
            // Test recurring tab separately to avoid DOM conflicts
            render(<OutgoingList transfers={mockTransfers} activeTab="recurring" />);
            expect(screen.getByTestId("total-pages")).toHaveTextContent("Total pages: 2");
            expect(screen.getByTestId("total-items")).toHaveTextContent("Total items: 25");
            expect(screen.getByTestId("current-page")).toHaveTextContent("Current page: 1");
            expect(screen.getByTestId("current-items-per-page")).toHaveTextContent("Items per page: 20");
        });

        test("handles invalid tab by using default pagination values", () => {
            render(<OutgoingList transfers={mockTransfers} activeTab="invalid" />);
            expect(screen.getByTestId("total-pages")).toHaveTextContent("Total pages: 0");
            expect(screen.getByTestId("total-items")).toHaveTextContent("Total items: 0");
        });

        test("handles page navigation", () => {
            const manyTransfers = Array.from({ length: 25 }, (_, i) => ({
                ...mockTransfers[0],
                id: `${i + 1}`,
                transferScheduledId: 1000 + i,
            }));

            const { rerender } = render(<OutgoingList transfers={manyTransfers} activeTab="instant" />);

            // Click next page button
            fireEvent.click(screen.getByTestId("next-page-button"));

            // Re-render to reflect the URL change
            rerender(<OutgoingList transfers={manyTransfers} activeTab="instant" />);

            // Should have navigated to page 2
            expect(screen.getByTestId("current-page")).toHaveTextContent("Current page: 2");
        });

        test("handles items per page change", () => {
            const manyTransfers = Array.from({ length: 25 }, (_, i) => ({
                ...mockTransfers[0],
                id: `${i + 1}`,
                transferScheduledId: 1000 + i,
            }));

            const { rerender } = render(<OutgoingList transfers={manyTransfers} activeTab="instant" />);

            // Change items per page to 20
            fireEvent.change(screen.getByTestId("items-per-page-select"), {
                target: { value: "20" },
            });

            // Re-render to reflect the URL change
            rerender(<OutgoingList transfers={manyTransfers} activeTab="instant" />);

            // Should show 20 items per page and use server-side total pages from Redux
            expect(screen.getByTestId("current-items-per-page")).toHaveTextContent("Items per page: 20");
            expect(screen.getByTestId("total-pages")).toHaveTextContent("Total pages: 5");
        });

        test("disables previous button on first page", () => {
            render(<OutgoingList transfers={mockTransfers} activeTab="instant" />);

            const previousButton = screen.getByTestId("previous-page-button");
            expect(previousButton).toBeDisabled();
        });

        test("disables next button on last page", () => {
            // Update Redux state to simulate being on the last page
            const lastPageState = {
                transfer: {
                    ...mockReduxState.transfer,
                    transfersHasNext: false, // No more pages available
                },
            };

            useAppSelector.mockImplementation((selector) => selector(lastPageState));

            render(<OutgoingList transfers={mockTransfers} activeTab="instant" />);

            const nextButton = screen.getByTestId("next-page-button");
            expect(nextButton).toBeDisabled();
        });

        test("handles null/undefined pagination values with fallbacks for instant tab", () => {
            // Create state with null/undefined values to test the || fallback operators
            const stateWithNullValues = {
                transfer: {
                    ...mockReduxState.transfer,
                    transfersTotalElements: null,
                    transfersTotalPages: undefined,
                    transfersHasNext: null,
                    transfersHasPrevious: undefined,
                },
            };

            useAppSelector.mockImplementation((selector) => selector(stateWithNullValues));

            render(<OutgoingList transfers={mockTransfers} activeTab="instant" />);

            // Should use fallback values (0 for numbers, false for booleans)
            expect(screen.getByTestId("total-items")).toHaveTextContent("Total items: 0");
            expect(screen.getByTestId("total-pages")).toHaveTextContent("Total pages: 0");
            expect(screen.getByTestId("next-page-button")).toBeDisabled();
            expect(screen.getByTestId("previous-page-button")).toBeDisabled();
        });

        test("handles null/undefined pagination values with fallbacks for scheduled tab", () => {
            // Create state with null/undefined values to test the || fallback operators
            const stateWithNullValues = {
                transfer: {
                    ...mockReduxState.transfer,
                    scheduledTransfersTotalElements: null,
                    scheduledTransfersTotalPages: undefined,
                    scheduledTransfersHasNext: null,
                    scheduledTransfersHasPrevious: undefined,
                },
            };

            useAppSelector.mockImplementation((selector) => selector(stateWithNullValues));

            render(<OutgoingList transfers={mockTransfers} activeTab="scheduled" />);

            // Should use fallback values (0 for numbers, false for booleans)
            expect(screen.getByTestId("total-items")).toHaveTextContent("Total items: 0");
            expect(screen.getByTestId("total-pages")).toHaveTextContent("Total pages: 0");
            expect(screen.getByTestId("next-page-button")).toBeDisabled();
            expect(screen.getByTestId("previous-page-button")).toBeDisabled();
        });

        test("handles null/undefined pagination values with fallbacks for recurring tab", () => {
            // Create state with null/undefined values to test the || fallback operators
            const stateWithNullValues = {
                transfer: {
                    ...mockReduxState.transfer,
                    recurringTransfersTotalElements: null,
                    recurringTransfersTotalPages: undefined,
                    recurringTransfersHasNext: null,
                    recurringTransfersHasPrevious: undefined,
                },
            };

            useAppSelector.mockImplementation((selector) => selector(stateWithNullValues));

            render(<OutgoingList transfers={mockTransfers} activeTab="recurring" />);

            // Should use fallback values (0 for numbers, false for booleans)
            expect(screen.getByTestId("total-items")).toHaveTextContent("Total items: 0");
            expect(screen.getByTestId("total-pages")).toHaveTextContent("Total pages: 0");
            expect(screen.getByTestId("next-page-button")).toBeDisabled();
            expect(screen.getByTestId("previous-page-button")).toBeDisabled();
        });

        test("uses fallback values when searchParams return null for page and size", () => {
            // Reset mock search params to return null/empty values
            mockSearchParams.clear(); // This should make get() return null for all params

            render(<OutgoingList transfers={mockTransfers} activeTab="instant" />);

            // Should use fallback values: page defaults to 1, size defaults to 20
            expect(screen.getByTestId("current-page")).toHaveTextContent("Current page: 1");
            expect(screen.getByTestId("current-items-per-page")).toHaveTextContent("Items per page: 20");
        });

        test("uses fallback values when searchParams return NaN values", () => {
            // Set invalid non-numeric values that will result in NaN when Number() is called
            mockSearchParams.set("page", "invalid");
            mockSearchParams.set("size", "not-a-number");

            render(<OutgoingList transfers={mockTransfers} activeTab="instant" />);

            // Should use fallback values since Number("invalid") returns NaN, which is falsy
            expect(screen.getByTestId("current-page")).toHaveTextContent("Current page: 1");
            expect(screen.getByTestId("current-items-per-page")).toHaveTextContent("Items per page: 20");
        });
    });
});
