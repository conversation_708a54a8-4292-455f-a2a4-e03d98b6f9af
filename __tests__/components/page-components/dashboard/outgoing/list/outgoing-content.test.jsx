import React from "react";
import { render, screen, fireEvent, act } from "@testing-library/react";
import "@testing-library/jest-dom";
import OutgoingContent from "@/components/page-components/dashboard/outgoing/list/outgoing-content";
import { OutgoingContentState } from "@/components/page-components/dashboard/outgoing/types";
import { Provider } from "react-redux";
import configureMockStore from "redux-mock-store";

// Mock Redux hooks
const useAppSelectorMock = jest.fn();
const useAppDispatchMock = jest.fn();

jest.mock("@/redux/hooks", () => ({
    useAppDispatch: () => useAppDispatchMock(),
    useAppSelector: (selector) => useAppSelectorMock(selector),
}));

// Mock Redux actions
const fetchTransfersMock = jest.fn(() => ({ type: "MOCK_FETCH_TRANSFERS" }));
const fetchScheduledTransfersMock = jest.fn(() => ({ type: "MOCK_FETCH_SCHEDULED_TRANSFERS" }));
const fetchRecurringTransfersMock = jest.fn(() => ({ type: "MOCK_FETCH_RECURRING_TRANSFERS" }));

jest.mock("@/redux/actions/transferActions", () => ({
    fetchTransfers: (params) => fetchTransfersMock(params),
    fetchScheduledTransfers: (params) => fetchScheduledTransfersMock(params),
    fetchRecurringTransfers: (params) => fetchRecurringTransfersMock(params),
}));

// Mock the icons
jest.mock("@/components/icons/outgoing", () => ({
    ArrowDownIcon: () => <div data-testid="arrow-down-icon">Arrow Down Icon</div>,
}));

// Mock the child components
jest.mock("@/components/common/checkbox", () => ({
    __esModule: true,
    default: ({ checked, onChange, size }) => (
        <input
            type="checkbox"
            data-testid="checkbox-component"
            checked={checked}
            onChange={() => onChange && onChange(!checked)}
            data-size={size}
        />
    ),
}));

jest.mock("@/components/page-components/dashboard/outgoing/list/loading-state", () => ({
    __esModule: true,
    default: () => <div data-testid="loading-state">Loading State</div>,
}));

jest.mock("@/components/page-components/dashboard/outgoing/list/error-state", () => ({
    __esModule: true,
    default: ({ onRetry }) => (
        <div data-testid="error-state" onClick={() => onRetry && onRetry()}>
            Error fetching payments
        </div>
    ),
}));

jest.mock("@/components/page-components/dashboard/outgoing/list/empty-state", () => ({
    __esModule: true,
    default: ({ activeTab }) => <div data-testid="empty-state">Empty State for {activeTab}</div>,
}));

jest.mock("@/components/page-components/dashboard/outgoing/list/filtered-empty-state", () => ({
    __esModule: true,
    default: () => <div data-testid="filtered-empty-state">Filtered Empty State</div>,
}));

jest.mock("@/components/page-components/dashboard/outgoing/list/outgoing-list", () => ({
    __esModule: true,
    default: ({ transfers, activeTab }) => (
        <div data-testid="outgoing-list">
            Outgoing List for {activeTab} with {transfers.length} items
            {transfers.length === 0 && <div data-testid="empty-state">Empty State for {activeTab}</div>}
        </div>
    ),
}));

// Mock feedback functions
jest.mock("@/functions/feedback", () => ({
    sendCatchFeedback: jest.fn(),
}));

// Mock window.location.search for URL parameters
Object.defineProperty(window, "location", {
    value: {
        search: "?page=1&size=20",
    },
    writable: true,
});

const mockStore = configureMockStore();

describe("OutgoingContent Component", () => {
    const mockTransfers = [
        {
            id: "1",
            date: "2023-01-01",
            counterparty: "Test User",
            narration: "Test Payment",
            status: "SUCCESSFUL",
            amount: "₦100,000",
        },
        {
            id: "2",
            date: "2023-01-02",
            counterparty: "Another User",
            narration: "Another Payment",
            status: "PENDING",
            amount: "₦50,000",
        },
    ];

    beforeEach(() => {
        // Clear all mocks before each test
        jest.clearAllMocks();

        // Set up dispatch mock to return the action
        const mockDispatch = jest.fn((action) => action);
        useAppDispatchMock.mockReturnValue(mockDispatch);
    });

    // Create a function to setup the component with custom mock state
    const setupComponent = (stateOverrides = {}, props = {}) => {
        // Create default props
        const defaultProps = {
            activeTab: "instant",
            ...props,
        };

        // Create default state
        const defaultState = {
            transfer: {
                transfers: [],
                transfersLoading: false,
                transfersError: "",
                scheduledTransfers: [],
                scheduledTransfersLoading: false,
                scheduledTransfersError: "",
                recurringTransfers: [],
                recurringTransfersLoading: false,
                recurringTransfersError: "",
                ...stateOverrides,
            },
        };

        // Configure useAppSelector to return state based on the selector
        useAppSelectorMock.mockImplementation((selector) => selector(defaultState));

        return render(<OutgoingContent {...defaultProps} />);
    };

    describe("Renders different states correctly", () => {
        test("renders loading state when transfers are loading", () => {
            setupComponent({ transfersLoading: true });
            expect(screen.getByTestId("loading-state")).toBeInTheDocument();
        });

        test("renders error state when there's an error", () => {
            setupComponent({ transfersError: "Failed to fetch transfers" });
            expect(screen.getByTestId("error-state")).toBeInTheDocument();
            expect(screen.getByTestId("error-state")).toHaveTextContent("Error fetching payments");
        });

        test("renders empty state when there are no transfers", () => {
            setupComponent({ transfers: [] });
            expect(screen.getByTestId("empty-state")).toBeInTheDocument();
        });

        test("renders filtered empty state when filtered transfers is empty array", () => {
            // Use empty filteredTransfers array to trigger FILTERED_EMPTY state
            const { container } = render(
                <OutgoingContent activeTab="instant" filteredTransfers={[]} areFiltersApplied={true} />
            );
            expect(screen.getByTestId("filtered-empty-state")).toBeInTheDocument();
        });

        test("renders data state when transfers are available", () => {
            setupComponent({ transfers: mockTransfers });
            expect(screen.getByTestId("outgoing-list")).toBeInTheDocument();
            expect(screen.getByTestId("outgoing-list")).toHaveTextContent("Outgoing List for instant with 2 items");
        });
    });

    describe("Tab-specific data and loading states", () => {
        test("uses instant transfers data for 'instant' tab", () => {
            setupComponent({ transfers: mockTransfers }, { activeTab: "instant" });
            expect(screen.getByTestId("outgoing-list")).toHaveTextContent("Outgoing List for instant with 2 items");
        });

        test("uses scheduled transfers data for 'scheduled' tab", () => {
            setupComponent({ scheduledTransfers: mockTransfers }, { activeTab: "scheduled" });
            expect(screen.getByTestId("outgoing-list")).toHaveTextContent("Outgoing List for scheduled with 2 items");
        });

        test("uses recurring transfers data for 'recurring' tab", () => {
            setupComponent({ recurringTransfers: mockTransfers }, { activeTab: "recurring" });
            expect(screen.getByTestId("outgoing-list")).toHaveTextContent("Outgoing List for recurring with 2 items");
        });
    });

    describe("Event handlers and interactions", () => {
        test("handles retry function when error state is clicked for instant tab", () => {
            // Setup the component with an error to show the error state
            setupComponent({ transfersError: "Error message" });

            // Click the error state to retry
            act(() => {
                fireEvent.click(screen.getByTestId("error-state"));
            });

            // Check that fetchTransfers was called with correct pagination params from URL
            expect(fetchTransfersMock).toHaveBeenCalledWith(
                expect.objectContaining({
                    pageNo: 0, // page 1 - 1 (zero-indexed)
                    pageSize: 20,
                    paymentType: "INSTANT",
                    isUserInitiated: true,
                })
            );
        });

        test("handles retry function when error state is clicked for scheduled tab", () => {
            // Setup the component with an error to show the error state
            setupComponent({ scheduledTransfersError: "Error message" }, { activeTab: "scheduled" });

            // Click the error state to retry
            act(() => {
                fireEvent.click(screen.getByTestId("error-state"));
            });

            // Check that fetchScheduledTransfers was called with correct pagination params
            expect(fetchScheduledTransfersMock).toHaveBeenCalledWith(
                expect.objectContaining({
                    pageNo: 0,
                    pageSize: 20,
                    paymentType: "SCHEDULED",
                    isUserInitiated: true,
                })
            );
        });

        test("handles retry function when error state is clicked for recurring tab", () => {
            // Setup the component with an error to show the error state
            setupComponent({ recurringTransfersError: "Error message" }, { activeTab: "recurring" });

            // Click the error state to retry
            act(() => {
                fireEvent.click(screen.getByTestId("error-state"));
            });

            // Check that fetchRecurringTransfers was called with correct pagination params
            expect(fetchRecurringTransfersMock).toHaveBeenCalledWith(
                expect.objectContaining({
                    pageNo: 0,
                    pageSize: 20,
                    paymentType: "RECURRING",
                    isUserInitiated: true,
                })
            );
        });
    });

    describe("State management and data flow", () => {
        test("correctly determines loading state for instant tab", () => {
            setupComponent({ transfersLoading: true }, { activeTab: "instant" });
            expect(screen.getByTestId("loading-state")).toBeInTheDocument();
        });

        test("correctly determines loading state for scheduled tab", () => {
            setupComponent({ scheduledTransfersLoading: true }, { activeTab: "scheduled" });
            expect(screen.getByTestId("loading-state")).toBeInTheDocument();
        });

        test("correctly determines loading state for recurring tab", () => {
            setupComponent({ recurringTransfersLoading: true }, { activeTab: "recurring" });
            expect(screen.getByTestId("loading-state")).toBeInTheDocument();
        });

        test("correctly determines error state for instant tab", () => {
            setupComponent({ transfersError: "Error message" }, { activeTab: "instant" });
            expect(screen.getByTestId("error-state")).toBeInTheDocument();
        });

        test("correctly determines error state for scheduled tab", () => {
            setupComponent({ scheduledTransfersError: "Error message" }, { activeTab: "scheduled" });
            expect(screen.getByTestId("error-state")).toBeInTheDocument();
        });

        test("correctly determines error state for recurring tab", () => {
            setupComponent({ recurringTransfersError: "Error message" }, { activeTab: "recurring" });
            expect(screen.getByTestId("error-state")).toBeInTheDocument();
        });
    });

    // Add a new test suite for the switch default case
    describe("Switch statement default case", () => {
        test("returns null for unknown content state", () => {
            // Create a simple test component that replicates the switch statement
            const TestComponent = ({ contentState }) => {
                switch (contentState) {
                    case OutgoingContentState.LOADING:
                        return <div data-testid="loading">Loading</div>;
                    case OutgoingContentState.ERROR:
                        return <div data-testid="error">Error</div>;
                    case OutgoingContentState.EMPTY:
                        return <div data-testid="empty">Empty</div>;
                    case OutgoingContentState.FILTERED_EMPTY:
                        return <div data-testid="filtered-empty">Filtered Empty</div>;
                    case OutgoingContentState.DATA:
                        return <div data-testid="data">Data</div>;
                    default:
                        return null; // This is the line we want to test
                }
            };

            // Render the test component with a valid state
            const { rerender, container } = render(<TestComponent contentState={OutgoingContentState.LOADING} />);
            expect(screen.getByTestId("loading")).toBeInTheDocument();

            // Re-render with an invalid state to trigger the default case
            rerender(<TestComponent contentState="INVALID_STATE" />);

            // Verify that nothing was rendered (null was returned)
            expect(container.firstChild).toBeNull();
        });
    });

    describe("filteredTransfers prop coverage", () => {
        test("uses filteredTransfers when provided", () => {
            const mockFilteredTransfers = [{ id: "filtered-1", counterparty: "Filtered Transfer", amount: 100 }];

            render(<OutgoingContent activeTab="instant" filteredTransfers={mockFilteredTransfers} />);

            expect(screen.getByTestId("outgoing-list")).toBeInTheDocument();
        });

        test("falls back to Redux data when filteredTransfers is undefined", () => {
            render(<OutgoingContent activeTab="instant" />);

            expect(screen.getByTestId("outgoing-list")).toBeInTheDocument();
        });
    });

    describe("Empty state logic", () => {
        test("shows regular empty state when no data exists and no filters are applied", () => {
            // Mock Redux state with no transfers
            setupComponent({ transfers: [] });

            // Should show regular empty state, not filtered empty state
            expect(screen.getByTestId("empty-state")).toBeInTheDocument();
            expect(screen.queryByTestId("filtered-empty-state")).not.toBeInTheDocument();
        });

        test("shows filtered empty state when filters are applied but no results match", () => {
            // Pass empty filteredTransfers array AND areFiltersApplied=true to indicate filters are active
            render(<OutgoingContent activeTab="instant" filteredTransfers={[]} areFiltersApplied={true} />);

            // Should show filtered empty state
            expect(screen.getByTestId("filtered-empty-state")).toBeInTheDocument();
            expect(screen.queryByTestId("empty-state")).not.toBeInTheDocument();
        });

        test("shows regular empty state when filteredTransfers is empty but no filters are applied", () => {
            // Pass empty filteredTransfers array BUT areFiltersApplied=false (no filters applied)
            render(<OutgoingContent activeTab="instant" filteredTransfers={[]} areFiltersApplied={false} />);

            // Should show regular empty state, not filtered empty state
            expect(screen.getByTestId("empty-state")).toBeInTheDocument();
            expect(screen.queryByTestId("filtered-empty-state")).not.toBeInTheDocument();
        });

        test("shows regular empty state when filteredTransfers is undefined and no Redux data", () => {
            // Mock Redux state with no transfers and don't pass filteredTransfers prop
            setupComponent({ transfers: [] });

            // Should show regular empty state
            expect(screen.getByTestId("empty-state")).toBeInTheDocument();
            expect(screen.queryByTestId("filtered-empty-state")).not.toBeInTheDocument();
        });
    });

    describe("Content state transitions", () => {
        test("transitions from loading to data state", () => {
            // Start with loading state
            const { rerender } = setupComponent({ transfersLoading: true });
            expect(screen.getByTestId("loading-state")).toBeInTheDocument();

            // Mock updated state with data
            const updatedState = {
                transfer: {
                    transfers: mockTransfers,
                    transfersLoading: false,
                    transfersError: "",
                    scheduledTransfers: [],
                    scheduledTransfersLoading: false,
                    scheduledTransfersError: "",
                    recurringTransfers: [],
                    recurringTransfersLoading: false,
                    recurringTransfersError: "",
                },
            };

            useAppSelectorMock.mockImplementation((selector) => selector(updatedState));

            // Re-render with new state
            rerender(<OutgoingContent activeTab="instant" />);

            // Should now show data state
            expect(screen.getByTestId("outgoing-list")).toBeInTheDocument();
            expect(screen.queryByTestId("loading-state")).not.toBeInTheDocument();
        });

        test("transitions from error to data state after retry", () => {
            // Start with error state
            setupComponent({ transfersError: "Error message" });
            expect(screen.getByTestId("error-state")).toBeInTheDocument();

            // Click retry (this would trigger a new API call in real scenario)
            act(() => {
                fireEvent.click(screen.getByTestId("error-state"));
            });

            // Verify retry action was called with correct pagination params
            expect(fetchTransfersMock).toHaveBeenCalledWith(
                expect.objectContaining({
                    pageNo: 0,
                    pageSize: 20,
                    paymentType: "INSTANT",
                    isUserInitiated: true,
                })
            );
        });

        test("handles retry with different URL pagination parameters", () => {
            // Update window.location.search to different pagination values
            Object.defineProperty(window, "location", {
                value: { search: "?page=3&size=50" },
                writable: true,
            });

            // Setup the component with an error to show the error state
            setupComponent({ transfersError: "Error message" }, { activeTab: "instant" });

            // Click the error state to retry
            act(() => {
                fireEvent.click(screen.getByTestId("error-state"));
            });

            // Check that fetchTransfers was called with URL pagination params
            expect(fetchTransfersMock).toHaveBeenCalledWith(
                expect.objectContaining({
                    pageNo: 2, // page 3 - 1 (zero-indexed)
                    pageSize: 50,
                    paymentType: "INSTANT",
                    isUserInitiated: true,
                })
            );

            // Reset window.location.search for other tests
            Object.defineProperty(window, "location", {
                value: { search: "?page=1&size=20" },
                writable: true,
            });
        });
    });

    describe("Default tab handling", () => {
        test("handles unknown activeTab gracefully", () => {
            // Test with an invalid tab value
            setupComponent({}, { activeTab: "unknown_tab" });

            // Should render empty state since unknown tab defaults to empty array
            expect(screen.getByTestId("empty-state")).toBeInTheDocument();
        });
    });
});
