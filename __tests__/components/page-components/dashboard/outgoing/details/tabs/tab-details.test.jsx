import React from "react";
import { render, screen, within } from "@testing-library/react";
import "@testing-library/jest-dom";
import OutgoingDetailsTab from "@/components/page-components/dashboard/outgoing/details/tabs/tab-details";
import { formatNumberToNaira } from "@/functions/stringManipulations";
import { formatDateWithTime, formatDate } from "@/functions/date";
import {
    isBulkTransfer,
    calculateBulkTransferAmount,
} from "@/components/page-components/dashboard/outgoing/utils/statusUtils";

// Mock the imported components and functions
jest.mock("@/components/common/transaction-id", () => ({
    TransactionId: ({ id }) => <div data-testid="transaction-id">{id}</div>,
}));

jest.mock("@/functions/stringManipulations", () => ({
    formatNumberToNaira: jest.fn((amount) => `₦${amount.toLocaleString()}`),
}));

jest.mock("@/functions/date", () => ({
    formatDateWithTime: jest.fn((date) => {
        if (!date) return "-";
        return new Date(date).toLocaleDateString("en-US", {
            weekday: "long",
            year: "numeric",
            month: "long",
            day: "numeric",
            hour: "numeric",
            minute: "2-digit",
            hour12: true,
        });
    }),
    formatDate: jest.fn((date) => {
        if (!date) return "-";
        return new Date(date).toLocaleDateString("en-US");
    }),
}));

// Mock the status utility functions
jest.mock("@/components/page-components/dashboard/outgoing/utils/statusUtils", () => ({
    isBulkTransfer: jest.fn(),
    calculateBulkTransferAmount: jest.fn(),
}));

// Sample transfer data for testing
const mockTransfer = {
    id: "TRF123456789",
    date: "2025-03-12T10:15:30.000Z",
    counterparty: "John Doe",
    accountNumber: "**********",
    narration: "Payment for services",
    amount: 50000,
    bank: "First Bank",
    frequency: "Weekly",
    firstPaymentDate: "2025-03-28",
    lastPaymentDate: "2025-06-28",
    frequencyType: "WEEKLY",
};

const mockBulkTransfer = {
    ...mockTransfer,
    totalTransfers: 5,
    amount: 250000,
    recipients: [{ amount: 50000 }, { amount: 50000 }, { amount: 50000 }, { amount: 50000 }, { amount: 50000 }],
};

describe("OutgoingDetailsTab Component", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        // Default mock implementation for non-bulk transfers
        isBulkTransfer.mockReturnValue(false);
        calculateBulkTransferAmount.mockReturnValue(50000);
    });

    test("renders the sent tab with correct details", () => {
        render(<OutgoingDetailsTab transfer={mockTransfer} activeTab="sent" />);

        // Check that basic fields are rendered
        expect(screen.getByText("Counterparty")).toBeInTheDocument();
        expect(screen.getByText("Amount")).toBeInTheDocument();
        expect(screen.getByText("Date created")).toBeInTheDocument();
        expect(screen.getByText("Bank")).toBeInTheDocument();
        expect(screen.getByText("Account number")).toBeInTheDocument();
        expect(screen.getByText("Transaction reference")).toBeInTheDocument();
        expect(screen.getByText("Narration")).toBeInTheDocument();

        // Check the values
        expect(screen.getByText(mockTransfer.counterparty)).toBeInTheDocument();
        expect(formatNumberToNaira).toHaveBeenCalledWith(mockTransfer.amount);
        expect(formatDateWithTime).toHaveBeenCalledWith(mockTransfer.date);
        expect(screen.getByText(mockTransfer.bank)).toBeInTheDocument();
        expect(screen.getByText(mockTransfer.accountNumber)).toBeInTheDocument();
        expect(screen.getByTestId("transaction-id")).toHaveTextContent(mockTransfer.id);
        expect(screen.getByText(mockTransfer.narration)).toBeInTheDocument();

        // Verify specific fields for sent tab are NOT present
        expect(screen.queryByText("Frequency")).not.toBeInTheDocument();
        expect(screen.queryByText("On")).not.toBeInTheDocument();
        expect(screen.queryByText("Ends")).not.toBeInTheDocument();
        expect(screen.queryByText("Scheduled for")).not.toBeInTheDocument();
    });

    test("renders the scheduled tab with correct details", () => {
        render(<OutgoingDetailsTab transfer={mockTransfer} activeTab="scheduled" />);

        // Check that basic fields are rendered
        expect(screen.getByText("Counterparty")).toBeInTheDocument();
        expect(screen.getByText("Amount")).toBeInTheDocument();
        expect(screen.getByText("Date created")).toBeInTheDocument();

        // Check scheduled-specific field
        expect(screen.getByText("Scheduled for")).toBeInTheDocument();

        // Verify specific fields for recurring tab are NOT present
        expect(screen.queryByText("Frequency")).not.toBeInTheDocument();
        expect(screen.queryByText("On")).not.toBeInTheDocument();
        expect(screen.queryByText("Ends")).not.toBeInTheDocument();
    });

    test("renders the recurring tab with correct details", () => {
        render(<OutgoingDetailsTab transfer={mockTransfer} activeTab="recurring" />);

        // Check recurring-specific fields
        expect(screen.getByText("Frequency")).toBeInTheDocument();
        expect(screen.getByText("On")).toBeInTheDocument();
        expect(screen.getByText("Ends")).toBeInTheDocument();
        expect(screen.getByText(mockTransfer.frequency)).toBeInTheDocument();

        // Check that "Mondays, 9:00am" or similar is rendered for the On field
        const onField = screen.getByText("On").nextElementSibling;
        expect(onField.textContent).toContain("s,"); // Should contain day with 's,' suffix

        // Check the Ends field has the formatted date
        expect(formatDate).toHaveBeenCalledWith(mockTransfer.lastPaymentDate);

        // Verify scheduled-specific field is NOT present
        expect(screen.queryByText("Scheduled for")).not.toBeInTheDocument();
    });

    test("handles missing data gracefully", () => {
        const incompleteTransfer = {
            id: "",
            date: null,
            counterparty: null,
            accountNumber: null,
            narration: null,
            amount: 0,
            bank: null,
        };

        render(<OutgoingDetailsTab transfer={incompleteTransfer} activeTab="sent" />);

        // Check that default values are displayed for missing data by testing each field container
        const counterpartyRow = screen.getByText("Counterparty").closest(".flex.items-start");
        const bankRow = screen.getByText("Bank").closest(".flex.items-start");
        const accountNumberRow = screen.getByText("Account number").closest(".flex.items-start");

        // Verify each field has the correct default value
        expect(within(counterpartyRow).getByText("-")).toBeInTheDocument();
        expect(within(bankRow).getByText("-")).toBeInTheDocument();
        expect(within(accountNumberRow).getByText("-")).toBeInTheDocument();

        // Test transaction ID separately
        expect(screen.getByTestId("transaction-id")).toHaveTextContent("-");

        // Test narration default
        const narrationRow = screen.getByText("Narration").closest(".flex.items-start");
        expect(within(narrationRow).getByText("-")).toBeInTheDocument();
    });

    test("renders recurring fields with monthly frequencyType correctly", () => {
        const monthlyTransfer = {
            ...mockTransfer,
            frequencyType: "MONTHLY",
            firstPaymentDate: "2025-03-15", // 15th of the month
        };

        render(<OutgoingDetailsTab transfer={monthlyTransfer} activeTab="recurring" />);

        // Should format as "15th of each month, [time]"
        const onField = screen.getByText("On").nextElementSibling;
        expect(onField.textContent).toContain("th of each month");
    });

    test("formats different day suffixes correctly for monthly payments", () => {
        // Test different days to check suffix rendering
        const days = [
            { day: "2025-03-01", expected: "1st" },
            { day: "2025-03-02", expected: "2nd" },
            { day: "2025-03-03", expected: "3rd" },
            { day: "2025-03-04", expected: "4th" },
            { day: "2025-03-11", expected: "11th" },
            { day: "2025-03-21", expected: "21st" },
            { day: "2025-03-22", expected: "22nd" },
            { day: "2025-03-23", expected: "23rd" },
        ];

        // Test one day to validate
        const sampleDay = days[3]; // 4th
        const sampleTransfer = {
            ...mockTransfer,
            frequencyType: "MONTHLY",
            firstPaymentDate: sampleDay.day,
        };

        render(<OutgoingDetailsTab transfer={sampleTransfer} activeTab="recurring" />);

        // Should contain correct day with suffix
        const onField = screen.getByText("On").nextElementSibling;
        expect(onField.textContent).toContain(sampleDay.expected);
    });

    test("getDaySuffix returns correct suffix for all day number cases", () => {
        // Extract the getDaySuffix function for direct testing
        // We need to use some internal knowledge about how the component works
        // to test this private function

        // First we'll create a special transfer with different days of the month
        // and render multiple instances to check the formatting
        const testCases = [
            // Test branch: day > 3 && day < 21 (all return "th")
            { day: 4, expected: "th" },
            { day: 11, expected: "th" },
            { day: 12, expected: "th" },
            { day: 13, expected: "th" },
            { day: 20, expected: "th" },

            // Test branch: day % 10 === 1 (return "st")
            { day: 1, expected: "st" },
            { day: 21, expected: "st" },
            { day: 31, expected: "st" },

            // Test branch: day % 10 === 2 (return "nd")
            { day: 2, expected: "nd" },
            { day: 22, expected: "nd" },

            // Test branch: day % 10 === 3 (return "rd")
            { day: 3, expected: "rd" },
            { day: 23, expected: "rd" },

            // Test branch: default case (return "th")
            { day: 10, expected: "th" },
            { day: 24, expected: "th" },
            { day: 25, expected: "th" },
            { day: 30, expected: "th" },
        ];

        // For each test case, create a transfer with that day and verify
        // the formatted output contains the expected suffix
        testCases.forEach(({ day, expected }) => {
            // Create date string with the test day
            const dateStr = `2025-03-${day.toString().padStart(2, "0")}`; // e.g., "2025-03-01"

            const transfer = {
                ...mockTransfer,
                frequencyType: "MONTHLY",
                firstPaymentDate: dateStr,
            };

            const { unmount } = render(<OutgoingDetailsTab transfer={transfer} activeTab="recurring" />);

            // Check if the formatted string contains the day with correct suffix
            const onField = screen.getByText("On").nextElementSibling;

            // For day 1, check for "1st of each month"
            // For day 2, check for "2nd of each month"
            // etc.
            expect(onField.textContent).toContain(`${day}${expected} of each month`);

            // Unmount after each test to avoid conflicts between renders
            unmount();
        });
    });

    test("renders just time for frequencies other than weekly and monthly", () => {
        const dailyTransfer = {
            ...mockTransfer,
            frequencyType: "DAILY",
            firstPaymentDate: "2025-03-15",
        };

        render(<OutgoingDetailsTab transfer={dailyTransfer} activeTab="recurring" />);

        // Should only show the time for frequencies other than weekly/monthly
        const onField = screen.getByText("On").nextElementSibling;
        expect(onField.textContent).not.toContain("of each month");
        expect(onField.textContent).not.toContain("days,");
        // Should contain time in am/pm format
        expect(onField.textContent).toMatch(/\d{1,2}:\d{2}\s*(am|pm)/);
    });

    // New test for bulk transfer rendering
    test("renders bulk transfer with correct fields", () => {
        // Setup isBulkTransfer mock to return true for this test
        isBulkTransfer.mockReturnValue(true);
        calculateBulkTransferAmount.mockReturnValue(250000);

        render(<OutgoingDetailsTab transfer={mockBulkTransfer} activeTab="sent" />);

        // Check that only the 5 fields for bulk transfers are rendered
        expect(screen.getByText("Counterparty")).toBeInTheDocument();
        expect(screen.getByText("Total amount")).toBeInTheDocument();
        expect(screen.getByText("Date")).toBeInTheDocument();
        expect(screen.getByText("Transaction reference")).toBeInTheDocument();
        expect(screen.getByText("Narration")).toBeInTheDocument();

        // Check that bulk-specific values are shown
        expect(screen.getByText("5 recipients")).toBeInTheDocument();

        // Check that amounts are calculated correctly
        expect(calculateBulkTransferAmount).toHaveBeenCalledWith(mockBulkTransfer);
        expect(formatNumberToNaira).toHaveBeenCalledWith(250000);

        // Regular fields that shouldn't appear in bulk view
        expect(screen.queryByText("Bank")).not.toBeInTheDocument();
        expect(screen.queryByText("Account number")).not.toBeInTheDocument();
    });

    // Test for bulk transfer with each tab type
    test("renders bulk transfer in recurring tab with correct fields", () => {
        isBulkTransfer.mockReturnValue(true);
        calculateBulkTransferAmount.mockReturnValue(250000);

        render(<OutgoingDetailsTab transfer={mockBulkTransfer} activeTab="recurring" />);

        // Should still show only the 5 bulk transfer fields
        expect(screen.getByText("Counterparty")).toBeInTheDocument();
        expect(screen.getByText("Total amount")).toBeInTheDocument();
        expect(screen.getByText("Date")).toBeInTheDocument();
        expect(screen.getByText("Transaction reference")).toBeInTheDocument();
        expect(screen.getByText("Narration")).toBeInTheDocument();

        // Even though it's recurring tab, should NOT show recurring-specific fields for bulk
        expect(screen.queryByText("Frequency")).not.toBeInTheDocument();
        expect(screen.queryByText("On")).not.toBeInTheDocument();
        expect(screen.queryByText("Ends")).not.toBeInTheDocument();
    });

    // Test for incomplete bulk transfer data
    test("handles missing data in bulk transfers gracefully", () => {
        isBulkTransfer.mockReturnValue(true);
        calculateBulkTransferAmount.mockReturnValue(0);

        const incompleteBulkTransfer = {
            totalTransfers: 3,
            id: "",
            date: null,
            narration: null,
        };

        render(<OutgoingDetailsTab transfer={incompleteBulkTransfer} activeTab="sent" />);

        // Check that default values are shown for missing fields
        expect(screen.getByTestId("transaction-id")).toHaveTextContent("-");

        // Verify the date field shows the default "-"
        const dateRow = screen.getByText("Date").closest(".flex.items-start");
        expect(within(dateRow).getByText("-")).toBeInTheDocument();

        // Should still show placeholder for recipients count
        expect(screen.getByText("3 recipients")).toBeInTheDocument();

        // Narration should fallback to default
        const narrationRow = screen.getByText("Narration").closest(".flex.items-start");
        expect(within(narrationRow).getByText("-")).toBeInTheDocument();
    });
});
