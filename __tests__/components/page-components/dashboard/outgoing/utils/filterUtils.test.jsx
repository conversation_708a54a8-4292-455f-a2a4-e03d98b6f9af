import {
    filterOutgoing,
    matchesAmount,
    matchesDateRange,
    matchesExactAmount,
    matchesSearch,
} from "@/components/page-components/dashboard/outgoing/utils/filterUtils";

const sampleOutgoing = [
    {
        counterparty: "<PERSON>",
        narration: "Payment for services",
        accountNumber: "*********",
        amount: 500,
        date: "2025-03-01",
    },
    {
        counterparty: "<PERSON>",
        narration: "Refund",
        accountNumber: "*********",
        amount: 1500,
        date: "2025-02-20",
    },
    {
        counterparty: "<PERSON>",
        narration: "Loan repayment",
        accountNumber: "*********",
        amount: 800,
        date: "2025-02-25",
    },
];

describe("Filtering functions - Functional Coverage", () => {
    test("matchesSearch correctly matches search queries", () => {
        expect(matchesSearch(sampleOutgoing[0], "john")).toBe(true);
        expect(matchesSearch(sampleOutgoing[0], "payment")).toBe(true);
        expect(matchesSearch(sampleOutgoing[0], "123456")).toBe(true);
        expect(matchesSearch(sampleOutgoing[0], "XYZ")).toBe(false);
        expect(matchesSearch(sampleOutgoing[1], "Smith")).toBe(true);
        expect(matchesSearch(sampleOutgoing[2], "Loan repayment")).toBe(true);
    });

    test("matchesAmount handles all edge cases", () => {
        expect(matchesAmount(sampleOutgoing[0], "400", "600")).toBe(true);
        expect(matchesAmount(sampleOutgoing[0], "600", "1000")).toBe(false);
        expect(matchesAmount(sampleOutgoing[1], "1000", undefined)).toBe(true);
        expect(matchesAmount(sampleOutgoing[1], undefined, "1400")).toBe(false);
        expect(matchesAmount(sampleOutgoing[1], undefined, undefined)).toBe(true);
    });

    test("matchesAmount handles 'greater than' filtering correctly", () => {
        // Sample data: sampleOutgoing[0] has amount: 500, sampleOutgoing[1] has amount: 1500

        // "Greater than 500" should NOT include 500 (exact match)
        expect(matchesAmount(sampleOutgoing[0], "500", undefined)).toBe(false);

        // "Greater than 499" should include 500
        expect(matchesAmount(sampleOutgoing[0], "499", undefined)).toBe(true);

        // "Greater than 501" should NOT include 500
        expect(matchesAmount(sampleOutgoing[0], "501", undefined)).toBe(false);

        // "Greater than 1500" should NOT include 1500 (exact match)
        expect(matchesAmount(sampleOutgoing[1], "1500", undefined)).toBe(false);

        // "Greater than 1499" should include 1500
        expect(matchesAmount(sampleOutgoing[1], "1499", undefined)).toBe(true);
    });

    test("matchesAmount handles 'less than' filtering correctly", () => {
        // Sample data: sampleOutgoing[0] has amount: 500, sampleOutgoing[1] has amount: 1500

        // "Less than 500" should NOT include 500 (exact match)
        expect(matchesAmount(sampleOutgoing[0], undefined, "500")).toBe(false);

        // "Less than 501" should include 500
        expect(matchesAmount(sampleOutgoing[0], undefined, "501")).toBe(true);

        // "Less than 499" should NOT include 500
        expect(matchesAmount(sampleOutgoing[0], undefined, "499")).toBe(false);

        // "Less than 1500" should NOT include 1500 (exact match)
        expect(matchesAmount(sampleOutgoing[1], undefined, "1500")).toBe(false);

        // "Less than 1501" should include 1500
        expect(matchesAmount(sampleOutgoing[1], undefined, "1501")).toBe(true);
    });

    test("matchesDateRange correctly handles various date cases", () => {
        expect(matchesDateRange(sampleOutgoing[0], "2025-03-01", "2025-03-10")).toBe(true);
        expect(matchesDateRange(sampleOutgoing[0], "2025-02-01", "2025-02-28")).toBe(false);
        expect(matchesDateRange(sampleOutgoing[1], undefined, "2025-02-25")).toBe(true);
        expect(matchesDateRange(sampleOutgoing[2], "2025-03-01", undefined)).toBe(false);
        expect(matchesDateRange(sampleOutgoing[2], undefined, undefined)).toBe(true);
    });

    test("matchesDateRange uses inclusive date comparisons", () => {
        // Transaction date is "2025-03-01"
        const transaction = sampleOutgoing[0];

        // Test exact match on start date (should be included)
        expect(matchesDateRange(transaction, "2025-03-01", "2025-03-10")).toBe(true);

        // Test exact match on end date (should be included)
        expect(matchesDateRange(transaction, "2025-02-25", "2025-03-01")).toBe(true);

        // Test exact match on both start and end date (should be included)
        expect(matchesDateRange(transaction, "2025-03-01", "2025-03-01")).toBe(true);

        // Transaction date is "2025-02-20"
        const transaction2 = sampleOutgoing[1];

        // Test exact match on start date
        expect(matchesDateRange(transaction2, "2025-02-20", "2025-02-28")).toBe(true);

        // Test exact match on end date
        expect(matchesDateRange(transaction2, "2025-02-15", "2025-02-20")).toBe(true);

        // Test exact match on both dates
        expect(matchesDateRange(transaction2, "2025-02-20", "2025-02-20")).toBe(true);

        // Test date outside range (should not be included)
        expect(matchesDateRange(transaction, "2025-03-02", "2025-03-10")).toBe(false);
        expect(matchesDateRange(transaction, "2025-02-01", "2025-02-28")).toBe(false);
    });

    test("matchesDateRange handles timestamps with time components correctly", () => {
        // Transfer with timestamp at end of day
        const transferWithTime = {
            date: "2025-03-01T23:45:30.789012",
            amount: 1000,
        };

        // Should be included when filtering by exact date (end of day boundary)
        expect(matchesDateRange(transferWithTime, "2025-02-25", "2025-03-01")).toBe(true);

        // Should be included when filtering by same date range
        expect(matchesDateRange(transferWithTime, "2025-03-01", "2025-03-01")).toBe(true);

        // Transfer with timestamp at start of day
        const transferStartOfDay = {
            date: "2025-03-01T00:00:00.000000",
            amount: 1000,
        };

        // Should be included when filtering by exact date (start of day boundary)
        expect(matchesDateRange(transferStartOfDay, "2025-03-01", "2025-03-05")).toBe(true);

        // Transfer with mid-day timestamp
        const transferMidDay = {
            date: "2025-03-01T14:30:15.123456",
            amount: 1000,
        };

        // Should be included for same day filter
        expect(matchesDateRange(transferMidDay, "2025-03-01", "2025-03-01")).toBe(true);

        // Should NOT be included if date is outside range
        const transferNextDay = {
            date: "2025-03-02T00:00:01.000000",
            amount: 1000,
        };

        expect(matchesDateRange(transferNextDay, "2025-02-25", "2025-03-01")).toBe(false);
    });

    test("matchesExactAmount covers all possibilities", () => {
        expect(matchesExactAmount(sampleOutgoing[0], "500")).toBe(true);
        expect(matchesExactAmount(sampleOutgoing[0], "600")).toBe(false);
        expect(matchesExactAmount(sampleOutgoing[1], undefined)).toBe(true);
        expect(matchesExactAmount(sampleOutgoing[2], "800")).toBe(true);
    });

    test("filterOutgoing applies all filter criteria correctly", () => {
        const filteredBySearch = filterOutgoing(sampleOutgoing, { search: "Jane" });
        expect(filteredBySearch).toEqual([sampleOutgoing[1]]);

        const filteredByAmount = filterOutgoing(sampleOutgoing, { minAmount: "600", maxAmount: "1000" });
        expect(filteredByAmount).toEqual([sampleOutgoing[2]]);

        const filteredByDate = filterOutgoing(sampleOutgoing, { startDate: "2025-02-22", endDate: "2025-03-05" });
        expect(filteredByDate).toEqual([sampleOutgoing[0], sampleOutgoing[2]]);

        // Test inclusive date filtering
        const filteredByExactStartDate = filterOutgoing(sampleOutgoing, {
            startDate: "2025-03-01",
            endDate: "2025-03-05",
        });
        expect(filteredByExactStartDate).toEqual([sampleOutgoing[0]]);

        const filteredByExactEndDate = filterOutgoing(sampleOutgoing, {
            startDate: "2025-02-15",
            endDate: "2025-02-20",
        });
        expect(filteredByExactEndDate).toEqual([sampleOutgoing[1]]);

        const filteredByExactBothDates = filterOutgoing(sampleOutgoing, {
            startDate: "2025-02-25",
            endDate: "2025-02-25",
        });
        expect(filteredByExactBothDates).toEqual([sampleOutgoing[2]]);

        const filteredByExactAmount = filterOutgoing(sampleOutgoing, { amount: "500" });
        expect(filteredByExactAmount).toEqual([sampleOutgoing[0]]);

        const filteredByMultipleCriteria = filterOutgoing(sampleOutgoing, {
            search: "Alice",
            minAmount: "600",
            maxAmount: "1000",
            startDate: "2025-02-01",
            endDate: "2025-03-05",
        });
        expect(filteredByMultipleCriteria).toEqual([sampleOutgoing[2]]);
    });

    test("filterOutgoing handles 'greater than' amount filtering correctly", () => {
        // Sample data: amounts are 500, 1500, 800

        // "Greater than 500" should only include 1500 and 800 (not 500 itself)
        const greaterThan500 = filterOutgoing(sampleOutgoing, { minAmount: "500" });
        expect(greaterThan500).toEqual([sampleOutgoing[1], sampleOutgoing[2]]); // 1500 and 800

        // "Greater than 800" should only include 1500 (not 800 itself)
        const greaterThan800 = filterOutgoing(sampleOutgoing, { minAmount: "800" });
        expect(greaterThan800).toEqual([sampleOutgoing[1]]); // only 1500

        // "Greater than 1500" should include nothing (not 1500 itself)
        const greaterThan1500 = filterOutgoing(sampleOutgoing, { minAmount: "1500" });
        expect(greaterThan1500).toEqual([]);
    });

    test("filterOutgoing handles 'less than' amount filtering correctly", () => {
        // Sample data: amounts are 500, 1500, 800

        // "Less than 800" should only include 500 (not 800 itself)
        const lessThan800 = filterOutgoing(sampleOutgoing, { maxAmount: "800" });
        expect(lessThan800).toEqual([sampleOutgoing[0]]); // only 500

        // "Less than 500" should include nothing (not 500 itself)
        const lessThan500 = filterOutgoing(sampleOutgoing, { maxAmount: "500" });
        expect(lessThan500).toEqual([]);

        // "Less than 1500" should include 500 and 800 (not 1500 itself)
        const lessThan1500 = filterOutgoing(sampleOutgoing, { maxAmount: "1500" });
        expect(lessThan1500).toEqual([sampleOutgoing[0], sampleOutgoing[2]]); // 500 and 800
    });
});

describe("Filtering functions - Edge Cases and Coverage Improvement", () => {
    // Test null/undefined handling
    test("handles null and undefined transfers array", () => {
        expect(filterOutgoing(null, { search: "test" })).toEqual([]);
        expect(filterOutgoing(undefined, { search: "test" })).toEqual([]);
        expect(filterOutgoing("not an array", { search: "test" })).toEqual([]);
        expect(filterOutgoing(123, { search: "test" })).toEqual([]);
    });

    // Test edge cases for individual filter functions
    test("matchesSearch handles edge cases", () => {
        // Null/undefined transfer
        expect(matchesSearch(null, "test")).toBe(true);
        expect(matchesSearch(undefined, "test")).toBe(true);

        // Empty query
        expect(matchesSearch(sampleOutgoing[0], "")).toBe(true);
        expect(matchesSearch(sampleOutgoing[0], null)).toBe(true);
        expect(matchesSearch(sampleOutgoing[0], undefined)).toBe(true);

        // Transfer with missing properties
        const incompleteTransfer = { amount: 100 };
        expect(matchesSearch(incompleteTransfer, "test")).toBe(false);

        // Transfer with null properties
        const nullPropsTransfer = {
            counterparty: null,
            narration: null,
            accountNumber: null,
            amount: 100,
        };
        expect(matchesSearch(nullPropsTransfer, "test")).toBe(false);
    });

    test("matchesAmount handles edge cases", () => {
        // Null/undefined transfer
        expect(matchesAmount(null, "100", "200")).toBe(true);
        expect(matchesAmount(undefined, "100", "200")).toBe(true);

        // Transfer with non-number amount
        const invalidAmountTransfer = { amount: "not a number" };
        expect(matchesAmount(invalidAmountTransfer, "100", "200")).toBe(true);

        // Transfer with null amount
        const nullAmountTransfer = { amount: null };
        expect(matchesAmount(nullAmountTransfer, "100", "200")).toBe(true);

        // Transfer with undefined amount
        const undefinedAmountTransfer = { amount: undefined };
        expect(matchesAmount(undefinedAmountTransfer, "100", "200")).toBe(true);
    });

    test("matchesDateRange handles edge cases", () => {
        // Transfer with null date
        const nullDateTransfer = { date: null, amount: 100 };
        expect(matchesDateRange(nullDateTransfer, "2025-01-01", "2025-12-31")).toBe(true);

        // Transfer with undefined date
        const undefinedDateTransfer = { date: undefined, amount: 100 };
        expect(matchesDateRange(undefinedDateTransfer, "2025-01-01", "2025-12-31")).toBe(true);

        // Transfer with missing date property
        const noDateTransfer = { amount: 100 };
        expect(matchesDateRange(noDateTransfer, "2025-01-01", "2025-12-31")).toBe(true);
    });

    test("matchesExactAmount handles edge cases", () => {
        // Null/undefined transfer
        expect(matchesExactAmount(null, "100")).toBe(true);
        expect(matchesExactAmount(undefined, "100")).toBe(true);

        // Transfer with non-number amount
        const invalidAmountTransfer = { amount: "not a number" };
        expect(matchesExactAmount(invalidAmountTransfer, "100")).toBe(true);

        // Transfer with null amount
        const nullAmountTransfer = { amount: null };
        expect(matchesExactAmount(nullAmountTransfer, "100")).toBe(true);
    });

    // Test complex filter combinations to increase branch coverage
    test("filterOutgoing with complex filter combinations", () => {
        // Test exact amount with search and date filters
        const complexFilter1 = filterOutgoing(sampleOutgoing, {
            amount: "500",
            search: "John",
            startDate: "2025-02-01",
            endDate: "2025-03-31",
        });
        expect(complexFilter1).toEqual([sampleOutgoing[0]]);

        // Test min/max amount with search and date filters
        const complexFilter2 = filterOutgoing(sampleOutgoing, {
            minAmount: "400",
            maxAmount: "600",
            search: "payment",
            startDate: "2025-02-01",
            endDate: "2025-03-31",
        });
        expect(complexFilter2).toEqual([sampleOutgoing[0]]);

        // Test with all filters but no matches
        const noMatches = filterOutgoing(sampleOutgoing, {
            search: "NonExistent",
            minAmount: "2000",
            maxAmount: "3000",
            startDate: "2025-01-01",
            endDate: "2025-01-31",
        });
        expect(noMatches).toEqual([]);
    });

    // Test empty filters object
    test("filterOutgoing with empty filters", () => {
        const result = filterOutgoing(sampleOutgoing, {});
        expect(result).toEqual(sampleOutgoing);
    });

    // Test with only some filter properties
    test("filterOutgoing with partial filters", () => {
        const result1 = filterOutgoing(sampleOutgoing, { search: "" });
        expect(result1).toEqual(sampleOutgoing);

        const result2 = filterOutgoing(sampleOutgoing, { minAmount: undefined, maxAmount: undefined });
        expect(result2).toEqual(sampleOutgoing);
    });
});

// Additional coverage tests for complete function coverage
describe("Additional Function Coverage Tests", () => {
    test("filterTransfers alias function works correctly", () => {
        const { filterTransfers } = jest.requireActual(
            "@/components/page-components/dashboard/outgoing/utils/filterUtils"
        );

        // Test that filterTransfers is an alias for filterOutgoing
        const result = filterTransfers(sampleOutgoing, { search: "Jane" });
        expect(result).toEqual([sampleOutgoing[1]]);
    });

    test("all exported functions are properly covered", () => {
        const filterUtils = jest.requireActual("@/components/page-components/dashboard/outgoing/utils/filterUtils");

        // Ensure all exported functions exist and are callable
        expect(typeof filterUtils.matchesSearch).toBe("function");
        expect(typeof filterUtils.matchesAmount).toBe("function");
        expect(typeof filterUtils.matchesDateRange).toBe("function");
        expect(typeof filterUtils.matchesExactAmount).toBe("function");
        expect(typeof filterUtils.filterOutgoing).toBe("function");
        expect(typeof filterUtils.filterTransfers).toBe("function");

        // Test that filterTransfers is the same as filterOutgoing (alias)
        expect(filterUtils.filterTransfers).toBe(filterUtils.filterOutgoing);
    });

    // Test date formatting edge case (invalid date that throws in formatDateForSearch)
    test("matchesSearch handles invalid date formatting gracefully", () => {
        // Create a mock Date constructor that throws an error
        const originalDate = global.Date;
        global.Date = jest.fn(() => {
            throw new Error("Invalid date");
        });
        global.Date.prototype = originalDate.prototype;

        const transferWithDate = {
            counterparty: "Test User",
            date: "invalid-date-string",
            amount: 100,
        };

        // Should handle the error gracefully and still perform search
        expect(matchesSearch(transferWithDate, "Test")).toBe(true);
        expect(matchesSearch(transferWithDate, "invalid")).toBe(true);

        // Restore original Date
        global.Date = originalDate;
    });

    // Test bulk transfer formatting (totalTransfers > 1)
    test("matchesSearch handles bulk transfers correctly", () => {
        const bulkTransfer = {
            counterparty: "Bulk Payment",
            narration: "Salary payments",
            totalTransfers: 5,
            amount: 50000,
            date: "2025-03-01",
        };

        // Should match on "recipients" text for bulk transfers
        expect(matchesSearch(bulkTransfer, "5 recipients")).toBe(true);
        expect(matchesSearch(bulkTransfer, "recipients")).toBe(true);
        expect(matchesSearch(bulkTransfer, "5")).toBe(true);

        // Should still match on other fields
        expect(matchesSearch(bulkTransfer, "Bulk")).toBe(true);
        expect(matchesSearch(bulkTransfer, "Salary")).toBe(true);
    });

    // Test edge case where formatCounterpartySecondLine is called with exactly 1 totalTransfers
    test("matchesSearch handles single transfer with explicit totalTransfers=1", () => {
        const singleTransfer = {
            counterparty: "John Doe",
            bank: "Test Bank",
            accountNumber: "**********",
            totalTransfers: 1,
            amount: 1000,
            date: "2025-03-01",
        };

        // Should search in bank and account number for single transfers
        expect(matchesSearch(singleTransfer, "Test Bank")).toBe(true);
        expect(matchesSearch(singleTransfer, "**********")).toBe(true);
        expect(matchesSearch(singleTransfer, "Test Bank, **********")).toBe(true);
    });

    // Test edge case for invalid date that creates Date object but isNaN
    test("matchesSearch handles invalid date that creates NaN Date", () => {
        const transferWithInvalidDate = {
            counterparty: "Test User",
            date: "not-a-valid-date",
            amount: 100,
        };

        // Should handle invalid date gracefully (formatDateForSearch returns the original string)
        expect(matchesSearch(transferWithInvalidDate, "not-a-valid")).toBe(true);
        expect(matchesSearch(transferWithInvalidDate, "Test")).toBe(true);
    });

    // Test transfer with undefined amount (optional chaining branch coverage)
    test("matchesSearch handles transfer with undefined amount", () => {
        const transferWithoutAmount = {
            counterparty: "Test User",
            narration: "Test payment",
            // amount is undefined
        };

        // Should handle undefined amount gracefully
        expect(matchesSearch(transferWithoutAmount, "Test")).toBe(true);
        expect(matchesSearch(transferWithoutAmount, "payment")).toBe(true);
        expect(matchesSearch(transferWithoutAmount, "nonexistent")).toBe(false);
    });
});
