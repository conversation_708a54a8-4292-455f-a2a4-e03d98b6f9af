/* eslint-disable @typescript-eslint/no-require-imports */
import AllTransactions from "@/components/page-components/dashboard/transactions/all-transactions";
import "@testing-library/jest-dom";
import { act, render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";

// Mock all necessary modules
jest.mock("react-i18next", () => ({
    useTranslation: () => ({
        t: (key) => key,
    }),
}));

jest.mock("next/navigation", () => ({
    useSearchParams: jest.fn(() => new URLSearchParams()),
    useRouter: jest.fn(() => ({
        push: jest.fn(),
    })),
    usePathname: jest.fn(() => "/transactions"),
}));

jest.mock("use-debounce", () => ({
    useDebounce: (value) => [value],
}));

// Correct mock for redux hooks
jest.mock("@/redux/hooks", () => {
    const originalModule = jest.requireActual("@/redux/hooks");
    return {
        ...originalModule,
        useAppDispatch: jest.fn(() => jest.fn()),
        useAppSelector: jest.fn((selector) => {
            // Mock the Redux state structure
            const mockState = {
                permissions: {
                    systemPermissions: [
                        { id: 1, name: "View all transactions (CIB + external channels)", appModule: "transactions" },
                        { id: 2, name: "View transaction details", appModule: "transactions" },
                        { id: 3, name: "Generate transaction receipts", appModule: "transactions" },
                        { id: 4, name: "View team members", appModule: "team" },
                    ],
                    userPermissions: [
                        "View all transactions (CIB + external channels)",
                        "View transaction details",
                        "Generate transaction receipts",
                    ],
                    systemPermissionsLoading: false,
                    userPermissionsLoading: false,
                    systemPermissionsError: null,
                    userPermissionsError: null,
                    isReady: true,
                    isInitialized: true,
                    isUsingCache: false,
                    sessionId: "test-session",
                },
                transactions: {
                    accounts: {
                        loading: false,
                        data: [
                            { accountNumber: "*********0", name: "Test Account 1", balance: 1000 },
                            { accountNumber: "0*********", name: "Test Account 2", balance: 2000 },
                        ],
                    },
                    allTransactions: {
                        loading: false,
                        data: {
                            transactions: [
                                {
                                    id: "1",
                                    amount: 100,
                                    description: "Test Transaction",
                                    date: "2023-01-01",
                                    type: "credit",
                                },
                            ],
                            pagination: { total: 1, page: 1, limit: 10 },
                        },
                    },
                    currentAccount: "*********0",
                    filters: {},
                    pagination: { page: 1, limit: 10 },
                },
            };
            return selector(mockState);
        }),
    };
});

jest.mock("@/redux/actions/transactionActions", () => ({
    getAccounts: jest.fn().mockResolvedValue({ type: "GET_ACCOUNTS_SUCCESS" }),
    getTransactions: jest.fn().mockResolvedValue({ type: "GET_TRANSACTIONS_SUCCESS" }),
}));

jest.mock("@/redux/actions/transferActions", () => ({
    downloadReceipt: jest.fn().mockResolvedValue({ type: "DOWNLOAD_RECEIPT_SUCCESS" }),
}));

jest.mock("@/redux/slices/transactionSlice", () => ({
    transactionActions: {
        onPageChange: jest.fn(),
        onChangeRowsSize: jest.fn(),
        setCurrentAccount: jest.fn(),
    },
}));

// Mock permission-related components
jest.mock("@/hooks/usePermissionCheck", () => ({
    usePermissionCheck: () => ({
        canAccessRoute: () => true,
    }),
}));

jest.mock("@/contexts/PermissionContext", () => ({
    usePermissions: () => ({
        userPermissions: ["view_transactions", "export_transactions"],
        isLoadingPermissions: false,
        hasPermission: () => true,
        hasAnyPermission: () => true,
        hasAllPermissions: () => true,
    }),
}));

// Mock HOC to bypass permission checks
jest.mock("@/components/hoc/withPermissionCheck", () => {
    return (Component) => {
        return (props) => <Component {...props} />;
    };
});

// Mock the new dynamic permission HOC
jest.mock("@/components/hoc/withDynamicPermissionCheck", () => ({
    withDynamicPermissionCheck: (Component, options) => {
        const WrappedComponent = (props) => <Component {...props} />;
        WrappedComponent.displayName = `WithDynamicPermissionCheck(${Component.displayName || Component.name})`;
        return WrappedComponent;
    },
}));

// Mock the usePermissionConstants hook
jest.mock("@/hooks/usePermissionConstants", () => ({
    usePermissionConstants: () => ({
        PERMISSIONS: {
            TRANSACTIONS: {
                VIEW_ALL: "View all transactions (CIB + external channels)",
                VIEW_DETAILS: "View transaction details",
                GENERATE_RECEIPTS: "Generate transaction receipts",
            },
            TEAM: {
                VIEW_MEMBERS: "View team members",
            },
        },
        isLoaded: true,
        validatePermissions: jest.fn(() => true),
        hasPermission: jest.fn(() => true),
        hasAnyPermission: jest.fn(() => true),
        hasAllPermissions: jest.fn(() => true),
    }),
}));

// Mock the dynamic permissions constants
jest.mock("@/constants/dynamicPermissions", () => ({
    TRANSACTIONS_PERMISSIONS: {
        VIEW_ALL: "View all transactions (CIB + external channels)",
        VIEW_DETAILS: "View transaction details",
        GENERATE_RECEIPTS: "Generate transaction receipts",
    },
    TEAM_MANAGEMENT_PERMISSIONS: {
        VIEW_TEAM_MEMBERS: "View team members",
    },
}));

// Mock PermissionGate to always render children
jest.mock("@/components/common/permission-gate", () => {
    return ({ children }) => <>{children}</>;
});

// Mock child components
jest.mock("@/components/page-components/dashboard/transactions/transaction-details", () => ({
    TransactionDetails: ({ isOpen, handleCloseDetails, transactionId, handleOpenReport }) => (
        <div data-testid="transaction-details">
            {isOpen && (
                <div>
                    <div>Transaction ID: {transactionId}</div>
                    <button onClick={handleCloseDetails}>Close Details</button>
                    <button onClick={handleOpenReport}>Open Report</button>
                </div>
            )}
        </div>
    ),
}));

jest.mock("@/components/page-components/dashboard/transactions/multi-step-transaction-form-modal", () => ({
    MultiStepTransactionFormModal: ({ isOpen, closeModal, onComplete }) => (
        <div data-testid="multi-step-modal">
            {isOpen && (
                <div>
                    <div>Multi Step Modal</div>
                    <button onClick={closeModal}>Close Modal</button>
                    <button onClick={onComplete}>Complete</button>
                </div>
            )}
        </div>
    ),
}));

jest.mock("@/components/page-components/dashboard/transactions/account-selector", () => ({
    AccountSelector: ({ accounts, onAccountChange }) => (
        <select data-testid="account-selector" onChange={(e) => onAccountChange(e.target.value)}>
            <option value="">Select Account</option>
            {accounts?.map((account) => (
                <option key={account.accountNumber} value={account.accountNumber}>
                    {account.name}
                </option>
            ))}
        </select>
    ),
}));

jest.mock("@/components/page-components/dashboard/transactions/transaction-filter", () => ({
    TransactionFilter: ({ currentFilters, tempFilters, setTempFilters, onApplyFilter, onClearAll, onSearch }) => (
        <div data-testid="transaction-filter">
            <input
                data-testid="search-input"
                placeholder="Search"
                value={currentFilters.search}
                onChange={(e) => onSearch(e)}
            />
            <input
                data-testid="start-date-input"
                type="date"
                value={tempFilters.startDate}
                onChange={(e) => setTempFilters({ ...tempFilters, startDate: e.target.value })}
            />
            <input
                data-testid="end-date-input"
                type="date"
                value={tempFilters.endDate}
                onChange={(e) => setTempFilters({ ...tempFilters, endDate: e.target.value })}
            />
            <input
                data-testid="min-amount-input"
                type="number"
                value={tempFilters.minAmount}
                onChange={(e) => setTempFilters({ ...tempFilters, minAmount: e.target.value })}
            />
            <input
                data-testid="max-amount-input"
                type="number"
                value={tempFilters.maxAmount}
                onChange={(e) => setTempFilters({ ...tempFilters, maxAmount: e.target.value })}
            />
            <button data-testid="apply-filter-btn" onClick={onApplyFilter}>
                Apply Filter
            </button>
            <button data-testid="clear-filter-btn" onClick={onClearAll}>
                Clear All
            </button>
        </div>
    ),
}));

jest.mock("@/components/page-components/dashboard/transactions/export-transactions", () => ({
    ExportTransactions: ({ accounts, columns, data, selectedRows }) => (
        <div data-testid="export-transactions">
            <button data-testid="export-btn">Export</button>
            <div data-testid="export-data-count">{data.length} transactions</div>
            <div data-testid="selected-rows-count">{selectedRows.length} selected</div>
        </div>
    ),
}));

jest.mock("@/components/common/table/DataTable", () => ({
    DataTable: ({ columns, table, loading }) => (
        <div data-testid="data-table">
            {loading && <div data-testid="table-loading">Loading...</div>}
            <table>
                <thead>
                    <tr>
                        {columns.map((col, index) => (
                            <th key={index}>{col.header}</th>
                        ))}
                    </tr>
                </thead>
                <tbody>
                    {table.getRowModel().rows.map((row, index) => (
                        <tr key={index} data-testid={`table-row-${index}`}>
                            {row.getVisibleCells().map((cell, cellIndex) => (
                                <td key={cellIndex}>{cell.getValue?.()}</td>
                            ))}
                        </tr>
                    ))}
                </tbody>
            </table>
            <button
                data-testid="row-action-btn"
                onClick={() => {
                    table.options.meta?.setIsOpen(true);
                    table.options.meta?.setTransactionId("test-id");
                }}
            >
                View Details
            </button>
            <button
                data-testid="download-receipt-btn"
                onClick={() => table.options.meta?.handleReportDownload("test-id")}
            >
                Download Receipt
            </button>
            <button
                data-testid="select-row-btn"
                onClick={() => {
                    // Simulate selecting the first row
                    table.options.onRowSelectionChange?.({ 0: true });
                }}
            >
                Select Row
            </button>
        </div>
    ),
}));

jest.mock("@/components/common/pagination", () => ({
    Pagination: ({ totalItems, onPageChange, onItemsPerPageChange, initialItemsPerPage, initialPage }) => (
        <div data-testid="pagination">
            <button data-testid="prev-page" onClick={() => onPageChange(initialPage - 1)}>
                Previous
            </button>
            <span data-testid="current-page">Page {initialPage}</span>
            <button data-testid="next-page" onClick={() => onPageChange(initialPage + 1)}>
                Next
            </button>
            <select
                data-testid="page-size-select"
                value={initialItemsPerPage}
                onChange={(e) => onItemsPerPageChange(Number(e.target.value))}
            >
                <option value={10}>10</option>
                <option value={20}>20</option>
                <option value={50}>50</option>
            </select>
            <div data-testid="total-items">Total: {totalItems}</div>
        </div>
    ),
}));

jest.mock("@/components/page-components/dashboard/transactions/column-data", () => ({
    columns: [
        { header: "ID", accessorKey: "id" },
        { header: "Amount", accessorKey: "amount" },
        { header: "Date", accessorKey: "date" },
        { header: "Description", accessorKey: "description" },
    ],
}));

jest.mock("@/components/page-components/dashboard/transactions/transaction-receipts/receipt", () => ({
    Receipt: ({ variant, details }) => (
        <div data-testid="receipt">
            <div>Receipt variant: {variant}</div>
            <div>Transaction: {details?.id}</div>
        </div>
    ),
}));

jest.mock("@/functions/stringManipulations", () => ({
    convertCamelCaseToWords: (str) => str.replace(/([A-Z])/g, " $1").trim(),
}));

describe("AllTransactions Component", () => {
    const mockDispatch = jest.fn();
    const mockPush = jest.fn();
    const mockUseSearchParams = jest.fn();
    const mockUseRouter = jest.fn();
    const mockUsePathname = jest.fn();

    let mockGetAccounts;
    let mockGetTransactions;
    let mockDownloadReceipt;

    const defaultState = {
        list: {
            loading: false,
            count: 10,
            isAvailable: true,
            currentPage: 1,
            size: 10,
        },
        transactions: [
            { id: "1", amount: 100, date: "2023-01-01", description: "Test transaction" },
            { id: "2", amount: 200, date: "2023-01-02", description: "Another transaction" },
        ],
        accountGet: {
            loading: false,
            accounts: [
                { accountNumber: "*********", name: "Test Account" },
                { accountNumber: "*********", name: "Another Account" },
            ],
            account: { accountNumber: "*********", name: "Test Account" },
        },
    };

    beforeEach(() => {
        jest.clearAllMocks();

        mockGetAccounts = require("@/redux/actions/transactionActions").getAccounts;
        mockGetTransactions = require("@/redux/actions/transactionActions").getTransactions;
        mockDownloadReceipt = require("@/redux/actions/transferActions").downloadReceipt;

        mockUseSearchParams.mockReturnValue(new URLSearchParams());
        mockUseRouter.mockReturnValue({ push: mockPush });
        mockUsePathname.mockReturnValue("/transactions");

        require("next/navigation").useSearchParams = mockUseSearchParams;
        require("next/navigation").useRouter = mockUseRouter;
        require("next/navigation").usePathname = mockUsePathname;

        require("@/redux/hooks").useAppDispatch.mockImplementation(() => mockDispatch);
        require("@/redux/hooks").useAppSelector.mockImplementation(() => defaultState);
    });

    describe("Component Rendering", () => {
        it("renders the component with title and account selector", async () => {
            await act(async () => {
                render(<AllTransactions />);
            });

            expect(screen.getByText("Transactions")).toBeInTheDocument();
            expect(screen.getByTestId("account-selector")).toBeInTheDocument();
        });

        it("displays loading state for accounts", async () => {
            require("@/redux/hooks").useAppSelector.mockImplementation(() => ({
                ...defaultState,
                accountGet: {
                    loading: true,
                    accounts: [],
                    account: null,
                },
            }));

            await act(async () => {
                render(<AllTransactions />);
            });

            expect(screen.getByText("Loading accounts...")).toBeInTheDocument();
        });

        it("shows no accounts available message", async () => {
            require("@/redux/hooks").useAppSelector.mockImplementation(() => ({
                ...defaultState,
                accountGet: {
                    loading: false,
                    accounts: [],
                    account: null,
                },
            }));

            await act(async () => {
                render(<AllTransactions />);
            });

            expect(screen.getByText("No Accounts Available")).toBeInTheDocument();
            expect(
                screen.getByText(
                    "You don't have any accounts set up yet. Transaction data will be displayed once you have at least one account."
                )
            ).toBeInTheDocument();
        });

        it("renders transaction filter and export button when accounts exist", async () => {
            await act(async () => {
                render(<AllTransactions />);
            });

            expect(screen.getByTestId("transaction-filter")).toBeInTheDocument();
            expect(screen.getByTestId("export-transactions")).toBeInTheDocument();
        });

        it("renders data table and pagination when transactions exist", async () => {
            await act(async () => {
                render(<AllTransactions />);
            });

            expect(screen.getByTestId("data-table")).toBeInTheDocument();
            expect(screen.getByTestId("pagination")).toBeInTheDocument();
        });
    });

    describe("Account Selection", () => {
        it("dispatches setCurrentAccount when account is changed", async () => {
            const user = userEvent.setup();

            await act(async () => {
                render(<AllTransactions />);
            });

            const accountSelector = screen.getByTestId("account-selector");
            await user.selectOptions(accountSelector, "*********");

            expect(mockDispatch).toHaveBeenCalledWith(
                require("@/redux/slices/transactionSlice").transactionActions.setCurrentAccount("*********")
            );
        });
    });

    describe("Search Functionality", () => {
        it("handles search input changes", async () => {
            const user = userEvent.setup();

            await act(async () => {
                render(<AllTransactions />);
            });

            const searchInput = screen.getByTestId("search-input");
            await user.type(searchInput, "test search");

            expect(mockPush).toHaveBeenCalled();
        });

        it("debounces search input", async () => {
            const mockDebounce = jest.fn().mockReturnValue(["debounced value"]);
            require("use-debounce").useDebounce = mockDebounce;

            await act(async () => {
                render(<AllTransactions />);
            });

            expect(mockDebounce).toHaveBeenCalledWith("", 300);
        });
    });

    describe("Filter Functionality", () => {
        // it("applies filters correctly", async () => {
        //     const user = userEvent.setup();

        //     await act(async () => {
        //         render(<AllTransactions />);
        //     });

        //     const startDateInput = screen.getByTestId("start-date-input");
        //     await user.type(startDateInput, "2023-01-01");

        //     const applyFilterBtn = screen.getByTestId("apply-filter-btn");
        //     await user.click(applyFilterBtn);

        //     expect(mockGetTransactions).toHaveBeenCalledWith({
        //         accountNumber: "*********",
        //         params: expect.objectContaining({
        //             startDate: "2023-01-01",
        //         }),
        //     });
        // });

        it("clears all filters", async () => {
            const user = userEvent.setup();

            await act(async () => {
                render(<AllTransactions />);
            });

            const clearFilterBtn = screen.getByTestId("clear-filter-btn");
            await user.click(clearFilterBtn);

            expect(mockPush).toHaveBeenCalled();
        });

        it("displays current filter items", async () => {
            mockUseSearchParams.mockReturnValue(new URLSearchParams("startDate=2023-01-01&endDate=2023-12-31"));

            await act(async () => {
                render(<AllTransactions />);
            });

            expect(screen.getByText("Clear Filter")).toBeInTheDocument();
        });
    });

    describe("Pagination", () => {
        it("handles page change", async () => {
            const user = userEvent.setup();

            await act(async () => {
                render(<AllTransactions />);
            });

            const nextPageBtn = screen.getByTestId("next-page");
            await user.click(nextPageBtn);

            expect(mockDispatch).toHaveBeenCalledWith(
                require("@/redux/slices/transactionSlice").transactionActions.onPageChange(2)
            );
        });

        it("handles page size change", async () => {
            const user = userEvent.setup();

            await act(async () => {
                render(<AllTransactions />);
            });

            const pageSizeSelect = screen.getByTestId("page-size-select");
            await user.selectOptions(pageSizeSelect, "20");

            expect(mockDispatch).toHaveBeenCalledWith(
                require("@/redux/slices/transactionSlice").transactionActions.onChangeRowsSize(20)
            );
        });
    });

    describe("Transaction Details Modal", () => {
        it("opens transaction details modal", async () => {
            const user = userEvent.setup();

            await act(async () => {
                render(<AllTransactions />);
            });

            const viewDetailsBtn = screen.getByTestId("row-action-btn");
            await user.click(viewDetailsBtn);

            expect(screen.getByText("Transaction ID: test-id")).toBeInTheDocument();
        });

        it("closes transaction details modal", async () => {
            const user = userEvent.setup();

            await act(async () => {
                render(<AllTransactions />);
            });

            const viewDetailsBtn = screen.getByTestId("row-action-btn");
            await user.click(viewDetailsBtn);

            const closeBtn = screen.getByText("Close Details");
            await user.click(closeBtn);

            expect(screen.queryByText("Transaction ID: test-id")).not.toBeInTheDocument();
        });

        it("opens report modal from transaction details", async () => {
            const user = userEvent.setup();

            await act(async () => {
                render(<AllTransactions />);
            });

            const viewDetailsBtn = screen.getByTestId("row-action-btn");
            await user.click(viewDetailsBtn);

            const openReportBtn = screen.getByText("Open Report");
            await user.click(openReportBtn);

            expect(screen.getByText("Multi Step Modal")).toBeInTheDocument();
        });
    });

    describe("Receipt Download", () => {
        it("downloads transaction receipt", async () => {
            const user = userEvent.setup();

            await act(async () => {
                render(<AllTransactions />);
            });

            const downloadBtn = screen.getByTestId("download-receipt-btn");
            await user.click(downloadBtn);

            expect(mockDispatch).toHaveBeenCalledWith(
                mockDownloadReceipt({
                    transactionId: "test-id",
                    isUserInitiated: true,
                })
            );
        });
    });

    // describe("URL Parameters", () => {
    //     it("initializes filters from URL parameters", async () => {
    //         mockUseSearchParams.mockReturnValue(
    //             new URLSearchParams("pageNo=2&pageSize=20&search=test&startDate=2023-01-01")
    //         );

    //         await act(async () => {
    //             render(<AllTransactions />);
    //         });

    //         expect(mockGetTransactions).toHaveBeenCalledWith({
    //             accountNumber: "*********",
    //             params: expect.objectContaining({
    //                 pageNo: 1, // pageNo - 1
    //                 pageSize: 20,
    //                 startDate: "2023-01-01",
    //             }),
    //         });
    //     });
    // });

    describe("Loading States", () => {
        it("shows table loading state", async () => {
            require("@/redux/hooks").useAppSelector.mockImplementation(() => ({
                ...defaultState,
                list: {
                    ...defaultState.list,
                    loading: true,
                },
            }));

            await act(async () => {
                render(<AllTransactions />);
            });

            expect(screen.getByTestId("table-loading")).toBeInTheDocument();
        });

        it("hides pagination when no transactions", async () => {
            require("@/redux/hooks").useAppSelector.mockImplementation(() => ({
                ...defaultState,
                transactions: [],
            }));

            await act(async () => {
                render(<AllTransactions />);
            });

            expect(screen.queryByTestId("pagination")).not.toBeInTheDocument();
        });
    });

    describe("Edge Cases", () => {
        it("handles empty transaction list", async () => {
            require("@/redux/hooks").useAppSelector.mockImplementation(() => ({
                ...defaultState,
                transactions: [],
                list: {
                    ...defaultState.list,
                    count: 0,
                },
            }));

            await act(async () => {
                render(<AllTransactions />);
            });

            expect(screen.getByTestId("data-table")).toBeInTheDocument();
            expect(screen.queryByTestId("pagination")).not.toBeInTheDocument();
        });

        it("handles missing account", async () => {
            require("@/redux/hooks").useAppSelector.mockImplementation(() => ({
                ...defaultState,
                accountGet: {
                    ...defaultState.accountGet,
                    account: null,
                },
            }));

            await act(async () => {
                render(<AllTransactions />);
            });

            // Should not call getTransactions without account
            expect(mockGetTransactions).not.toHaveBeenCalled();
        });

        // it("formats dates correctly", async () => {
        //     mockUseSearchParams.mockReturnValue(new URLSearchParams("startDate=2023-01-01T00%3A00%3A00.000Z"));

        //     await act(async () => {
        //         render(<AllTransactions />);
        //     });

        //     expect(mockGetTransactions).toHaveBeenCalledWith({
        //         accountNumber: "*********",
        //         params: expect.objectContaining({
        //             startDate: "2023-01-01",
        //         }),
        //     });
        // });
    });

    describe("Data Fetching", () => {
        it("dispatches getAccounts on mount", async () => {
            await act(async () => {
                render(<AllTransactions />);
            });

            expect(mockDispatch).toHaveBeenCalledWith(mockGetAccounts());
        });

        it("fetches transactions when account is available", async () => {
            await act(async () => {
                render(<AllTransactions />);
            });

            expect(mockGetTransactions).toHaveBeenCalled();
        });

        it("refetches transactions when filters change", async () => {
            const user = userEvent.setup();

            await act(async () => {
                render(<AllTransactions />);
            });

            // Clear previous calls
            mockGetTransactions.mockClear();

            const minAmountInput = screen.getByTestId("min-amount-input");
            await user.type(minAmountInput, "100");

            const applyFilterBtn = screen.getByTestId("apply-filter-btn");
            await user.click(applyFilterBtn);

            expect(mockGetTransactions).toHaveBeenCalledWith({
                accountNumber: "*********",
                params: expect.objectContaining({
                    minAmount: "100",
                }),
            });
        });
    });

    describe("Print Functionality", () => {
        it("renders print receipt button", async () => {
            await act(async () => {
                render(<AllTransactions />);
            });

            const printBtn = screen.getByText("Print Receipt");
            expect(printBtn).toBeInTheDocument();
        });

        it("renders receipt component", async () => {
            await act(async () => {
                render(<AllTransactions />);
            });

            expect(screen.getByTestId("receipt")).toBeInTheDocument();
        });
    });

    describe("Component State Management", () => {
        it("manages modal states correctly", async () => {
            const user = userEvent.setup();

            await act(async () => {
                render(<AllTransactions />);
            });

            // Open transaction details
            const viewDetailsBtn = screen.getByTestId("row-action-btn");
            await user.click(viewDetailsBtn);

            expect(screen.getByText("Transaction ID: test-id")).toBeInTheDocument();

            // Open report modal
            const openReportBtn = screen.getByText("Open Report");
            await user.click(openReportBtn);

            expect(screen.getByText("Multi Step Modal")).toBeInTheDocument();

            // Close report modal
            const closeModalBtn = screen.getByText("Close Modal");
            await user.click(closeModalBtn);

            expect(screen.queryByText("Multi Step Modal")).not.toBeInTheDocument();
        });

        it("handles sorting state", async () => {
            await act(async () => {
                render(<AllTransactions />);
            });

            // The DataTable component should handle sorting internally
            expect(screen.getByTestId("data-table")).toBeInTheDocument();
        });

        it("handles row selection state", async () => {
            await act(async () => {
                render(<AllTransactions />);
            });

            // Export component should show selected rows count
            expect(screen.getByTestId("selected-rows-count")).toHaveTextContent("0 selected");
        });

        it("handles row selection with selected rows", async () => {
            const user = userEvent.setup();

            await act(async () => {
                render(<AllTransactions />);
            });

            // Initially no rows selected
            expect(screen.getByTestId("selected-rows-count")).toHaveTextContent("0 selected");

            // Click the select row button to simulate row selection
            await act(async () => {
                await user.click(screen.getByTestId("select-row-btn"));
            });

            // Export component should show selected rows count > 0
            expect(screen.getByTestId("selected-rows-count")).toHaveTextContent("1 selected");
        });
    });
});
