import { columns } from "@/components/page-components/dashboard/transactions/column-data";
import "@testing-library/jest-dom";
import { fireEvent, render, screen } from "@testing-library/react";

// Mock the Checkbox component
jest.mock("@/components/common/checkbox", () => ({
    __esModule: true,
    default: ({ checked, onCheckedChange, "aria-label": ariaLabel }) => (
        <input
            type="checkbox"
            checked={checked}
            onChange={(e) => onCheckedChange(e.target.checked)}
            aria-label={ariaLabel}
        />
    ),
}));

// Mock the TableMoreAction component
jest.mock("@/components/common/table/table-more-action", () => ({
    __esModule: true,
    default: jest.fn(({ data, menuItems }) => (
        <div>
            <button>More</button>
            <ul>
                {menuItems.map((item, index) => (
                    <li key={index}>
                        <button onClick={() => item.onClick(data)}>{item.label}</button>
                    </li>
                ))}
            </ul>
        </div>
    )),
}));

// Mock the entire stringManipulations module
jest.mock("@/functions/stringManipulations", () => ({
    convertFirstLetterToUppercase: jest.fn(),
    getNameInitials: jest.fn((name) =>
        name
            .split(" ")
            .map((n) => n[0])
            .join("")
    ),
}));

// Mock the Avatar component
jest.mock("@/components/common/Avatar", () => ({
    Avatar: ({ children }) => <div>{children}</div>,
    AvatarImage: () => <img alt="" />,
    AvatarFallback: ({ children }) => <span>{children}</span>,
}));

// Mock the ArrowDownIcon component
jest.mock("@/components/icons/table", () => ({
    ArrowDownIcon: () => <span>↓</span>,
}));

// Mock the formatDate function
jest.mock("@/functions/date", () => ({
    formatDate: jest.fn((date) => new Date(date).toLocaleDateString()),
}));

describe("Table Columns", () => {
    const mockRow = {
        original: {
            id: "1",
            createdDate: "2023-05-01",
            transactionType: "debit",
            counterparty: "John Doe",
            counterpartyLogo: "logo.png",
            bank: "Bank of America",
            source: "main account",
            amount: "1000.00",
        },
        getIsSelected: jest.fn(() => false),
        toggleSelected: jest.fn(),
        getValue: jest.fn((key) => mockRow.original[key]),
    };

    const mockTable = {
        getIsAllPageRowsSelected: jest.fn(() => false),
        getIsSomePageRowsSelected: jest.fn(() => false),
        toggleAllPageRowsSelected: jest.fn(),
        options: {
            meta: {
                setIsOpen: jest.fn(),
                setTransactionType: jest.fn(),
                setTransactionId: jest.fn(),
                setIsReportOpen: jest.fn(),
            },
        },
    };

    const mockColumn = {
        toggleSorting: jest.fn(),
        getIsSorted: jest.fn(() => "asc"),
    };

    test("select column", () => {
        const selectColumn = columns.find((col) => col.id === "select");
        expect(selectColumn).toBeDefined();

        if (selectColumn && selectColumn.header) {
            const HeaderComponent = selectColumn.header;
            render(<HeaderComponent table={mockTable} />);
            const checkbox = screen.getByLabelText("Select all");
            expect(checkbox).toBeInTheDocument();
            fireEvent.click(checkbox);
            expect(mockTable.toggleAllPageRowsSelected).toHaveBeenCalledWith(true);
        }

        if (selectColumn && selectColumn.cell) {
            const CellComponent = selectColumn.cell;
            render(<CellComponent row={mockRow} />);
            const checkbox = screen.getByLabelText("Select row");
            expect(checkbox).toBeInTheDocument();
            fireEvent.click(checkbox);
            expect(mockRow.toggleSelected).toHaveBeenCalledWith(true);
        }

        expect(selectColumn?.enableSorting).toBe(false);
        expect(selectColumn?.enableHiding).toBe(false);
    });

    test("date column", () => {
        const dateColumn = columns.find((col) => col.accessorKey === "createdDate");
        expect(dateColumn).toBeDefined();

        if (dateColumn && dateColumn.header) {
            const HeaderComponent = dateColumn.header;
            render(<HeaderComponent column={mockColumn} />);
            const button = screen.getByRole("button");
            expect(button).toHaveTextContent("Date");
            expect(screen.getByText("↓")).toBeInTheDocument();
            fireEvent.click(button);
            expect(mockColumn.toggleSorting).toHaveBeenCalledWith(true);
        }
    });

    test("narration column", () => {
        const narrationColumn = columns.find((col) => col.accessorKey === "narration");
        expect(narrationColumn).toBeDefined();
        expect(narrationColumn?.header).toBe("Narration");
    });

    test("amount column", () => {
        const amountColumn = columns.find((col) => col.accessorKey === "amount");
        expect(amountColumn).toBeDefined();

        if (amountColumn && amountColumn.header) {
            const HeaderComponent = amountColumn.header;
            render(<HeaderComponent column={mockColumn} />);
            const button = screen.getByRole("button");
            expect(button).toHaveTextContent("Amount");
            expect(screen.getByText("↓")).toBeInTheDocument();
            fireEvent.click(button);
            expect(mockColumn.toggleSorting).toHaveBeenCalledWith(true);
        }

        if (amountColumn && amountColumn.cell) {
            const CellComponent = amountColumn.cell;
            render(<CellComponent row={mockRow} />);
            expect(screen.getByText("- ₦1,000.00")).toBeInTheDocument();
        }
    });

    test("actions column", () => {
        const actionsColumn = columns.find((col) => col.id === "actions");
        expect(actionsColumn).toBeDefined();

        if (actionsColumn && actionsColumn.cell) {
            const CellComponent = actionsColumn.cell;

            const testCases = [
                { type: "credit", expectedItems: ["View", "Report", "Download receipt"] },
                { type: "debit", expectedItems: ["View", "Report", "Download receipt"] },
            ];

            testCases.forEach(({ type, expectedItems }) => {
                const mockRowWithType = {
                    ...mockRow,
                    original: { ...mockRow.original, transactionType: type },
                };

                const mockTableWithSetIsOpen = {
                    ...mockTable,
                    options: {
                        meta: {
                            setIsOpen: jest.fn(),
                            setTransactionType: jest.fn(),
                            setTransactionId: jest.fn(),
                            setIsReportOpen: jest.fn(),
                        },
                    },
                };

                render(<CellComponent row={mockRowWithType} table={mockTableWithSetIsOpen} />);

                // Test onClick functionality for each menu item
                // expectedItems.forEach((item) => {
                //     const mockConsoleLog = jest.spyOn(console, "log");
                //     fireEvent.click(screen.queryAllByText(item)[0]);
                //     expect(mockConsoleLog).toHaveBeenCalledWith(mockRowWithType.original.id);
                //     mockConsoleLog.mockRestore();
                // });
            });

            // Test when setIsOpen is undefined
            const mockTableWithoutSetIsOpen = {
                ...mockTable,
                options: {
                    meta: {},
                },
            };

            const { rerender } = render(<CellComponent row={mockRow} table={mockTableWithoutSetIsOpen} />);
            rerender(<CellComponent row={mockRow} table={mockTableWithoutSetIsOpen} />);

            const mockConsoleLog = jest.spyOn(console, "log");
            fireEvent.click(screen.getAllByText("View")[0]);
            // expect(mockConsoleLog).toHaveBeenCalledWith(mockRow.original.id);
            // setIsOpen should not be called when it's undefined
            expect(mockTableWithoutSetIsOpen.options.meta.setIsOpen).toBeUndefined();
            mockConsoleLog.mockRestore();
        }
    });

    test("narration column renders correctly", () => {
        const narrationColumn = columns.find((col) => col.accessorKey === "narration");
        expect(narrationColumn).toBeDefined();

        if (narrationColumn && narrationColumn.cell) {
            const CellComponent = narrationColumn.cell;
            render(<CellComponent row={mockRow} />);
            expect(screen.getByText(mockRow.original.narration)).toBeInTheDocument();
        }
    });

    test("actions column handles report download correctly", () => {
        const actionsColumn = columns.find((col) => col.id === "actions");
        expect(actionsColumn).toBeDefined();

        if (actionsColumn && actionsColumn.cell) {
            const CellComponent = actionsColumn.cell;
            const handleReportDownload = jest.fn();
            const mockTableWithHandleReportDownload = {
                ...mockTable,
                options: {
                    meta: {
                        setIsOpen: jest.fn(),
                        setTransactionId: jest.fn(),
                        handleReportDownload,
                    },
                },
            };

            render(<CellComponent row={mockRow} table={mockTableWithHandleReportDownload} />);
            fireEvent.click(screen.getByText("Download receipt"));
            expect(handleReportDownload).toHaveBeenCalledTimes(1);
            expect(handleReportDownload).toHaveBeenCalledWith(mockRow.original.transactionId);
        }
    });

    test("actions column handles view correctly", () => {
        const actionsColumn = columns.find((col) => col.id === "actions");
        expect(actionsColumn).toBeDefined();

        if (actionsColumn && actionsColumn.cell) {
            const CellComponent = actionsColumn.cell;
            const setIsOpen = jest.fn();
            const setTransactionId = jest.fn();
            const mockTableWithSetIsOpen = {
                ...mockTable,
                options: {
                    meta: {
                        setIsOpen,
                        setTransactionId,
                        handleReportDownload: jest.fn(),
                    },
                },
            };

            render(<CellComponent row={mockRow} table={mockTableWithSetIsOpen} />);
            fireEvent.click(screen.getByText("View"));
            expect(setTransactionId).toHaveBeenCalledTimes(1);
            expect(setTransactionId).toHaveBeenCalledWith(mockRow.original.transactionId);
            expect(setIsOpen).toHaveBeenCalledTimes(1);
            expect(setIsOpen).toHaveBeenCalledWith(true);
        }
    });
});
