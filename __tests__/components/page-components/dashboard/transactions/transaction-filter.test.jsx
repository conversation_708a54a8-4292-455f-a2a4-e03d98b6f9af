import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { TransactionFilter } from "@/components/page-components/dashboard/transactions/transaction-filter";

// Mock the dependencies
jest.mock("@/components/common/button", () => ({
    __esModule: true,
    default: ({ children, onClick }) => <button onClick={onClick}>{children}</button>,
}));

jest.mock("@/components/common/label-input", () => ({
    __esModule: true,
    default: ({ name, label, value, onChange }) => (
        <input data-testid={name} placeholder={label} value={value} onChange={onChange} />
    ),
}));

jest.mock("@/components/common/dropdown", () => ({
    __esModule: true,
    default: ({ name, value, options, onChange }) => (
        <select data-testid={name} value={value.value} onChange={(e) => onChange({ value: e.target.value })}>
            {options.map((option) => (
                <option key={option.value} value={option.value}>
                    {option.label}
                </option>
            ))}
        </select>
    ),
}));

jest.mock("@/components/common/date-picker", () => ({
    __esModule: true,
    default: ({ label, value, onChange }) => (
        <input
            type="date"
            data-testid={label.toLowerCase().replace(" ", "-")}
            value={value?.toISOString().split("T")[0] || ""}
            onChange={(e) => onChange(new Date(e.target.value))}
        />
    ),
}));

jest.mock("@/components/common/drawer", () => ({
    __esModule: true,
    default: ({ children, isOpen }) => (isOpen ? <div data-testid="side-drawer">{children}</div> : null),
}));

jest.mock("@/components/common/search-input", () => ({
    __esModule: true,
    default: ({ value, onChange }) => <input data-testid="search-input" value={value} onChange={onChange} />,
}));

describe("TransactionFilter", () => {
    const mockProps = {
        currentFilters: {},
        tempFilters: {},
        setTempFilters: jest.fn(),
        onApplyFilter: jest.fn(),
        onClearAll: jest.fn(),
        onSearch: jest.fn(),
    };

    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("renders without crashing", () => {
        render(<TransactionFilter {...mockProps} />);
        expect(screen.getByText("Add filter")).toBeInTheDocument();
    });

    it("opens the filter drawer when 'Add filter' is clicked", () => {
        render(<TransactionFilter {...mockProps} />);
        fireEvent.click(screen.getByText("Add filter"));
        expect(screen.getByTestId("side-drawer")).toBeInTheDocument();
    });

    it("calls onSearch when the search input changes", () => {
        render(<TransactionFilter {...mockProps} />);
        const searchInput = screen.getByTestId("search-input");
        fireEvent.change(searchInput, { target: { value: "test search" } });
        expect(mockProps.onSearch).toHaveBeenCalled();
    });

    it("updates date range when a preset is selected for last 30days", async () => {
        render(<TransactionFilter {...mockProps} />);
        fireEvent.click(screen.getByText("Add filter"));

        const dateDropdown = screen.getByTestId("date");
        fireEvent.change(dateDropdown, { target: { value: "Last 30 days" } });

        await waitFor(() => {
            expect(mockProps.setTempFilters).toHaveBeenCalledWith(
                expect.objectContaining({
                    startDate: expect.any(String),
                    endDate: expect.any(String),
                })
            );
        });
    });

    it("updates date range when a preset is selected for last 3 months", async () => {
        render(<TransactionFilter {...mockProps} />);
        fireEvent.click(screen.getByText("Add filter"));

        const dateDropdown = screen.getByTestId("date");
        fireEvent.change(dateDropdown, { target: { value: "Last 3 months" } });

        await waitFor(() => {
            expect(mockProps.setTempFilters).toHaveBeenCalledWith(
                expect.objectContaining({
                    startDate: expect.any(String),
                    endDate: expect.any(String),
                })
            );
        });
    });

    it("updates date range when a preset is selected for last 6 months", async () => {
        render(<TransactionFilter {...mockProps} />);
        fireEvent.click(screen.getByText("Add filter"));

        const dateDropdown = screen.getByTestId("date");
        fireEvent.change(dateDropdown, { target: { value: "Last 6 months" } });

        await waitFor(() => {
            expect(mockProps.setTempFilters).toHaveBeenCalledWith(
                expect.objectContaining({
                    startDate: expect.any(String),
                    endDate: expect.any(String),
                })
            );
        });
    });

    it("updates date range when a preset is selected for custom", async () => {
        render(<TransactionFilter {...mockProps} />);
        fireEvent.click(screen.getByText("Add filter"));

        const dateDropdown = screen.getByTestId("date");
        fireEvent.change(dateDropdown, { target: { value: "custom" } });

        await waitFor(() => {
            expect(mockProps.setTempFilters).toHaveBeenCalledWith(
                expect.objectContaining({
                    startDate: expect.any(String),
                    endDate: expect.any(String),
                })
            );
        });
    });

    it("updates amount range when 'Is between' is selected", () => {
        render(<TransactionFilter {...mockProps} />);
        fireEvent.click(screen.getByText("Add filter"));

        const amountDropdown = screen.getByTestId("amount");
        fireEvent.change(amountDropdown, { target: { value: "Is between" } });

        const minAmountInput = screen.getByTestId("min-amount");
        const maxAmountInput = screen.getByTestId("max-amount");

        fireEvent.change(minAmountInput, { target: { value: "100" } });
        expect(mockProps.setTempFilters).toHaveBeenCalledWith(
            expect.objectContaining({
                minAmount: "100",
                maxAmount: "",
            })
        );

        fireEvent.change(maxAmountInput, { target: { value: "200" } });
        expect(mockProps.setTempFilters).toHaveBeenCalledWith(
            expect.objectContaining({
                minAmount: "",
                maxAmount: "200",
            })
        );
    });

    it("updates amount 'Is less than' is selected", () => {
        render(<TransactionFilter {...mockProps} />);
        fireEvent.click(screen.getByText("Add filter"));

        const amountDropdown = screen.getByTestId("amount");
        fireEvent.change(amountDropdown, { target: { value: "Is less than" } });

        const maxAmountInput = screen.getByTestId("max-amount");

        fireEvent.change(maxAmountInput, { target: { value: "100" } });
        expect(mockProps.setTempFilters).toHaveBeenCalledWith(
            expect.objectContaining({
                minAmount: "",
                maxAmount: "100",
            })
        );
    });

    it("updates amount 'Is greater than' is selected", () => {
        render(<TransactionFilter {...mockProps} />);
        fireEvent.click(screen.getByText("Add filter"));

        const amountDropdown = screen.getByTestId("amount");
        fireEvent.change(amountDropdown, { target: { value: "Is greater than" } });

        const minAmountInput = screen.getByTestId("min-amount");

        fireEvent.change(minAmountInput, { target: { value: "100" } });
        expect(mockProps.setTempFilters).toHaveBeenCalledWith(
            expect.objectContaining({
                minAmount: "100",
                maxAmount: "",
            })
        );
    });

    it("updates amount 'Is exactly' is selected", () => {
        render(<TransactionFilter {...mockProps} />);
        fireEvent.click(screen.getByText("Add filter"));

        const amountDropdown = screen.getByTestId("amount");
        fireEvent.change(amountDropdown, { target: { value: "Is exactly" } });

        const ExactAmountInput = screen.getByPlaceholderText("Enter amount");

        fireEvent.change(ExactAmountInput, { target: { value: "100" } });
        expect(mockProps.setTempFilters).toHaveBeenCalledWith(
            expect.objectContaining({
                amount: "100",
            })
        );
    });

    it("updates amount 'undefined' is selected", () => {
        render(<TransactionFilter {...mockProps} />);
        fireEvent.click(screen.getByText("Add filter"));

        const amountDropdown = screen.getByTestId("amount");
        fireEvent.change(amountDropdown, { target: { value: "" } });

        expect(screen.queryByTestId("min-amount")).not.toBeInTheDocument();
        expect(screen.queryByTestId("max-amount")).not.toBeInTheDocument();
        expect(screen.queryByPlaceholderText("Enter amount")).not.toBeInTheDocument();
    });

    it("calls onApplyFilter and closes drawer when Apply filters is clicked", () => {
        render(<TransactionFilter {...mockProps} />);
        fireEvent.click(screen.getByText("Add filter"));

        fireEvent.click(screen.getByText("Apply filters"));

        expect(mockProps.onApplyFilter).toHaveBeenCalled();
        expect(screen.queryByTestId("side-drawer")).not.toBeInTheDocument();
    });

    it("calls onClearAll when Clear is clicked", () => {
        render(<TransactionFilter {...mockProps} />);
        fireEvent.click(screen.getByText("Add filter"));

        fireEvent.click(screen.getByText("Clear"));

        expect(mockProps.onClearAll).toHaveBeenCalled();
        expect(screen.queryByTestId("side-drawer")).toBeInTheDocument();
    });
});
