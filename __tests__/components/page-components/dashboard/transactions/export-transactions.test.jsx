import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import "@testing-library/jest-dom";
import { ExportTransactions } from "@/components/page-components/dashboard/transactions/export-transactions";

// Mock date-fns
jest.mock("date-fns", () => {
    const actualDateFns = jest.requireActual("date-fns");
    return {
        ...actualDateFns,
        format: jest.fn().mockImplementation((date, formatStr) => {
            if (formatStr === "yyyy-MM-dd") return "2023-07-01";
            if (formatStr === "dd MMM yyyy") return "01 Jul 2023";
            if (formatStr.includes("HH:mm")) return "01 Jul 2023 12:00";
            if (formatStr.includes("yyyyMMdd_HHmm")) return "20230701_1200";
            return "01 Jul 2023";
        }),
        startOfMonth: jest.fn().mockReturnValue(new Date("2023-07-01")),
        endOfMonth: jest.fn().mockReturnValue(new Date("2023-07-31")),
        subMonths: jest.fn().mockImplementation((date, months) => {
            // Return different dates based on months parameter
            if (months === 3) return new Date("2023-04-01");
            if (months === 6) return new Date("2023-01-01");
            return new Date("2023-04-01");
        }),
    };
});

// Mock the necessary modules
jest.mock("@/components/common/buttonv3", () => ({
    Button: ({ children, onClick, variant, size, rightIcon, disabled, type, "data-testid": dataTestId }) => (
        <button
            onClick={onClick}
            data-testid={dataTestId || `button-${variant || "default"}-${type || "button"}`}
            disabled={disabled}
            size={size}
        >
            {children}
            {rightIcon && <span>Icon</span>}
        </button>
    ),
}));

jest.mock("@/components/common/dropdown", () => ({
    __esModule: true,
    default: ({ label, options, size, onChange, "data-testid": dataTestId }) => (
        <div>
            <label>{label}</label>
            <select
                data-testid={dataTestId || "dropdown"}
                size={size}
                onChange={(e) => onChange && onChange(e.target.value)}
            >
                {options.map((option) => (
                    <option key={option.value} value={option.value}>
                        {option.label}
                    </option>
                ))}
            </select>
        </div>
    ),
}));

jest.mock("@/components/common/drawer", () => ({
    __esModule: true,
    default: ({ children, isOpen }) => (isOpen ? <div data-testid="side-drawer">{children}</div> : null),
}));

jest.mock("@/components/common/checkbox", () => ({
    __esModule: true,
    default: ({ label, checked, onCheckedChange, id, size, "data-testid": dataTestId }) => (
        <input
            type="checkbox"
            checked={checked}
            onChange={() => onCheckedChange && onCheckedChange(!checked)}
            id={id}
            data-testid={dataTestId || `checkbox-${id}`}
            size={size}
        />
    ),
}));

jest.mock("@/components/common/radio-group", () => ({
    RadioGroup: ({ children, defaultValue, onValueChange, value, className, "data-testid": dataTestId }) => (
        <div
            data-testid={dataTestId || "radio-group"}
            className={className}
            onChange={(e) => onValueChange && onValueChange(e.target.value)}
            value={value || defaultValue}
        >
            {children}
        </div>
    ),
    RadioGroupItem: ({ value, id, "data-testid": dataTestId }) => (
        <input type="radio" value={value} id={id} data-testid={dataTestId || `radio-${id}`} />
    ),
}));

jest.mock("@/components/common/label", () => ({
    Label: ({ htmlFor, children, className }) => (
        <label htmlFor={htmlFor} className={className}>
            {children}
        </label>
    ),
}));

jest.mock("@/components/icons/transaction", () => ({
    ExportIcon: () => <span>ExportIcon</span>,
}));

jest.mock("@/components/common/close-x", () => ({
    __esModule: true,
    default: ({ onClick, color, "data-testid": dataTestId }) => (
        <button data-testid={dataTestId || "close-button"} onClick={onClick} color={color}>
            X
        </button>
    ),
}));

jest.mock("lucide-react", () => ({
    Loader2: ({ className }) => (
        <div data-testid="loading-spinner" className={className}>
            Loading...
        </div>
    ),
}));

// Mock the API and export libraries
jest.mock("@/api/axios", () => ({
    acctsAxios: {
        get: jest.fn().mockResolvedValue({
            data: {
                content: [
                    {
                        id: 1,
                        amount: 100,
                        createdDate: "2023-07-26",
                        narration: "Test transaction",
                        transactionType: "Credit",
                        status: "Completed",
                        reference: "REF1",
                    },
                    {
                        id: 2,
                        amount: 200,
                        createdDate: "2023-07-26",
                        narration: "Another transaction",
                        transactionType: "Debit",
                        status: "Pending",
                        reference: "REF2",
                    },
                ],
            },
        }),
    },
}));

// Mock jsPDF
jest.mock("jspdf", () =>
    jest.fn().mockImplementation(() => ({
        setFillColor: jest.fn(),
        rect: jest.fn(),
        setFont: jest.fn(),
        setFontSize: jest.fn(),
        setTextColor: jest.fn(),
        text: jest.fn(),
        internal: {
            pageSize: { width: 210, height: 297 },
            getNumberOfPages: jest.fn().mockReturnValue(1),
        },
        autoTable: jest.fn(),
        save: jest.fn(),
        Props: {},
    }))
);

jest.mock("jspdf-autotable", () => ({}));

// Mock export-to-csv
// const mockGenerateCsvFn = jest.fn();
// const mockDownloadFn = jest.fn();

jest.mock("export-to-csv", () => ({
    mkConfig: jest.fn().mockReturnValue({}),
    generateCsv: jest.fn().mockReturnValue(() => {}),
    download: jest.fn().mockReturnValue(() => {}),
}));

// Mock XLSX
jest.mock("xlsx", () => ({
    utils: {
        book_new: jest.fn().mockReturnValue({ Props: {} }),
        json_to_sheet: jest.fn().mockReturnValue({}),
        book_append_sheet: jest.fn(),
        sheet_add_aoa: jest.fn(),
    },
    writeFile: jest.fn(),
}));

jest.mock("@/functions/feedback", () => ({
    sendCatchFeedback: jest.fn(),
}));

describe("ExportTransactions Component", () => {
    const mockAccounts = [
        { accountName: "Account 1", accountNumber: "********" },
        { accountName: "Account 2", accountNumber: "********" },
    ];

    const mockSelectedRows = [
        {
            id: 1,
            amount: 100,
            createdDate: "2023-07-26",
            narration: "Test transaction",
            transactionType: "Credit",
            status: "Completed",
            reference: "REF1",
        },
        {
            id: 2,
            amount: 200,
            createdDate: "2023-07-26",
            narration: "Another transaction",
            transactionType: "Debit",
            status: "Pending",
            reference: "REF2",
        },
    ];

    // Mock data and columns props
    const mockData = [];
    const mockColumns = [];

    beforeEach(() => {
        jest.clearAllMocks();
    });

    test("renders the Export button and opens the SideDrawer when clicked", () => {
        render(<ExportTransactions accounts={mockAccounts} data={mockData} columns={mockColumns} selectedRows={[]} />);

        const exportButton = screen.getByText("Export");
        expect(exportButton).toBeInTheDocument();

        fireEvent.click(exportButton);
        expect(screen.getByTestId("side-drawer")).toBeInTheDocument();
    });

    test("renders the SideDrawer content correctly", () => {
        render(<ExportTransactions accounts={mockAccounts} data={mockData} columns={mockColumns} selectedRows={[]} />);

        fireEvent.click(screen.getByText("Export"));

        expect(screen.getByText("Export transactions")).toBeInTheDocument();
        expect(screen.getByText("Download and analyze your transaction history.")).toBeInTheDocument();
        expect(screen.getByText("Date range")).toBeInTheDocument();
        expect(screen.getByText("Choose an account")).toBeInTheDocument();
        expect(screen.getByText("Choose your export format")).toBeInTheDocument();
        expect(screen.getByText("Columns")).toBeInTheDocument();
    });

    // test("handles date range RadioGroup selection", () => {
    //     render(<ExportTransactions accounts={mockAccounts} data={mockData} columns={mockColumns} selectedRows={[]} />);

    //     fireEvent.click(screen.getByText("Export"));

    //     const radioGroup = screen.getByTestId("radio-group");
    //     expect(radioGroup).toBeInTheDocument();

    //     // Test selecting different date ranges
    //     fireEvent.change(radioGroup, { target: { value: "last-3-months" } });
    //     fireEvent.change(radioGroup, { target: { value: "custom" } });
    // });

    // test("handles custom date range inputs", async () => {
    //     render(<ExportTransactions accounts={mockAccounts} data={mockData} columns={mockColumns} selectedRows={[]} />);

    //     fireEvent.click(screen.getByText("Export"));

    //     // Select custom date range
    //     const radioGroup = screen.getByTestId("radio-group");
    //     fireEvent.change(radioGroup, { target: { value: "custom" } });

    //     // Wait for date inputs to appear
    //     await waitFor(() => {
    //         // Since the component might not have data-testid attributes, we'll look for date inputs by type
    //         const dateInputs = screen.getAllByRole("textbox", { hidden: true });

    //         // If no textboxes are found, try to find inputs directly
    //         if (dateInputs.length === 0) {
    //             const inputs = document.querySelectorAll("input[type='date']");
    //             if (inputs.length > 0) {
    //                 // Change start date on the first date input
    //                 fireEvent.change(inputs[0], { target: { value: "2023-06-01" } });

    //                 // Change end date on the second date input if it exists
    //                 if (inputs.length > 1) {
    //                     fireEvent.change(inputs[1], { target: { value: "2023-06-30" } });
    //                 }
    //             }
    //         } else {
    //             // Change start date
    //             if (dateInputs[0]) fireEvent.change(dateInputs[0], { target: { value: "2023-06-01" } });

    //             // Change end date
    //             if (dateInputs[1]) fireEvent.change(dateInputs[1], { target: { value: "2023-06-30" } });
    //         }
    //     });
    // });

    // test("shows loading state during API calls", async () => {
    //     // Mock the API to delay response
    //     const acctsAxios = jest.requireMock("@/api/axios").acctsAxios;
    //     acctsAxios.get.mockImplementationOnce(
    //         () =>
    //             new Promise((resolve) => {
    //                 setTimeout(() => {
    //                     resolve({
    //                         data: {
    //                             content: [{ id: 1, amount: 100, createdDate: "2023-07-26" }],
    //                         },
    //                     });
    //                 }, 100);
    //             })
    //     );

    //     render(<ExportTransactions accounts={mockAccounts} data={mockData} columns={mockColumns} selectedRows={[]} />);

    //     fireEvent.click(screen.getByText("Export"));

    //     // Select an account first
    //     const accountDropdown = screen.getByTestId("dropdown");
    //     fireEvent.change(accountDropdown, { target: { value: "********" } });

    //     // Select CSV format
    //     const csvCheckbox = screen.getByTestId("checkbox-csv");
    //     fireEvent.click(csvCheckbox);

    //     // Click Export button
    //     const exportButtons = screen.getAllByText("Export");
    //     fireEvent.click(exportButtons[exportButtons.length - 1]);

    //     // Check for loading state - look for the loading text in the button
    //     await waitFor(
    //         () => {
    //             const loadingButton = screen.getByText("Loading...");
    //             expect(loadingButton).toBeInTheDocument();
    //         },
    //         { timeout: 1000 }
    //     );
    // });

    test("handles account dropdown selection", () => {
        render(<ExportTransactions accounts={mockAccounts} data={mockData} columns={mockColumns} selectedRows={[]} />);

        fireEvent.click(screen.getByText("Export"));

        const dropdown = screen.getByTestId("dropdown");
        fireEvent.change(dropdown, { target: { value: "********" } });
    });

    test("handles export format selection", () => {
        render(<ExportTransactions accounts={mockAccounts} data={mockData} columns={mockColumns} selectedRows={[]} />);

        fireEvent.click(screen.getByText("Export"));

        // Find checkboxes by their IDs
        const csvCheckbox = screen.getByTestId("checkbox-csv");
        const pdfCheckbox = screen.getByTestId("checkbox-pdf");
        const excelCheckbox = screen.getByTestId("checkbox-excel");

        // Test CSV selection
        fireEvent.click(csvCheckbox);

        // Test PDF selection
        fireEvent.click(pdfCheckbox);

        // Test Excel selection
        fireEvent.click(excelCheckbox);
    });

    test("handles column selection", () => {
        render(<ExportTransactions accounts={mockAccounts} data={mockData} columns={mockColumns} selectedRows={[]} />);

        fireEvent.click(screen.getByText("Export"));

        // Find column checkboxes - we'll use a more flexible approach
        const columnCheckboxes = Array.from(document.querySelectorAll("input[type='checkbox']")).filter((checkbox) =>
            columnValues.some((col) => checkbox.id === col)
        );

        // If we found checkboxes matching column values, test them
        if (columnCheckboxes.length > 0) {
            fireEvent.click(columnCheckboxes[0]);
            fireEvent.click(columnCheckboxes[1]);
        } else {
            // Fallback to testing by test ID
            const amountCheckbox = screen.getByTestId("checkbox-Amount");
            const statusCheckbox = screen.getByTestId("checkbox-Status");

            fireEvent.click(amountCheckbox);
            fireEvent.click(statusCheckbox);
        }
    });

    test("shows selected rows message when rows are selected", () => {
        render(
            <ExportTransactions
                accounts={mockAccounts}
                data={mockData}
                columns={mockColumns}
                selectedRows={mockSelectedRows}
            />
        );

        fireEvent.click(screen.getByText("Export"));

        // Look for text containing the number of selected rows
        const selectedRowsText = screen.getByText(/selected for export. Only selected rows will be exported./i);
        expect(selectedRowsText).toBeInTheDocument();
    });

    test("handles Cancel button click", () => {
        render(<ExportTransactions accounts={mockAccounts} data={mockData} columns={mockColumns} selectedRows={[]} />);

        fireEvent.click(screen.getByText("Export"));

        // Find and click the Cancel button
        const cancelButton = screen.getByText("Cancel");
        fireEvent.click(cancelButton);

        // The drawer should be closed
        expect(screen.queryByTestId("side-drawer")).not.toBeInTheDocument();
    });

    test("handles Export button click with CSV format", async () => {
        // Get the mocked functions
        const csvModule = jest.requireMock("export-to-csv");
        const generateCsv = csvModule.generateCsv;
        const download = csvModule.download;

        render(
            <ExportTransactions
                accounts={mockAccounts}
                data={mockData}
                columns={mockColumns}
                selectedRows={mockSelectedRows}
            />
        );

        fireEvent.click(screen.getAllByText("Export")[0]);

        // Select CSV format
        const csvCheckbox = screen.getByTestId("checkbox-csv");
        fireEvent.click(csvCheckbox);

        // Click Export button
        const exportButton = screen.getAllByText(/Export$/);
        fireEvent.click(exportButton[1]);

        // Wait for the export process to complete
        await waitFor(() => {
            // Verify CSV export was called
            expect(generateCsv).toHaveBeenCalled();
            expect(download).toHaveBeenCalled();
        });
    });

    test("handles Export button click with PDF format", async () => {
        // Get the mocked constructor
        const jsPDFMock = jest.requireMock("jspdf");

        render(
            <ExportTransactions
                accounts={mockAccounts}
                data={mockData}
                columns={mockColumns}
                selectedRows={mockSelectedRows}
            />
        );

        fireEvent.click(screen.getAllByText("Export")[0]);

        // Select PDF format
        const pdfCheckbox = screen.getByTestId("checkbox-pdf");
        fireEvent.click(pdfCheckbox);

        // Click Export button
        const exportButton = screen.getAllByText(/Export$/);
        fireEvent.click(exportButton[1]);

        // Wait for the export process to complete
        await waitFor(() => {
            // Verify PDF export was called
            expect(jsPDFMock).toHaveBeenCalled();
        });
    });

    test("handles Export button click with Excel format", async () => {
        // Get the mocked module
        const xlsxModule = jest.requireMock("xlsx");

        render(
            <ExportTransactions
                accounts={mockAccounts}
                data={mockData}
                columns={mockColumns}
                selectedRows={mockSelectedRows}
            />
        );

        fireEvent.click(screen.getAllByText("Export")[0]);

        // Select Excel format
        const excelCheckbox = screen.getByTestId("checkbox-excel");
        fireEvent.click(excelCheckbox);

        // Click Export button
        const exportButton = screen.getAllByText(/Export$/);
        fireEvent.click(exportButton[1]);

        // Wait for the export process to complete
        await waitFor(() => {
            // Verify Excel export was called
            expect(xlsxModule.utils.book_new).toHaveBeenCalled();
            expect(xlsxModule.utils.json_to_sheet).toHaveBeenCalled();
            expect(xlsxModule.utils.book_append_sheet).toHaveBeenCalled();
            expect(xlsxModule.writeFile).toHaveBeenCalled();
        });
    });

    test("handles API errors gracefully", async () => {
        // Mock the API to return an error
        const acctsAxios = jest.requireMock("@/api/axios").acctsAxios;
        const sendCatchFeedback = jest.requireMock("@/functions/feedback").sendCatchFeedback;

        acctsAxios.get.mockRejectedValueOnce(new Error("API Error"));

        render(<ExportTransactions accounts={mockAccounts} data={mockData} columns={mockColumns} selectedRows={[]} />);

        fireEvent.click(screen.getByText("Export"));

        // Select an account first
        const accountDropdown = screen.getByTestId("dropdown");
        fireEvent.change(accountDropdown, { target: { value: "********" } });

        // Select CSV format
        const csvCheckbox = screen.getByTestId("checkbox-csv");
        fireEvent.click(csvCheckbox);

        // Click Export button
        const exportButtons = screen.getAllByText("Export");
        fireEvent.click(exportButtons[exportButtons.length - 1]);

        // Wait for error handling
        await waitFor(() => {
            expect(sendCatchFeedback).toHaveBeenCalled();
        });
    });

    test("handles export errors gracefully", async () => {
        // Mock the CSV generation to throw an error
        const csvModule = jest.requireMock("export-to-csv");
        const sendCatchFeedback = jest.requireMock("@/functions/feedback").sendCatchFeedback;

        // Make generateCsv throw an error
        csvModule.generateCsv.mockImplementationOnce(() => {
            throw new Error("CSV Generation Error");
        });

        render(
            <ExportTransactions
                accounts={mockAccounts}
                data={mockData}
                columns={mockColumns}
                selectedRows={mockSelectedRows}
            />
        );

        fireEvent.click(screen.getAllByText("Export")[0]);

        // Select CSV format
        const csvCheckbox = screen.getByTestId("checkbox-csv");
        fireEvent.click(csvCheckbox);

        // Click Export button
        const exportButton = screen.getAllByText(/Export$/);
        fireEvent.click(exportButton[1]);

        // Wait for error handling
        await waitFor(() => {
            expect(sendCatchFeedback).toHaveBeenCalled();
        });
    });

    test("handles empty data gracefully", async () => {
        // Mock the API to return empty data
        const acctsAxios = jest.requireMock("@/api/axios").acctsAxios;
        const sendCatchFeedback = jest.requireMock("@/functions/feedback").sendCatchFeedback;

        acctsAxios.get.mockResolvedValueOnce({
            data: {
                content: [],
            },
        });

        render(<ExportTransactions accounts={mockAccounts} data={mockData} columns={mockColumns} selectedRows={[]} />);

        fireEvent.click(screen.getByText("Export"));

        // Select an account first
        const accountDropdown = screen.getByTestId("dropdown");
        fireEvent.change(accountDropdown, { target: { value: "********" } });

        // Select CSV format
        const csvCheckbox = screen.getByTestId("checkbox-csv");
        fireEvent.click(csvCheckbox);

        // Click Export button
        const exportButtons = screen.getAllByText("Export");
        fireEvent.click(exportButtons[exportButtons.length - 1]);

        // Wait for error handling
        await waitFor(() => {
            expect(sendCatchFeedback).toHaveBeenCalledWith("No data available for export");
        });
    });

    // Add these additional tests to the existing ExportTransactions test suite

    test("handles date range selection for 'this-month'", () => {
        render(<ExportTransactions accounts={mockAccounts} data={mockData} columns={mockColumns} selectedRows={[]} />);

        fireEvent.click(screen.getByText("Export"));

        // Find the radio group
        const thisMonthRadio = screen.getByLabelText("This month");
        fireEvent.click(thisMonthRadio);

        // Verify the date range was updated (indirectly through the component's behavior)
        // We can check this by triggering an export and seeing if the correct date format is used
        const csvCheckbox = screen.getByTestId("checkbox-csv");
        fireEvent.click(csvCheckbox);

        // The date-fns mock should be called with the correct dates
        expect(jest.requireMock("date-fns").startOfMonth).toHaveBeenCalled();
        expect(jest.requireMock("date-fns").endOfMonth).toHaveBeenCalled();
    });

    test("handles date range selection for 'last-3-months'", () => {
        render(<ExportTransactions accounts={mockAccounts} data={mockData} columns={mockColumns} selectedRows={[]} />);

        fireEvent.click(screen.getByText("Export"));

        // Find the radio group
        const last3MonthsRadio = screen.getByLabelText("Last 3 months");
        fireEvent.click(last3MonthsRadio);

        // Verify the date range was updated
        expect(jest.requireMock("date-fns").subMonths).toHaveBeenCalledWith(expect.any(Date), 3);
    });

    test("handles date range selection for 'last-6-months'", () => {
        render(<ExportTransactions accounts={mockAccounts} data={mockData} columns={mockColumns} selectedRows={[]} />);

        fireEvent.click(screen.getByText("Export"));

        // Find the radio group
        const last6MonthsRadio = screen.getByLabelText("Last 6 months");
        fireEvent.click(last6MonthsRadio);

        // Verify the date range was updated
        expect(jest.requireMock("date-fns").subMonths).toHaveBeenCalledWith(expect.any(Date), 6);
    });

    test("handles export with no account selected", async () => {
        // Mock the feedback function
        const sendCatchFeedback = jest.requireMock("@/functions/feedback").sendCatchFeedback;

        // Render with accounts but no account selected
        render(<ExportTransactions accounts={mockAccounts} data={mockData} columns={mockColumns} selectedRows={[]} />);

        fireEvent.click(screen.getByText("Export"));

        // Select CSV format
        const csvCheckbox = screen.getByTestId("checkbox-csv");
        fireEvent.click(csvCheckbox);

        // The export button should be disabled when no account is selected
        const exportButtons = screen.getAllByText("Export");
        const exportButton = exportButtons[exportButtons.length - 1];
        expect(exportButton).toBeDisabled();

        // Try to click the disabled button (should not trigger validation)
        fireEvent.click(exportButton);

        // Should not call sendCatchFeedback since button is disabled
        expect(sendCatchFeedback).not.toHaveBeenCalled();
    });

    test("handles export with empty response from API", async () => {
        // Mock the API to return empty content
        const acctsAxios = jest.requireMock("@/api/axios").acctsAxios;
        const sendCatchFeedback = jest.requireMock("@/functions/feedback").sendCatchFeedback;

        acctsAxios.get.mockResolvedValueOnce({
            data: {
                content: null, // Explicitly null content
            },
        });

        render(<ExportTransactions accounts={mockAccounts} data={mockData} columns={mockColumns} selectedRows={[]} />);

        fireEvent.click(screen.getByText("Export"));

        // Select an account first
        const accountDropdown = screen.getByTestId("dropdown");
        fireEvent.change(accountDropdown, { target: { value: "********" } });

        // Select CSV format
        const csvCheckbox = screen.getByTestId("checkbox-csv");
        fireEvent.click(csvCheckbox);

        // Click Export button
        const exportButtons = screen.getAllByText("Export");
        fireEvent.click(exportButtons[exportButtons.length - 1]);

        // Wait for error handling
        await waitFor(() => {
            expect(sendCatchFeedback).toHaveBeenCalledWith("No transaction data available for the selected period.");
        });
    });

    test("handles export with malformed transaction data", async () => {
        // Mock selected rows with malformed data
        const malformedRows = [
            {
                id: 1,
                amount: "not-a-number", // String instead of number
                createdDate: "invalid-date", // Invalid date
                narration: null, // Null value
                transactionType: undefined, // Undefined value
                status: "", // Empty string
                reference: { complex: "object" }, // Complex object
            },
        ];

        // Get the mocked functions
        const csvModule = jest.requireMock("export-to-csv");
        const generateCsv = csvModule.generateCsv;
        const download = csvModule.download;

        render(
            <ExportTransactions
                accounts={mockAccounts}
                data={mockData}
                columns={mockColumns}
                selectedRows={malformedRows}
            />
        );

        fireEvent.click(screen.getAllByText("Export")[0]);

        // Select CSV format
        const csvCheckbox = screen.getByTestId("checkbox-csv");
        fireEvent.click(csvCheckbox);

        // Click Export button
        const exportButton = screen.getAllByText(/Export$/);
        fireEvent.click(exportButton[1]);

        // Wait for the export process to complete
        await waitFor(() => {
            // Verify CSV export was called
            expect(generateCsv).toHaveBeenCalled();
            expect(download).toHaveBeenCalled();
        });
    });

    test("handles export with extremely long text values", async () => {
        // Mock selected rows with very long text
        const longTextRows = [
            {
                id: 1,
                amount: 100,
                createdDate: "2023-07-26",
                narration: "A".repeat(1000), // Very long narration
                transactionType: "Credit",
                status: "Completed",
                reference: "B".repeat(500), // Very long reference
            },
        ];

        // Get the mocked constructor
        const jsPDFMock = jest.requireMock("jspdf");

        render(
            <ExportTransactions
                accounts={mockAccounts}
                data={mockData}
                columns={mockColumns}
                selectedRows={longTextRows}
            />
        );

        fireEvent.click(screen.getAllByText("Export")[0]);

        // Select PDF format
        const pdfCheckbox = screen.getByTestId("checkbox-pdf");
        fireEvent.click(pdfCheckbox);

        // Click Export button
        const exportButton = screen.getAllByText(/Export$/);
        fireEvent.click(exportButton[1]);

        // Wait for the export process to complete
        await waitFor(() => {
            // Verify PDF export was called
            expect(jsPDFMock).toHaveBeenCalled();
        });
    });

    test("handles export with all formats selected simultaneously", async () => {
        // Get the mocked modules
        const jsPDFMock = jest.requireMock("jspdf");
        const csvModule = jest.requireMock("export-to-csv");
        const xlsxModule = jest.requireMock("xlsx");

        render(
            <ExportTransactions
                accounts={mockAccounts}
                data={mockData}
                columns={mockColumns}
                selectedRows={mockSelectedRows}
            />
        );

        fireEvent.click(screen.getAllByText("Export")[0]);

        // Select all formats
        const csvCheckbox = screen.getByTestId("checkbox-csv");
        const pdfCheckbox = screen.getByTestId("checkbox-pdf");
        const excelCheckbox = screen.getByTestId("checkbox-excel");

        fireEvent.click(csvCheckbox);
        fireEvent.click(pdfCheckbox);
        fireEvent.click(excelCheckbox);

        // The last one clicked should be the active one (excel)

        // Click Export button
        const exportButton = screen.getAllByText(/Export$/);
        fireEvent.click(exportButton[1]);

        // Wait for the export process to complete
        await waitFor(() => {
            // Verify Excel export was called (since it was the last one selected)
            expect(xlsxModule.utils.book_new).toHaveBeenCalled();
        });
    });

    test("handles closing the drawer with the X button", () => {
        render(<ExportTransactions accounts={mockAccounts} data={mockData} columns={mockColumns} selectedRows={[]} />);

        fireEvent.click(screen.getByText("Export"));

        // The drawer should be open
        expect(screen.getByTestId("side-drawer")).toBeInTheDocument();

        // Click the close button
        const closeButton = screen.getByTestId("modal-close");
        fireEvent.click(closeButton);

        // The drawer should be closed
        expect(screen.queryByTestId("side-drawer")).not.toBeInTheDocument();
    });

    test("handles custom date range input for start date", async () => {
        render(<ExportTransactions accounts={mockAccounts} data={mockData} columns={mockColumns} selectedRows={[]} />);

        fireEvent.click(screen.getByText("Export"));

        // Select custom date range
        const customRadio = screen.getByTestId("radio-r4");
        fireEvent.click(customRadio);

        // Wait for the date inputs to appear
        await waitFor(() => {
            // Find date inputs by type instead of label
            const dateInputs = document.querySelectorAll('input[type="date"]');
            expect(dateInputs.length).toBeGreaterThan(0);

            // Change the start date (first date input)
            fireEvent.change(dateInputs[0], { target: { value: "2023-05-01" } });

            // Verify the format function was called
            expect(jest.requireMock("date-fns").format).toHaveBeenCalledWith(expect.any(Date), "yyyy-MM-dd");
        });
    });

    test("handles custom date range input for end date", async () => {
        render(<ExportTransactions accounts={mockAccounts} data={mockData} columns={mockColumns} selectedRows={[]} />);

        fireEvent.click(screen.getByText("Export"));

        // Select custom date range
        const customRadio = screen.getByTestId("radio-r4");
        fireEvent.click(customRadio);

        // Wait for the date inputs to appear
        await waitFor(() => {
            // Find date inputs by type
            const dateInputs = document.querySelectorAll('input[type="date"]');
            expect(dateInputs.length).toBeGreaterThan(1);

            // Change the end date (second date input)
            fireEvent.change(dateInputs[1], { target: { value: "2023-05-31" } });

            // Verify the format function was called
            expect(jest.requireMock("date-fns").format).toHaveBeenCalledWith(expect.any(Date), "yyyy-MM-dd");
        });
    });

    // Fix for column selection tests
    test("handles column selection toggling", () => {
        render(<ExportTransactions accounts={mockAccounts} data={mockData} columns={mockColumns} selectedRows={[]} />);

        fireEvent.click(screen.getByText("Export"));

        // Find column checkboxes by test ID instead of label
        const amountCheckbox = screen.getByTestId("checkbox-Amount");

        // Uncheck a column
        fireEvent.click(amountCheckbox);

        // Check it again
        fireEvent.click(amountCheckbox);
    });

    // Fix for Excel export with different column selections
    test("handles Excel export with different column selections", async () => {
        // Get the mocked module
        const xlsxModule = jest.requireMock("xlsx");

        render(
            <ExportTransactions
                accounts={mockAccounts}
                data={mockData}
                columns={mockColumns}
                selectedRows={mockSelectedRows}
            />
        );

        fireEvent.click(screen.getAllByText("Export")[0]);

        // Select Excel format
        const excelCheckbox = screen.getByTestId("checkbox-excel");
        fireEvent.click(excelCheckbox);

        // Uncheck some columns by test ID
        const statusCheckbox = screen.getByTestId("checkbox-Status");
        const referenceCheckbox = screen.getByTestId("checkbox-Reference");

        fireEvent.click(statusCheckbox); // Uncheck Status
        fireEvent.click(referenceCheckbox); // Uncheck Reference

        // Click Export button
        const exportButtons = screen.getAllByText("Export");
        const exportButton = exportButtons[exportButtons.length - 1];
        fireEvent.click(exportButton);

        // Wait for the export process to complete
        await waitFor(() => {
            // Verify Excel export was called
            expect(xlsxModule.utils.book_new).toHaveBeenCalled();
            expect(xlsxModule.utils.json_to_sheet).toHaveBeenCalled();
        });
    });

    // Fix for CSV export with different column selections
    test("handles CSV export with different column selections", async () => {
        // Get the mocked functions
        const csvModule = jest.requireMock("export-to-csv");
        const generateCsv = csvModule.generateCsv;
        const download = csvModule.download;

        render(
            <ExportTransactions
                accounts={mockAccounts}
                data={mockData}
                columns={mockColumns}
                selectedRows={mockSelectedRows}
            />
        );

        fireEvent.click(screen.getAllByText("Export")[0]);

        // Select CSV format
        const csvCheckbox = screen.getByTestId("checkbox-csv");
        fireEvent.click(csvCheckbox);

        // Uncheck some columns by test ID
        const statusCheckbox = screen.getByTestId("checkbox-Status");
        const referenceCheckbox = screen.getByTestId("checkbox-Reference");

        fireEvent.click(statusCheckbox); // Uncheck Status
        fireEvent.click(referenceCheckbox); // Uncheck Reference

        // Click Export button - use index to get the last one
        const exportButtons = screen.getAllByText("Export");
        fireEvent.click(exportButtons[exportButtons.length - 1]);

        // Wait for the export process to complete
        await waitFor(() => {
            // Verify CSV export was called
            expect(generateCsv).toHaveBeenCalled();
            expect(download).toHaveBeenCalled();
        });
    });

    // Fix for export with no columns selected
    test("handles export with no columns selected", async () => {
        render(
            <ExportTransactions
                accounts={mockAccounts}
                data={mockData}
                columns={mockColumns}
                selectedRows={mockSelectedRows}
            />
        );

        fireEvent.click(screen.getAllByText("Export")[0]);

        // Uncheck all columns by test ID
        for (const column of columnValues) {
            const checkbox = screen.getByTestId(`checkbox-${column}`);
            fireEvent.click(checkbox);
        }

        // Select CSV format
        const csvCheckbox = screen.getByTestId("checkbox-csv");
        fireEvent.click(csvCheckbox);

        // Click Export button - use queryAllByText to avoid errors if there are multiple
        const exportButtons = screen.getAllByText("Export");
        fireEvent.click(exportButtons[exportButtons.length - 1]);

        // The export should still proceed with default columns
        await waitFor(() => {
            const csvModule = jest.requireMock("export-to-csv");
            expect(csvModule.generateCsv).toHaveBeenCalled();
        });
    });

    // New tests for early validation functionality
    test("prevents export when no account selected and no rows selected", async () => {
        const sendCatchFeedback = jest.requireMock("@/functions/feedback").sendCatchFeedback;

        render(<ExportTransactions accounts={mockAccounts} data={mockData} columns={mockColumns} selectedRows={[]} />);

        fireEvent.click(screen.getByText("Export"));

        // Select CSV format
        const csvCheckbox = screen.getByTestId("checkbox-csv");
        fireEvent.click(csvCheckbox);

        // The export button should be disabled when no account is selected
        const exportButtons = screen.getAllByText("Export");
        const exportButton = exportButtons[exportButtons.length - 1];
        expect(exportButton).toBeDisabled();

        // Try to click the disabled button (should not trigger validation)
        fireEvent.click(exportButton);

        // Should not call sendCatchFeedback since button is disabled
        expect(sendCatchFeedback).not.toHaveBeenCalled();
    });

    test("allows export when rows are selected even without account", async () => {
        const csvModule = jest.requireMock("export-to-csv");
        const generateCsv = csvModule.generateCsv;
        const download = csvModule.download;

        render(
            <ExportTransactions
                accounts={mockAccounts}
                data={mockData}
                columns={mockColumns}
                selectedRows={mockSelectedRows}
            />
        );

        fireEvent.click(screen.getByText("Export"));

        // Select CSV format
        const csvCheckbox = screen.getByTestId("checkbox-csv");
        fireEvent.click(csvCheckbox);

        // Click Export button
        const exportButtons = screen.getAllByText("Export");
        fireEvent.click(exportButtons[exportButtons.length - 1]);

        // Should export selected rows without requiring account selection
        await waitFor(() => {
            expect(generateCsv).toHaveBeenCalled();
            expect(download).toHaveBeenCalled();
        });
    });

    test("allows export when account is selected even without rows", async () => {
        const acctsAxios = jest.requireMock("@/api/axios").acctsAxios;
        const csvModule = jest.requireMock("export-to-csv");
        const generateCsv = csvModule.generateCsv;
        const download = csvModule.download;

        // Mock successful API response
        acctsAxios.get.mockResolvedValueOnce({
            data: {
                content: [
                    {
                        id: 1,
                        amount: 100,
                        createdDate: "2023-07-26",
                        narration: "Test transaction",
                        transactionType: "Credit",
                        status: "Completed",
                        reference: "REF1",
                    },
                ],
            },
        });

        render(<ExportTransactions accounts={mockAccounts} data={mockData} columns={mockColumns} selectedRows={[]} />);

        fireEvent.click(screen.getByText("Export"));

        // Select an account
        const accountDropdown = screen.getByTestId("dropdown");
        fireEvent.change(accountDropdown, { target: { value: "********" } });

        // Select CSV format
        const csvCheckbox = screen.getByTestId("checkbox-csv");
        fireEvent.click(csvCheckbox);

        // Click Export button
        const exportButtons = screen.getAllByText("Export");
        fireEvent.click(exportButtons[exportButtons.length - 1]);

        // Should export API data
        await waitFor(() => {
            expect(acctsAxios.get).toHaveBeenCalledWith("/api/v1/accounts/********/transactions", {
                params: {
                    pageNo: 0,
                    pageSize: 1000,
                    startDate: "2023-07-01",
                    endDate: "2023-07-01",
                },
            });
            expect(generateCsv).toHaveBeenCalled();
            expect(download).toHaveBeenCalled();
        });
    });

    test("shows account selection info message when no rows selected", () => {
        render(<ExportTransactions accounts={mockAccounts} data={mockData} columns={mockColumns} selectedRows={[]} />);

        fireEvent.click(screen.getByText("Export"));

        // Should show the account selection info message
        expect(screen.getByText("Please select an account to export transactions.")).toBeInTheDocument();
    });

    test("shows selected rows info message when rows are selected", () => {
        render(
            <ExportTransactions
                accounts={mockAccounts}
                data={mockData}
                columns={mockColumns}
                selectedRows={mockSelectedRows}
            />
        );

        fireEvent.click(screen.getByText("Export"));

        // Should show the selected rows info message
        expect(
            screen.getByText("2 row(s) selected for export. Only selected rows will be exported.")
        ).toBeInTheDocument();
    });

    test("export button is disabled when no account selected and no rows selected", () => {
        render(<ExportTransactions accounts={mockAccounts} data={mockData} columns={mockColumns} selectedRows={[]} />);

        fireEvent.click(screen.getByText("Export"));

        // Find the export button in the drawer
        const exportButtons = screen.getAllByText("Export");
        const exportButton = exportButtons[exportButtons.length - 1];

        // Should be disabled
        expect(exportButton).toBeDisabled();
    });

    test("export button is enabled when account is selected", () => {
        render(<ExportTransactions accounts={mockAccounts} data={mockData} columns={mockColumns} selectedRows={[]} />);

        fireEvent.click(screen.getByText("Export"));

        // Select an account
        const accountDropdown = screen.getByTestId("dropdown");
        fireEvent.change(accountDropdown, { target: { value: "********" } });

        // Find the export button in the drawer
        const exportButtons = screen.getAllByText("Export");
        const exportButton = exportButtons[exportButtons.length - 1];

        // Should be enabled
        expect(exportButton).not.toBeDisabled();
    });

    test("export button is enabled when rows are selected", () => {
        render(
            <ExportTransactions
                accounts={mockAccounts}
                data={mockData}
                columns={mockColumns}
                selectedRows={mockSelectedRows}
            />
        );

        fireEvent.click(screen.getByText("Export"));

        // Find the export button in the drawer
        const exportButtons = screen.getAllByText("Export");
        const exportButton = exportButtons[exportButtons.length - 1];

        // Should be enabled
        expect(exportButton).not.toBeDisabled();
    });

    test("clears account selection when switching to row selection mode", () => {
        const { rerender } = render(
            <ExportTransactions accounts={mockAccounts} data={mockData} columns={mockColumns} selectedRows={[]} />
        );

        fireEvent.click(screen.getByText("Export"));

        // Select an account
        const accountDropdown = screen.getByTestId("dropdown");
        fireEvent.change(accountDropdown, { target: { value: "********" } });

        // Now switch to row selection mode
        rerender(
            <ExportTransactions
                accounts={mockAccounts}
                data={mockData}
                columns={mockColumns}
                selectedRows={mockSelectedRows}
            />
        );

        fireEvent.click(screen.getAllByText("Export")[0]);

        // Should show selected rows message instead of account selection
        expect(
            screen.getByText("2 row(s) selected for export. Only selected rows will be exported.")
        ).toBeInTheDocument();
        expect(screen.queryByText("Please select an account to export transactions.")).not.toBeInTheDocument();
    });
});

// Define columnValues for the tests
const columnValues = ["Amount", "Status", "Counterparty", "Reference", "Bank"];
