import { fireEvent, render, screen } from "@testing-library/react";
import {
    bulkPaymentFormSteps,
    getPaymentTypes,
    multiplePaymentFormSteps,
    recurringFrequencyOptions,
    recurringReminderScheduleOptions,
    singlePaymentFormSteps,
} from "../../../../../src/components/page-components/dashboard/send-money/data";

// Mock clickHandler function
const clickHandlerMock = jest.fn();

describe("Data File Tests", () => {
    // Test for getPaymentTypes function
    describe("getPaymentTypes function", () => {
        it("should return an array of three payment types", () => {
            const paymentTypes = getPaymentTypes({ clickHandler: clickHandlerMock });
            expect(paymentTypes).toHaveLength(3);
        });

        it("should return correct titles for each payment type", () => {
            const paymentTypes = getPaymentTypes({ clickHandler: clickHandlerMock });

            expect(paymentTypes[0].title).toBe("Single payment");
            expect(paymentTypes[1].title).toBe("Multiple recipients");
            expect(paymentTypes[2].title).toBe("Bulk payment");
        });

        it("should return correct descriptions for each payment type", () => {
            const paymentTypes = getPaymentTypes({ clickHandler: clickHandlerMock });

            expect(paymentTypes[0].description).toBe(
                "Send an instant, scheduled or recurring payment to a single person or business"
            );
            expect(paymentTypes[1].description).toBe("Pay up to 15 people at once without uploading a CSV template.");
            expect(paymentTypes[2].description).toBe(
                "Download our CSV template and pay up to 1,000 people or businesses all at once."
            );
        });

        it("should call clickHandler with correct title when onClick is triggered", () => {
            render(
                <div>
                    {getPaymentTypes({ clickHandler: clickHandlerMock }).map((paymentType) => (
                        <button key={paymentType.title} onClick={paymentType.onClick}>
                            {paymentType.title}
                        </button>
                    ))}
                </div>
            );

            // Use getByRole to select button elements
            fireEvent.click(screen.getByRole("button", { name: "Single payment" }));
            expect(clickHandlerMock).toHaveBeenCalledWith("Single payment");

            fireEvent.click(screen.getByRole("button", { name: "Multiple recipients" }));
            expect(clickHandlerMock).toHaveBeenCalledWith("Multiple recipients");

            fireEvent.click(screen.getByRole("button", { name: "Bulk payment" }));
            expect(clickHandlerMock).toHaveBeenCalledWith("Bulk payment");
        });
    });

    // Test for formSteps
    describe("formSteps", () => {
        it("should contain the correct steps", () => {
            expect(singlePaymentFormSteps).toEqual(["Payment information", "Review payment"]);
        });
        it("should contain the correct steps for multiple", () => {
            expect(multiplePaymentFormSteps).toEqual(["Select recipients", "Payment information", "Review payment"]);
        });
    });

    // Test for recurringFrequencyOptions
    describe("recurringFrequencyOptions", () => {
        it("should have 4 options", () => {
            expect(recurringFrequencyOptions.length).toBe(4);
        });

        it("should have the correct options", () => {
            const expectedOptions = [
                { label: "Daily", value: "DAILY" },
                { label: "Weekly", value: "WEEKLY" },
                { label: "Monthly", value: "MONTHLY" },
                { label: "Yearly", value: "YEARLY" },
            ];

            expect(recurringFrequencyOptions).toEqual(expectedOptions);
        });

        it("should have label and value properties for each option", () => {
            recurringFrequencyOptions.forEach((option) => {
                expect(option).toHaveProperty("label");
                expect(option).toHaveProperty("value");
            });
        });

        it("should have unique values for each option", () => {
            const values = recurringFrequencyOptions.map((option) => option.value);
            expect(values).toEqual([...new Set(values)]);
        });
    });

    // Test for recurringReminderScheduleOptions
    describe("recurringReminderScheduleOptions", () => {
        it("should contain correct recurring reminder schedule options", () => {
            expect(recurringReminderScheduleOptions).toEqual(["1 day before", "2 days before", "1 week before"]);
        });
    });
});

describe("bulkPaymentFormSteps", () => {
    it("should be an array with three steps", () => {
        expect(Array.isArray(bulkPaymentFormSteps)).toBe(true);
        expect(bulkPaymentFormSteps).toHaveLength(3);
    });

    it("should contain the correct steps in order", () => {
        expect(bulkPaymentFormSteps).toEqual(["Prepare file", "Payment information", "Review payment"]);
    });

    it("should not contain unexpected values", () => {
        expect(bulkPaymentFormSteps).not.toContain("Invalid Step");
    });
});
