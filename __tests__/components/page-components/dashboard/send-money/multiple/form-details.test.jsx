/* eslint-disable @typescript-eslint/no-require-imports */
import { store } from "@/redux";
import { fireEvent, render, screen } from "@testing-library/react";
import { Provider } from "react-redux";
import FormDetails from "../../../../../../src/components/page-components/dashboard/send-money/local/multiple-payment-form/form-details";

// Dummy initial values for our form.
const initialValues = {
    transferSource: "Account A",
    amount: "",
    narration: "",
    transferType: "INSTANT",
    reoccurringFrequency: "",
    reoccurringStartDate: "",
    reoccurringReminderSchedule: "",
    recurringTenureType: "",
    reoccurringEndDate: "",
    reoccurringEndOccurrences: "",
    scheduledDate: "",
};

// --- Mocks for child components ---

// Mock AccountSelector so that it displays its selected account and calls onAccountChange when clicked.
jest.mock(
    "../../../../../../src/components/page-components/dashboard/accounts/payments/components/account-selector.tsx",
    () => (props) => (
        <div
            data-testid="account-selector"
            onClick={() => {
                props.onChange("New Account");
                props.updateExternalBalance && props.updateExternalBalance(5000);
            }}
        >
            AccountSelector - {props.selectedAccount} - {props.labelName}
        </div>
    )
);

// Mock AmountInput to simply render its label.
jest.mock("@/components/common/amount-input", () => (props) => <div data-testid="amount-input">{props.label}</div>);

// Mock TransferTypeSelector to render unique text.
jest.mock(
    "../../../../../../src/components/page-components/dashboard/send-money/local/transfer-type-selector.tsx",
    () => (props) => <div data-testid="transfer-type-selector">TransferTypeSelector</div>
);

// Mock LabelInput to render its label and placeholder with unique testids based on name prop.
jest.mock("@/components/common/label-input", () => (props) => (
    <div data-testid={`label-input-${props.name || "default"}`}>
        {props.label} - {props.placeholder || ""} -{props.disabled ? "disabled" : "enabled"} -
        {props.readOnly ? "readonly" : "editable"} - value: {props.value || "empty"} - error:{" "}
        {props.showError ? "true" : "false"}
    </div>
));

// Mock formatNumberToNaira function
jest.mock("@/functions/stringManipulations", () => ({
    formatNumberToNaira: jest.fn((amount, decimals) => `₦${amount.toFixed(decimals || 2)}`),
}));

// Mock pluralize function
jest.mock("pluralize", () => ({
    __esModule: true,
    default: jest.fn((word, count) => (count === 1 ? word : `${word}s`)),
}));

describe("FormDetails component", () => {
    const openScheduleModal = jest.fn();
    const openRecurringModal = jest.fn();
    const setAccountBalance = jest.fn();

    const selectedRecipients = [
        { accountName: "Recipient One", accountNumber: "**********", bank: "Bank A" },
        { accountName: "Recipient Two", accountNumber: "**********", bank: "Bank B" },
    ];

    const bulkRecipients = [
        { accountName: "Recipient One", accountNumber: "**********", bank: "Bank A", amount: 1000 },
        { accountName: "Recipient Two", accountNumber: "**********", bank: "Bank B", amount: 2000 },
    ];

    const setFieldValue = jest.fn();
    const setFieldTouched = jest.fn();
    const formik = {
        values: initialValues,
        setFieldValue,
        setFieldTouched,
    };

    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe("Multiple payment type", () => {
        it("renders basic structure correctly", () => {
            render(
                <Provider store={store}>
                    <FormDetails
                        formik={formik}
                        selectedRecipients={selectedRecipients}
                        openRecurringModal={openRecurringModal}
                        openScheduleModal={openScheduleModal}
                        setAccountBalance={setAccountBalance}
                    />
                </Provider>
            );

            expect(screen.getByTestId("heading")).toHaveTextContent("Payment information");
            expect(screen.getByText("2 recipients")).toBeInTheDocument();
        });

        it("displays singular recipient text when count is 1", () => {
            render(
                <Provider store={store}>
                    <FormDetails
                        formik={formik}
                        selectedRecipients={[selectedRecipients[0]]}
                        openRecurringModal={openRecurringModal}
                        openScheduleModal={openScheduleModal}
                        setAccountBalance={setAccountBalance}
                    />
                </Provider>
            );

            expect(screen.getByText("1 recipient")).toBeInTheDocument();
        });

        it("disables AmountInput when type is bulk", () => {
            render(
                <Provider store={store}>
                    <FormDetails
                        formik={formik}
                        selectedRecipients={bulkRecipients}
                        type="bulk"
                        openRecurringModal={openRecurringModal}
                        openScheduleModal={openScheduleModal}
                        setAccountBalance={setAccountBalance}
                    />
                </Provider>
            );

            const amountInput = screen.getByTestId("amount-input");
            expect(amountInput).toHaveTextContent("Amount");
        });

        it("calls setFieldValue with selected account from AccountSelector", () => {
            render(
                <Provider store={store}>
                    <FormDetails
                        formik={formik}
                        selectedRecipients={selectedRecipients}
                        openRecurringModal={openRecurringModal}
                        openScheduleModal={openScheduleModal}
                        setAccountBalance={setAccountBalance}
                    />
                </Provider>
            );

            fireEvent.click(screen.getByTestId("account-selector"));
            expect(setFieldValue).toHaveBeenCalledWith("transferSource", "New Account");
            expect(setAccountBalance).toHaveBeenCalledWith(5000);
        });

        it("renders TransferTypeSelector and LabelInput for narration", () => {
            render(
                <Provider store={store}>
                    <FormDetails
                        formik={formik}
                        selectedRecipients={selectedRecipients}
                        openRecurringModal={openRecurringModal}
                        openScheduleModal={openScheduleModal}
                        setAccountBalance={setAccountBalance}
                    />
                </Provider>
            );

            expect(screen.getByTestId("transfer-type-selector")).toBeInTheDocument();
            expect(screen.getByTestId("label-input-narration")).toHaveTextContent(
                "Narration - Write something here..."
            );
        });
    });

    describe("Bulk payment type behavior", () => {
        it("sets formik amount field when totalAmount changes", () => {
            render(
                <Provider store={store}>
                    <FormDetails
                        formik={formik}
                        selectedRecipients={bulkRecipients}
                        type="bulk"
                        openRecurringModal={openRecurringModal}
                        openScheduleModal={openScheduleModal}
                        setAccountBalance={setAccountBalance}
                    />
                </Provider>
            );

            expect(setFieldValue).toHaveBeenCalledWith("amount", 3000);
        });

        it("calls setFieldTouched when accountBalance is defined in bulk mode", () => {
            render(
                <Provider store={store}>
                    <FormDetails
                        formik={{ ...formik, setFieldTouched }}
                        selectedRecipients={bulkRecipients}
                        type="bulk"
                        accountBalance={5000}
                        openRecurringModal={openRecurringModal}
                        openScheduleModal={openScheduleModal}
                        setAccountBalance={setAccountBalance}
                    />
                </Provider>
            );

            expect(setFieldTouched).toHaveBeenCalledWith("amount", true, true);
        });

        it("calculates totalAmount correctly with mixed values", () => {
            const mixed = [
                { accountName: "A", accountNumber: "1", bank: "X", amount: "1500" },
                { accountName: "B", accountNumber: "2", bank: "Y", amount: 500 },
                { accountName: "C", accountNumber: "3", bank: "Z" }, // no amount
            ];

            render(
                <Provider store={store}>
                    <FormDetails
                        formik={formik}
                        selectedRecipients={mixed}
                        type="bulk"
                        openRecurringModal={openRecurringModal}
                        openScheduleModal={openScheduleModal}
                        setAccountBalance={setAccountBalance}
                    />
                </Provider>
            );

            expect(setFieldValue).toHaveBeenCalledWith("amount", 2000);
        });
    });

    describe("Edge Cases", () => {
        it("handles empty selectedRecipients", () => {
            render(
                <Provider store={store}>
                    <FormDetails
                        formik={formik}
                        selectedRecipients={[]}
                        openRecurringModal={openRecurringModal}
                        openScheduleModal={openScheduleModal}
                        setAccountBalance={setAccountBalance}
                    />
                </Provider>
            );

            expect(screen.getByText("0 recipients")).toBeInTheDocument();
        });

        it("does not call setFieldTouched when accountBalance is undefined", () => {
            render(
                <Provider store={store}>
                    <FormDetails
                        formik={{ ...formik, setFieldTouched }}
                        selectedRecipients={bulkRecipients}
                        type="bulk"
                        openRecurringModal={openRecurringModal}
                        openScheduleModal={openScheduleModal}
                        setAccountBalance={setAccountBalance}
                    />
                </Provider>
            );

            expect(setFieldTouched).not.toHaveBeenCalledWith("amount", true, true);
        });

        it("does not set formik amount for multiple type", () => {
            render(
                <Provider store={store}>
                    <FormDetails
                        formik={formik}
                        selectedRecipients={bulkRecipients}
                        type="multiple"
                        openRecurringModal={openRecurringModal}
                        openScheduleModal={openScheduleModal}
                        setAccountBalance={setAccountBalance}
                    />
                </Provider>
            );

            expect(setFieldValue).not.toHaveBeenCalledWith("amount", expect.anything());
        });
    });
});
