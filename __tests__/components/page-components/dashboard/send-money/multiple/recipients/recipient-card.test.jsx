import RecipientCard from "@/components/page-components/dashboard/send-money/local/multiple-payment-form/recipients/recipient-card";
import { fireEvent, render, screen } from "@testing-library/react";

const mockRecipient = {
    accountName: "John Doe",
    accountNumber: "**********",
    bank: "ABC Bank",
    accountType: "Personal",
    dateAdded: "2024-01-01",
    bankCode: "001",
};

describe("RecipientCard Component", () => {
    it("renders recipient details correctly", () => {
        render(<RecipientCard recipient={mockRecipient} onClick={jest.fn()} isSelected={false} totalRecipients={0} />);

        expect(screen.getByText("John Doe")).toBeInTheDocument();
        expect(screen.getByLabelText("View details for John Doe")).toBeInTheDocument();
    });

    it("shows Add icon when not selected", () => {
        render(<RecipientCard recipient={mockRecipient} onClick={jest.fn()} isSelected={false} totalRecipients={0} />);

        expect(screen.getByTestId("plus-icon")).toBeInTheDocument(); // Assuming AddRecipientIcon renders this
    });

    it("shows Delete icon when selected", () => {
        render(<RecipientCard recipient={mockRecipient} onClick={jest.fn()} isSelected={true} totalRecipients={0} />);

        expect(screen.getByTestId("trash-icon")).toBeInTheDocument(); // Assuming DeleteRecipientIcon renders this
    });

    it("disables Add button when totalRecipients >= 15", () => {
        render(<RecipientCard recipient={mockRecipient} onClick={jest.fn()} isSelected={false} totalRecipients={15} />);

        const button = screen.getByRole("button");
        expect(button).toBeDisabled();
    });

    it("enables Add button when totalRecipients < 15", () => {
        render(<RecipientCard recipient={mockRecipient} onClick={jest.fn()} isSelected={false} totalRecipients={10} />);

        const button = screen.getByRole("button");
        expect(button).toBeEnabled();
    });

    it("triggers onClick when Add button is clicked and under limit", () => {
        const mockClick = jest.fn();
        render(<RecipientCard recipient={mockRecipient} onClick={mockClick} isSelected={false} totalRecipients={5} />);

        fireEvent.click(screen.getByRole("button"));
        expect(mockClick).toHaveBeenCalled();
    });

    it("does not trigger onClick when button is disabled (limit reached)", () => {
        const mockClick = jest.fn();
        render(<RecipientCard recipient={mockRecipient} onClick={mockClick} isSelected={false} totalRecipients={15} />);

        fireEvent.click(screen.getByRole("button"));
        expect(mockClick).not.toHaveBeenCalled();
    });
});
