import FormSummary from "@/components/page-components/dashboard/send-money/local/multiple-payment-form/form-summary";
import { fireEvent, render, screen } from "@testing-library/react";
import { Provider } from "react-redux";
import configureStore from "redux-mock-store";

// Mocks
jest.mock("@/components/common/buttonv3", () => ({
    __esModule: true,
    Button: ({ children, onClick, type }) => (
        <button type={type} onClick={onClick} data-testid="button">
            {children}
        </button>
    ),
}));

jest.mock("@/functions/stringManipulations", () => ({
    __esModule: true,
    formatNumberToNaira: jest.fn((value) => `₦${Number(value).toFixed(2)}`),
}));

jest.mock("@/hooks/useTransactionFees", () => ({
    useTransactionFees: jest.fn(() => ({ fees: 500, loading: false })),
}));

jest.mock("@/components/page-components/dashboard/send-money/local/utils", () => ({
    calculateTransactionFee: jest.fn(() => 100),
    getFrequencyValue: jest.fn(() => 1),
}));

const mockStore = configureStore([]);
const mockGoToPrevStep = jest.fn();

const mockFormik = {
    values: {
        transferSource: {
            id: "string",
            accountName: "account",
            accountNumber: "**********",
            customerId: "string",
            currencyCode: "string",
            schemeType: "string",
            schemeCode: "string",
            status: "string",
            preferredName: "string",
            primaryFlag: true,
            hiddenFlag: true,
        },
        amount: "1000",
        transferType: "INSTANT",
        narration: "Test Narration",
        scheduledDate: "2024-04-25",
        reoccurringFrequency: "",
        reoccurringStartDate: "",
        reoccurringReminderSchedule: "",
        recurringTenureType: "",
        reoccurringEndDate: "",
        reoccurringEndOccurrences: "",
    },
    setFieldValue: jest.fn(),
    handleChange: jest.fn(),
};

const mockSelectedRecipients = [
    {
        accountName: "John Doe",
        accountNumber: "**********",
        maskedAccountNumber: "****7890",
        bank: "Bank of Nigeria",
        accountType: "Corporate",
        dateAdded: "23 Mar, 2024",
        bankCode: "001",
        amount: "1000",
    },
];

const mockBulkRecipients = [
    {
        accountName: "John Doe",
        accountNumber: "**********",
        maskedAccountNumber: "****7890",
        bank: "Bank of Nigeria",
        accountType: "Corporate",
        dateAdded: "23 Mar, 2024",
        bankCode: "001",
        amount: "500",
    },
    {
        accountName: "Jane Smith",
        accountNumber: "**********",
        maskedAccountNumber: "****4321",
        bank: "First Bank",
        accountType: "Savings",
        dateAdded: "24 Mar, 2024",
        bankCode: "002",
        amount: "700",
    },
];

const banksMock = [
    { bankCode: "001", bankName: "Bank of Nigeria" },
    { bankCode: "002", bankName: "First Bank" },
];

const renderWithStore = (ui, state = {}) => {
    const store = mockStore({
        recipient: {
            getLocalBanks: {
                banks: banksMock,
                ...state,
            },
        },
    });
    return render(<Provider store={store}>{ui}</Provider>);
};

describe("FormSummary Component", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    test("calls goToPrevStep when edit button is clicked", () => {
        renderWithStore(
            <FormSummary
                formik={mockFormik}
                goToPrevStep={mockGoToPrevStep}
                paymentType="Single Transfer"
                selectedRecipients={mockSelectedRecipients}
            />
        );

        fireEvent.click(screen.getByText("Edit"));
        expect(mockGoToPrevStep).toHaveBeenCalledTimes(1);
    });

    test("renders fees for multiple recipients", () => {
        renderWithStore(
            <FormSummary
                formik={mockFormik}
                goToPrevStep={mockGoToPrevStep}
                paymentType="Multiple recipients"
                selectedRecipients={mockBulkRecipients}
            />
        );

        expect(screen.getByText("Fees")).toBeInTheDocument();
        expect(screen.getAllByText("₦1000.00")[0]).toBeInTheDocument();
    });

    test("renders total amount for multiple recipients", () => {
        renderWithStore(
            <FormSummary
                formik={{ ...mockFormik, values: { ...mockFormik.values, transferType: "ONE_TIME" } }}
                goToPrevStep={mockGoToPrevStep}
                paymentType="Multiple recipients"
                selectedRecipients={mockBulkRecipients}
            />
        );

        expect(screen.getByText("Total Amount")).toBeInTheDocument();
        expect(screen.getByText("₦2000.00")).toBeInTheDocument();
    });

    test("renders recurring amount for recurring transfer type", () => {
        renderWithStore(
            <FormSummary
                formik={{ ...mockFormik, values: { ...mockFormik.values, transferType: "RECURRING" } }}
                goToPrevStep={mockGoToPrevStep}
                paymentType="Single Transfer"
                selectedRecipients={mockSelectedRecipients}
            />
        );

        expect(screen.getByText("Recurring amount")).toBeInTheDocument();
        expect(screen.getAllByText("₦1000.00")[0]).toBeInTheDocument();
    });

    test("renders total amount for recurring transfer type", () => {
        renderWithStore(
            <FormSummary
                formik={{
                    ...mockFormik,
                    values: {
                        ...mockFormik.values,
                        transferType: "RECURRING",
                        reoccurringFrequency: "MONTHLY",
                        reoccurringStartDate: "2024-01-01",
                        reoccurringEndDate: "2024-12-31",
                    },
                }}
                goToPrevStep={mockGoToPrevStep}
                paymentType="Multiple recipients"
                selectedRecipients={mockBulkRecipients}
            />
        );

        expect(screen.getByText("Total amount")).toBeInTheDocument();
    });

    test("renders payment delivery type", () => {
        renderWithStore(
            <FormSummary
                formik={mockFormik}
                goToPrevStep={mockGoToPrevStep}
                paymentType="Single Transfer"
                selectedRecipients={mockSelectedRecipients}
            />
        );

        expect(screen.getByText("Payment Delivery")).toBeInTheDocument();
        expect(screen.getByText("instant")).toBeInTheDocument();
    });

    test("renders send from information", () => {
        renderWithStore(
            <FormSummary
                formik={mockFormik}
                goToPrevStep={mockGoToPrevStep}
                paymentType="Single Transfer"
                selectedRecipients={mockSelectedRecipients}
            />
        );

        expect(screen.getByText("Send from")).toBeInTheDocument();
        expect(screen.getByText("account ・**********")).toBeInTheDocument();
    });
});
