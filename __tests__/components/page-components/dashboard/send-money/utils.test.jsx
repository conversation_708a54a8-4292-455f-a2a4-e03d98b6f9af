/* eslint-disable @typescript-eslint/no-require-imports */
import {
    calculateTransactionFee,
    getCorrespondingIndexForFormStepForMultiple,
    getFrequencyValue,
    getRecurringEndDateFromOccurrences,
    getRecurringEndStringFromOccurrences,
    getRecurringFeedbackString,
} from "../../../../../src/components/page-components/dashboard/send-money/local/utils";

// Mock date-fns dependencies
jest.mock("date-fns", () => ({
    differenceInDays: jest.fn((end, start) => Math.round((end - start) / (1000 * 60 * 60 * 24))),
    differenceInWeeks: jest.fn((end, start) => Math.round((end - start) / (1000 * 60 * 60 * 24 * 7))),
    differenceInMonths: jest.fn((end, start) => {
        const months = (end.getFullYear() - start.getFullYear()) * 12 + (end.getMonth() - start.getMonth());
        return months;
    }),
    differenceInYears: jest.fn((end, start) => end.getFullYear() - start.getFullYear()),
    addDays: jest.fn((date, amount) => {
        const result = new Date(date);
        result.setDate(date.getDate() + amount);
        return result;
    }),
    addWeeks: jest.fn((date, amount) => {
        const result = new Date(date);
        result.setDate(date.getDate() + amount * 7);
        return result;
    }),
    addMonths: jest.fn((date, amount) => {
        const result = new Date(date);
        result.setMonth(date.getMonth() + amount);
        return result;
    }),
    addYears: jest.fn((date, amount) => {
        const result = new Date(date);
        result.setFullYear(date.getFullYear() + amount);
        return result;
    }),
}));

// Mock multiplePaymentFormSteps
jest.mock("../../../../../src/components/page-components/dashboard/send-money/data.tsx", () => ({
    multiplePaymentFormSteps: ["Select recipients", "Payment information", "Review payment"],
}));

describe("getFrequencyValue", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    test("calculates daily frequency correctly", () => {
        const result = getFrequencyValue({
            frequency: "DAILY",
            startDate: "2025-01-01",
            endDate: "2025-01-10",
        });
        expect(result).toBe(10);
        expect(require("date-fns").differenceInDays).toHaveBeenCalledWith(
            new Date("2025-01-10"),
            new Date("2025-01-01")
        );
    });

    test("calculates weekly frequency correctly", () => {
        const result = getFrequencyValue({
            frequency: "WEEKLY",
            startDate: "2025-01-01",
            endDate: "2025-01-22",
        });
        expect(result).toBe(3);
        expect(require("date-fns").differenceInWeeks).toHaveBeenCalledWith(
            new Date("2025-01-22"),
            new Date("2025-01-01")
        );
    });

    test("calculates monthly frequency correctly", () => {
        const result = getFrequencyValue({
            frequency: "MONTHLY",
            startDate: "2025-01-01",
            endDate: "2025-04-01",
        });
        expect(result).toBe(3);
        expect(require("date-fns").differenceInMonths).toHaveBeenCalledWith(
            new Date("2025-04-01"),
            new Date("2025-01-01")
        );
    });

    test("calculates yearly frequency correctly", () => {
        const result = getFrequencyValue({
            frequency: "YEARLY",
            startDate: "2025-01-01",
            endDate: "2028-01-01",
        });
        expect(result).toBe(3);
        expect(require("date-fns").differenceInYears).toHaveBeenCalledWith(
            new Date("2028-01-01"),
            new Date("2025-01-01")
        );
    });

    test("returns endCount when provided", () => {
        const result = getFrequencyValue({
            frequency: "DAILY",
            startDate: "2025-01-01",
            endDate: "2025-01-10",
            endCount: "5",
        });
        expect(result).toBe(5);
        expect(require("date-fns").differenceInDays).not.toHaveBeenCalled();
    });

    test("returns 1 for unsupported frequency", () => {
        const result = getFrequencyValue({
            frequency: "HOURLY",
            startDate: "2025-01-01",
            endDate: "2025-01-02",
        });
        expect(result).toBe(1);
        expect(require("date-fns").differenceInDays).not.toHaveBeenCalled();
    });

    test("handles same start and end date", () => {
        const result = getFrequencyValue({
            frequency: "DAILY",
            startDate: "2025-01-01",
            endDate: "2025-01-01",
        });
        expect(result).toBe(1);
    });
});

describe("getCorrespondingIndexForFormStepForMultiple", () => {
    it("returns 0 for 'Select recipients'", () => {
        expect(getCorrespondingIndexForFormStepForMultiple("Select recipients")).toBe(0);
    });

    it("returns 1 for 'Payment information'", () => {
        expect(getCorrespondingIndexForFormStepForMultiple("Payment information")).toBe(1);
    });

    it("returns 2 for 'Review payment'", () => {
        expect(getCorrespondingIndexForFormStepForMultiple("Review payment")).toBe(2);
    });

    it("returns multiplePaymentFormSteps.length for an unknown step", () => {
        expect(getCorrespondingIndexForFormStepForMultiple("Unknown Step")).toBe(3);
    });
});

describe("getRecurringFeedbackString", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    test("returns correct string for DAILY frequency", () => {
        const result = getRecurringFeedbackString({
            frequency: "DAILY",
            startDate: "2025-01-01",
            endDate: "2025-01-10",
        });
        expect(result).toBe("10 daily");
        expect(require("date-fns").differenceInDays).toHaveBeenCalledWith(
            new Date("2025-01-10"),
            new Date("2025-01-01")
        );
    });

    test("returns correct string for WEEKLY frequency", () => {
        const result = getRecurringFeedbackString({
            frequency: "WEEKLY",
            startDate: "2025-01-01",
            endDate: "2025-01-22",
        });
        expect(result).toBe("3 weekly");
        expect(require("date-fns").differenceInWeeks).toHaveBeenCalledWith(
            new Date("2025-01-22"),
            new Date("2025-01-01")
        );
    });

    test("returns correct string for MONTHLY frequency", () => {
        const result = getRecurringFeedbackString({
            frequency: "MONTHLY",
            startDate: "2025-01-01",
            endDate: "2025-04-01",
        });
        expect(result).toBe("3 monthly");
        expect(require("date-fns").differenceInMonths).toHaveBeenCalledWith(
            new Date("2025-04-01"),
            new Date("2025-01-01")
        );
    });

    test("returns correct string for YEARLY frequency", () => {
        const result = getRecurringFeedbackString({
            frequency: "YEARLY",
            startDate: "2025-01-01",
            endDate: "2028-01-01",
        });
        expect(result).toBe("3 yearly");
        expect(require("date-fns").differenceInYears).toHaveBeenCalledWith(
            new Date("2028-01-01"),
            new Date("2025-01-01")
        );
    });

    test("returns default string for unsupported frequency", () => {
        const result = getRecurringFeedbackString({
            frequency: "HOURLY",
            startDate: "2025-01-01",
            endDate: "2025-01-02",
        });
        expect(result).toBe("3 months");
        expect(require("date-fns").differenceInDays).not.toHaveBeenCalled();
    });

    test("handles same start and end date", () => {
        const result = getRecurringFeedbackString({
            frequency: "DAILY",
            startDate: "2025-01-01",
            endDate: "2025-01-01",
        });
        expect(result).toBe("1 daily");
    });
});

describe("getRecurringEndDateFromOccurrences", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    test("calculates end date for DAILY frequency", () => {
        const result = getRecurringEndDateFromOccurrences({
            frequency: "DAILY",
            startDate: "2025-01-01",
            occurrences: 5,
        });
        expect(result).toBe("2025-01-06");
        expect(require("date-fns").addDays).toHaveBeenCalledWith(new Date("2025-01-01"), 5);
    });

    test("calculates end date for WEEKLY frequency", () => {
        const result = getRecurringEndDateFromOccurrences({
            frequency: "WEEKLY",
            startDate: "2025-01-01",
            occurrences: 2,
        });
        expect(result).toBe("2025-01-15");
        expect(require("date-fns").addWeeks).toHaveBeenCalledWith(new Date("2025-01-01"), 2);
    });

    test("calculates end date for MONTHLY frequency", () => {
        const result = getRecurringEndDateFromOccurrences({
            frequency: "MONTHLY",
            startDate: "2025-01-01",
            occurrences: 3,
        });
        expect(result).toBe("2025-04-01");
        expect(require("date-fns").addMonths).toHaveBeenCalledWith(new Date("2025-01-01"), 3);
    });

    test("calculates end date for YEARLY frequency", () => {
        const result = getRecurringEndDateFromOccurrences({
            frequency: "YEARLY",
            startDate: "2025-01-01",
            occurrences: 2,
        });
        expect(result).toBe("2027-01-01");
        expect(require("date-fns").addYears).toHaveBeenCalledWith(new Date("2025-01-01"), 2);
    });

    test("returns start date for unsupported frequency", () => {
        const result = getRecurringEndDateFromOccurrences({
            frequency: "HOURLY",
            startDate: "2025-01-01",
            occurrences: 5,
        });
        expect(result).toBe("2025-01-01");
        expect(require("date-fns").addDays).not.toHaveBeenCalled();
    });

    test("handles zero occurrences", () => {
        const result = getRecurringEndDateFromOccurrences({
            frequency: "DAILY",
            startDate: "2025-01-01",
            occurrences: 0,
        });
        expect(result).toBe("2025-01-01");
        expect(require("date-fns").addDays).toHaveBeenCalledWith(new Date("2025-01-01"), 0);
    });
});

describe("getRecurringEndStringFromOccurrences", () => {
    test("returns correct string for DAILY frequency", () => {
        const result = getRecurringEndStringFromOccurrences({ occurrences: "5", frequency: "DAILY" });
        expect(result).toBe("5 daily");
    });

    test("returns correct string for MONTHLY frequency", () => {
        const result = getRecurringEndStringFromOccurrences({ occurrences: "3", frequency: "MONTHLY" });
        expect(result).toBe("3 monthly");
    });

    test("returns correct string for WEEKLY frequency", () => {
        const result = getRecurringEndStringFromOccurrences({ occurrences: "2", frequency: "WEEKLY" });
        expect(result).toBe("2 weekly");
    });

    test("returns correct string for YEARLY frequency", () => {
        const result = getRecurringEndStringFromOccurrences({ occurrences: "1", frequency: "YEARLY" });
        expect(result).toBe("1 yearly");
    });

    test("returns empty string for unknown frequency", () => {
        const result = getRecurringEndStringFromOccurrences({ occurrences: "5", frequency: "UNKNOWN" });
        expect(result).toBe("");
    });
});

describe("Transaction Fee Calculations", () => {
    describe("calculateTransactionFee", () => {
        const FCMBSortCode = "000003";
        const VAT = 0.075; // 7.5%

        // Helper function to calculate expected taxed fee
        const calculateExpectedTaxedFee = (baseFee) => {
            const taxedFees = VAT * baseFee;
            const totalFees = taxedFees + baseFee;
            return parseFloat((Math.ceil(totalFees * 100) / 100).toFixed(2));
        };

        describe("FCMB transactions (destinationBank === FCMBSortCode)", () => {
            it("returns 0 for FCMB sort code regardless of amount", () => {
                expect(calculateTransactionFee({ amount: 0, destinationBank: FCMBSortCode })).toBe(0);
                expect(calculateTransactionFee({ amount: 1000, destinationBank: FCMBSortCode })).toBe(0);
                expect(calculateTransactionFee({ amount: 10000, destinationBank: FCMBSortCode })).toBe(0);
                expect(calculateTransactionFee({ amount: 100000, destinationBank: FCMBSortCode })).toBe(0);
            });

            it("returns 0 for FCMB sort code with edge case amounts", () => {
                expect(calculateTransactionFee({ amount: 5000, destinationBank: FCMBSortCode })).toBe(0);
                expect(calculateTransactionFee({ amount: 50000, destinationBank: FCMBSortCode })).toBe(0);
                expect(calculateTransactionFee({ amount: -100, destinationBank: FCMBSortCode })).toBe(0);
            });
        });

        describe("Non-FCMB transactions - Tier 1 (0 <= amount <= 5000)", () => {
            const baseFee = 10;
            const expectedFee = calculateExpectedTaxedFee(baseFee);

            it("returns correct fee for amount = 0", () => {
                expect(calculateTransactionFee({ amount: 0, destinationBank: "001" })).toBe(expectedFee);
            });

            it("returns correct fee for small positive amounts", () => {
                expect(calculateTransactionFee({ amount: 1, destinationBank: "001" })).toBe(expectedFee);
                expect(calculateTransactionFee({ amount: 100, destinationBank: "001" })).toBe(expectedFee);
                expect(calculateTransactionFee({ amount: 2500, destinationBank: "001" })).toBe(expectedFee);
            });

            it("returns correct fee for boundary amount = 5000", () => {
                expect(calculateTransactionFee({ amount: 5000, destinationBank: "001" })).toBe(expectedFee);
                expect(calculateTransactionFee({ amount: 5000, destinationBank: "002" })).toBe(expectedFee);
                expect(calculateTransactionFee({ amount: 5000, destinationBank: "999" })).toBe(expectedFee);
            });

            it("works with different bank codes", () => {
                expect(calculateTransactionFee({ amount: 1000, destinationBank: "044" })).toBe(expectedFee);
                expect(calculateTransactionFee({ amount: 1000, destinationBank: "058" })).toBe(expectedFee);
                expect(calculateTransactionFee({ amount: 1000, destinationBank: "011" })).toBe(expectedFee);
            });
        });

        describe("Non-FCMB transactions - Tier 2 (5000 < amount <= 50000)", () => {
            const baseFee = 25;
            const expectedFee = calculateExpectedTaxedFee(baseFee);

            it("returns correct fee for amount just above 5000", () => {
                expect(calculateTransactionFee({ amount: 5001, destinationBank: "001" })).toBe(expectedFee);
                expect(calculateTransactionFee({ amount: 5000.01, destinationBank: "001" })).toBe(expectedFee);
            });

            it("returns correct fee for mid-range amounts", () => {
                expect(calculateTransactionFee({ amount: 10000, destinationBank: "001" })).toBe(expectedFee);
                expect(calculateTransactionFee({ amount: 25000, destinationBank: "001" })).toBe(expectedFee);
                expect(calculateTransactionFee({ amount: 30000, destinationBank: "002" })).toBe(expectedFee);
            });

            it("returns correct fee for boundary amount = 50000", () => {
                expect(calculateTransactionFee({ amount: 50000, destinationBank: "001" })).toBe(expectedFee);
                expect(calculateTransactionFee({ amount: 50000, destinationBank: "044" })).toBe(expectedFee);
            });

            it("works with different bank codes", () => {
                expect(calculateTransactionFee({ amount: 20000, destinationBank: "214" })).toBe(expectedFee);
                expect(calculateTransactionFee({ amount: 35000, destinationBank: "070" })).toBe(expectedFee);
            });

            it("handles decimal amounts in tier 2", () => {
                expect(calculateTransactionFee({ amount: 15000.5, destinationBank: "001" })).toBe(expectedFee);
                expect(calculateTransactionFee({ amount: 49999.99, destinationBank: "001" })).toBe(expectedFee);
            });
        });

        describe("Non-FCMB transactions - Tier 3 (amount > 50000)", () => {
            const baseFee = 50;
            const expectedFee = calculateExpectedTaxedFee(baseFee);

            it("returns correct fee for amount just above 50000", () => {
                expect(calculateTransactionFee({ amount: 50001, destinationBank: "001" })).toBe(expectedFee);
                expect(calculateTransactionFee({ amount: 50000.01, destinationBank: "001" })).toBe(expectedFee);
            });

            it("returns correct fee for large amounts", () => {
                expect(calculateTransactionFee({ amount: 100000, destinationBank: "001" })).toBe(expectedFee);
                expect(calculateTransactionFee({ amount: 500000, destinationBank: "002" })).toBe(expectedFee);
                expect(calculateTransactionFee({ amount: 1000000, destinationBank: "003" })).toBe(expectedFee);
            });

            it("returns correct fee for very large amounts", () => {
                expect(calculateTransactionFee({ amount: ********, destinationBank: "001" })).toBe(expectedFee);
                expect(calculateTransactionFee({ amount: *********, destinationBank: "001" })).toBe(expectedFee);
            });

            it("works with different bank codes", () => {
                expect(calculateTransactionFee({ amount: 75000, destinationBank: "221" })).toBe(expectedFee);
                expect(calculateTransactionFee({ amount: 100000, destinationBank: "232" })).toBe(expectedFee);
            });

            it("handles decimal amounts in tier 3", () => {
                expect(calculateTransactionFee({ amount: 75000.75, destinationBank: "001" })).toBe(expectedFee);
                expect(calculateTransactionFee({ amount: 999999.99, destinationBank: "001" })).toBe(expectedFee);
            });
        });

        describe("Edge cases and special scenarios", () => {
            it("handles string bank codes that match FCMB", () => {
                expect(calculateTransactionFee({ amount: 1000, destinationBank: "000003" })).toBe(0);
            });

            it("handles bank codes with leading/trailing spaces", () => {
                // Assuming the function doesn't trim spaces - this would NOT match FCMB
                expect(calculateTransactionFee({ amount: 1000, destinationBank: " 000003" })).not.toBe(0);
                expect(calculateTransactionFee({ amount: 1000, destinationBank: "000003 " })).not.toBe(0);
            });

            it("handles different case bank codes", () => {
                // Bank codes are typically case-sensitive strings
                expect(calculateTransactionFee({ amount: 1000, destinationBank: "FCMB" })).not.toBe(0);
                expect(calculateTransactionFee({ amount: 1000, destinationBank: "fcmb" })).not.toBe(0);
            });

            it("handles empty string bank code", () => {
                expect(calculateTransactionFee({ amount: 1000, destinationBank: "" })).toBe(
                    calculateExpectedTaxedFee(10)
                );
            });

            it("handles null-like bank codes", () => {
                expect(calculateTransactionFee({ amount: 1000, destinationBank: "null" })).toBe(
                    calculateExpectedTaxedFee(10)
                );
                expect(calculateTransactionFee({ amount: 1000, destinationBank: "undefined" })).toBe(
                    calculateExpectedTaxedFee(10)
                );
            });

            it("handles boundary transitions correctly", () => {
                // Test the exact boundary points
                expect(calculateTransactionFee({ amount: 4999.99, destinationBank: "001" })).toBe(
                    calculateExpectedTaxedFee(10)
                );
                expect(calculateTransactionFee({ amount: 5000, destinationBank: "001" })).toBe(
                    calculateExpectedTaxedFee(10)
                );
                expect(calculateTransactionFee({ amount: 5000.01, destinationBank: "001" })).toBe(
                    calculateExpectedTaxedFee(25)
                );

                expect(calculateTransactionFee({ amount: 49999.99, destinationBank: "001" })).toBe(
                    calculateExpectedTaxedFee(25)
                );
                expect(calculateTransactionFee({ amount: 50000, destinationBank: "001" })).toBe(
                    calculateExpectedTaxedFee(25)
                );
                expect(calculateTransactionFee({ amount: 50000.01, destinationBank: "001" })).toBe(
                    calculateExpectedTaxedFee(50)
                );
            });

            it("calculates VAT correctly for each tier", () => {
                // Tier 1: 10 + (0.075 * 10) = 10 + 0.75 = 10.75
                expect(calculateTransactionFee({ amount: 1000, destinationBank: "001" })).toBe(10.75);

                // Tier 2: 25 + (0.075 * 25) = 25 + 1.875 = 26.88
                expect(calculateTransactionFee({ amount: 10000, destinationBank: "001" })).toBe(26.88);

                // Tier 3: 50 + (0.075 * 50) = 50 + 3.75 = 53.75
                expect(calculateTransactionFee({ amount: 100000, destinationBank: "001" })).toBe(53.75);
            });
        });

        describe("Data type handling", () => {
            it("handles integer amounts", () => {
                expect(calculateTransactionFee({ amount: 1000, destinationBank: "001" })).toBe(10.75);
                expect(calculateTransactionFee({ amount: 10000, destinationBank: "001" })).toBe(26.88);
            });

            it("handles float amounts", () => {
                expect(calculateTransactionFee({ amount: 1000.5, destinationBank: "001" })).toBe(10.75);
                expect(calculateTransactionFee({ amount: 10000.75, destinationBank: "001" })).toBe(26.88);
            });

            it("handles very small decimal amounts", () => {
                expect(calculateTransactionFee({ amount: 0.01, destinationBank: "001" })).toBe(10.75);
                expect(calculateTransactionFee({ amount: 0.99, destinationBank: "001" })).toBe(10.75);
            });
        });

        describe("Comprehensive tier boundary testing", () => {
            // Test all critical boundary points
            const testCases = [
                // Tier 1 boundaries
                { amount: 0, expectedFee: 10.75, tier: "Tier 1" },
                { amount: 1, expectedFee: 10.75, tier: "Tier 1" },
                { amount: 4999, expectedFee: 10.75, tier: "Tier 1" },
                { amount: 5000, expectedFee: 10.75, tier: "Tier 1" },

                // Tier 2 boundaries
                { amount: 5001, expectedFee: 26.88, tier: "Tier 2" },
                { amount: 6000, expectedFee: 26.88, tier: "Tier 2" },
                { amount: 25000, expectedFee: 26.88, tier: "Tier 2" },
                { amount: 49999, expectedFee: 26.88, tier: "Tier 2" },
                { amount: 50000, expectedFee: 26.88, tier: "Tier 2" },

                // Tier 3 boundaries
                { amount: 50001, expectedFee: 53.75, tier: "Tier 3" },
                { amount: 75000, expectedFee: 53.75, tier: "Tier 3" },
                { amount: 1000000, expectedFee: 53.75, tier: "Tier 3" },
            ];

            testCases.forEach(({ amount, expectedFee, tier }) => {
                it(`correctly calculates fee for ${tier} with amount ${amount}`, () => {
                    expect(calculateTransactionFee({ amount, destinationBank: "001" })).toBe(expectedFee);
                });
            });
        });
    });
});
