/* eslint-disable quotes */
import { fireEvent, render, screen } from "@testing-library/react";
import { Form, Formik } from "formik";
import React from "react";
import RecurringPaymentModal from "../../../../../src/components/page-components/dashboard/send-money/local/single-payment-form/modals/recurring-payment-modal";

// Mock the components that are used within the RecurringPaymentModal
jest.mock("@/components/common/amount-input", () => (props) => (
    <input
        data-testid="amount-input"
        type="text"
        value={props.formik.values.amount || ""}
        onChange={(e) => props.formik.setFieldValue("amount", e.target.value)}
        className={props.className}
    />
));

jest.mock("@/components/common/buttonv3", () => ({
    Button: ({ children, onClick, type, disabled, variant, ...props }) => (
        <button
            data-testid={`${children.toLowerCase().replace(/\s+/g, "-")}-button`}
            onClick={onClick}
            type={type}
            disabled={disabled}
            data-variant={variant}
            {...props}
        >
            {children}
        </button>
    ),
}));

jest.mock(
    "@/components/common/custom-modal",
    () =>
        ({ children, isOpen, onRequestClose, title }) =>
            isOpen ? (
                <div data-testid="custom-modal" role="dialog">
                    <div data-testid="modal-header">{title}</div>
                    <button data-testid="modal-close" onClick={onRequestClose}>
                        ×
                    </button>
                    {children}
                </div>
            ) : null
);

jest.mock("@/components/common/dropdown", () => (props) => (
    <select
        data-testid={`${props.name}-dropdown`}
        value={props.value?.value || props.formik.values[props.name] || ""}
        onChange={(e) => props.formik.setFieldValue(props.name, e.target.value)}
        className={props.className}
    >
        <option value="">Select...</option>
        {props.options.map((option) => (
            <option key={option.value} value={option.value}>
                {option.label}
            </option>
        ))}
    </select>
));

jest.mock("@/components/common/label", () => ({
    Label: ({ children, htmlFor }) => <label htmlFor={htmlFor}>{children}</label>,
}));

jest.mock("@/components/common/label-input", () => (props) => (
    <input
        data-testid={`${props.name}-input`}
        type={props.type || "text"}
        value={props.useFormik === false ? props.value : props.formik.values[props.name] || ""}
        onChange={props.useFormik === false ? undefined : (e) => props.formik.setFieldValue(props.name, e.target.value)}
        disabled={props.disabled}
        readOnly={props.readOnly}
        min={props.min}
        className={props.className}
    />
));

jest.mock("@/components/common/radio-group", () => ({
    RadioGroup: ({ value, onValueChange, children, className }) => (
        <div data-testid="radio-group" className={className}>
            {React.Children.map(children, (child) =>
                React.cloneElement(child, {
                    checked: child.props.value === value,
                    onChange: (e) => onValueChange(e.target.value),
                })
            )}
        </div>
    ),
    RadioGroupItem: ({ value, id, checked, onChange }) => (
        <input data-testid={`radio-${id}`} type="radio" value={value} id={id} checked={checked} onChange={onChange} />
    ),
}));

// Mock data imports
jest.mock("../../../../../src/components/page-components/dashboard/send-money/data.tsx", () => ({
    recurringFrequencyOptions: [
        { label: "Daily", value: "DAILY" },
        { label: "Weekly", value: "WEEKLY" },
        { label: "Monthly", value: "MONTHLY" },
        { label: "Yearly", value: "YEARLY" },
    ],
    recurringReminderScheduleOptions: ["1 day before", "2 days before", "1 week before"],
}));

describe("RecurringPaymentModal", () => {
    const initialValues = {
        reoccurringFrequency: "",
        reoccurringStartDate: "",
        reoccurringReminderSchedule: "",
        recurringTenureType: "",
        reoccurringEndDate: "",
        reoccurringEndOccurrences: "",
        amount: "",
        accountName: "John Doe",
        transferType: "",
    };

    const createMockFormik = (values = {}) => ({
        values: { ...initialValues, ...values },
        setFieldValue: jest.fn(),
    });

    const renderModal = (props = {}) => {
        const mockFormik = props.formik || createMockFormik();
        return render(
            <Formik initialValues={initialValues} onSubmit={jest.fn()}>
                <Form>
                    <RecurringPaymentModal open={true} onClose={jest.fn()} formik={mockFormik} {...props} />
                </Form>
            </Formik>
        );
    };

    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe("Rendering", () => {
        it("renders the modal with all fields when open is true", () => {
            renderModal();

            expect(screen.getByTestId("custom-modal")).toBeInTheDocument();
            expect(screen.getByText("Set up recurring mandate")).toBeInTheDocument();
            expect(screen.getByText(/Create an automatic payment schedule to/)).toBeInTheDocument();
            expect(screen.getByTestId("amount-input")).toBeInTheDocument();
            expect(screen.getByTestId("reoccurringFrequency-dropdown")).toBeInTheDocument();
            expect(screen.getByTestId("reoccurringStartDate-input")).toBeInTheDocument();
            expect(screen.getByTestId("reoccurringReminderSchedule-dropdown")).toBeInTheDocument();
            expect(screen.getByText("When should this payment end?")).toBeInTheDocument();
            expect(screen.getByTestId("radio-date")).toBeInTheDocument();
            expect(screen.getByTestId("radio-occurrence")).toBeInTheDocument();
            expect(screen.getByTestId("cancel-button")).toBeInTheDocument();
            expect(screen.getByTestId("confirm-button")).toBeInTheDocument();
        });

        it("does not render modal when open is false", () => {
            renderModal({ open: false });
            expect(screen.queryByTestId("custom-modal")).not.toBeInTheDocument();
        });

        it("displays account name in the description", () => {
            const mockFormik = createMockFormik({ accountName: "Jane Smith" });
            renderModal({ formik: mockFormik });

            expect(screen.getByText("Jane Smith")).toBeInTheDocument();
        });

        it("renders amount input for non-bulk payments", () => {
            renderModal({ isBulk: false });
            expect(screen.getByTestId("amount-input")).toBeInTheDocument();
        });

        it("renders disabled amount input for bulk payments", () => {
            renderModal({ isBulk: true, bulkAmount: "5000" });

            const bulkAmountInput = screen.getByTestId("amount-input");
            expect(bulkAmountInput).toBeInTheDocument();
            expect(bulkAmountInput).toHaveValue("5000");
            expect(bulkAmountInput).toBeDisabled();
        });
    });

    describe("Form Interactions", () => {
        it("updates formik values on input change", () => {
            const mockFormik = createMockFormik();
            renderModal({ formik: mockFormik });

            const amountInput = screen.getByTestId("amount-input");
            fireEvent.change(amountInput, { target: { value: "100" } });
            expect(mockFormik.setFieldValue).toHaveBeenCalledWith("amount", "100");

            const frequencyDropdown = screen.getByTestId("reoccurringFrequency-dropdown");
            fireEvent.change(frequencyDropdown, { target: { value: "MONTHLY" } });
            expect(mockFormik.setFieldValue).toHaveBeenCalledWith("reoccurringFrequency", "MONTHLY");

            const startDateInput = screen.getByTestId("reoccurringStartDate-input");
            fireEvent.change(startDateInput, { target: { value: "2023-10-01" } });
            expect(mockFormik.setFieldValue).toHaveBeenCalledWith("reoccurringStartDate", "2023-10-01");

            const reminderDropdown = screen.getByTestId("reoccurringReminderSchedule-dropdown");
            fireEvent.change(reminderDropdown, { target: { value: "1 day before" } });
            expect(mockFormik.setFieldValue).toHaveBeenCalledWith("reoccurringReminderSchedule", "1 day before");
        });

        it("updates recurring tenure type on radio button selection", () => {
            const mockFormik = createMockFormik();
            renderModal({ formik: mockFormik });

            const dateRadio = screen.getByTestId("radio-date");
            fireEvent.click(dateRadio);
            expect(mockFormik.setFieldValue).toHaveBeenCalledWith("recurringTenureType", "date");

            const occurrenceRadio = screen.getByTestId("radio-occurrence");
            fireEvent.click(occurrenceRadio);
            expect(mockFormik.setFieldValue).toHaveBeenCalledWith("recurringTenureType", "occurrence");
        });

        it("shows end date input when date radio is selected", () => {
            const mockFormik = createMockFormik({ recurringTenureType: "date" });
            renderModal({ formik: mockFormik });

            const endDateInput = screen.getByTestId("reoccurringEndDate-input");
            expect(endDateInput).toBeInTheDocument();
            expect(endDateInput).not.toBeDisabled();
        });

        it("shows occurrences input when occurrence radio is selected", () => {
            const mockFormik = createMockFormik({ recurringTenureType: "occurrence" });
            renderModal({ formik: mockFormik });

            const occurrencesInput = screen.getByTestId("reoccurringEndOccurrences-input");
            expect(occurrencesInput).toBeInTheDocument();
            expect(occurrencesInput).not.toBeDisabled();
            expect(screen.getByText("Occurrences")).toBeInTheDocument();
        });

        it("disables end date input when occurrence is selected", () => {
            const mockFormik = createMockFormik({ recurringTenureType: "occurrence" });
            renderModal({ formik: mockFormik });

            const endDateInput = screen.getByTestId("reoccurringEndDate-input");
            expect(endDateInput).toBeDisabled();
        });

        it("disables occurrences input when date is selected", () => {
            const mockFormik = createMockFormik({ recurringTenureType: "date" });
            renderModal({ formik: mockFormik });

            const occurrencesInput = screen.getByTestId("reoccurringEndOccurrences-input");
            expect(occurrencesInput).toBeDisabled();
        });
    });

    describe("Button States", () => {
        it("disables Confirm button when required fields are missing", () => {
            renderModal();
            const confirmButton = screen.getByTestId("confirm-button");
            expect(confirmButton).toBeDisabled();
        });

        it("disables Confirm button when frequency is missing", () => {
            const mockFormik = createMockFormik({
                reoccurringStartDate: "2023-10-01",
                recurringTenureType: "date",
                reoccurringEndDate: "2023-12-31",
            });
            renderModal({ formik: mockFormik });

            const confirmButton = screen.getByTestId("confirm-button");
            expect(confirmButton).toBeDisabled();
        });

        it("disables Confirm button when start date is missing", () => {
            const mockFormik = createMockFormik({
                reoccurringFrequency: "MONTHLY",
                recurringTenureType: "date",
                reoccurringEndDate: "2023-12-31",
            });
            renderModal({ formik: mockFormik });

            const confirmButton = screen.getByTestId("confirm-button");
            expect(confirmButton).toBeDisabled();
        });

        it("disables Confirm button when tenure type is missing", () => {
            const mockFormik = createMockFormik({
                reoccurringFrequency: "MONTHLY",
                reoccurringStartDate: "2023-10-01",
            });
            renderModal({ formik: mockFormik });

            const confirmButton = screen.getByTestId("confirm-button");
            expect(confirmButton).toBeDisabled();
        });

        it("disables Confirm button when date tenure type is selected but end date is missing", () => {
            const mockFormik = createMockFormik({
                reoccurringFrequency: "MONTHLY",
                reoccurringStartDate: "2023-10-01",
                recurringTenureType: "date",
            });
            renderModal({ formik: mockFormik });

            const confirmButton = screen.getByTestId("confirm-button");
            expect(confirmButton).toBeDisabled();
        });

        it("disables Confirm button when occurrence tenure type is selected but occurrences is missing", () => {
            const mockFormik = createMockFormik({
                reoccurringFrequency: "MONTHLY",
                reoccurringStartDate: "2023-10-01",
                recurringTenureType: "occurrence",
            });
            renderModal({ formik: mockFormik });

            const confirmButton = screen.getByTestId("confirm-button");
            expect(confirmButton).toBeDisabled();
        });

        it("enables Confirm button when all required fields for date tenure are filled", () => {
            const mockFormik = createMockFormik({
                reoccurringFrequency: "MONTHLY",
                reoccurringStartDate: "2023-10-01",
                recurringTenureType: "date",
                reoccurringEndDate: "2023-12-31",
            });
            renderModal({ formik: mockFormik });

            const confirmButton = screen.getByTestId("confirm-button");
            expect(confirmButton).not.toBeDisabled();
        });

        it("enables Confirm button when all required fields for occurrence tenure are filled", () => {
            const mockFormik = createMockFormik({
                reoccurringFrequency: "MONTHLY",
                reoccurringStartDate: "2023-10-01",
                recurringTenureType: "occurrence",
                reoccurringEndOccurrences: "12",
            });
            renderModal({ formik: mockFormik });

            const confirmButton = screen.getByTestId("confirm-button");
            expect(confirmButton).not.toBeDisabled();
        });
    });

    describe("Modal Actions", () => {
        it("calls onClose when Confirm button is clicked", () => {
            const onCloseMock = jest.fn();
            const mockFormik = createMockFormik({
                reoccurringFrequency: "MONTHLY",
                reoccurringStartDate: "2023-10-01",
                recurringTenureType: "date",
                reoccurringEndDate: "2023-12-31",
            });
            renderModal({ onClose: onCloseMock, formik: mockFormik });

            const confirmButton = screen.getByTestId("confirm-button");
            fireEvent.click(confirmButton);

            expect(onCloseMock).toHaveBeenCalledTimes(1);
        });

        it("calls closeModalAndClearState when Cancel button is clicked", () => {
            const onCloseMock = jest.fn();
            const mockFormik = createMockFormik();
            renderModal({ onClose: onCloseMock, formik: mockFormik });

            const cancelButton = screen.getByTestId("cancel-button");
            fireEvent.click(cancelButton);

            expect(onCloseMock).toHaveBeenCalledTimes(1);
            expect(mockFormik.setFieldValue).toHaveBeenCalledTimes(7);
            expect(mockFormik.setFieldValue).toHaveBeenNthCalledWith(1, "reoccurringFrequency", "");
            expect(mockFormik.setFieldValue).toHaveBeenNthCalledWith(2, "reoccurringStartDate", "");
            expect(mockFormik.setFieldValue).toHaveBeenNthCalledWith(3, "reoccurringEndDate", "");
            expect(mockFormik.setFieldValue).toHaveBeenNthCalledWith(4, "recurringTenureType", "");
            expect(mockFormik.setFieldValue).toHaveBeenNthCalledWith(5, "reoccurringReminderSchedule", "");
            expect(mockFormik.setFieldValue).toHaveBeenNthCalledWith(6, "reoccurringEndOccurrences", "");
            expect(mockFormik.setFieldValue).toHaveBeenNthCalledWith(7, "transferType", "INSTANT");
        });

        it("calls closeModalAndClearState when modal close button is clicked", () => {
            const onCloseMock = jest.fn();
            const mockFormik = createMockFormik();
            renderModal({ onClose: onCloseMock, formik: mockFormik });

            const modalCloseButton = screen.getByTestId("modal-close");
            fireEvent.click(modalCloseButton);

            expect(onCloseMock).toHaveBeenCalledTimes(1);
            expect(mockFormik.setFieldValue).toHaveBeenCalledTimes(7);
        });
    });

    describe("Form Field Attributes", () => {
        it("sets minimum date for start date input to today", () => {
            renderModal();
            const startDateInput = screen.getByTestId("reoccurringStartDate-input");
            const today = new Date().toISOString().split("T")[0];
            expect(startDateInput).toHaveAttribute("min", today);
        });

        it("sets minimum date for end date input to today", () => {
            const mockFormik = createMockFormik({ recurringTenureType: "date" });
            renderModal({ formik: mockFormik });

            const endDateInput = screen.getByTestId("reoccurringEndDate-input");
            const today = new Date().toISOString().split("T")[0];
            expect(endDateInput).toHaveAttribute("min", today);
        });

        it("sets number type for occurrences input", () => {
            const mockFormik = createMockFormik({ recurringTenureType: "occurrence" });
            renderModal({ formik: mockFormik });

            const occurrencesInput = screen.getByTestId("reoccurringEndOccurrences-input");
            expect(occurrencesInput).toHaveAttribute("type", "number");
        });

        it("sets date type for date inputs", () => {
            renderModal();

            const startDateInput = screen.getByTestId("reoccurringStartDate-input");
            expect(startDateInput).toHaveAttribute("type", "date");
        });
    });

    describe("Dropdown Options", () => {
        it("populates frequency dropdown with correct options", () => {
            renderModal();

            const frequencyDropdown = screen.getByTestId("reoccurringFrequency-dropdown");
            expect(frequencyDropdown).toBeInTheDocument();

            const options = frequencyDropdown.querySelectorAll("option");
            expect(options).toHaveLength(5); // Including default "Select..." option
            expect(options[1]).toHaveTextContent("Daily");
            expect(options[2]).toHaveTextContent("Weekly");
            expect(options[3]).toHaveTextContent("Monthly");
            expect(options[4]).toHaveTextContent("Yearly");
        });

        it("populates reminder schedule dropdown with correct options", () => {
            renderModal();

            const reminderDropdown = screen.getByTestId("reoccurringReminderSchedule-dropdown");
            expect(reminderDropdown).toBeInTheDocument();

            const options = reminderDropdown.querySelectorAll("option");
            expect(options).toHaveLength(4); // Including default "Select..." option
            expect(options[1]).toHaveTextContent("1 day before");
            expect(options[2]).toHaveTextContent("2 days before");
            expect(options[3]).toHaveTextContent("1 week before");
        });
    });

    describe("Radio Group Behavior", () => {
        it("renders radio group with correct structure", () => {
            renderModal();

            const radioGroup = screen.getByTestId("radio-group");
            expect(radioGroup).toBeInTheDocument();

            const dateRadio = screen.getByTestId("radio-date");
            const occurrenceRadio = screen.getByTestId("radio-occurrence");

            expect(dateRadio).toBeInTheDocument();
            expect(occurrenceRadio).toBeInTheDocument();
            expect(screen.getByText("Specify date")).toBeInTheDocument();
            expect(screen.getByText("End after")).toBeInTheDocument();
        });
    });

    describe("Edge Cases", () => {
        it("handles empty formik values gracefully", () => {
            const mockFormik = createMockFormik();
            renderModal({ formik: mockFormik });

            expect(screen.getByTestId("custom-modal")).toBeInTheDocument();
            expect(screen.getByTestId("confirm-button")).toBeDisabled();
        });

        it("handles missing accountName gracefully", () => {
            const mockFormik = createMockFormik({ accountName: undefined });
            renderModal({ formik: mockFormik });

            expect(screen.getByTestId("custom-modal")).toBeInTheDocument();
        });

        it("handles bulk payment without bulkAmount", () => {
            renderModal({ isBulk: true });

            const amountInput = screen.getByTestId("amount-input");
            expect(amountInput).toBeInTheDocument();
            expect(amountInput).toBeDisabled();
        });
    });
});
