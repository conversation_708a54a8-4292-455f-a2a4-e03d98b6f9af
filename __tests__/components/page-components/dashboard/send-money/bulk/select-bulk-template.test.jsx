import { sendCatchFeedback, sendFeedback } from "@/functions/feedback";
import { convertCsvToArrayOfObjects } from "@/functions/file-control";
import * as sendMoneyActions from "@/redux/actions/sendMoneyActions";
import "@testing-library/jest-dom";
import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import { Provider } from "react-redux";
import configureStore from "redux-mock-store";
import SelectBulkTemplate from "../../../../../../src/components/page-components/dashboard/send-money/local/bulk-payment-form/select-bulk-template";

// Mock Redux store
const mockStore = configureStore([]);

// Mock the imports
jest.mock("@/functions/file-control", () => ({
    convertCsvToArrayOfObjects: jest.fn(),
}));

jest.mock("@/functions/feedback", () => ({
    sendFeedback: jest.fn(),
    sendCatchFeedback: jest.fn(),
}));

// Mock redux actions
jest.mock("@/redux/actions/sendMoneyActions", () => ({
    downloadBulkTemplate: jest.fn(() => ({ type: "DOWNLOAD_BULK_TEMPLATE" })),
}));

// Mock the FileAttachment component
jest.mock("@/components/common/file-attachment", () => {
    return {
        __esModule: true,
        default: jest.fn((props) => {
            // Store the callbacks to access in tests
            mockFileAttachmentProps = props;
            return (
                <div data-testid="file-attachment">
                    <button
                        data-testid="mock-file-upload-button"
                        onClick={() => {
                            // This will be used to simulate file uploads in tests
                        }}
                    >
                        Upload File
                    </button>
                    <div>{props.headerText}</div>
                    <div>{props.descriptionText}</div>
                    <button
                        data-testid="mock-file-remove-button"
                        onClick={() => props.onFileRemoved && props.onFileRemoved()}
                    >
                        Remove File
                    </button>
                </div>
            );
        }),
    };
});

// Mock for SelectSavedTemplate component
jest.mock(
    "../../../../../../src/components/page-components/dashboard/send-money/local/bulk-payment-form/saved-templates/select-saved-template",
    () => {
        return {
            __esModule: true,
            default: jest.fn((props) => (
                <div data-testid="select-saved-template">
                    <span>Saved Templates Mock</span>
                    <button
                        data-testid="mock-template-select"
                        onClick={() => {
                            // Simulate template selection
                            const mockTemplate = { id: "template-1", name: "Test Template" };
                            props.setSelectedTemplate(mockTemplate);
                        }}
                    >
                        Select Template
                    </button>
                </div>
            )),
        };
    }
);

// Store mock props for testing
let mockFileAttachmentProps;

describe("SelectBulkTemplate Component - Complete Test Coverage", () => {
    const defaultProps = {
        uploadedFile: undefined,
        setUploadedFile: jest.fn(),
        isUploading: false,
        setIsUploading: jest.fn(),
        setIsUploadComplete: jest.fn(),
        setSelectedRecipients: jest.fn(),
        setProcessedModal: jest.fn(),
        setSelectedTemplate: jest.fn(),
    };

    let store;
    const mockBanks = [
        { bankCode: "ABC123", bankName: "Test Bank" },
        { bankCode: "XYZ789", bankName: "Another Bank" },
        { bankCode: "DEF456", bankName: "Third Bank" },
    ];

    beforeEach(() => {
        jest.clearAllMocks();
        mockFileAttachmentProps = null;

        // Create a fresh mock store for each test
        store = mockStore({
            sendMoney: {
                downloadBulkTemplate: {
                    loading: false,
                    data: null,
                    success: false,
                    error: null,
                },
            },
            recipient: {
                getLocalBanks: {
                    banks: mockBanks,
                },
            },
        });
    });

    const renderWithProvider = (component, reduxStore = store) => {
        return render(<Provider store={reduxStore}>{component}</Provider>);
    };

    // ===== BASIC RENDERING TESTS =====
    describe("Basic Rendering", () => {
        test("renders component with correct title and sections", () => {
            renderWithProvider(<SelectBulkTemplate {...defaultProps} />);

            expect(screen.getByTestId("heading")).toHaveTextContent("Send a bulk payment");
            expect(screen.getByText("Create a new bulk payment")).toBeInTheDocument();
            expect(screen.getByText("Download our CSV template")).toBeInTheDocument();
            expect(screen.getByText("Upload and verify")).toBeInTheDocument();
        });

        test("renders template download section with correct content", () => {
            renderWithProvider(<SelectBulkTemplate {...defaultProps} />);

            expect(screen.getByText("Download our CSV template")).toBeInTheDocument();
            expect(screen.getByText(/Get started by downloading our CSV template/)).toBeInTheDocument();
            expect(screen.getByText(/Fill the template with the recipients' transfer details/)).toBeInTheDocument();

            const downloadButton = screen.getByText("Download");
            expect(downloadButton).toBeInTheDocument();
        });

        test("renders file upload section with correct content", () => {
            renderWithProvider(<SelectBulkTemplate {...defaultProps} />);

            expect(screen.getByText("Upload and verify")).toBeInTheDocument();
            expect(screen.getByText(/Once you're done, upload the file for review/)).toBeInTheDocument();
            expect(screen.getByText("Attachments")).toBeInTheDocument();
            expect(screen.getByText("Supported format: CSV. File size up to 10MB")).toBeInTheDocument();
        });

        test("renders SelectSavedTemplate component", () => {
            renderWithProvider(<SelectBulkTemplate {...defaultProps} />);

            expect(screen.getByTestId("select-saved-template")).toBeInTheDocument();
            expect(screen.getByText("Saved Templates Mock")).toBeInTheDocument();
        });
    });

    // ===== DOWNLOAD TEMPLATE TESTS =====
    describe("Template Download", () => {
        test("dispatches downloadBulkTemplate action when download button is clicked", async () => {
            renderWithProvider(<SelectBulkTemplate {...defaultProps} />);

            const downloadButton = screen.getByText("Download");
            fireEvent.click(downloadButton);

            expect(sendMoneyActions.downloadBulkTemplate).toHaveBeenCalled();
            expect(store.getActions()).toContainEqual({ type: "DOWNLOAD_BULK_TEMPLATE" });
        });

        test("shows success feedback when template download succeeds", () => {
            const successStore = mockStore({
                sendMoney: {
                    downloadBulkTemplate: {
                        loading: false,
                        data: { templateUrl: "http://example.com/template.csv" },
                        success: true,
                        error: null,
                    },
                },
                recipient: {
                    getLocalBanks: { banks: mockBanks },
                },
            });

            renderWithProvider(<SelectBulkTemplate {...defaultProps} />, successStore);

            expect(sendFeedback).toHaveBeenCalledWith("Template downloaded successfully", "success");
        });
    });

    // ===== FILE UPLOAD STATE TESTS =====
    describe("File Upload States", () => {
        test("shows loading indicator when file is being uploaded", () => {
            renderWithProvider(
                <SelectBulkTemplate {...defaultProps} isUploading={true} uploadedFile={new File([""], "test.csv")} />
            );

            expect(screen.getByText("Hold on while we verify your file")).toBeInTheDocument();
        });

        test("does not show loading indicator when not uploading", () => {
            renderWithProvider(<SelectBulkTemplate {...defaultProps} />);

            expect(screen.queryByText("Hold on while we verify your file")).not.toBeInTheDocument();
        });

        test("does not show loading indicator when uploading but no file", () => {
            renderWithProvider(<SelectBulkTemplate {...defaultProps} isUploading={true} uploadedFile={undefined} />);

            expect(screen.queryByText("Hold on while we verify your file")).not.toBeInTheDocument();
        });

        test("handles upload state change correctly", () => {
            renderWithProvider(<SelectBulkTemplate {...defaultProps} />);

            // Simulate upload completion
            mockFileAttachmentProps.onUploadStateChange(true);
            expect(defaultProps.setIsUploading).toHaveBeenCalledWith(false);
            expect(defaultProps.setIsUploadComplete).toHaveBeenCalledWith(true);

            // Simulate upload start/failure
            mockFileAttachmentProps.onUploadStateChange(false);
            expect(defaultProps.setIsUploading).toHaveBeenCalledWith(true);
            expect(defaultProps.setIsUploadComplete).toHaveBeenCalledWith(false);
            expect(defaultProps.setUploadedFile).toHaveBeenCalledWith(undefined);
        });

        test("handles file removal", () => {
            renderWithProvider(<SelectBulkTemplate {...defaultProps} />);

            const removeButton = screen.getByTestId("mock-file-remove-button");
            fireEvent.click(removeButton);

            expect(defaultProps.setUploadedFile).toHaveBeenCalledWith(undefined);
        });
    });

    // ===== FILE UPLOAD PROCESSING TESTS =====
    describe("File Upload Processing", () => {
        test("does not process upload when no file is selected", async () => {
            renderWithProvider(<SelectBulkTemplate {...defaultProps} />);

            // Empty file list
            const mockFileList = {
                length: 0,
                item: () => undefined,
            };

            await mockFileAttachmentProps.onFilesSelected(mockFileList);

            expect(defaultProps.setIsUploading).not.toHaveBeenCalled();
            expect(defaultProps.setUploadedFile).not.toHaveBeenCalled();
            expect(convertCsvToArrayOfObjects).not.toHaveBeenCalled();
        });

        test("handles valid CSV data upload successfully", async () => {
            const mockData = [
                {
                    ToAccount: "**********",
                    ToBankCode: "ABC123",
                    Amount: "1000",
                    ToAccountName: "John Doe",
                    ToBankName: "Test Bank",
                    Narration: "Test Payment",
                    data_id: "1",
                },
                {
                    ToAccount: "**********",
                    ToBankCode: "XYZ789",
                    Amount: "500",
                    ToAccountName: "Jane Smith",
                    ToBankName: "Another Bank",
                    Narration: "Another Payment",
                    data_id: "2",
                },
            ];

            convertCsvToArrayOfObjects.mockResolvedValue(mockData);

            renderWithProvider(<SelectBulkTemplate {...defaultProps} />);

            const file = new File(["test content"], "test.csv", { type: "text/csv" });
            const mockFileList = {
                0: file,
                length: 1,
                item: (index) => file,
            };

            await mockFileAttachmentProps.onFilesSelected(mockFileList);

            await waitFor(() => {
                expect(defaultProps.setIsUploading).toHaveBeenCalledWith(true);
                expect(defaultProps.setUploadedFile).toHaveBeenCalledWith(file);
                expect(defaultProps.setIsUploadComplete).toHaveBeenCalledWith(false);
                expect(convertCsvToArrayOfObjects).toHaveBeenCalledWith(file);

                expect(defaultProps.setSelectedRecipients).toHaveBeenCalledWith([
                    {
                        destinationAccount: "**********",
                        destinationAccountName: "John Doe",
                        bankCode: "ABC123",
                        bankName: "Test Bank",
                        amount: "1000",
                        narration: "Test Payment",
                        data_id: "1",
                        error: null,
                    },
                    {
                        destinationAccount: "**********",
                        destinationAccountName: "Jane Smith",
                        bankCode: "XYZ789",
                        bankName: "Another Bank",
                        amount: "500",
                        narration: "Another Payment",
                        data_id: "2",
                        error: null,
                    },
                ]);
            });
        });

        test("handles file upload error gracefully", async () => {
            const mockError = new Error("Failed to process CSV");
            convertCsvToArrayOfObjects.mockRejectedValue(mockError);

            renderWithProvider(<SelectBulkTemplate {...defaultProps} />);

            const file = new File(["test content"], "test.csv", { type: "text/csv" });
            const mockFileList = {
                0: file,
                length: 1,
                item: (index) => file,
            };

            await mockFileAttachmentProps.onFilesSelected(mockFileList);

            await waitFor(() => {
                expect(sendCatchFeedback).toHaveBeenCalledWith(mockError);
            });
        });
    });

    // ===== DATA VALIDATION TESTS =====
    describe("CSV Data Validation", () => {
        test("validates account number correctly", async () => {
            const mockData = [
                {
                    ToAccount: "123", // Too short
                    ToBankCode: "ABC123",
                    Amount: "1000",
                    ToAccountName: "John Doe",
                    ToBankName: "Test Bank",
                    Narration: "Test Payment",
                    data_id: "1",
                },
                {
                    ToAccount: "**********1", // Too long
                    ToBankCode: "ABC123",
                    Amount: "1000",
                    ToAccountName: "Jane Smith",
                    ToBankName: "Test Bank",
                    Narration: "Test Payment",
                    data_id: "2",
                },
                {
                    ToAccount: "", // Empty
                    ToBankCode: "ABC123",
                    Amount: "1000",
                    ToAccountName: "Bob Johnson",
                    ToBankName: "Test Bank",
                    Narration: "Test Payment",
                    data_id: "3",
                },
            ];

            convertCsvToArrayOfObjects.mockResolvedValue(mockData);
            renderWithProvider(<SelectBulkTemplate {...defaultProps} />);

            const file = new File(["test content"], "test.csv", { type: "text/csv" });
            const mockFileList = { 0: file, length: 1, item: (index) => file };

            await mockFileAttachmentProps.onFilesSelected(mockFileList);

            await waitFor(() => {
                const recipients = defaultProps.setSelectedRecipients.mock.calls[0][0];
                expect(recipients[0].error).toBe("Invalid Account Number");
                expect(recipients[1].error).toBe("Invalid Account Number");
                expect(recipients[2].error).toBe("Invalid Account Number");
            });
        });

        test("validates bank code correctly", async () => {
            const mockData = [
                {
                    ToAccount: "**********",
                    ToBankCode: "", // Empty
                    Amount: "1000",
                    ToAccountName: "John Doe",
                    ToBankName: "Test Bank",
                    Narration: "Test Payment",
                    data_id: "1",
                },
                {
                    ToAccount: "**********",
                    ToBankCode: null, // Null
                    Amount: "1000",
                    ToAccountName: "Jane Smith",
                    ToBankName: "Test Bank",
                    Narration: "Test Payment",
                    data_id: "2",
                },
            ];

            convertCsvToArrayOfObjects.mockResolvedValue(mockData);
            renderWithProvider(<SelectBulkTemplate {...defaultProps} />);

            const file = new File(["test content"], "test.csv", { type: "text/csv" });
            const mockFileList = { 0: file, length: 1, item: (index) => file };

            await mockFileAttachmentProps.onFilesSelected(mockFileList);

            await waitFor(() => {
                const recipients = defaultProps.setSelectedRecipients.mock.calls[0][0];
                expect(recipients[0].error).toBe("Invalid Bank Code");
                expect(recipients[1].error).toBe("Invalid Bank Code");
            });
        });

        test("validates account name correctly", async () => {
            const mockData = [
                {
                    ToAccount: "**********",
                    ToBankCode: "ABC123",
                    Amount: "1000",
                    ToAccountName: "", // Empty
                    ToBankName: "Test Bank",
                    Narration: "Test Payment",
                    data_id: "1",
                },
                {
                    ToAccount: "**********",
                    ToBankCode: "ABC123",
                    Amount: "1000",
                    ToAccountName: null, // Null
                    ToBankName: "Test Bank",
                    Narration: "Test Payment",
                    data_id: "2",
                },
            ];

            convertCsvToArrayOfObjects.mockResolvedValue(mockData);
            renderWithProvider(<SelectBulkTemplate {...defaultProps} />);

            const file = new File(["test content"], "test.csv", { type: "text/csv" });
            const mockFileList = { 0: file, length: 1, item: (index) => file };

            await mockFileAttachmentProps.onFilesSelected(mockFileList);

            await waitFor(() => {
                const recipients = defaultProps.setSelectedRecipients.mock.calls[0][0];
                expect(recipients[0].error).toBe("Invalid Account Name");
                expect(recipients[1].error).toBe("Invalid Account Name");
            });
        });

        test("validates bank name correctly", async () => {
            const mockData = [
                {
                    ToAccount: "**********",
                    ToBankCode: "ABC123",
                    Amount: "1000",
                    ToAccountName: "John Doe",
                    ToBankName: "", // Empty
                    Narration: "Test Payment",
                    data_id: "1",
                },
                {
                    ToAccount: "**********",
                    ToBankCode: "ABC123",
                    Amount: "1000",
                    ToAccountName: "Jane Smith",
                    ToBankName: null, // Null
                    Narration: "Test Payment",
                    data_id: "2",
                },
            ];

            convertCsvToArrayOfObjects.mockResolvedValue(mockData);
            renderWithProvider(<SelectBulkTemplate {...defaultProps} />);

            const file = new File(["test content"], "test.csv", { type: "text/csv" });
            const mockFileList = { 0: file, length: 1, item: (index) => file };

            await mockFileAttachmentProps.onFilesSelected(mockFileList);

            await waitFor(() => {
                const recipients = defaultProps.setSelectedRecipients.mock.calls[0][0];
                expect(recipients[0].error).toBe("Invalid Bank Name");
                expect(recipients[1].error).toBe("Invalid Bank Name");
            });
        });
    });

    // ===== BANK VALIDATION TESTS =====
    describe("Bank Validation Logic", () => {
        test("corrects bank name based on bank code when name doesn't match", async () => {
            const mockData = [
                {
                    ToAccount: "**********",
                    ToBankCode: "ABC123",
                    Amount: "1000",
                    ToAccountName: "John Doe",
                    ToBankName: "Wrong Bank Name", // Incorrect bank name
                    Narration: "Test Payment",
                    data_id: "1",
                },
            ];

            convertCsvToArrayOfObjects.mockResolvedValue(mockData);
            renderWithProvider(<SelectBulkTemplate {...defaultProps} />);

            const file = new File(["test content"], "test.csv", { type: "text/csv" });
            const mockFileList = { 0: file, length: 1, item: (index) => file };

            await mockFileAttachmentProps.onFilesSelected(mockFileList);

            await waitFor(() => {
                const recipients = defaultProps.setSelectedRecipients.mock.calls[0][0];
                expect(recipients[0].bankName).toBe("Test Bank"); // Should be corrected
                expect(recipients[0].error).toBeNull();
            });
        });

        test("sets error when bank code is not found in bank list", async () => {
            const mockData = [
                {
                    ToAccount: "**********",
                    ToBankCode: "INVALID123", // Non-existent bank code
                    Amount: "1000",
                    ToAccountName: "John Doe",
                    ToBankName: "Unknown Bank",
                    Narration: "Test Payment",
                    data_id: "1",
                },
            ];

            convertCsvToArrayOfObjects.mockResolvedValue(mockData);
            renderWithProvider(<SelectBulkTemplate {...defaultProps} />);

            const file = new File(["test content"], "test.csv", { type: "text/csv" });
            const mockFileList = { 0: file, length: 1, item: (index) => file };

            await mockFileAttachmentProps.onFilesSelected(mockFileList);

            await waitFor(() => {
                const recipients = defaultProps.setSelectedRecipients.mock.calls[0][0];
                expect(recipients[0].error).toBe("Invalid bank selected");
            });
        });

        test("handles bank validation when banks list is empty", async () => {
            const emptyBanksStore = mockStore({
                sendMoney: {
                    downloadBulkTemplate: {
                        loading: false,
                        data: null,
                        success: false,
                        error: null,
                    },
                },
                recipient: {
                    getLocalBanks: {
                        banks: null, // No banks available
                    },
                },
            });

            const mockData = [
                {
                    ToAccount: "**********",
                    ToBankCode: "ABC123",
                    Amount: "1000",
                    ToAccountName: "John Doe",
                    ToBankName: "Test Bank",
                    Narration: "Test Payment",
                    data_id: "1",
                },
            ];

            convertCsvToArrayOfObjects.mockResolvedValue(mockData);
            renderWithProvider(<SelectBulkTemplate {...defaultProps} />, emptyBanksStore);

            const file = new File(["test content"], "test.csv", { type: "text/csv" });
            const mockFileList = { 0: file, length: 1, item: (index) => file };

            await mockFileAttachmentProps.onFilesSelected(mockFileList);

            await waitFor(() => {
                const recipients = defaultProps.setSelectedRecipients.mock.calls[0][0];
                // Should still process without bank validation
                expect(recipients[0].error).toBeNull();
            });
        });
    });

    // ===== BULK LIMIT TESTS =====
    describe("Bulk Limit Processing", () => {
        test("processes only up to BULK_LIMIT (1000) recipients", async () => {
            const OVER_LIMIT = 1050;
            const mockData = Array.from({ length: OVER_LIMIT }, (_, i) => ({
                ToAccount: "**********",
                ToBankCode: "ABC123",
                Amount: "1000",
                ToAccountName: `User ${i}`,
                ToBankName: "Test Bank",
                Narration: "Test Payment",
                data_id: `${i + 1}`,
            }));

            convertCsvToArrayOfObjects.mockResolvedValue(mockData);
            renderWithProvider(<SelectBulkTemplate {...defaultProps} />);

            const file = new File(["test content"], "test.csv", { type: "text/csv" });
            const mockFileList = { 0: file, length: 1, item: () => file };

            await mockFileAttachmentProps.onFilesSelected(mockFileList);

            await waitFor(() => {
                const recipients = defaultProps.setSelectedRecipients.mock.calls[0][0];
                expect(recipients.length).toBe(1000); // Should be limited to 1000
                expect(recipients[0].destinationAccountName).toBe("User 0");
                expect(recipients[999].destinationAccountName).toBe("User 999");
            });
        });

        test("processes all recipients when under BULK_LIMIT", async () => {
            const UNDER_LIMIT = 500;
            const mockData = Array.from({ length: UNDER_LIMIT }, (_, i) => ({
                ToAccount: "**********",
                ToBankCode: "ABC123",
                Amount: "1000",
                ToAccountName: `User ${i}`,
                ToBankName: "Test Bank",
                Narration: "Test Payment",
                data_id: `${i + 1}`,
            }));

            convertCsvToArrayOfObjects.mockResolvedValue(mockData);
            renderWithProvider(<SelectBulkTemplate {...defaultProps} />);

            const file = new File(["test content"], "test.csv", { type: "text/csv" });
            const mockFileList = { 0: file, length: 1, item: () => file };

            await mockFileAttachmentProps.onFilesSelected(mockFileList);

            await waitFor(() => {
                const recipients = defaultProps.setSelectedRecipients.mock.calls[0][0];
                expect(recipients.length).toBe(UNDER_LIMIT);
                expect(recipients[0].destinationAccountName).toBe("User 0");
                expect(recipients[UNDER_LIMIT - 1].destinationAccountName).toBe(`User ${UNDER_LIMIT - 1}`);
            });
        });

        test("processes exactly BULK_LIMIT recipients", async () => {
            const EXACT_LIMIT = 1000;
            const mockData = Array.from({ length: EXACT_LIMIT }, (_, i) => ({
                ToAccount: "**********",
                ToBankCode: "ABC123",
                Amount: "1000",
                ToAccountName: `User ${i}`,
                ToBankName: "Test Bank",
                Narration: "Test Payment",
                data_id: `${i + 1}`,
            }));

            convertCsvToArrayOfObjects.mockResolvedValue(mockData);
            renderWithProvider(<SelectBulkTemplate {...defaultProps} />);

            const file = new File(["test content"], "test.csv", { type: "text/csv" });
            const mockFileList = { 0: file, length: 1, item: () => file };

            await mockFileAttachmentProps.onFilesSelected(mockFileList);

            await waitFor(() => {
                const recipients = defaultProps.setSelectedRecipients.mock.calls[0][0];
                expect(recipients.length).toBe(EXACT_LIMIT);
            });
        });
    });

    // ===== TEMPLATE SELECTION TESTS =====
    describe("Template Selection Integration", () => {
        test("clears selected template when file is uploaded", async () => {
            const mockData = [
                {
                    ToAccount: "**********",
                    ToBankCode: "ABC123",
                    Amount: "1000",
                    ToAccountName: "John Doe",
                    ToBankName: "Test Bank",
                    Narration: "Test Payment",
                    data_id: "1",
                },
            ];

            convertCsvToArrayOfObjects.mockResolvedValue(mockData);
            renderWithProvider(<SelectBulkTemplate {...defaultProps} />);
            const file = new File(["test content"], "test.csv", { type: "text/csv" });
            const mockFileList = { 0: file, length: 1, item: () => file };
            await mockFileAttachmentProps.onFilesSelected(mockFileList);
            expect(defaultProps.setSelectedTemplate).toHaveBeenCalledWith(undefined);
        });
        test("updates selected template when a template is chosen", () => {
            renderWithProvider(<SelectBulkTemplate {...defaultProps} />);

            const selectButton = screen.getByTestId("mock-template-select");
            fireEvent.click(selectButton);

            expect(defaultProps.setSelectedTemplate).toHaveBeenCalledWith({
                id: "template-1",
                name: "Test Template",
            });
        });
    });
});
