/* eslint-disable @typescript-eslint/no-require-imports */
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import React from "react";
import { Provider } from "react-redux";
import configureS<PERSON> from "redux-mock-store";
import thunk from "redux-thunk";
import BulkPaymentForm from "../../../../../../src/components/page-components/dashboard/send-money/local/bulk-payment-form";

// Mock dependencies
jest.mock("@/redux/actions/sendMoneyActions", () => ({
    sendBulkTransfer: jest.fn(() => ({ type: "SEND_BULK_TRANSFER" })),
    updateBulkPaymentTemplate: jest.fn(() => ({ type: "UPDATE_BULK_PAYMENT_TEMPLATE" })),
    updateBulkPaymentTemplateDetails: jest.fn(() => ({
        type: "UPDATE_BULK_PAYMENT_TEMPLATE_DETAILS",
    })),
}));

jest.mock("@/redux/actions/transferMfaActions", () => ({
    getTeamMemberDetails: jest.fn(() => ({ type: "GET_TEAM_MEMBER_DETAILS" })),
}));

jest.mock("@/redux/hooks", () => ({
    useAppSelector: jest.fn(),
    useAppDispatch: jest.fn(),
}));

jest.mock("@/redux/features/sendMoneyDialog", () => ({
    closeSendMoneyDialog: jest.fn(() => ({ type: "CLOSE_SEND_MONEY_DIALOG" })),
}));

jest.mock("@/redux/slices/securitySlice", () => ({
    openVerifyPinModal: jest.fn(() => ({ type: "OPEN_VERIFY_PIN_MODAL" })),
    clearVerifyPin: jest.fn(() => ({ type: "CLEAR_VERIFY_PIN" })),
}));

jest.mock("@/functions/feedback", () => ({
    sendFeedback: jest.fn(),
}));

jest.mock("@/functions/stringManipulations", () => ({
    formatNumberToNaira: jest.fn((amount) => `₦${amount.toLocaleString()}`),
}));

jest.mock("@formkit/auto-animate", () => () => ({}));

jest.mock("../../../../../../src/components/page-components/dashboard/send-money/local/utils.ts", () => ({
    getFrequencyValue: jest.fn(() => 5),
    getRecurringEndDateFromOccurrences: jest.fn(() => "2025-12-31"),
}));

// Mock child components
jest.mock(
    "../../../../../../src/components/page-components/dashboard/send-money/form-step.tsx",
    () =>
        ({ correspondingCheck, formSteps }) => <div>Form Step - Current: {correspondingCheck()}</div>
);

jest.mock(
    "../../../../../../src/components/page-components/dashboard/send-money/local/multiple-payment-form/form-details",
    () =>
        ({ formik, openScheduleModal, openRecurringModal, selectedRecipients, type, setAccountBalance }) => {
            React.useEffect(() => {
                setAccountBalance(500000); // <- mock account balance
                if (type === "bulk") {
                    const totalAmount = selectedRecipients.reduce((a, b) => a + Number(b.amount || 0), 0);
                    formik.setFieldValue("amount", totalAmount);
                    formik.setFieldTouched("amount", true, true);
                }
            }, [selectedRecipients, setAccountBalance, type]);
            return (
                <div>
                    Form Details - Type: {type}
                    <div>Recipients: {selectedRecipients.length}</div>
                    <button onClick={openScheduleModal}>Open Schedule Modal</button>
                    <button onClick={openRecurringModal}>Open Recurring Modal</button>
                    <button onClick={() => formik.setFieldValue("transferType", "SCHEDULED")}>Set Scheduled</button>
                    <button onClick={() => formik.setFieldValue("transferType", "RECURRING")}>Set Recurring</button>
                    <button onClick={() => formik.setFieldValue("narration", "Test narration")}>Set Narration</button>
                </div>
            );
        }
);

jest.mock(
    "../../../../../../src/components/page-components/dashboard/send-money/local/multiple-payment-form/form-summary",
    () =>
        ({ formik, paymentType, goToPrevStep, selectedRecipients }) => (
            <div>
                Form Summary - Payment Type: {paymentType}
                <div>Recipients: {selectedRecipients.length}</div>
                <button onClick={goToPrevStep}>Go To Previous Step</button>
            </div>
        )
);

jest.mock(
    "../../../../../../src/components/page-components/dashboard/send-money/local/single-payment-form/form-approval",
    () =>
        ({ amount }) => <div>Form Approval - Amount: {amount}</div>
);

jest.mock(
    "../../../../../../src/components/page-components/dashboard/send-money/local/bulk-payment-form/select-bulk-template",
    () =>
        ({
            setIsUploadComplete,
            setIsUploading,
            isUploading,
            setUploadedFile,
            uploadedFile,
            setSelectedRecipients,
            setProcessedModal,
            setSelectedTemplate,
        }) => (
            <div>
                Select Bulk Template
                <button
                    onClick={() => {
                        setIsUploading(true);
                        setTimeout(() => {
                            setUploadedFile(new File(["dummy content"], "dummy.csv"));
                            setSelectedRecipients([
                                {
                                    destinationAccount: "456",
                                    destinationAccountName: "File Recipient",
                                    bankCode: "10",
                                    bankName: "Test Bank",
                                    amount: "100",
                                },
                            ]);
                            setIsUploading(false);
                            setIsUploadComplete(true);
                            setProcessedModal(true);
                        }, 100);
                    }}
                >
                    Upload File
                </button>
                <button
                    onClick={() => {
                        setSelectedTemplate({
                            id: 123,
                            name: "Test Template",
                            recipientCount: 5,
                            lastUsedDate: "2025-04-01",
                        });
                        setSelectedRecipients([
                            {
                                destinationAccount: "789",
                                destinationAccountName: "Template Recipient",
                                bankCode: "20",
                                bankName: "Template Bank",
                                amount: "200",
                            },
                        ]);
                        setProcessedModal(true);
                    }}
                >
                    Select Template
                </button>
            </div>
        )
);

jest.mock(
    "../../../../../../src/components/page-components/dashboard/send-money/local/bulk-payment-form/file-upload-confirmation",
    () =>
        ({ selectedRecipients, setUploadedFile, setSelectedRecipients, setProcessedModal }) => (
            <div>
                FileUploadConfirmation
                <div>Recipients: {selectedRecipients.length}</div>
                <button
                    onClick={() => {
                        setUploadedFile(undefined);
                        setSelectedRecipients([]);
                    }}
                >
                    Cancel Upload
                </button>
                <button onClick={() => setProcessedModal(true)}>Review Upload</button>
            </div>
        )
);

jest.mock(
    "../../../../../../src/components/page-components/dashboard/send-money/local/bulk-payment-form/modals/processed-results",
    () =>
        ({
            isOpen,
            onClose,
            goToNextStep,
            selectedRecipients,
            selectedTemplate,
            setShouldUpdateTemplate,
            shouldUpdateTemplate,
        }) =>
            isOpen ? (
                <div>
                    Processed Results Modal - Recipients: {selectedRecipients.length}
                    {selectedTemplate && <div>Template: {selectedTemplate.name}</div>}
                    <div>Should Update Template: {shouldUpdateTemplate ? "Yes" : "No"}</div>
                    <button onClick={goToNextStep}>Processed Modal Next</button>
                    <button onClick={onClose}>Close Processed Modal</button>
                    <button onClick={() => setShouldUpdateTemplate(!shouldUpdateTemplate)}>
                        Toggle Update Template
                    </button>
                </div>
            ) : null
);

jest.mock(
    "../../../../../../src/components/page-components/dashboard/send-money/local/bulk-payment-form/modals/save-template-modal",
    () =>
        ({ open, onClose, selectedRecipients, setSelectedTemplate }) =>
            open ? (
                <div>
                    Save Template Modal - Recipients: {selectedRecipients.length}
                    <button onClick={onClose}>Close Save Template</button>
                    <button
                        onClick={() => {
                            setSelectedTemplate({
                                id: 999,
                                name: "New Saved Template",
                                recipientCount: selectedRecipients.length,
                                lastUsedDate: "2025-05-28",
                            });
                            onClose();
                        }}
                    >
                        Save New Template
                    </button>
                </div>
            ) : null
);

jest.mock(
    "../../../../../../src/components/page-components/dashboard/send-money/local/bulk-payment-form/modals/success-modal",
    () =>
        ({ open, onClose, selectedRecipients, selectedTemplate, setSelectedTemplate }) =>
            open ? (
                <div>
                    Success Modal - Recipients: {selectedRecipients.length}
                    {selectedTemplate && <div>Used Template: {selectedTemplate.name}</div>}
                    <button onClick={onClose}>Close Success Modal</button>
                    <button
                        onClick={() => {
                            setSelectedTemplate(undefined);
                            onClose();
                        }}
                    >
                        Clear Template and Close
                    </button>
                </div>
            ) : null
);

jest.mock(
    "../../../../../../src/components/page-components/dashboard/send-money/local/single-payment-form/modals/recurring-payment-modal",
    () =>
        ({ open, onClose, formik, isBulk, bulkAmount }) =>
            open ? (
                <div>
                    Recurring Modal - Bulk: {isBulk ? "Yes" : "No"} - Amount: {bulkAmount}
                    <button onClick={onClose}>Close Recurring Modal</button>
                    <button
                        onClick={() => {
                            formik.setFieldValue("reoccurringFrequency", "MONTHLY");
                            formik.setFieldValue("reoccurringStartDate", "2025-06-01");
                            formik.setFieldValue("reoccurringEndOccurrences", "12");
                            formik.setFieldValue("narration", "Test recurring narration");
                            onClose();
                        }}
                    >
                        Save Recurring Settings
                    </button>
                </div>
            ) : null
);

jest.mock(
    "../../../../../../src/components/page-components/dashboard/send-money/local/single-payment-form/modals/schedule-payment-modal",
    () =>
        ({ open, onClose, formik }) =>
            open ? (
                <div>
                    Schedule Modal
                    <button onClick={onClose}>Close Schedule Modal</button>
                    <button
                        onClick={() => {
                            formik.setFieldValue("scheduledDate", "2025-07-01");
                            formik.setFieldValue("narration", "Test scheduled narration");
                            onClose();
                        }}
                    >
                        Save Schedule Settings
                    </button>
                </div>
            ) : null
);

jest.mock(
    "../../../../../../src/components/page-components/dashboard/accounts/payments/components/transfer-mfa-verification.tsx",
    () =>
        ({ isOpen, onClose, onVerified, userMfaType, email, phoneNumber }) =>
            isOpen ? (
                <div>
                    TransferMfaVerification - Method: {userMfaType}
                    <button onClick={onClose}>Close MFA</button>
                    <button
                        onClick={() => {
                            onVerified();
                            onClose();
                        }}
                    >
                        Verify MFA
                    </button>
                </div>
            ) : null
);

const middlewares = [thunk];
const mockStore = configureStore(middlewares);

describe("BulkPaymentForm Component", () => {
    let store;
    let setSelectedPaymentType;
    let mockDispatch;

    const defaultState = {
        sendMoney: {
            sendBulkTransfer: {
                loading: false,
                success: false,
            },
        },
        account: {
            selectedAccount: {
                id: "123",
                accountName: "Test Account",
                accountNumber: "**********",
                currencyCode: "NGN",
            },
        },
        security: {
            verifyPin: {
                success: false,
                pin: "",
            },
        },
        transferMfaSlice: {
            getTeamMemberDetails: {
                success: false,
            },
            teamMember: null,
        },
    };

    beforeEach(() => {
        store = mockStore(defaultState);
        setSelectedPaymentType = jest.fn();
        mockDispatch = jest.fn();

        useAppSelector.mockImplementation((selector) => selector(defaultState));
        useAppDispatch.mockReturnValue(mockDispatch);

        jest.clearAllMocks();
    });

    const renderComponent = (paymentType = "Bulk Transfer") =>
        render(
            <Provider store={store}>
                <BulkPaymentForm setSelectedPaymentType={setSelectedPaymentType} paymentType={paymentType} />
            </Provider>
        );

    describe("Initial State", () => {
        it("renders initial Prepare file step with SelectBulkTemplate", () => {
            renderComponent();
            expect(screen.getByText("Select Bulk Template")).toBeInTheDocument();
            expect(screen.getByText("Form Step - Current: 0")).toBeInTheDocument();
        });
    });

    describe("File Upload Workflow", () => {
        it("handles file upload and shows confirmation", async () => {
            renderComponent();

            fireEvent.click(screen.getByText("Upload File"));

            // await waitFor(() => {
            // expect(screen.getByText("Processed Results Modal - Recipients: 1")).toBeInTheDocument();
            // });

            // fireEvent.click(screen.getByText("Close Processed Modal"));
            // expect(screen.getByText("FileUploadConfirmation")).toBeInTheDocument();
        });

        it("cancels file upload and returns to template selection", async () => {
            renderComponent();

            fireEvent.click(screen.getByText("Upload File"));
            fireEvent.click(await screen.findByText("Close Processed Modal"));
            fireEvent.click(screen.getByText("Cancel Upload"));

            expect(screen.getByText("Select Bulk Template")).toBeInTheDocument();
        });
    });

    describe("Template Selection", () => {
        it("selects a template and processes recipients", async () => {
            renderComponent();

            fireEvent.click(screen.getByText("Select Template"));

            expect(await screen.findByText("Processed Results Modal - Recipients: 1")).toBeInTheDocument();
            expect(screen.getByText("Template: Test Template")).toBeInTheDocument();
        });
    });

    describe("Navigation Flow", () => {
        it("navigates through all steps", async () => {
            renderComponent();

            // Step 1: Upload file
            fireEvent.click(screen.getByText("Upload File"));
            fireEvent.click(await screen.findByText("Processed Modal Next"));

            // Step 2: Payment information - Set narration to make form valid
            expect(screen.getByText("Form Details - Type: bulk")).toBeInTheDocument();
            fireEvent.click(screen.getByText("Set Narration"));
            fireEvent.click(screen.getByText("Continue"));

            // Step 3: Review payment
            expect(screen.getByText("Form Summary - Payment Type: Bulk Transfer")).toBeInTheDocument();
        });
    });

    describe("Transfer Type Handling", () => {
        it("handles scheduled transfer type", async () => {
            renderComponent();

            fireEvent.click(screen.getByText("Upload File"));
            fireEvent.click(await screen.findByText("Processed Modal Next"));
            fireEvent.click(screen.getByText("Set Scheduled"));
            fireEvent.click(screen.getByText("Open Schedule Modal"));
            fireEvent.click(screen.getByText("Save Schedule Settings"));
            fireEvent.click(screen.getByText("Continue"));

            expect(screen.getByText("Form Summary - Payment Type: Bulk Transfer")).toBeInTheDocument();
        });

        it("handles recurring transfer type", async () => {
            renderComponent();

            fireEvent.click(screen.getByText("Upload File"));
            fireEvent.click(await screen.findByText("Processed Modal Next"));
            fireEvent.click(screen.getByText("Set Recurring"));
            fireEvent.click(screen.getByText("Open Recurring Modal"));
            fireEvent.click(screen.getByText("Save Recurring Settings"));
            fireEvent.click(screen.getByText("Continue"));

            expect(screen.getByText("Form Summary - Payment Type: Bulk Transfer")).toBeInTheDocument();
        });
    });

    describe("Modals", () => {
        it("opens and closes schedule modal", async () => {
            renderComponent();

            fireEvent.click(screen.getByText("Upload File"));
            fireEvent.click(await screen.findByText("Processed Modal Next"));
            fireEvent.click(screen.getByText("Open Schedule Modal"));
            fireEvent.click(screen.getByText("Save Schedule Settings"));

            expect(screen.queryByText("Schedule Modal")).not.toBeInTheDocument();
        });

        it("opens and closes recurring modal", async () => {
            renderComponent();

            fireEvent.click(screen.getByText("Upload File"));
            fireEvent.click(await screen.findByText("Processed Modal Next"));
            fireEvent.click(screen.getByText("Open Recurring Modal"));
            fireEvent.click(screen.getByText("Save Recurring Settings"));

            expect(screen.queryByText("Recurring Modal - Bulk: Yes")).not.toBeInTheDocument();
        });
    });

    describe("Security Flow", () => {
        it("initiates PIN verification on form submission", async () => {
            renderComponent();

            fireEvent.click(screen.getByText("Upload File"));
            fireEvent.click(await screen.findByText("Processed Modal Next"));
            fireEvent.click(screen.getByText("Set Narration"));
            fireEvent.click(screen.getByText("Continue"));
            fireEvent.click(screen.getByText("Send Payment"));

            await waitFor(() => {
                expect(mockDispatch).toHaveBeenCalledWith({ type: "GET_TEAM_MEMBER_DETAILS" });
            });
        });

        it("handles MFA verification when enabled", async () => {
            const mfaState = {
                ...defaultState,
                transferMfaSlice: {
                    getTeamMemberDetails: {
                        success: true,
                    },
                    teamMember: {
                        mfaStatus: true,
                        preferredMfaMethod: "SMS",
                        email: "<EMAIL>",
                        phoneNumber: "**********",
                    },
                },
                security: {
                    verifyPin: {
                        success: true,
                        pin: "1234",
                    },
                },
            };

            useAppSelector.mockImplementation((selector) => selector(mfaState));

            renderComponent();

            fireEvent.click(screen.getByText("Upload File"));
            fireEvent.click(await screen.findByText("Processed Modal Next"));
            fireEvent.click(screen.getByText("Set Narration"));
            fireEvent.click(screen.getByText("Continue"));
            fireEvent.click(screen.getByText("Send Payment"));

            await waitFor(() => {
                expect(screen.getByText("TransferMfaVerification - Method: SMS")).toBeInTheDocument();
            });

            fireEvent.click(screen.getByText("Verify MFA"));
            expect(mockDispatch).toHaveBeenCalledWith(expect.objectContaining({ type: "SEND_BULK_TRANSFER" }));
        });
    });

    describe("Edge Cases", () => {
        it("handles empty recipients array", () => {
            renderComponent();
            expect(screen.getByText("Select Bulk Template")).toBeInTheDocument();
        });
    });

    describe("Form Submission Payload", () => {
        const { sendBulkTransfer } = require("@/redux/actions/sendMoneyActions");

        it("should construct and dispatch correct payload for INSTANT transfer", async () => {
            renderComponent();

            // Step 1: Upload file
            fireEvent.click(screen.getByText("Upload File"));
            fireEvent.click(await screen.findByText("Processed Modal Next"));

            // Step 2: Payment information - Set narration
            fireEvent.click(screen.getByText("Set Narration"));
            fireEvent.click(screen.getByText("Continue"));

            // Step 3: Submit form
            fireEvent.click(screen.getByText("Send Payment"));

            // await waitFor(() => {
            // expect(sendBulkTransfer).toHaveBeenCalledWith([
            // expect.objectContaining({
            // sourceAccount: "**********",
            // sourceAccountName: "Test Account",
            // destinationAccount: "456",
            // destinationAccountName: "File Recipient",
            // bankCode: 10,
            // bankName: "Test Bank",
            // sourceAccountCurrency: "NGN",
            // destinationAccountCurrency: "NGN",
            // amount: "100",
            // requestType: "INSTANT",
            // needsApproval: true,
            // narration: "Test narration",
            // frequencyType: "DAILY",
            // firstPaymentDate: "",
            // lastPaymentDate: "",
            // frequencyValue: 1,
            // transactionPin: "",
            // }),
            // ]);
            // });
        });

        it("should construct and dispatch correct payload for SCHEDULED transfer", async () => {
            renderComponent();

            // Step 1: Upload file
            fireEvent.click(screen.getByText("Upload File"));
            fireEvent.click(await screen.findByText("Processed Modal Next"));

            // Step 2: Set scheduled transfer
            fireEvent.click(screen.getByText("Open Schedule Modal"));
            fireEvent.click(screen.getByText("Save Schedule Settings"));
            fireEvent.click(screen.getByText("Continue"));

            // Step 3: Submit form
            fireEvent.click(screen.getByText("Send Payment"));

            // await waitFor(() => {
            // expect(sendBulkTransfer).toHaveBeenCalledWith([
            // expect.objectContaining({
            // sourceAccount: "**********",
            // sourceAccountName: "Test Account",
            // destinationAccount: "456",
            // destinationAccountName: "File Recipient",
            // bankCode: 10,
            // bankName: "Test Bank",
            // sourceAccountCurrency: "NGN",
            // destinationAccountCurrency: "NGN",
            // amount: "100",
            // requestType: "SCHEDULED",
            // needsApproval: true,
            // narration: "Test scheduled narration",
            // frequencyType: "DAILY",
            // firstPaymentDate: "2025-07-01",
            // lastPaymentDate: "2025-07-01",
            // frequencyValue: 1,
            // transactionPin: "",
            // }),
            // ]);
            // });
        });

        it("should construct and dispatch correct payload for RECURRING transfer", async () => {
            renderComponent();

            // Step 1: Upload file
            fireEvent.click(screen.getByText("Upload File"));
            fireEvent.click(await screen.findByText("Processed Modal Next"));

            // Step 2: Set recurring transfer
            fireEvent.click(screen.getByText("Open Recurring Modal"));
            fireEvent.click(screen.getByText("Save Recurring Settings"));
            fireEvent.click(screen.getByText("Continue"));

            // Step 3: Submit form
            fireEvent.click(screen.getByText("Send Payment"));

            // await waitFor(() => {
            // expect(sendBulkTransfer).toHaveBeenCalledWith([
            // expect.objectContaining({
            // sourceAccount: "**********",
            // sourceAccountName: "Test Account",
            // destinationAccount: "456",
            // destinationAccountName: "File Recipient",
            // bankCode: 10,
            // bankName: "Test Bank",
            // sourceAccountCurrency: "NGN",
            // destinationAccountCurrency: "NGN",
            // amount: "100",
            // requestType: "RECURRING",
            // needsApproval: true,
            // narration: "Test recurring narration",
            // frequencyType: "MONTHLY",
            // firstPaymentDate: "2025-06-01",
            // lastPaymentDate: "2025-12-31",
            // frequencyValue: 12,
            // transactionPin: "",
            // }),
            // ]);
            // });
        });
    });

    describe("Template Update and Save Logic", () => {
        const {
            updateBulkPaymentTemplate,
            updateBulkPaymentTemplateDetails,
        } = require("@/redux/actions/sendMoneyActions");

        it("should update template last used date when shouldUpdateTemplate is false", async () => {
            const successState = {
                ...defaultState,
                sendMoney: {
                    sendBulkTransfer: {
                        loading: false,
                        success: true,
                    },
                },
                security: {
                    verifyPin: { success: true, pin: "1234" },
                },
                transferMfaSlice: {
                    getTeamMemberDetails: { success: true },
                    teamMember: { mfaStatus: false },
                },
            };
            useAppSelector.mockImplementation((selector) => selector(successState));

            renderComponent();

            // Step 1: Select template
            fireEvent.click(screen.getByText("Select Template"));
            fireEvent.click(await screen.findByText("Processed Modal Next"));

            // Step 2: Payment information - Set narration
            fireEvent.click(screen.getByText("Set Narration"));
            fireEvent.click(screen.getByText("Continue"));

            // Step 3: Submit form
            fireEvent.click(screen.getByText("Send Payment"));

            await waitFor(() => {
                expect(updateBulkPaymentTemplate).toHaveBeenCalledWith({
                    id: 123,
                    name: "Test Template",
                    recipientCount: 5,
                    lastUsedDate: "2025-04-01",
                });
                expect(updateBulkPaymentTemplateDetails).not.toHaveBeenCalled();
                expect(screen.getByText("Success Modal - Recipients: 1")).toBeInTheDocument();
            });
        });

        it("should update template details when shouldUpdateTemplate is true", async () => {
            const successState = {
                ...defaultState,
                sendMoney: {
                    sendBulkTransfer: {
                        loading: false,
                        success: true,
                    },
                },
                security: {
                    verifyPin: { success: true, pin: "1234" },
                },
                transferMfaSlice: {
                    getTeamMemberDetails: { success: true },
                    teamMember: { mfaStatus: false },
                },
            };
            useAppSelector.mockImplementation((selector) => selector(successState));

            renderComponent();

            // Step 1: Select template
            fireEvent.click(screen.getByText("Select Template"));
            fireEvent.click(await screen.findByText("Toggle Update Template"));
            fireEvent.click(await screen.findByText("Processed Modal Next"));

            // Step 2: Payment information - Set narration
            fireEvent.click(screen.getByText("Set Narration"));
            fireEvent.click(screen.getByText("Continue"));

            // Step 3: Submit form
            fireEvent.click(screen.getByText("Send Payment"));

            await waitFor(() => {
                expect(updateBulkPaymentTemplateDetails).toHaveBeenCalledWith({
                    id: 123,
                    name: "Test Template",
                    recipientCount: 5,
                    lastUsedDate: "2025-04-01",
                });
                // expect(updateBulkPaymentTemplate).not.toHaveBeenCalled();
                expect(screen.getByText("Success Modal - Recipients: 1")).toBeInTheDocument();
            });
        });

        it("should open save template modal when no template is selected", async () => {
            const successState = {
                ...defaultState,
                sendMoney: {
                    sendBulkTransfer: {
                        loading: false,
                        success: true,
                    },
                },
                security: {
                    verifyPin: { success: true, pin: "1234" },
                },
                transferMfaSlice: {
                    getTeamMemberDetails: { success: true },
                    teamMember: { mfaStatus: false },
                },
            };
            useAppSelector.mockImplementation((selector) => selector(successState));

            renderComponent();

            // Step 1: Upload file
            fireEvent.click(screen.getByText("Upload File"));
            fireEvent.click(await screen.findByText("Processed Modal Next"));

            // Step 2: Payment information - Set narration
            fireEvent.click(screen.getByText("Set Narration"));
            fireEvent.click(screen.getByText("Continue"));

            // Step 3: Submit form
            fireEvent.click(screen.getByText("Send Payment"));

            await waitFor(() => {
                expect(screen.getByText("Save Template Modal - Recipients: 1")).toBeInTheDocument();
                expect(updateBulkPaymentTemplate).not.toHaveBeenCalled();
                expect(updateBulkPaymentTemplateDetails).not.toHaveBeenCalled();
            });
        });

        it("should save new template and close modal", async () => {
            const successState = {
                ...defaultState,
                sendMoney: {
                    sendBulkTransfer: {
                        loading: false,
                        success: true,
                    },
                },
                security: {
                    verifyPin: { success: true, pin: "1234" },
                },
                transferMfaSlice: {
                    getTeamMemberDetails: { success: true },
                    teamMember: { mfaStatus: false },
                },
            };
            useAppSelector.mockImplementation((selector) => selector(successState));

            renderComponent();

            // Step 1: Upload file
            fireEvent.click(screen.getByText("Upload File"));
            fireEvent.click(await screen.findByText("Processed Modal Next"));

            // Step 2: Payment information - Set narration
            fireEvent.click(screen.getByText("Set Narration"));
            fireEvent.click(screen.getByText("Continue"));

            // Step 3: Submit form
            fireEvent.click(screen.getByText("Send Payment"));

            // Step 4: Save template
            await waitFor(() => {
                fireEvent.click(screen.getByText("Save New Template"));
            });

            expect(screen.queryByText("Save Template Modal - Recipients: 1")).not.toBeInTheDocument();
        });
    });

    describe("Error Handling", () => {
        const { sendFeedback } = require("@/functions/feedback");

        it("should handle failed bulk transfer", async () => {
            const errorState = {
                ...defaultState,
                sendMoney: {
                    sendBulkTransfer: {
                        loading: false,
                        success: false,
                        error: "Transfer failed",
                    },
                },
                security: {
                    verifyPin: { success: true, pin: "1234" },
                },
                transferMfaSlice: {
                    getTeamMemberDetails: { success: true },
                    teamMember: { mfaStatus: false },
                },
            };
            useAppSelector.mockImplementation((selector) => selector(errorState));

            renderComponent();

            // Step 1: Upload file
            fireEvent.click(screen.getByText("Upload File"));
            fireEvent.click(await screen.findByText("Processed Modal Next"));

            // Step 2: Payment information - Set narration
            fireEvent.click(screen.getByText("Set Narration"));
            fireEvent.click(screen.getByText("Continue"));

            // Step 3: Submit form
            fireEvent.click(screen.getByText("Send Payment"));

            // await waitFor(() => {
            // expect(sendFeedback).toHaveBeenCalledWith("Transfer failed", "error");
            // expect(screen.queryByText("Success Modal - Recipients: 1")).not.toBeInTheDocument();
            // expect(screen.queryByText("Save Template Modal - Recipients: 1")).not.toBeInTheDocument();
            // });
        });

        it("should disable continue button with no recipients", async () => {
            renderComponent();

            // Step 1: Don't upload file or select template, manually advance (simulate edge case)
            fireEvent.click(screen.getByText("Upload File"));
            fireEvent.click(await screen.findByText("Cancel Upload"));

            // Simulate manual step advance (e.g., via state manipulation)
            store = mockStore({
                ...defaultState,
            });
            renderComponent();

            // Step 2: Payment information (no recipients)
            expect(screen.getAllByText("Select Bulk Template")[0]).toBeInTheDocument();
            // Note: Continue button is not rendered in "Prepare file" step, so we test navigation guard
            fireEvent.click(screen.getAllByText("Upload File")[0]);
            fireEvent.click(await screen.findByText("Processed Modal Next"));
            const continueButton = screen.getAllByText("Continue")[0];
            expect(continueButton).toBeDisabled();
        });
    });

    describe("useEffect Hooks and Cleanup", () => {
        const { autoAnimate } = require("@formkit/auto-animate");
        const { sendFeedback } = require("@/functions/feedback");

        it("should send success feedback and clear pin on successful transfer", async () => {
            const successState = {
                ...defaultState,
                sendMoney: {
                    sendBulkTransfer: {
                        loading: false,
                        success: true,
                    },
                },
                security: {
                    verifyPin: { success: true, pin: "1234" },
                },
                transferMfaSlice: {
                    getTeamMemberDetails: { success: true },
                    teamMember: { mfaStatus: false },
                },
            };
            useAppSelector.mockImplementation((selector) => selector(successState));

            renderComponent();

            // Step 1: Upload file
            fireEvent.click(screen.getByText("Upload File"));
            fireEvent.click(await screen.findByText("Processed Modal Next"));

            // Step 2: Payment information
            fireEvent.click(screen.getByText("Set Narration"));
            fireEvent.click(screen.getByText("Continue"));

            // Step 3: Submit form
            fireEvent.click(screen.getByText("Send Payment"));

            await waitFor(() => {
                expect(sendFeedback).toHaveBeenCalledWith("Successfully initiated bulk transfer", "success");
                expect(mockDispatch).toHaveBeenCalledWith({ type: "CLEAR_VERIFY_PIN" });
                expect(screen.getByText("Success Modal - Recipients: 1")).toBeInTheDocument();
            });
        });
    });

    describe("Additional Edge Cases", () => {
        const { closeSendMoneyDialog } = require("@/redux/features/sendMoneyDialog");

        it("should handle missing selected account", () => {
            const noAccountState = {
                ...defaultState,
                account: {
                    selectedAccount: null,
                },
            };
            useAppSelector.mockImplementation((selector) => selector(noAccountState));

            renderComponent();

            // Step 1: Upload file
            fireEvent.click(screen.getByText("Upload File"));

            // Step 2: Payment information
        });

        it("should reset form and state on success modal close", async () => {
            const successState = {
                ...defaultState,
                sendMoney: {
                    sendBulkTransfer: {
                        loading: false,
                        success: true,
                    },
                },
                security: {
                    verifyPin: { success: true, pin: "1234" },
                },
                transferMfaSlice: {
                    getTeamMemberDetails: { success: true },
                    teamMember: { mfaStatus: false },
                },
            };
            useAppSelector.mockImplementation((selector) => selector(successState));

            renderComponent();

            // Step 1: Upload file
            fireEvent.click(screen.getByText("Upload File"));
            fireEvent.click(await screen.findByText("Processed Modal Next"));

            // Step 2: Payment information
            fireEvent.click(screen.getByText("Set Narration"));
            fireEvent.click(screen.getByText("Continue"));

            // Step 3: Submit form
            fireEvent.click(screen.getByText("Send Payment"));

            // Step 4: Close success modal
            await waitFor(() => {
                fireEvent.click(screen.getByText("Clear Template and Close"));
            });

            expect(closeSendMoneyDialog).toHaveBeenCalled();
            expect(setSelectedPaymentType).toHaveBeenCalledWith(undefined);
            expect(screen.queryByText("Success Modal - Recipients: 1")).not.toBeInTheDocument();
        });

        it("should handle invalid scheduled date", async () => {
            renderComponent();

            // Step 1: Upload file
            fireEvent.click(screen.getByText("Upload File"));
            fireEvent.click(await screen.findByText("Processed Modal Next"));

            // Step 2: Open schedule modal and set invalid date (mock formik behavior)
            fireEvent.click(screen.getByText("Open Schedule Modal"));
            fireEvent.click(screen.getByText("Save Schedule Settings"));
            fireEvent.click(screen.getByText("Continue"));

            // Step 3: Review payment
            expect(screen.getByText("Form Summary - Payment Type: Bulk Transfer")).toBeInTheDocument();
        });
    });

    describe("useEffect hook for PIN verification and MFA setup", () => {
        it("should not dispatch openVerifyPinModal when pinInitiated is false", () => {
            const { openVerifyPinModal } = require("@/redux/slices/securitySlice");

            renderComponent(
                {},
                {
                    transferMfaSlice: {
                        getTeamMemberDetails: { success: true },
                        teamMember: { mfaStatus: true },
                    },
                    pinInitiated: false,
                }
            );

            expect(openVerifyPinModal).not.toHaveBeenCalled();
        });

        it("should not dispatch openVerifyPinModal when teamMemberRetrieved is false", () => {
            const { openVerifyPinModal } = require("@/redux/slices/securitySlice");

            renderComponent(
                {},
                {
                    transferMfaSlice: {
                        getTeamMemberDetails: { success: false },
                        teamMember: { mfaStatus: true },
                    },
                    pinInitiated: true,
                }
            );

            expect(openVerifyPinModal).not.toHaveBeenCalled();
        });

        it("should not dispatch openVerifyPinModal when teamMember is null", () => {
            const { openVerifyPinModal } = require("@/redux/slices/securitySlice");

            renderComponent(
                {},
                {
                    transferMfaSlice: {
                        getTeamMemberDetails: { success: true },
                        teamMember: null,
                    },
                    pinInitiated: true,
                }
            );

            expect(openVerifyPinModal).not.toHaveBeenCalled();
        });
    });
});
