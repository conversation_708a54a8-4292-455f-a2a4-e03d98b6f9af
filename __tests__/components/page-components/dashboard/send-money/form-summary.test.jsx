import { fireEvent, render, screen } from "@testing-library/react";
import { Formik } from "formik";
import { Provider } from "react-redux";
import configureMockStore from "redux-mock-store";
import FormSummary from "../../../../../src/components/page-components/dashboard/send-money/local/single-payment-form/form-summary";

// Mock helpers
jest.mock("@/functions/stringManipulations", () => ({
    formatNumberToNaira: jest.fn(() => "₦1000.00"),
    getNameInitials: jest.fn(() => "JD"),
    maskNumber: jest.fn(() => "******7890"),
}));

jest.mock(
    "../../../../../src/components/page-components/dashboard/send-money/local/transfer-type-indicator.tsx",
    () => ({
        __esModule: true,
        default: ({ formik }) => <div data-testid="transfer-type-indicator">{formik.values.transferType}</div>,
    })
);

const mockUseTransactionFees = jest.fn();
jest.mock("@/hooks/useTransactionFees", () => ({
    useTransactionFees: (...args) => mockUseTransactionFees(...args),
}));

const mockStore = configureMockStore([]);
const store = mockStore({
    recipient: {
        getLocalBanks: {
            banks: [
                { bankCode: "Access Bank", bankName: "Access Bank Ltd" },
                { bankCode: "GTB", bankName: "Guaranty Trust Bank" },
            ],
        },
    },
});

const baseFormikValues = {
    transferSource: {
        accountName: "Account 1",
        accountNumber: "**********",
    },
    accountNumber: "**********",
    bank: "Access Bank",
    accountName: "John Doe",
    amount: "1000",
    transferType: "INSTANT",
    narration: "Payment for services",
    scheduledDate: "",
    reoccurringFrequency: "",
    reoccurringStartDate: "",
    reoccurringReminderSchedule: "",
    recurringTenureType: "",
    reoccurringEndDate: "",
    reoccurringEndOccurrences: "",
};

const renderComponent = (formikOverrides = {}, options = {}) => {
    const formikMock = {
        values: {
            ...baseFormikValues,
            ...formikOverrides,
        },
        handleChange: jest.fn(),
        setFieldValue: jest.fn(),
    };

    const goToPrevStep = jest.fn();

    render(
        <Provider store={store}>
            <Formik initialValues={formikMock.values} onSubmit={jest.fn()}>
                <FormSummary
                    formik={formikMock}
                    paymentType={options.paymentType || "Single payment"}
                    goToPrevStep={goToPrevStep}
                />
            </Formik>
        </Provider>
    );

    return { goToPrevStep };
};

describe("FormSummary Component - Full Branch Coverage", () => {
    beforeEach(() => {
        mockUseTransactionFees.mockReturnValue({ fees: 500, loading: false });
    });

    test("renders static content correctly", () => {
        renderComponent();

        expect(screen.getByTestId("heading")).toHaveTextContent("One last check before sending!");
        expect(screen.getByText(/Recipient/i)).toBeInTheDocument();
        expect(screen.getByText(/Payment information/i)).toBeInTheDocument();
    });

    test("displays all payment information fields", () => {
        renderComponent();

        expect(screen.getByText(/Payment Type/i)).toBeInTheDocument();
        expect(screen.getByText(/Payment Delivery/i)).toBeInTheDocument();
        expect(screen.getByText(/Amount/i)).toBeInTheDocument();
        expect(screen.getByText(/Fees/i)).toBeInTheDocument();
        expect(screen.getByText(/Send from/i)).toBeInTheDocument();
    });

    test("calls goToPrevStep when 'Change' is clicked", () => {
        const { goToPrevStep } = renderComponent();
        fireEvent.click(screen.getByText("Change"));
        expect(goToPrevStep).toHaveBeenCalled();
    });

    test("calls goToPrevStep when 'Edit' is clicked", () => {
        const { goToPrevStep } = renderComponent();
        fireEvent.click(screen.getByText("Edit"));
        expect(goToPrevStep).toHaveBeenCalled();
    });

    test("renders the transfer type indicator correctly", () => {
        renderComponent();
        expect(screen.getByTestId("transfer-type-indicator")).toHaveTextContent("INSTANT");
    });

    test("renders with different payment type", () => {
        renderComponent({}, { paymentType: "Multiple recipients" });
        expect(screen.getByText("Multiple recipients")).toBeInTheDocument();
    });

    test("renders recurring payment fields", () => {
        renderComponent({
            transferType: "RECURRING",
            reoccurringFrequency: "WEEKLY",
            reoccurringStartDate: "2024-01-01",
            reoccurringEndDate: "2024-01-15",
            reoccurringEndOccurrences: "2",
        });

        expect(screen.getByText(/Recurring amount/i)).toBeInTheDocument();
        expect(screen.getByText(/Total amount/i)).toBeInTheDocument();
        expect(screen.getByText(/per payment cycle/i)).toBeInTheDocument();
    });

    test("renders loading indicator when loading is true", () => {
        mockUseTransactionFees.mockReturnValue({ fees: 0, loading: true });
        renderComponent();
        expect(screen.getByTestId("loading-indicator")).toBeInTheDocument();
    });

    test("does not render recipient card if accountNumber is missing", () => {
        renderComponent({ accountNumber: "" });
        expect(screen.queryByText("Change")).not.toBeInTheDocument();
    });

    test("displays formatted bank name from Redux store", () => {
        renderComponent();
        expect(screen.getByText(/Access Bank Ltd/i)).toBeInTheDocument();
    });
});
