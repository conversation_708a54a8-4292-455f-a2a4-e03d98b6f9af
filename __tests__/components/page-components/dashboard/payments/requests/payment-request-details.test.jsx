"use client";

import { render, screen, fireEvent } from "@testing-library/react";
import { Provider } from "react-redux";
import configureS<PERSON> from "redux-mock-store";
import thunk from "redux-thunk";
import {
    PaymentRequestDetails,
    PaymentRequestBadge,
} from "@/components/page-components/dashboard/payments/requests/payment-request-details";
import { paymentRequestActions } from "@/redux/slices/payments/requestSlice";

// Mock Redux hooks
const mockDispatch = jest.fn();
const mockSelector = jest.fn();

jest.mock("@/redux/hooks", () => ({
    useAppDispatch: () => mockDispatch,
    useAppSelector: () => mockSelector(),
}));

// Mock components with proper structure
jest.mock("@/components/common/drawer", () => ({
    __esModule: true,
    default: ({ children, isOpen, className }) =>
        isOpen ? (
            <div data-testid="side-drawer" className={className}>
                {children}
            </div>
        ) : null,
}));

jest.mock("lucide-react", () => ({
    X: () => <div data-testid="x-icon">X</div>,
}));

jest.mock("@/components/common/Avatar", () => ({
    Avatar: ({ children, className }) => (
        <div data-testid="avatar" className={className}>
            {children}
        </div>
    ),
    AvatarImage: ({ src, alt }) => <img data-testid="avatar-image" src={src || "/placeholder.svg"} alt={alt} />,
    AvatarFallback: ({ children, className }) => (
        <div data-testid="avatar-fallback" className={className}>
            {children}
        </div>
    ),
}));

jest.mock("@/components/common/tab-switch", () => ({
    __esModule: true,
    default: ({ tabs, panels, panelClassName, className, tabSpacing }) => (
        <div data-testid="tab-switch" className={className}>
            {tabs.map((tab, index) => (
                <div key={tab} data-testid={`tab-${index}`}>
                    {tab}
                </div>
            ))}
            <div className={panelClassName}>
                {panels[0]} {/* Render first panel by default */}
            </div>
        </div>
    ),
}));

jest.mock("@/components/common/buttonv3", () => ({
    Button: ({ children, onClick, variant, type }) => (
        <button data-testid={`button-${variant || "primary"}`} onClick={onClick} type={type}>
            {children}
        </button>
    ),
}));

jest.mock("@/functions/stringManipulations", () => ({
    formatNumberToNaira: jest.fn((amount) => `₦${amount?.toLocaleString() || 0}`),
    getNameInitials: jest.fn((name) => name?.slice(0, 2).toUpperCase() || ""),
}));

jest.mock("@/functions/date", () => ({
    formatDate: jest.fn((date) => new Date(date).toLocaleDateString()),
}));

// Mock TabDetails component
jest.mock("@/components/page-components/dashboard/payments/requests/tab-details.tsx", () => ({
    __esModule: true,
    default: ({ request }) => <div data-testid="tab-details">{request?.narration || "No narration"}</div>,
}));

const middlewares = [thunk];
const mockStore = configureStore(middlewares);

describe("PaymentRequestDetails Component", () => {
    const mockPaymentRequest = {
        id: 123,
        counterpartyTitle: "Vendor XYZ",
        amount: 500000,
        narration: "Office supplies payment",
        createdDate: "2024-12-10T00:04:00Z",
        numberOfApprovalsGotten: 1,
        requiredNumberOfApprovals: 3,
        createdBy: "John Doe",
        counterpartyDetails: "Business Account",
        paymentRequestType: "TRANSFER", // Added missing property for component logic
    };

    const mockProps = {
        requestId: 123,
        isOpen: true,
        handleCloseDetails: jest.fn(),
        handleApproveRequest: jest.fn(),
        handleDeclineRequest: jest.fn(),
    };

    // Fix the Redux state structure to match what the component expects
    const defaultReduxState = {
        paymentRequest: {
            request: mockPaymentRequest, // Component expects state.paymentRequest.request
            get: {
                loading: false,
                error: null,
            },
        },
    };

    beforeEach(() => {
        jest.clearAllMocks();

        // Mock the selector to return the specific state structure the component expects
        mockSelector.mockReturnValue({
            request: mockPaymentRequest, // Component expects state.paymentRequest.request
            get: {
                loading: false,
                error: null,
            },
        });

        mockDispatch.mockImplementation((action) => {
            if (typeof action === "function") {
                return action(mockDispatch, () => defaultReduxState);
            }
            return action;
        });
    });

    it("renders correctly when open", () => {
        const store = mockStore(defaultReduxState);

        render(
            <Provider store={store}>
                <PaymentRequestDetails {...mockProps} />
            </Provider>
        );

        expect(screen.getByTestId("side-drawer")).toBeInTheDocument();
        expect(screen.getByText("Vendor XYZ")).toBeInTheDocument();
        expect(screen.getAllByText("Office supplies payment")[0]).toBeInTheDocument();
        expect(screen.getByText("pending")).toBeInTheDocument();
    });

    it("doesn't render when closed", () => {
        const store = mockStore(defaultReduxState);

        render(
            <Provider store={store}>
                <PaymentRequestDetails {...mockProps} isOpen={false} />
            </Provider>
        );

        expect(screen.queryByTestId("side-drawer")).not.toBeInTheDocument();
    });

    it("calls handleCloseDetails when close button is clicked", () => {
        const store = mockStore(defaultReduxState);

        render(
            <Provider store={store}>
                <PaymentRequestDetails {...mockProps} />
            </Provider>
        );

        const closeButton = screen.getByTestId("close-btn");
        fireEvent.click(closeButton);
        expect(mockProps.handleCloseDetails).toHaveBeenCalledTimes(1);
    });

    it("renders the tab switch component with correct tabs", () => {
        const store = mockStore(defaultReduxState);

        render(
            <Provider store={store}>
                <PaymentRequestDetails {...mockProps} />
            </Provider>
        );

        expect(screen.getByTestId("tab-switch")).toBeInTheDocument();
        expect(screen.getByTestId("tab-0")).toHaveTextContent("Details");
        expect(screen.getByTestId("tab-1")).toHaveTextContent("People involved");
    });

    it("renders the details tab with correct payment request information", () => {
        const store = mockStore(defaultReduxState);

        render(
            <Provider store={store}>
                <PaymentRequestDetails {...mockProps} />
            </Provider>
        );

        expect(screen.getByTestId("tab-details")).toHaveTextContent("Office supplies payment");
    });

    it("renders avatar with correct initials", () => {
        const store = mockStore(defaultReduxState);

        render(
            <Provider store={store}>
                <PaymentRequestDetails {...mockProps} />
            </Provider>
        );

        expect(screen.getByTestId("avatar-fallback")).toBeInTheDocument();
        // eslint-disable-next-line @typescript-eslint/no-require-imports
        expect(require("@/functions/stringManipulations").getNameInitials).toHaveBeenCalledWith("Vendor XYZ");
    });

    it("calls handleApproveRequest when Approve button is clicked", () => {
        const store = mockStore(defaultReduxState);

        render(
            <Provider store={store}>
                <PaymentRequestDetails {...mockProps} />
            </Provider>
        );

        fireEvent.click(screen.getByText("Approve"));
        expect(mockProps.handleApproveRequest).toHaveBeenCalledWith(123);
    });

    it("calls handleDeclineRequest when Decline button is clicked", () => {
        const store = mockStore(defaultReduxState);

        render(
            <Provider store={store}>
                <PaymentRequestDetails {...mockProps} />
            </Provider>
        );

        fireEvent.click(screen.getByText("Decline"));
        expect(mockProps.handleDeclineRequest).toHaveBeenCalledWith(123);
    });

    it("dispatches getPaymentRequest action when requestId changes", () => {
        const store = mockStore(defaultReduxState);

        render(
            <Provider store={store}>
                <PaymentRequestDetails {...mockProps} />
            </Provider>
        );

        expect(mockDispatch).toHaveBeenCalledWith(paymentRequestActions.getPaymentRequest(123));
    });

    it("handles loading state correctly", () => {
        const loadingState = {
            request: null,
            get: { loading: true, error: null },
        };

        mockSelector.mockReturnValue(loadingState);

        const store = mockStore({ paymentRequest: loadingState });

        render(
            <Provider store={store}>
                <PaymentRequestDetails {...mockProps} />
            </Provider>
        );

        // Should render drawer but not content when loading
        expect(screen.getByTestId("side-drawer")).toBeInTheDocument();
        expect(screen.queryByText("Vendor XYZ")).not.toBeInTheDocument();
    });

    it("handles error state correctly", () => {
        const errorState = {
            request: null,
            get: { loading: false, error: "Failed to fetch" },
        };

        mockSelector.mockReturnValue(errorState);

        const store = mockStore({ paymentRequest: errorState });

        render(
            <Provider store={store}>
                <PaymentRequestDetails {...mockProps} />
            </Provider>
        );

        // Should render drawer but not content when error
        expect(screen.getByTestId("side-drawer")).toBeInTheDocument();
        expect(screen.queryByText("Vendor XYZ")).not.toBeInTheDocument();
    });

    it("doesn't dispatch getPaymentRequest when requestId is falsy", () => {
        const store = mockStore(defaultReduxState);

        render(
            <Provider store={store}>
                <PaymentRequestDetails {...mockProps} requestId={0} />
            </Provider>
        );

        expect(mockDispatch).not.toHaveBeenCalledWith(paymentRequestActions.getPaymentRequest(expect.any(Number)));
    });

    it("renders formatted amount correctly", () => {
        const store = mockStore(defaultReduxState);

        render(
            <Provider store={store}>
                <PaymentRequestDetails {...mockProps} />
            </Provider>
        );

        expect(screen.getByText("₦500,000")).toBeInTheDocument();
        // eslint-disable-next-line @typescript-eslint/no-require-imports
        expect(require("@/functions/stringManipulations").formatNumberToNaira).toHaveBeenCalledWith(500000);
    });

    it("renders formatted date correctly", () => {
        const store = mockStore(defaultReduxState);

        render(
            <Provider store={store}>
                <PaymentRequestDetails {...mockProps} />
            </Provider>
        );

        // eslint-disable-next-line @typescript-eslint/no-require-imports
        expect(require("@/functions/date").formatDate).toHaveBeenCalledWith("2024-12-10T00:04:00Z");
    });
});

describe("PaymentRequestBadge Component", () => {
    it("renders pending payment request type correctly", () => {
        render(<PaymentRequestBadge paymentRequestType="pending" />);

        const badge = screen.getByText("pending");
        expect(badge).toBeInTheDocument();
        expect(badge).toHaveStyle({
            backgroundColor: "#FFFAEB",
            color: "#B54708",
        });
    });

    it("renders canceled payment request type correctly", () => {
        render(<PaymentRequestBadge paymentRequestType="canceled" />);

        const badge = screen.getByText("canceled");
        expect(badge).toBeInTheDocument();
        expect(badge).toHaveStyle({
            backgroundColor: "#FEF3F2",
            color: "#B42318",
        });
    });

    it("renders approved payment request type correctly", () => {
        render(<PaymentRequestBadge paymentRequestType="approved" />);

        const badge = screen.getByText("approved");
        expect(badge).toBeInTheDocument();
        expect(badge).toHaveStyle({
            backgroundColor: "#ECFDF3",
            color: "#027A48",
        });
    });

    it("renders unknown payment request type with default styles", () => {
        // @ts-ignore - Testing with an invalid type
        render(<PaymentRequestBadge paymentRequestType="unknown" />);

        const badge = screen.getByText("unknown");
        expect(badge).toBeInTheDocument();
        expect(badge).toHaveStyle({
            backgroundColor: "",
            color: "",
        });
    });

    it("applies correct CSS classes", () => {
        render(<PaymentRequestBadge paymentRequestType="pending" />);

        const badge = screen.getByText("pending");
        expect(badge).toHaveClass(
            "rounded-full",
            "py-[6px]",
            "px-[10px]",
            "max-w-max",
            "capitalize",
            "text-[14px]",
            "leading-[18px]",
            "font-medium"
        );
    });
});
