import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import TabDetails from "@/components/page-components/dashboard/payments/requests/tab-details";
import { convertCamelCaseToWords, copyToClipboard, formatNumberToNaira } from "@/functions/stringManipulations";

// Mock Next.js router
const mockPush = jest.fn();
jest.mock("next/navigation", () => ({
    useRouter: () => ({
        push: mockPush,
        replace: jest.fn(),
        back: jest.fn(),
        forward: jest.fn(),
        refresh: jest.fn(),
        prefetch: jest.fn(),
    }),
}));

// Mock the imports
jest.mock("@/functions/stringManipulations", () => ({
    convertCamelCaseToWords: jest.fn(),
    formatNumberToNaira: jest.fn(),
    copyToClipboard: jest.fn(),
}));

jest.mock("lucide-react", () => ({
    ChevronRight: jest.fn().mockReturnValue(<div data-testid="chevron-right-icon" />),
}));

jest.mock("@/components/icons/auth", () => ({
    CopyIcon: jest.fn().mockReturnValue(<div data-testid="copy-icon" />),
}));

jest.mock("@/components/common/buttonv3", () => ({
    Button: ({ children, onClick, rightIcon, variant, size }) => (
        <button onClick={onClick} data-testid="button" data-variant={variant} data-size={size}>
            {children}
            {rightIcon}
        </button>
    ),
}));

const DUMMY_PAYMENT_REQUEST = {
    createdDate: "2024-12-10T12:04:00",
    id: "12345",
    corporateId: "67890",
    requestId: "REQ12345",
    numberOfApprovalsGotten: 2,
    requiredNumberOfApprovals: 3,
    amount: 7300000,
    beneficiaryName: "Dangote Farm",
    bankName: "First Bank",
    accountNumber: "**********",
    reference: "TRX-240123-4567",
    narration: "Fertilizer suppliers",
    status: "pending",
};

describe("TabDetails Component", () => {
    beforeEach(() => {
        // Reset mocks before each test
        jest.clearAllMocks();

        // Set up mock implementations
        convertCamelCaseToWords.mockImplementation((key) => {
            const words = {
                amount: "Amount",
                beneficiaryName: "Beneficiary Name",
                bankName: "Bank Name",
                accountNumber: "Account Number",
                reference: "Reference",
                narration: "Narration",
                status: "Status",
            };
            return words[key] || key;
        });

        formatNumberToNaira.mockImplementation((amount) => `₦${amount.toLocaleString()}.00`);
    });

    it("should render all payment request details", () => {
        // Render the TabDetails component with the dummy payment request
        render(<TabDetails request={DUMMY_PAYMENT_REQUEST} />);

        // Check if convertCamelCaseToWords was called for each key
        expect(convertCamelCaseToWords).toHaveBeenCalledWith("amount");
        expect(convertCamelCaseToWords).toHaveBeenCalledWith("beneficiaryName");
        expect(convertCamelCaseToWords).toHaveBeenCalledWith("bankName");
        expect(convertCamelCaseToWords).toHaveBeenCalledWith("accountNumber");
        expect(convertCamelCaseToWords).toHaveBeenCalledWith("reference");
        expect(convertCamelCaseToWords).toHaveBeenCalledWith("narration");
        expect(convertCamelCaseToWords).toHaveBeenCalledWith("status");

        // Check that formatNumberToNaira was called for the amount
        expect(formatNumberToNaira).toHaveBeenCalledWith(7300000);

        // Verify details are displayed
        expect(screen.getByText("Beneficiary Name")).toBeInTheDocument();
        expect(screen.getByText("Dangote Farm")).toBeInTheDocument();

        expect(screen.getByText("Bank Name")).toBeInTheDocument();
        expect(screen.getByText("First Bank")).toBeInTheDocument();

        expect(screen.getByText("Account Number")).toBeInTheDocument();
        expect(screen.getByText("**********")).toBeInTheDocument();

        expect(screen.getByText("Reference")).toBeInTheDocument();
        expect(screen.getByText("TRX-240123-4567")).toBeInTheDocument();

        expect(screen.getByText("Narration")).toBeInTheDocument();
        expect(screen.getByText("Fertilizer suppliers")).toBeInTheDocument();

        expect(screen.getByText("Status")).toBeInTheDocument();
        expect(screen.getByText("pending")).toBeInTheDocument();
    });

    it("should render approval status information", () => {
        render(<TabDetails request={DUMMY_PAYMENT_REQUEST} />);

        expect(screen.getByText(/approvals granted/i)).toBeInTheDocument();
        expect(screen.getByText("This payment will be sent once all approvals are completed.")).toBeInTheDocument();
    });

    it("should render the copy button for the reference field", () => {
        render(<TabDetails request={DUMMY_PAYMENT_REQUEST} />);

        // Check that the copy button is rendered for the reference
        const copyButton = screen.getByTestId("copy-authenticator");
        expect(copyButton).toBeInTheDocument();

        // Simulate a click on the copy button
        fireEvent.click(copyButton);

        // Check that the copyToClipboard function was called with the correct argument
        expect(copyToClipboard).toHaveBeenCalledWith("TRX-240123-4567");
    });

    it("should not render copy button for non-reference fields", () => {
        render(<TabDetails request={DUMMY_PAYMENT_REQUEST} />);

        // Get all instances of the CopyIcon component
        const copyIcons = screen.getAllByTestId("copy-icon");

        // There should be exactly one copy icon (for the reference field)
        expect(copyIcons.length).toBe(1);
    });

    it("should render a 'See approvers' button with the correct props", () => {
        render(<TabDetails request={DUMMY_PAYMENT_REQUEST} />);

        const button = screen.getByTestId("button");
        expect(button).toBeInTheDocument();
        expect(button).toHaveTextContent("See approvers");
        expect(button).toHaveAttribute("data-variant", "text-primary");
        expect(button).toHaveAttribute("data-size", "sm");

        const chevronIcon = screen.getByTestId("chevron-right-icon");
        expect(chevronIcon).toBeInTheDocument();
    });

    it("should update URL parameters when 'See approvers' button is clicked", () => {
        // Mock window.location
        const mockLocation = {
            pathname: "/payments/requests/123",
            search: "?tab=0",
        };
        Object.defineProperty(window, "location", {
            value: mockLocation,
            writable: true,
        });

        // Clear the mock before the test
        mockPush.mockClear();

        render(<TabDetails request={DUMMY_PAYMENT_REQUEST} />);

        const button = screen.getByTestId("button");
        fireEvent.click(button);

        // Verify that the button click updates the URL to switch to tab=1
        expect(mockPush).toHaveBeenCalledWith("/payments/requests/123?tab=1");
    });
});
