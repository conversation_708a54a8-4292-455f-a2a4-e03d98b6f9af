"use client";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import {
    PaymentRequestColumns,
    RenderMenuItem,
} from "@/components/page-components/dashboard/payments/requests/payment-request-column-data";

// Mock dependencies
jest.mock("@/components/common/checkbox", () => ({
    __esModule: true,
    default: ({ checked, onCheckedChange, "aria-label": ariaLabel, indeterminate, size }) => (
        <input
            type="checkbox"
            checked={checked}
            onChange={(e) => onCheckedChange(e.target.checked)}
            aria-label={ariaLabel}
            data-indeterminate={indeterminate}
            data-size={size}
            data-testid="checkbox"
        />
    ),
}));

jest.mock("@/components/common/table/table-more-action", () => ({
    __esModule: true,
    default: jest.fn(({ data, menuItems }) => (
        <div data-testid="table-more-action">
            <button data-testid="more-button">More</button>
            <ul data-testid="menu-items">
                {menuItems.map((item, index) => (
                    <li key={index}>
                        <button data-testid={`menu-item-${index}`} onClick={() => item.onClick(data)}>
                            {item.label}
                        </button>
                    </li>
                ))}
            </ul>
        </div>
    )),
}));

jest.mock("@/functions/stringManipulations", () => ({
    getNameInitials: jest.fn(
        (name) =>
            name
                ?.split(" ")
                .map((n) => n[0])
                .join("") || ""
    ),
    formatNumberToNaira: jest.fn((number) => {
        return new Intl.NumberFormat("en-NG", {
            style: "currency",
            currency: "NGN",
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
        }).format(Number(number));
    }),
}));

jest.mock("@/components/common/Avatar", () => ({
    Avatar: ({ children, className }) => (
        <div className={className} data-testid="avatar">
            {children}
        </div>
    ),
    AvatarImage: ({ src, alt }) => <img src={src || "/placeholder.svg"} alt={alt} data-testid="avatar-image" />,
    AvatarFallback: ({ children, className }) => (
        <span className={className} data-testid="avatar-fallback">
            {children}
        </span>
    ),
}));

jest.mock("lucide-react", () => ({
    Eye: () => <span data-testid="eye-icon">Eye Icon</span>,
    Check: () => <span data-testid="check-icon">Check Icon</span>,
}));

jest.mock("@/components/icons/paymnet-request", () => ({
    RemoveCircle: () => <span data-testid="remove-circle-icon">Remove Circle Icon</span>,
}));

jest.mock("@/components/icons/table", () => ({
    ArrowDownIcon: () => <span data-testid="arrow-down-icon">↓</span>,
}));

jest.mock("@/functions/date", () => ({
    formatDate: jest.fn((date) => new Date(date).toLocaleDateString()),
}));

describe("PaymentRequestColumns", () => {
    const mockRow = {
        original: {
            id: 123,
            createdDate: "2023-05-01",
            createdBy: "John Doe",
            numberOfApprovalsGotten: 1,
            requiredNumberOfApprovals: 3,
            counterpartyTitle: "ACME Corp",
            counterpartyDetails: "Business Account",
            narration: "Monthly payment",
            amount: "100000.00",
        },
        getIsSelected: jest.fn(() => false),
        toggleSelected: jest.fn(),
        getValue: jest.fn((key) => mockRow.original[key]),
    };

    const mockTable = {
        getIsAllPageRowsSelected: jest.fn(() => false),
        getIsSomePageRowsSelected: jest.fn(() => false),
        toggleAllPageRowsSelected: jest.fn(),
        options: {
            meta: {
                setIsOpen: jest.fn(),
                setRequestId: jest.fn(),
                setRequestApprovalModal: jest.fn(),
                setDeclineModal: jest.fn(),
            },
        },
    };

    const mockColumn = {
        toggleSorting: jest.fn(),
        getIsSorted: jest.fn(() => "asc"),
    };

    beforeEach(() => {
        jest.clearAllMocks();
    });

    test("select column renders and functions correctly", () => {
        const selectColumn = PaymentRequestColumns.find((col) => col.id === "select");
        expect(selectColumn).toBeDefined();

        // Test header
        if (selectColumn?.header) {
            const HeaderComponent = selectColumn.header;
            render(<HeaderComponent table={mockTable} />);
            const checkbox = screen.getByLabelText("Select all");
            expect(checkbox).toBeInTheDocument();
            fireEvent.click(checkbox);
            // When checkbox is clicked, it changes from unchecked (false) to checked (true)
            // So onCheckedChange(true) is called, which passes true to toggleAllPageRowsSelected
            expect(mockTable.toggleAllPageRowsSelected).toHaveBeenCalledWith(true);
        }

        // Test cell
        if (selectColumn?.cell) {
            const CellComponent = selectColumn.cell;
            render(<CellComponent row={mockRow} />);
            const checkbox = screen.getByLabelText("Select row");
            expect(checkbox).toBeInTheDocument();
            fireEvent.click(checkbox);
            // When checkbox is clicked, it changes from unselected (false) to selected (true)
            // So onCheckedChange(true) is called, which passes true to toggleSelected
            expect(mockRow.toggleSelected).toHaveBeenCalledWith(true);
        }

        expect(selectColumn?.enableSorting).toBe(false);
        expect(selectColumn?.enableHiding).toBe(false);
    });

    test("date column renders and sorts correctly", () => {
        const dateColumn = PaymentRequestColumns.find((col) => col.accessorKey === "createdDate");
        expect(dateColumn).toBeDefined();

        // Test header
        if (dateColumn?.header) {
            const HeaderComponent = dateColumn.header;
            render(<HeaderComponent column={mockColumn} />);
            const button = screen.getByRole("button");
            expect(button).toHaveTextContent("Date");
            expect(screen.getByTestId("arrow-down-icon")).toBeInTheDocument();
            fireEvent.click(button);
            // The logic is: toggleSorting(column.getIsSorted() === "asc")
            // Since getIsSorted() returns "asc", the expression evaluates to true
            expect(mockColumn.toggleSorting).toHaveBeenCalledWith(true);
        }

        // Test cell
        if (dateColumn?.cell) {
            const CellComponent = dateColumn.cell;
            render(<CellComponent row={mockRow} />);
            const date = new Date(mockRow.original.createdDate).toLocaleDateString();
            expect(screen.getByText(date)).toBeInTheDocument();
        }
    });

    test("initiator column displays user information correctly", () => {
        const initiatorColumn = PaymentRequestColumns.find((col) => col.accessorKey === "initiator");
        expect(initiatorColumn?.header).toBe("Initiator");

        if (initiatorColumn?.cell) {
            const CellComponent = initiatorColumn.cell;
            render(<CellComponent row={mockRow} />);

            expect(screen.getByTestId("avatar")).toBeInTheDocument();
            expect(screen.getByTestId("avatar-fallback")).toHaveTextContent("JD");
            expect(screen.getByText("John Doe")).toBeInTheDocument();
        }
    });

    test("approvals column shows correct approval ratio", () => {
        const approvalsColumn = PaymentRequestColumns.find((col) => col.accessorKey === "approvals");
        expect(approvalsColumn?.header).toBe("Approvals");

        if (approvalsColumn?.cell) {
            const CellComponent = approvalsColumn.cell;
            render(<CellComponent row={mockRow} />);
            expect(screen.getByText("1/3")).toBeInTheDocument();
        }
    });

    test("counterparty column displays counterparty information", () => {
        const counterpartyColumn = PaymentRequestColumns.find((col) => col.accessorKey === "counterparty");
        expect(counterpartyColumn?.header).toBe("Counterparty");

        if (counterpartyColumn?.cell) {
            const CellComponent = counterpartyColumn.cell;
            render(<CellComponent row={mockRow} />);

            expect(screen.getByTestId("avatar")).toBeInTheDocument();
            expect(screen.getByTestId("avatar-fallback")).toHaveTextContent("AC");
            expect(screen.getByText("ACME Corp")).toBeInTheDocument();
            expect(screen.getByText("Business Account")).toBeInTheDocument();
        }
    });

    test("amount column formats currency correctly", () => {
        const amountColumn = PaymentRequestColumns.find((col) => col.accessorKey === "amount");
        expect(amountColumn).toBeDefined();

        // Test header
        if (amountColumn?.header) {
            const HeaderComponent = amountColumn.header;
            render(<HeaderComponent column={mockColumn} />);
            const button = screen.getByRole("button");
            expect(button).toHaveTextContent("Amount");
            expect(button).toHaveClass("text-right");
        }

        // Test cell
        if (amountColumn?.cell) {
            const CellComponent = amountColumn.cell;
            render(<CellComponent row={mockRow} />);
            const amount = Number.parseFloat(mockRow.original.amount);
            const formatted = new Intl.NumberFormat("en-NG", {
                style: "currency",
                currency: "NGN",
                minimumFractionDigits: 2,
                maximumFractionDigits: 2,
            }).format(amount);
            expect(screen.getByText(formatted)).toBeInTheDocument();
        }
    });

    test("actions column renders menu items correctly", () => {
        const actionsColumn = PaymentRequestColumns.find((col) => col.id === "actions");
        expect(actionsColumn).toBeDefined();

        if (actionsColumn?.cell) {
            const CellComponent = actionsColumn.cell;
            render(<CellComponent row={mockRow} table={mockTable} />);

            expect(screen.getByTestId("table-more-action")).toBeInTheDocument();
            expect(screen.getByTestId("eye-icon")).toBeInTheDocument();
            expect(screen.getByTestId("check-icon")).toBeInTheDocument();
            expect(screen.getByTestId("remove-circle-icon")).toBeInTheDocument();

            // Test View action
            fireEvent.click(screen.getByTestId("menu-item-0"));
            expect(mockTable.options.meta.setRequestId).toHaveBeenCalledWith(123);
            expect(mockTable.options.meta.setIsOpen).toHaveBeenCalledWith(true);

            // Test Approve action
            fireEvent.click(screen.getByTestId("menu-item-1"));
            expect(mockTable.options.meta.setRequestId).toHaveBeenCalledWith(123);
            expect(mockTable.options.meta.setRequestApprovalModal).toHaveBeenCalledWith(true);

            // Test Decline action
            fireEvent.click(screen.getByTestId("menu-item-2"));
            expect(mockTable.options.meta.setRequestId).toHaveBeenCalledWith(123);
            expect(mockTable.options.meta.setDeclineModal).toHaveBeenCalledWith(true);
        }
    });

    test("actions column handles missing meta functions gracefully", () => {
        const actionsColumn = PaymentRequestColumns.find((col) => col.id === "actions");
        const mockTableWithoutMeta = {
            ...mockTable,
            options: { meta: {} },
        };

        if (actionsColumn?.cell) {
            const CellComponent = actionsColumn.cell;
            render(<CellComponent row={mockRow} table={mockTableWithoutMeta} />);

            // Should not throw errors when meta functions are missing
            fireEvent.click(screen.getByTestId("menu-item-0"));
            fireEvent.click(screen.getByTestId("menu-item-1"));
            fireEvent.click(screen.getByTestId("menu-item-2"));
        }
    });

    test("narration column exists", () => {
        const narrationColumn = PaymentRequestColumns.find((col) => col.accessorKey === "narration");
        expect(narrationColumn).toBeDefined();
        expect(narrationColumn?.header).toBe("Narration");
    });

    test("select column with different initial states", () => {
        const selectColumn = PaymentRequestColumns.find((col) => col.id === "select");

        // Test when all rows are already selected
        const mockTableAllSelected = {
            ...mockTable,
            getIsAllPageRowsSelected: jest.fn(() => true),
        };

        if (selectColumn?.header) {
            const HeaderComponent = selectColumn.header;
            render(<HeaderComponent table={mockTableAllSelected} />);
            const checkbox = screen.getByLabelText("Select all");
            fireEvent.click(checkbox);
            // When checkbox is clicked, it changes from checked (true) to unchecked (false)
            // So onCheckedChange(false) is called, which passes false to toggleAllPageRowsSelected
            expect(mockTableAllSelected.toggleAllPageRowsSelected).toHaveBeenCalledWith(false);
        }
    });

    test("date column with different sort states", () => {
        const dateColumn = PaymentRequestColumns.find((col) => col.accessorKey === "createdDate");

        // Test when column is sorted in descending order
        const mockColumnDesc = {
            ...mockColumn,
            getIsSorted: jest.fn(() => "desc"),
        };

        if (dateColumn?.header) {
            const HeaderComponent = dateColumn.header;
            render(<HeaderComponent column={mockColumnDesc} />);
            const button = screen.getByRole("button");
            fireEvent.click(button);
            // When getIsSorted() returns "desc", the expression "desc" === "asc" evaluates to false
            expect(mockColumnDesc.toggleSorting).toHaveBeenCalledWith(false);
        }
    });
});

describe("RenderMenuItem", () => {
    it("renders menu item with icon and title", () => {
        render(<RenderMenuItem icon={<span data-testid="test-icon">Icon</span>} title="Test Title" />);

        expect(screen.getByTestId("menu-item")).toBeInTheDocument();
        expect(screen.getByTestId("test-icon")).toBeInTheDocument();
        expect(screen.getByText("Test Title")).toBeInTheDocument();
        expect(screen.getByTestId("menu-item")).toHaveClass("flex items-center gap-2");
    });

    it("applies custom className", () => {
        render(<RenderMenuItem icon={<span>Icon</span>} title="Test Title" className="custom-class" />);

        expect(screen.getByTestId("menu-item")).toHaveClass("flex items-center gap-2 custom-class");
    });

    it("renders without custom className", () => {
        render(<RenderMenuItem icon={<span>Icon</span>} title="Test Title" />);

        expect(screen.getByTestId("menu-item")).toHaveClass("flex items-center gap-2");
        expect(screen.getByTestId("menu-item")).not.toHaveClass("custom-class");
    });
});
