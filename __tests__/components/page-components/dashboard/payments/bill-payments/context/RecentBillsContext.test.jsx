import React from "react";
import { render, screen, waitFor, act } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import {
    RecentBillsProvider,
    useRecentBills,
} from "@/components/page-components/dashboard/bill-payments/context/RecentBillsContext";
import { billAxios } from "@/api/axios";

// Mock the billAxios module
jest.mock("@/api/axios", () => ({
    billAxios: {
        get: jest.fn(),
    },
}));

// Test component that uses the context to verify all functionality
const TestComponent = () => {
    const { bills, loading, error, refetchRecentBills } = useRecentBills();

    if (loading) return <div data-testid="loading">Loading...</div>;
    if (error) return <div data-testid="error">{error}</div>;

    return (
        <div>
            <div data-testid="bill-count">{bills.length}</div>
            <button data-testid="refetch-button" onClick={() => refetchRecentBills()}>
                Refetch
            </button>
            <ul>
                {bills.map((bill) => (
                    <li key={bill.id} data-testid="bill-item">
                        <span data-testid={`provider-${bill.id}`}>{bill.provider}</span>
                        <span data-testid={`reference-${bill.id}`}>{bill.reference}</span>
                        <span data-testid={`amount-${bill.id}`}>{bill.amount}</span>
                        <span data-testid={`status-${bill.id}`}>{bill.status}</span>
                        <span data-testid={`icon-type-${bill.id}`}>{bill.iconType}</span>
                        <span data-testid={`is-bulk-${bill.id}`}>{bill.isBulkPayment.toString()}</span>
                        <span data-testid={`date-${bill.id}`}>{bill.date}</span>
                        <span data-testid={`narration-${bill.id}`}>{bill.narration}</span>
                        <span data-testid={`unique-billers-${bill.id}`}>{bill.uniqueBillerCount || 0}</span>
                        <span data-testid={`recipient-count-${bill.id}`}>{bill.recipientCount || 0}</span>
                    </li>
                ))}
            </ul>
        </div>
    );
};

// Component to test hook error when used outside provider
const ComponentWithoutProvider = () => {
    const { bills } = useRecentBills();
    return <div>{bills.length}</div>;
};

describe("RecentBillsContext", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        // Reset NODE_ENV for development mode testing
        delete process.env.NODE_ENV;
    });

    describe("useRecentBills hook", () => {
        it("should throw error when used outside RecentBillsProvider", () => {
            // Suppress console.error for this test to avoid noise
            const originalError = console.error;
            console.error = jest.fn();

            expect(() => {
                render(<ComponentWithoutProvider />);
            }).toThrow("useRecentBills must be used within a RecentBillsProvider");

            console.error = originalError;
        });
    });

    describe("Data fetching and loading states", () => {
        it("should fetch and display recent bills with loading state", async () => {
            // Mock API response with grouped object structure (single payments)
            const mockContent = {};
            for (let i = 0; i < 5; i++) {
                mockContent[`alphanumeric-key-${i}`] = [
                    {
                        id: i,
                        biller: `Provider ${i}`,
                        billType: "Test Category",
                        requestReference: `REF-${i}`,
                        amount: "1000.50",
                        createdAt: new Date(2023, 0, i + 1).toISOString(),
                        status: "SUCCESSFUL",
                        accountNumber: `*********${i}`,
                        narration: `Payment narration ${i}`,
                        paymentCode: `PAY${i}`,
                        customerId: `CUST${i}`,
                        customerMobile: `*********${i}`,
                        bulkPaymentAdviceDetails: null,
                    },
                ];
            }

            billAxios.get.mockResolvedValueOnce({
                data: {
                    content: mockContent,
                },
            });

            render(
                <RecentBillsProvider>
                    <TestComponent />
                </RecentBillsProvider>
            );

            // First the loading state should be shown
            expect(screen.getByTestId("loading")).toBeInTheDocument();

            // Then the bills should be shown
            await waitFor(() => {
                expect(screen.getByTestId("bill-count")).toHaveTextContent("5");
            });

            // Verify API call was made with correct parameters
            expect(billAxios.get).toHaveBeenCalledWith("/api/v1/vas/payments", {
                params: { pageNo: 1, pageSize: 15 },
            });

            // We should have the correct number of bill items
            expect(screen.getAllByTestId("bill-item")).toHaveLength(5);

            // Verify first bill data transformation
            expect(screen.getByTestId("provider-0")).toHaveTextContent("Provider 0");
            expect(screen.getByTestId("reference-0")).toHaveTextContent("REF-0");
            expect(screen.getByTestId("amount-0")).toHaveTextContent("₦1,000.50");
            expect(screen.getByTestId("status-0")).toHaveTextContent("Successful");
            expect(screen.getByTestId("icon-type-0")).toHaveTextContent("receipt");
            expect(screen.getByTestId("is-bulk-0")).toHaveTextContent("false");
            expect(screen.getByTestId("narration-0")).toHaveTextContent("Payment narration 0");
        });

        it("should limit bills to 15 items when API returns more", async () => {
            // Mock API response with grouped object containing 20 single payments (more than the limit)
            const mockContent = {};
            for (let i = 0; i < 20; i++) {
                mockContent[`single-payment-key-${i}`] = [
                    {
                        id: i,
                        biller: `Provider ${i}`,
                        billType: "Test Category",
                        requestReference: `REF-${i}`,
                        amount: "1000",
                        createdAt: new Date(2023, 0, i + 1).toISOString(),
                        status: "SUCCESSFUL",
                        bulkPaymentAdviceDetails: null,
                    },
                ];
            }

            billAxios.get.mockResolvedValueOnce({
                data: {
                    content: mockContent,
                },
            });

            render(
                <RecentBillsProvider>
                    <TestComponent />
                </RecentBillsProvider>
            );

            // Wait for the bills to be loaded
            await waitFor(() => {
                expect(screen.queryByTestId("loading")).not.toBeInTheDocument();
            });

            // We should only see 15 bills, even though 20 were returned
            expect(screen.getByTestId("bill-count")).toHaveTextContent("15");
            expect(screen.getAllByTestId("bill-item")).toHaveLength(15);
        });

        it("should handle empty response from API", async () => {
            // Mock empty API response with grouped object structure
            billAxios.get.mockResolvedValueOnce({
                data: {
                    content: {},
                },
            });

            render(
                <RecentBillsProvider>
                    <TestComponent />
                </RecentBillsProvider>
            );

            // Wait for the bills to be loaded
            await waitFor(() => {
                expect(screen.queryByTestId("loading")).not.toBeInTheDocument();
            });

            // We should have 0 bills
            expect(screen.getByTestId("bill-count")).toHaveTextContent("0");
            expect(screen.queryAllByTestId("bill-item")).toHaveLength(0);
        });

        it("should handle response without content property", async () => {
            // Mock API response without content
            billAxios.get.mockResolvedValueOnce({
                data: {},
            });

            render(
                <RecentBillsProvider>
                    <TestComponent />
                </RecentBillsProvider>
            );

            // Wait for loading to complete
            await waitFor(() => {
                expect(screen.queryByTestId("loading")).not.toBeInTheDocument();
            });

            // Should handle gracefully with 0 bills
            expect(screen.getByTestId("bill-count")).toHaveTextContent("0");
        });

        it("should NOT show error when API returns successful empty response", async () => {
            // This is the specific bug fix test case:
            // When API returns successful response with empty content,
            // it should show empty state, NOT error state
            billAxios.get.mockResolvedValueOnce({
                data: {
                    content: {},
                },
            });

            render(
                <RecentBillsProvider>
                    <TestComponent />
                </RecentBillsProvider>
            );

            await waitFor(() => {
                expect(screen.queryByTestId("loading")).not.toBeInTheDocument();
            });

            // Should show empty state (0 bills) and NO error
            expect(screen.getByTestId("bill-count")).toHaveTextContent("0");
            expect(screen.queryByTestId("error")).not.toBeInTheDocument();
        });

        it("should NOT show error when API returns successful response with null content", async () => {
            // Test when content is null but response is successful
            billAxios.get.mockResolvedValueOnce({
                data: {
                    content: null,
                },
            });

            render(
                <RecentBillsProvider>
                    <TestComponent />
                </RecentBillsProvider>
            );

            await waitFor(() => {
                expect(screen.queryByTestId("loading")).not.toBeInTheDocument();
            });

            // Should show empty state and NO error
            expect(screen.getByTestId("bill-count")).toHaveTextContent("0");
            expect(screen.queryByTestId("error")).not.toBeInTheDocument();
        });
    });

    describe("Data transformation and formatting", () => {
        it("should correctly format status with underscores and capitalization", async () => {
            // Test various status formats using grouped object structure
            const mockContent = {
                "status-test-1": [
                    {
                        id: 1,
                        biller: "Provider 1",
                        billType: "Test",
                        requestReference: "REF-1",
                        amount: "1000",
                        createdAt: "2023-01-01T00:00:00.000Z",
                        status: "pending_approval",
                        bulkPaymentAdviceDetails: null,
                    },
                ],
                "status-test-2": [
                    {
                        id: 2,
                        biller: "Provider 2",
                        billType: "Test",
                        requestReference: "REF-2",
                        amount: "2000",
                        createdAt: "2023-01-02T00:00:00.000Z",
                        status: "FAILED",
                        bulkPaymentAdviceDetails: null,
                    },
                ],
                "status-test-3": [
                    {
                        id: 3,
                        biller: "Provider 3",
                        billType: "Test",
                        requestReference: "REF-3",
                        amount: "3000",
                        createdAt: "2023-01-03T00:00:00.000Z",
                        status: "",
                        bulkPaymentAdviceDetails: null,
                    },
                ],
            };

            billAxios.get.mockResolvedValueOnce({
                data: { content: mockContent },
            });

            render(
                <RecentBillsProvider>
                    <TestComponent />
                </RecentBillsProvider>
            );

            await waitFor(() => {
                expect(screen.queryByTestId("loading")).not.toBeInTheDocument();
            });

            // Verify status formatting
            expect(screen.getByTestId("status-1")).toHaveTextContent("Pending approval");
            expect(screen.getByTestId("status-2")).toHaveTextContent("Failed");
            expect(screen.getByTestId("status-3")).toHaveTextContent(""); // Empty status
        });

        it("should correctly format amounts with currency symbol", async () => {
            // Test various amount formats using grouped object structure
            const mockContent = {
                "amount-test-1": [
                    {
                        id: 1,
                        biller: "Provider 1",
                        billType: "Test",
                        requestReference: "REF-1",
                        amount: "1000",
                        createdAt: "2023-01-01T00:00:00.000Z",
                        status: "SUCCESSFUL",
                        bulkPaymentAdviceDetails: null,
                    },
                ],
                "amount-test-2": [
                    {
                        id: 2,
                        biller: "Provider 2",
                        billType: "Test",
                        requestReference: "REF-2",
                        amount: "1234.56",
                        createdAt: "2023-01-02T00:00:00.000Z",
                        status: "SUCCESSFUL",
                        bulkPaymentAdviceDetails: null,
                    },
                ],
                "amount-test-3": [
                    {
                        id: 3,
                        biller: "Provider 3",
                        billType: "Test",
                        requestReference: "REF-3",
                        amount: "",
                        createdAt: "2023-01-03T00:00:00.000Z",
                        status: "SUCCESSFUL",
                        bulkPaymentAdviceDetails: null,
                    },
                ],
            };

            billAxios.get.mockResolvedValueOnce({
                data: { content: mockContent },
            });

            render(
                <RecentBillsProvider>
                    <TestComponent />
                </RecentBillsProvider>
            );

            await waitFor(() => {
                expect(screen.queryByTestId("loading")).not.toBeInTheDocument();
            });

            // Verify amount formatting
            expect(screen.getByTestId("amount-1")).toHaveTextContent("₦1,000.00");
            expect(screen.getByTestId("amount-2")).toHaveTextContent("₦1,234.56");
            expect(screen.getByTestId("amount-3")).toHaveTextContent("₦0.00"); // Empty amount
        });

        it("should handle missing or null data fields with fallbacks", async () => {
            // Test bill with missing/null fields using grouped object structure
            const mockContent = {
                "fallback-test": [
                    {
                        id: 1,
                        biller: null,
                        billType: null,
                        requestReference: "REF-1",
                        amount: "1000",
                        createdAt: null,
                        status: "SUCCESSFUL",
                        accountNumber: null,
                        narration: null,
                        bulkPaymentAdviceDetails: null,
                    },
                ],
            };

            billAxios.get.mockResolvedValueOnce({
                data: { content: mockContent },
            });

            render(
                <RecentBillsProvider>
                    <TestComponent />
                </RecentBillsProvider>
            );

            await waitFor(() => {
                expect(screen.queryByTestId("loading")).not.toBeInTheDocument();
            });

            // Verify fallback values
            expect(screen.getByTestId("provider-1")).toHaveTextContent("-");
            expect(screen.getByTestId("date-1")).toHaveTextContent("");
            expect(screen.getByTestId("narration-1")).toHaveTextContent("-");
        });

        it("should detect bulk payments and set correct icon type based on key pattern", async () => {
            // Test new grouped object API structure with numeric vs alphanumeric keys
            const mockContent = {
                35: [
                    // numeric key = bulk payment
                    {
                        id: 102,
                        paymentCode: "*************",
                        customerId: "**********",
                        amount: "15000",
                        requestReference: "lCyUDJ_BDan8CmuAjdNXqg",
                        status: "FAILED",
                        accountNumber: "**********",
                        biller: "GLO",
                        billType: "Mobile/Recharge",
                        narration: "bulk payment test",
                        createdAt: "2025-07-03T13:23:25.000Z",
                        bulkPaymentAdviceDetails: {
                            id: 35,
                        },
                    },
                ],
                cAtAqNH7l0cD83RqawHH7w: [
                    // alphanumeric key = single payment
                    {
                        id: 105,
                        paymentCode: "*********",
                        customerId: "**********",
                        amount: "4000",
                        requestReference: "cAtAqNH7l0cD83RqawHH7w",
                        status: "SUCCESSFUL",
                        accountNumber: "**********",
                        biller: "09test",
                        billType: "Mobile/recharge",
                        narration: "single payment for mobile",
                        createdAt: "2025-07-04T11:22:28.000Z",
                        bulkPaymentAdviceDetails: null,
                    },
                ],
            };

            billAxios.get.mockResolvedValueOnce({
                data: { content: mockContent },
            });

            render(
                <RecentBillsProvider>
                    <TestComponent />
                </RecentBillsProvider>
            );

            await waitFor(() => {
                expect(screen.queryByTestId("loading")).not.toBeInTheDocument();
            });

            // Should have 2 bills total
            expect(screen.getByTestId("bill-count")).toHaveTextContent("2");

            // Verify bulk payment detection (numeric key "35" becomes the ID)
            expect(screen.getByTestId("icon-type-35")).toHaveTextContent("bulk-payment");
            expect(screen.getByTestId("is-bulk-35")).toHaveTextContent("true");
            expect(screen.getByTestId("provider-35")).toHaveTextContent("GLO");

            // Verify single payment detection (individual payment ID "105")
            expect(screen.getByTestId("icon-type-105")).toHaveTextContent("receipt");
            expect(screen.getByTestId("is-bulk-105")).toHaveTextContent("false");
            expect(screen.getByTestId("provider-105")).toHaveTextContent("09test");
        });

        it("should handle mixed single and bulk payments from grouped object", async () => {
            // Test with multiple groups of different types
            const mockContent = {
                123: [
                    // numeric = bulk
                    {
                        id: 1,
                        biller: "Bulk Provider 1",
                        billType: "Test",
                        requestReference: "BULK-1",
                        amount: "1000",
                        createdAt: "2023-01-01T00:00:00.000Z",
                        status: "SUCCESSFUL",
                        bulkPaymentAdviceDetails: { id: 123 },
                    },
                    {
                        id: 2,
                        biller: "Bulk Provider 2",
                        billType: "Test",
                        requestReference: "BULK-2",
                        amount: "2000",
                        createdAt: "2023-01-02T00:00:00.000Z",
                        status: "SUCCESSFUL",
                        bulkPaymentAdviceDetails: { id: 123 },
                    },
                ],
                abc123def: [
                    // alphanumeric = single
                    {
                        id: 3,
                        biller: "Single Provider",
                        billType: "Test",
                        requestReference: "SINGLE-1",
                        amount: "3000",
                        createdAt: "2023-01-03T00:00:00.000Z",
                        status: "SUCCESSFUL",
                        bulkPaymentAdviceDetails: null,
                    },
                ],
                456: [
                    // numeric = bulk
                    {
                        id: 4,
                        biller: "Another Bulk Provider",
                        billType: "Test",
                        requestReference: "BULK-3",
                        amount: "4000",
                        createdAt: "2023-01-04T00:00:00.000Z",
                        status: "SUCCESSFUL",
                        bulkPaymentAdviceDetails: { id: 456 },
                    },
                ],
            };

            billAxios.get.mockResolvedValueOnce({
                data: { content: mockContent },
            });

            render(
                <RecentBillsProvider>
                    <TestComponent />
                </RecentBillsProvider>
            );

            await waitFor(() => {
                expect(screen.queryByTestId("loading")).not.toBeInTheDocument();
            });

            // Should have 3 bills total: 2 bulk (aggregated) + 1 single
            expect(screen.getByTestId("bill-count")).toHaveTextContent("3");

            // Verify bulk payments (group keys "123" and "456" become IDs)
            expect(screen.getByTestId("is-bulk-123")).toHaveTextContent("true");
            expect(screen.getByTestId("is-bulk-456")).toHaveTextContent("true");
            expect(screen.getByTestId("icon-type-123")).toHaveTextContent("bulk-payment");
            expect(screen.getByTestId("icon-type-456")).toHaveTextContent("bulk-payment");

            // Verify single payment (individual payment ID "3")
            expect(screen.getByTestId("is-bulk-3")).toHaveTextContent("false");
            expect(screen.getByTestId("icon-type-3")).toHaveTextContent("receipt");
        });

        it("should handle empty grouped object content", async () => {
            // Test with empty grouped object
            const mockContent = {};

            billAxios.get.mockResolvedValueOnce({
                data: { content: mockContent },
            });

            render(
                <RecentBillsProvider>
                    <TestComponent />
                </RecentBillsProvider>
            );

            await waitFor(() => {
                expect(screen.queryByTestId("loading")).not.toBeInTheDocument();
            });

            // Should show 0 bills and no error
            expect(screen.getByTestId("bill-count")).toHaveTextContent("0");
            expect(screen.queryByTestId("error")).not.toBeInTheDocument();
        });

        it("should handle grouped object with empty arrays", async () => {
            // Test with grouped object containing empty arrays
            const mockContent = {
                123: [],
                abc456: [],
            };

            billAxios.get.mockResolvedValueOnce({
                data: { content: mockContent },
            });

            render(
                <RecentBillsProvider>
                    <TestComponent />
                </RecentBillsProvider>
            );

            await waitFor(() => {
                expect(screen.queryByTestId("loading")).not.toBeInTheDocument();
            });

            // Should show 0 bills and no error
            expect(screen.getByTestId("bill-count")).toHaveTextContent("0");
            expect(screen.queryByTestId("error")).not.toBeInTheDocument();
        });

        it("should aggregate bulk payments with multiple billers into single entry", async () => {
            // Test bulk payment with multiple unique billers
            const mockContent = {
                35: [
                    // numeric key = bulk payment with 3 different billers
                    {
                        id: 101,
                        biller: "GLO",
                        amount: "1000",
                        billType: "Mobile/Recharge",
                        requestReference: "REF-101",
                        status: "SUCCESSFUL",
                        accountNumber: "**********",
                        createdAt: "2025-07-03T13:23:25.000Z",
                        narration: "Bulk airtime payment",
                        paymentCode: "PAY101",
                        customerId: "CUST101",
                        customerMobile: "*********78",
                    },
                    {
                        id: 102,
                        biller: "MTN",
                        amount: "2000",
                        billType: "Mobile/Recharge",
                        requestReference: "REF-102",
                        status: "SUCCESSFUL",
                        accountNumber: "**********",
                        createdAt: "2025-07-03T13:23:26.000Z",
                        narration: "Bulk airtime payment",
                        paymentCode: "PAY102",
                        customerId: "CUST102",
                        customerMobile: "*********79",
                    },
                    {
                        id: 103,
                        biller: "Airtel",
                        amount: "1500",
                        billType: "Mobile/Recharge",
                        requestReference: "REF-103",
                        status: "SUCCESSFUL",
                        accountNumber: "**********",
                        createdAt: "2025-07-03T13:23:27.000Z",
                        narration: "Bulk airtime payment",
                        paymentCode: "PAY103",
                        customerId: "CUST103",
                        customerMobile: "*********80",
                    },
                ],
            };

            billAxios.get.mockResolvedValueOnce({
                data: { content: mockContent },
            });

            render(
                <RecentBillsProvider>
                    <TestComponent />
                </RecentBillsProvider>
            );

            await waitFor(() => {
                expect(screen.queryByTestId("loading")).not.toBeInTheDocument();
            });

            // Should have only 1 aggregated entry for the bulk payment
            expect(screen.getByTestId("bill-count")).toHaveTextContent("1");

            // Check aggregated data
            expect(screen.getByTestId("provider-35")).toHaveTextContent("Multiple billers");
            expect(screen.getByTestId("amount-35")).toHaveTextContent("₦4,500.00"); // Sum of 1000 + 2000 + 1500
            expect(screen.getByTestId("is-bulk-35")).toHaveTextContent("true");
            expect(screen.getByTestId("icon-type-35")).toHaveTextContent("bulk-payment");
            expect(screen.getByTestId("unique-billers-35")).toHaveTextContent("3"); // 3 unique billers
            expect(screen.getByTestId("recipient-count-35")).toHaveTextContent("3"); // 3 recipients

            // Should use first payment's metadata
            expect(screen.getByTestId("reference-35")).toHaveTextContent("REF-101");
            expect(screen.getByTestId("narration-35")).toHaveTextContent("Bulk airtime payment");
        });

        it("should aggregate bulk payments with same biller into single entry", async () => {
            // Test bulk payment with same biller
            const mockContent = {
                42: [
                    // numeric key = bulk payment with same biller
                    {
                        id: 201,
                        biller: "GLO",
                        amount: "3000",
                        billType: "Mobile/Recharge",
                        requestReference: "REF-201",
                        status: "SUCCESSFUL",
                        accountNumber: "**********",
                        createdAt: "2025-07-04T10:00:00.000Z",
                        narration: "GLO bulk recharge",
                        paymentCode: "PAY201",
                        customerId: "CUST201",
                        customerMobile: "***********",
                    },
                    {
                        id: 202,
                        biller: "GLO",
                        amount: "5000",
                        billType: "Mobile/Recharge",
                        requestReference: "REF-202",
                        status: "SUCCESSFUL",
                        accountNumber: "**********",
                        createdAt: "2025-07-04T10:00:01.000Z",
                        narration: "GLO bulk recharge",
                        paymentCode: "PAY202",
                        customerId: "CUST202",
                        customerMobile: "***********",
                    },
                ],
            };

            billAxios.get.mockResolvedValueOnce({
                data: { content: mockContent },
            });

            render(
                <RecentBillsProvider>
                    <TestComponent />
                </RecentBillsProvider>
            );

            await waitFor(() => {
                expect(screen.queryByTestId("loading")).not.toBeInTheDocument();
            });

            // Should have only 1 aggregated entry
            expect(screen.getByTestId("bill-count")).toHaveTextContent("1");

            // Check aggregated data - should show single biller name
            expect(screen.getByTestId("provider-42")).toHaveTextContent("GLO");
            expect(screen.getByTestId("amount-42")).toHaveTextContent("₦8,000.00"); // Sum of 3000 + 5000
            expect(screen.getByTestId("is-bulk-42")).toHaveTextContent("true");
            expect(screen.getByTestId("unique-billers-42")).toHaveTextContent("1"); // 1 unique biller
            expect(screen.getByTestId("recipient-count-42")).toHaveTextContent("2"); // 2 recipients
        });

        it("should handle mixed bulk and single payments correctly", async () => {
            // Test with both bulk (aggregated) and single payments
            const mockContent = {
                100: [
                    // Bulk payment
                    {
                        id: 301,
                        biller: "DSTV",
                        amount: "7500",
                        billType: "Cable TV",
                        requestReference: "REF-301",
                        status: "SUCCESSFUL",
                        accountNumber: "**********",
                        createdAt: "2025-07-05T09:00:00.000Z",
                        narration: "DSTV subscriptions",
                        paymentCode: "PAY301",
                        customerId: "CUST301",
                        customerMobile: "***********",
                    },
                    {
                        id: 302,
                        biller: "DSTV",
                        amount: "12500",
                        billType: "Cable TV",
                        requestReference: "REF-302",
                        status: "SUCCESSFUL",
                        accountNumber: "**********",
                        createdAt: "2025-07-05T09:00:01.000Z",
                        narration: "DSTV subscriptions",
                        paymentCode: "PAY302",
                        customerId: "CUST302",
                        customerMobile: "***********",
                    },
                ],
                abc123: [
                    // Single payment
                    {
                        id: 303,
                        biller: "EKEDC",
                        amount: "25000",
                        billType: "Electricity",
                        requestReference: "abc123",
                        status: "SUCCESSFUL",
                        accountNumber: "**********",
                        createdAt: "2025-07-05T10:00:00.000Z",
                        narration: "Electricity bill payment",
                        paymentCode: "PAY303",
                        customerId: "CUST303",
                        customerMobile: "***********",
                    },
                ],
            };

            billAxios.get.mockResolvedValueOnce({
                data: { content: mockContent },
            });

            render(
                <RecentBillsProvider>
                    <TestComponent />
                </RecentBillsProvider>
            );

            await waitFor(() => {
                expect(screen.queryByTestId("loading")).not.toBeInTheDocument();
            });

            // Should have 2 entries: 1 aggregated bulk + 1 single
            expect(screen.getByTestId("bill-count")).toHaveTextContent("2");

            // Check bulk payment (aggregated)
            expect(screen.getByTestId("provider-100")).toHaveTextContent("DSTV");
            expect(screen.getByTestId("amount-100")).toHaveTextContent("₦20,000.00"); // 7500 + 12500
            expect(screen.getByTestId("is-bulk-100")).toHaveTextContent("true");

            // Check single payment (not aggregated)
            expect(screen.getByTestId("provider-303")).toHaveTextContent("EKEDC");
            expect(screen.getByTestId("amount-303")).toHaveTextContent("₦25,000.00");
            expect(screen.getByTestId("is-bulk-303")).toHaveTextContent("false");
        });
    });

    describe("Sorting functionality", () => {
        it("should display bills sorted by date (newest first)", async () => {
            // Create bills with different dates (out of order) using grouped object structure
            const mockContent = {
                "sort-test-1": [
                    {
                        id: 1,
                        biller: "Provider 1",
                        billType: "Test Category",
                        requestReference: "REF-1",
                        amount: "1000",
                        createdAt: new Date(2023, 0, 5).toISOString(), // Jan 5
                        status: "SUCCESSFUL",
                        bulkPaymentAdviceDetails: null,
                    },
                ],
                "sort-test-2": [
                    {
                        id: 2,
                        biller: "Provider 2",
                        billType: "Test Category",
                        requestReference: "REF-2",
                        amount: "1000",
                        createdAt: new Date(2023, 0, 10).toISOString(), // Jan 10 (newest)
                        status: "SUCCESSFUL",
                        bulkPaymentAdviceDetails: null,
                    },
                ],
                "sort-test-3": [
                    {
                        id: 3,
                        biller: "Provider 3",
                        billType: "Test Category",
                        requestReference: "REF-3",
                        amount: "1000",
                        createdAt: new Date(2023, 0, 1).toISOString(), // Jan 1 (oldest)
                        status: "SUCCESSFUL",
                        bulkPaymentAdviceDetails: null,
                    },
                ],
            };

            billAxios.get.mockResolvedValueOnce({
                data: { content: mockContent },
            });

            render(
                <RecentBillsProvider>
                    <TestComponent />
                </RecentBillsProvider>
            );

            await waitFor(() => {
                expect(screen.queryByTestId("loading")).not.toBeInTheDocument();
            });

            // We should have 3 bills
            expect(screen.getByTestId("bill-count")).toHaveTextContent("3");

            // The items should be sorted by date (newest first)
            const items = screen.getAllByTestId("bill-item");
            expect(items[0]).toHaveTextContent("Provider 2"); // Jan 10 (newest)
            expect(items[1]).toHaveTextContent("Provider 1"); // Jan 5
            expect(items[2]).toHaveTextContent("Provider 3"); // Jan 1 (oldest)
        });

        it("should handle empty dates in sorting (treat as oldest)", async () => {
            // Mix of bills with and without dates using grouped object structure
            const mockContent = {
                "date-test-1": [
                    {
                        id: 1,
                        biller: "No Date 1",
                        billType: "Test",
                        requestReference: "REF-1",
                        amount: "1000",
                        createdAt: "", // Empty date
                        status: "SUCCESSFUL",
                        bulkPaymentAdviceDetails: null,
                    },
                ],
                "date-test-2": [
                    {
                        id: 2,
                        biller: "With Date",
                        billType: "Test",
                        requestReference: "REF-2",
                        amount: "2000",
                        createdAt: new Date(2023, 0, 10).toISOString(),
                        status: "SUCCESSFUL",
                        bulkPaymentAdviceDetails: null,
                    },
                ],
                "date-test-3": [
                    {
                        id: 3,
                        biller: "No Date 2",
                        billType: "Test",
                        requestReference: "REF-3",
                        amount: "3000",
                        createdAt: null, // Null date
                        status: "SUCCESSFUL",
                        bulkPaymentAdviceDetails: null,
                    },
                ],
            };

            billAxios.get.mockResolvedValueOnce({
                data: { content: mockContent },
            });

            render(
                <RecentBillsProvider>
                    <TestComponent />
                </RecentBillsProvider>
            );

            await waitFor(() => {
                expect(screen.queryByTestId("loading")).not.toBeInTheDocument();
            });

            // The item with date should come first, empty dates last
            const items = screen.getAllByTestId("bill-item");
            expect(items[0]).toHaveTextContent("With Date"); // Has date (newest)
            expect(items[1]).toHaveTextContent("No Date 1"); // Empty date (oldest)
            expect(items[2]).toHaveTextContent("No Date 2"); // Null date (oldest)
        });
    });

    describe("Error handling", () => {
        it("should display error message when API call fails", async () => {
            // Mock API error
            billAxios.get.mockRejectedValueOnce(new Error("API Error"));

            render(
                <RecentBillsProvider>
                    <TestComponent />
                </RecentBillsProvider>
            );

            // First the loading state should be shown
            expect(screen.getByTestId("loading")).toBeInTheDocument();

            // Then the error should be shown
            await waitFor(() => {
                expect(screen.getByTestId("error")).toBeInTheDocument();
                expect(screen.getByTestId("error")).toHaveTextContent("Failed to fetch recent bills");
            });
        });

        it("should handle timeout errors with user-friendly message", async () => {
            // Mock timeout error
            const timeoutError = new Error("Request timeout exceeded");
            billAxios.get.mockRejectedValueOnce(timeoutError);

            render(
                <RecentBillsProvider>
                    <TestComponent />
                </RecentBillsProvider>
            );

            await waitFor(() => {
                expect(screen.getByTestId("error")).toBeInTheDocument();
                expect(screen.getByTestId("error")).toHaveTextContent("Unable to load recent bills at this time");
            });
        });

        it("should handle network timeout errors", async () => {
            // Mock another type of timeout error
            const networkError = new Error("Network timeout");
            billAxios.get.mockRejectedValueOnce(networkError);

            render(
                <RecentBillsProvider>
                    <TestComponent />
                </RecentBillsProvider>
            );

            await waitFor(() => {
                expect(screen.getByTestId("error")).toBeInTheDocument();
                expect(screen.getByTestId("error")).toHaveTextContent("Unable to load recent bills at this time");
            });
        });

        it("should handle errors where message includes 'exceeded' but not 'timeout'", async () => {
            // Mock error that has "exceeded" to test timeout detection logic
            // Since the code now uses || operator, "exceeded" should trigger timeout message
            const exceededError = new Error("Request limit exceeded");
            billAxios.get.mockRejectedValueOnce(exceededError);

            render(
                <RecentBillsProvider>
                    <TestComponent />
                </RecentBillsProvider>
            );

            await waitFor(() => {
                expect(screen.getByTestId("error")).toBeInTheDocument();
                // This should show the timeout-friendly error message because "exceeded" is detected
                expect(screen.getByTestId("error")).toHaveTextContent("Unable to load recent bills at this time");
            });
        });
    });

    describe("Refetch functionality", () => {
        it("should allow refetching bills data", async () => {
            const user = userEvent.setup();

            // Initial API response using grouped object structure
            const initialContent = {
                "refetch-initial": [
                    {
                        id: 1,
                        biller: "Provider 1",
                        billType: "Test",
                        requestReference: "REF-1",
                        amount: "1000",
                        createdAt: "2023-01-01T00:00:00.000Z",
                        status: "SUCCESSFUL",
                        bulkPaymentAdviceDetails: null,
                    },
                ],
            };

            // Refetch API response using grouped object structure
            const refetchContent = {
                "refetch-new": [
                    {
                        id: 2,
                        biller: "Provider 2",
                        billType: "Test",
                        requestReference: "REF-2",
                        amount: "2000",
                        createdAt: "2023-01-02T00:00:00.000Z",
                        status: "SUCCESSFUL",
                        bulkPaymentAdviceDetails: null,
                    },
                ],
            };

            billAxios.get.mockResolvedValueOnce({
                data: { content: initialContent },
            });

            render(
                <RecentBillsProvider>
                    <TestComponent />
                </RecentBillsProvider>
            );

            // Wait for initial load
            await waitFor(() => {
                expect(screen.getByTestId("bill-count")).toHaveTextContent("1");
                expect(screen.getByTestId("provider-1")).toHaveTextContent("Provider 1");
            });

            // Mock second API call for refetch
            billAxios.get.mockResolvedValueOnce({
                data: { content: refetchContent },
            });

            // Click refetch button
            await user.click(screen.getByTestId("refetch-button"));

            // Wait for refetch to complete
            await waitFor(() => {
                expect(screen.getByTestId("bill-count")).toHaveTextContent("1");
                expect(screen.getByTestId("provider-2")).toHaveTextContent("Provider 2");
            });

            // Verify API was called twice
            expect(billAxios.get).toHaveBeenCalledTimes(2);
        });

        it("should handle refetch errors properly", async () => {
            const user = userEvent.setup();

            // Initial successful response with empty grouped object
            billAxios.get.mockResolvedValueOnce({
                data: { content: {} },
            });

            render(
                <RecentBillsProvider>
                    <TestComponent />
                </RecentBillsProvider>
            );

            await waitFor(() => {
                expect(screen.getByTestId("bill-count")).toHaveTextContent("0");
            });

            // Mock refetch error
            billAxios.get.mockRejectedValueOnce(new Error("Refetch failed"));

            // Click refetch button
            await user.click(screen.getByTestId("refetch-button"));

            // Should show error after refetch fails
            await waitFor(() => {
                expect(screen.getByTestId("error")).toBeInTheDocument();
                expect(screen.getByTestId("error")).toHaveTextContent("Failed to fetch recent bills");
            });
        });
    });
});
