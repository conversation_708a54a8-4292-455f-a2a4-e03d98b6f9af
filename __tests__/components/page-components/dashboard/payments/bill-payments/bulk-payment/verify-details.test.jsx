import React from "react";
import { fireEvent, render, screen } from "@testing-library/react";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import VerifyBulkDetails from "@/components/page-components/dashboard/bill-payments/bulk-payment/verify-details";
import { getEntryStatus } from "@/components/page-components/dashboard/bill-payments/bulk-payment/table/utils";
import { resetBulkAirtime, setEntries, updateFilterState } from "@/redux/features/bulkAirtime";

// Mock dependencies
jest.mock("@/redux/hooks", () => ({
    useAppDispatch: jest.fn(),
    useAppSelector: jest.fn(),
}));

jest.mock("@/redux/features/bulkAirtime", () => ({
    resetBulkAirtime: jest.fn(),
    setEntries: jest.fn(),
    updateFilterState: jest.fn(),
}));

// Mock the centralized billers hook instead of direct Redux actions
jest.mock("@/components/page-components/dashboard/bill-payments/hooks/use-centralized-billers", () => ({
    useCentralizedBillers: jest.fn(),
}));

// Mock hooks
jest.mock("@/components/page-components/dashboard/bill-payments/hooks/useExitHandlers", () => ({
    useExitHandlers: jest.fn(() => ({
        isOpen: true,
        showExitConfirmation: false,
        handleClose: jest.fn(),
        handleConfirmExit: jest.fn(),
        handleCancelExit: jest.fn(),
    })),
}));

// Mock child components
jest.mock("@/components/common/stepper", () => ({
    __esModule: true,
    default: ({ steps, currentStep }) => (
        <div data-testid="stepper">
            <div data-testid="stepper-current-step">{currentStep}</div>
            <div data-testid="stepper-steps">{steps.length}</div>
        </div>
    ),
}));

jest.mock("@/components/common/summary-card", () => ({
    SummaryCard: ({ items }) => (
        <div data-testid="summary-card">
            {items.map((item, i) => (
                <div key={i} data-testid={`summary-item-${i}`}>
                    {item.label}: {item.value}
                </div>
            ))}
        </div>
    ),
}));

jest.mock("@/components/common/full-screen-drawer", () => ({
    __esModule: true,
    default: ({ isOpen, title, children, onClose, onConfirmExit, onCancelExit, showExitConfirmation }) =>
        isOpen ? (
            <div data-testid="full-screen-drawer">
                <h1 data-testid="drawer-title">{title}</h1>
                {children}
                <button data-testid="drawer-close" onClick={onClose}>
                    Close
                </button>
                {showExitConfirmation && (
                    <div data-testid="exit-confirmation">
                        <button data-testid="confirm-exit" onClick={onConfirmExit}>
                            Confirm Exit
                        </button>
                        <button data-testid="cancel-exit" onClick={onCancelExit}>
                            Cancel Exit
                        </button>
                    </div>
                )}
            </div>
        ) : null,
}));

jest.mock("@/components/common/buttonv3", () => ({
    Button: ({ children, onClick, disabled, variant }) => (
        <button
            data-testid={`button-${variant || children.toString().toLowerCase().replace(/\s+/g, "-")}`}
            onClick={onClick}
            disabled={disabled}
        >
            {children}
        </button>
    ),
}));

jest.mock("@/components/page-components/dashboard/bill-payments/bulk-payment/table/filter-buttons", () => ({
    FilterButtons: ({ currentFilter, onFilterChange }) => (
        <div data-testid="filter-buttons">
            {["All", "Valid", "Invalid"].map((filter) => (
                <button
                    key={filter}
                    data-testid={`filter-${filter.toLowerCase()}`}
                    onClick={() => onFilterChange(filter)}
                >
                    {filter}
                </button>
            ))}
        </div>
    ),
}));

jest.mock("@/components/page-components/dashboard/bill-payments/bulk-payment/table/airtime-table", () => ({
    AirtimeTable: ({
        entries,
        networkFilter,
        setNetworkFilter,
        uniqueNetworks,
        sortOrder,
        toggleSortOrder,
        onEdit,
        onDelete,
    }) => (
        <div data-testid="airtime-table">
            <span data-testid="entries-count">{entries.length}</span>
            <span data-testid="network-filter">{networkFilter}</span>
            <button data-testid="toggle-sort" onClick={toggleSortOrder}>
                Toggle Sort: {sortOrder || "none"}
            </button>
            {entries.map((entry) => (
                <div key={entry.id} data-testid={`entry-${entry.id}`}>
                    <span>{entry.phoneNumber}</span>
                    <span>{entry.network}</span>
                    <span>{entry.status}</span>
                    <span>{entry.amount}</span>
                    <button data-testid={`edit-entry-${entry.id}`} onClick={() => onEdit(entry)}>
                        Edit
                    </button>
                    <button data-testid={`delete-entry-${entry.id}`} onClick={() => onDelete(entry)}>
                        Delete
                    </button>
                </div>
            ))}
            <select
                data-testid="network-dropdown"
                value={networkFilter}
                onChange={(e) => setNetworkFilter(e.target.value)}
            >
                {uniqueNetworks.map((network) => (
                    <option key={network} value={network}>
                        {network}
                    </option>
                ))}
            </select>
        </div>
    ),
}));

jest.mock("@/components/page-components/dashboard/bill-payments/bulk-payment/airtime-dialog", () => ({
    __esModule: true,
    default: ({ isOpen, onClose, onSubmit, initialValues, mode }) =>
        isOpen ? (
            <div data-testid={`airtime-form-${mode}`}>
                <button
                    data-testid="submit-form"
                    onClick={() =>
                        onSubmit({
                            networkProvider: initialValues?.networkProvider || "MTN",
                            phoneNumber: initialValues?.phoneNumber || "07012345678",
                            amount: initialValues?.amount || 5000,
                        })
                    }
                >
                    Submit
                </button>
                <button data-testid="close-form" onClick={onClose}>
                    Close
                </button>
            </div>
        ) : null,
}));

jest.mock("@/components/common/delete-confirmation", () => ({
    __esModule: true,
    default: ({ isOpen, onClose, onConfirm }) =>
        isOpen ? (
            <div data-testid="delete-confirmation">
                <button data-testid="confirm-delete" onClick={onConfirm}>
                    Confirm Delete
                </button>
                <button data-testid="cancel-delete" onClick={onClose}>
                    Cancel Delete
                </button>
            </div>
        ) : null,
}));

jest.mock("@/components/page-components/dashboard/bill-payments/bulk-payment/table/utils", () => ({
    getEntryStatus: jest.fn().mockImplementation((phoneNumber, network, billers, amount) => {
        // Simple mock implementation for testing
        if (phoneNumber.length !== 11 || !amount || amount <= 0) {
            return "Invalid";
        }
        return "Valid";
    }),
}));

describe("VerifyBulkDetails", () => {
    const mockDispatch = jest.fn();
    const mockOnBack = jest.fn();
    const mockOnContinue = jest.fn();

    // Mock functions for centralized billers hook
    const mockFetchBillersForCategory = jest.fn();
    const mockHasBillersForCategory = jest.fn();
    const mockGetBillersForCategory = jest.fn();
    const mockClearBillersCache = jest.fn();

    // Sample test data
    const mockBillers = [
        { shortName: "MTN", billerName: "MTN" },
        { shortName: "Airtel", billerName: "Airtel" },
        { shortName: "Glo", billerName: "Glo" },
    ];

    const mockEntries = [
        { id: "1", phoneNumber: "07012345678", network: "MTN", status: "Valid", amount: 5000 },
        { id: "2", phoneNumber: "08023456789", network: "Airtel", status: "Valid", amount: 3000 },
        { id: "3", phoneNumber: "0901234", network: "Glo", status: "Invalid", amount: 1000 },
    ];

    // Default mock implementation for centralized billers hook
    const defaultCentralizedBillersHook = {
        billers: mockBillers,
        loading: false,
        error: null,
        cachedCategoryId: 4,
        lastFetched: Date.now(),
        fetchBillersForCategory: mockFetchBillersForCategory,
        getBillersForCategory: mockGetBillersForCategory,
        hasBillersForCategory: mockHasBillersForCategory,
        clearBillersCache: mockClearBillersCache,
    };

    beforeEach(() => {
        useAppDispatch.mockReturnValue(mockDispatch);

        // Mock the centralized billers hook with default successful state
        const {
            useCentralizedBillers,
        } = require("@/components/page-components/dashboard/bill-payments/hooks/use-centralized-billers");
        useCentralizedBillers.mockReturnValue(defaultCentralizedBillersHook);

        // Mock hasBillersForCategory to return false initially (to trigger fetch)
        mockHasBillersForCategory.mockReturnValue(false);

        // Use a more flexible approach for useAppSelector that handles different selectors
        useAppSelector.mockImplementation((selectorFn) => {
            // Create a fake state object that matches the structure expected by the component
            const fakeState = {
                bulkAirtime: {
                    entries: mockEntries,
                    validEntriesCount: 2,
                    totalValidAmount: 8000,
                    filterState: {
                        filter: "All",
                        networkFilter: "Network",
                        sortOrder: null,
                    },
                },
            };

            // Call the selector with our fake state
            return selectorFn(fakeState);
        });
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it("renders component with correct title and content", () => {
        render(<VerifyBulkDetails onBack={mockOnBack} onContinue={mockOnContinue} />);

        expect(screen.getByTestId("drawer-title")).toHaveTextContent("Airtime bulk bill payment");
        expect(screen.getByTestId("stepper")).toBeInTheDocument();
        expect(screen.getByTestId("stepper-current-step")).toHaveTextContent("2");
        expect(screen.getByTestId("summary-card")).toBeInTheDocument();
        expect(screen.getByTestId("filter-buttons")).toBeInTheDocument();
        expect(screen.getByTestId("airtime-table")).toBeInTheDocument();
        expect(screen.getByTestId("entries-count")).toHaveTextContent("3");
    });

    describe("Centralized Billers Hook Integration", () => {
        it("fetches billers when component mounts and no cached data exists", () => {
            // Mock that no billers exist for category 4
            mockHasBillersForCategory.mockReturnValue(false);

            render(<VerifyBulkDetails onBack={mockOnBack} onContinue={mockOnContinue} />);

            expect(mockHasBillersForCategory).toHaveBeenCalledWith(4);
            expect(mockFetchBillersForCategory).toHaveBeenCalledWith(4);
        });

        it("does not fetch billers when cached data exists", () => {
            // Mock that billers already exist for category 4
            mockHasBillersForCategory.mockReturnValue(true);

            render(<VerifyBulkDetails onBack={mockOnBack} onContinue={mockOnContinue} />);

            expect(mockHasBillersForCategory).toHaveBeenCalledWith(4);
            expect(mockFetchBillersForCategory).not.toHaveBeenCalled();
        });

        it("does not fetch billers when already loading", () => {
            // Mock loading state
            const {
                useCentralizedBillers,
            } = require("@/components/page-components/dashboard/bill-payments/hooks/use-centralized-billers");
            useCentralizedBillers.mockReturnValue({
                ...defaultCentralizedBillersHook,
                loading: true,
            });

            mockHasBillersForCategory.mockReturnValue(false);

            render(<VerifyBulkDetails onBack={mockOnBack} onContinue={mockOnContinue} />);

            expect(mockHasBillersForCategory).toHaveBeenCalledWith(4);
            expect(mockFetchBillersForCategory).not.toHaveBeenCalled();
        });

        it("displays loading state while fetching billers", () => {
            // Mock loading state
            const {
                useCentralizedBillers,
            } = require("@/components/page-components/dashboard/bill-payments/hooks/use-centralized-billers");
            useCentralizedBillers.mockReturnValue({
                ...defaultCentralizedBillersHook,
                loading: true,
                billers: null,
            });

            render(<VerifyBulkDetails onBack={mockOnBack} onContinue={mockOnContinue} />);

            expect(screen.getByText("Loading network providers...")).toBeInTheDocument();
            expect(screen.queryByTestId("airtime-table")).not.toBeInTheDocument();
        });

        it("displays error message when network providers API fails", () => {
            // Mock error state
            const {
                useCentralizedBillers,
            } = require("@/components/page-components/dashboard/bill-payments/hooks/use-centralized-billers");
            useCentralizedBillers.mockReturnValue({
                ...defaultCentralizedBillersHook,
                loading: false,
                error: "Failed to fetch network providers",
                billers: null,
            });

            render(<VerifyBulkDetails onBack={mockOnBack} onContinue={mockOnContinue} />);

            // Check that error message is displayed
            expect(screen.getByText(/Unable to load network providers/)).toBeInTheDocument();

            // Check that retry button exists
            expect(screen.getByText("Retry")).toBeInTheDocument();

            // Check that the table is not rendered
            expect(screen.queryByTestId("airtime-table")).not.toBeInTheDocument();

            // Verify ARIA attributes for accessibility
            expect(screen.getByRole("alert")).toBeInTheDocument();
        });

        it("handles retry button click in error state", () => {
            // Mock error state
            const {
                useCentralizedBillers,
            } = require("@/components/page-components/dashboard/bill-payments/hooks/use-centralized-billers");
            useCentralizedBillers.mockReturnValue({
                ...defaultCentralizedBillersHook,
                loading: false,
                error: "Failed to fetch network providers",
                billers: null,
            });

            render(<VerifyBulkDetails onBack={mockOnBack} onContinue={mockOnContinue} />);

            // Test that clicking the retry button calls fetchBillersForCategory
            fireEvent.click(screen.getByText("Retry"));
            expect(mockFetchBillersForCategory).toHaveBeenCalledWith(4);
        });

        it("renders table when billers are successfully loaded", () => {
            // Mock successful state with billers
            const {
                useCentralizedBillers,
            } = require("@/components/page-components/dashboard/bill-payments/hooks/use-centralized-billers");
            useCentralizedBillers.mockReturnValue({
                ...defaultCentralizedBillersHook,
                loading: false,
                error: null,
                billers: mockBillers,
            });

            render(<VerifyBulkDetails onBack={mockOnBack} onContinue={mockOnContinue} />);

            expect(screen.getByTestId("airtime-table")).toBeInTheDocument();
            expect(screen.queryByText("Loading network providers...")).not.toBeInTheDocument();
            expect(screen.queryByText(/Unable to load network providers/)).not.toBeInTheDocument();
        });

        it("handles transition from loading to success state", () => {
            // Start with loading state
            const {
                useCentralizedBillers,
            } = require("@/components/page-components/dashboard/bill-payments/hooks/use-centralized-billers");
            useCentralizedBillers.mockReturnValue({
                ...defaultCentralizedBillersHook,
                loading: true,
                billers: null,
            });

            const { rerender } = render(<VerifyBulkDetails onBack={mockOnBack} onContinue={mockOnContinue} />);

            expect(screen.getByText("Loading network providers...")).toBeInTheDocument();

            // Transition to success state
            useCentralizedBillers.mockReturnValue({
                ...defaultCentralizedBillersHook,
                loading: false,
                billers: mockBillers,
            });

            rerender(<VerifyBulkDetails onBack={mockOnBack} onContinue={mockOnContinue} />);

            expect(screen.queryByText("Loading network providers...")).not.toBeInTheDocument();
            expect(screen.getByTestId("airtime-table")).toBeInTheDocument();
        });

        it("handles transition from error to success state", () => {
            // Start with error state
            const {
                useCentralizedBillers,
            } = require("@/components/page-components/dashboard/bill-payments/hooks/use-centralized-billers");
            useCentralizedBillers.mockReturnValue({
                ...defaultCentralizedBillersHook,
                loading: false,
                error: "Network error",
                billers: null,
            });

            const { rerender } = render(<VerifyBulkDetails onBack={mockOnBack} onContinue={mockOnContinue} />);

            expect(screen.getByText(/Unable to load network providers/)).toBeInTheDocument();

            // Transition to success state
            useCentralizedBillers.mockReturnValue({
                ...defaultCentralizedBillersHook,
                loading: false,
                error: null,
                billers: mockBillers,
            });

            rerender(<VerifyBulkDetails onBack={mockOnBack} onContinue={mockOnContinue} />);

            expect(screen.queryByText(/Unable to load network providers/)).not.toBeInTheDocument();
            expect(screen.getByTestId("airtime-table")).toBeInTheDocument();
        });

        it("updates entry status when billers are loaded and status changes", () => {
            // Start with entries but no billers
            const entriesWithOutdatedStatus = [
                { id: "1", phoneNumber: "07012345678", network: "MTN", status: "Pending", amount: 5000 },
                { id: "2", phoneNumber: "08023456789", network: "Airtel", status: "Pending", amount: 3000 },
            ];

            useAppSelector.mockImplementation((selectorFn) => {
                const fakeState = {
                    bulkAirtime: {
                        entries: entriesWithOutdatedStatus,
                        validEntriesCount: 0,
                        totalValidAmount: 0,
                        filterState: {
                            filter: "All",
                            networkFilter: "Network",
                            sortOrder: null,
                        },
                    },
                };
                return selectorFn(fakeState);
            });

            // Mock getEntryStatus to return different status
            getEntryStatus.mockImplementation((phoneNumber, network, billers, amount) => {
                if (phoneNumber.length === 11 && amount > 0) {
                    return "Valid";
                }
                return "Invalid";
            });

            const {
                useCentralizedBillers,
            } = require("@/components/page-components/dashboard/bill-payments/hooks/use-centralized-billers");

            // Start with no billers
            useCentralizedBillers.mockReturnValue({
                ...defaultCentralizedBillersHook,
                billers: null,
                loading: false,
                error: null,
            });

            const { rerender } = render(<VerifyBulkDetails onBack={mockOnBack} onContinue={mockOnContinue} />);

            // Now provide billers data to trigger validation update
            useCentralizedBillers.mockReturnValue({
                ...defaultCentralizedBillersHook,
                billers: mockBillers,
                loading: false,
                error: null,
            });

            rerender(<VerifyBulkDetails onBack={mockOnBack} onContinue={mockOnContinue} />);

            // Verify that setEntries was called with updated status
            expect(mockDispatch).toHaveBeenCalledWith(
                setEntries([
                    expect.objectContaining({
                        id: "1",
                        phoneNumber: "07012345678",
                        network: "MTN",
                        status: "Valid",
                        amount: 5000,
                    }),
                    expect.objectContaining({
                        id: "2",
                        phoneNumber: "08023456789",
                        network: "Airtel",
                        status: "Valid",
                        amount: 3000,
                    }),
                ])
            );
        });

        it("does not dispatch setEntries when entry status remains unchanged", () => {
            // Mock entries that already have correct status
            const entriesWithCorrectStatus = [
                { id: "1", phoneNumber: "07012345678", network: "MTN", status: "Valid", amount: 5000 },
                { id: "2", phoneNumber: "08023456789", network: "Airtel", status: "Valid", amount: 3000 },
            ];

            useAppSelector.mockImplementation((selectorFn) => {
                const fakeState = {
                    bulkAirtime: {
                        entries: entriesWithCorrectStatus,
                        validEntriesCount: 2,
                        totalValidAmount: 8000,
                        filterState: {
                            filter: "All",
                            networkFilter: "Network",
                            sortOrder: null,
                        },
                    },
                };
                return selectorFn(fakeState);
            });

            // Mock getEntryStatus to return the same status
            getEntryStatus.mockImplementation(() => "Valid");

            const {
                useCentralizedBillers,
            } = require("@/components/page-components/dashboard/bill-payments/hooks/use-centralized-billers");
            useCentralizedBillers.mockReturnValue({
                ...defaultCentralizedBillersHook,
                billers: mockBillers,
                loading: false,
                error: null,
            });

            render(<VerifyBulkDetails onBack={mockOnBack} onContinue={mockOnContinue} />);

            // Verify that setEntries was NOT called because status didn't change
            expect(mockDispatch).not.toHaveBeenCalledWith(setEntries(expect.any(Array)));
        });
    });

    it("allows filtering entries by status", () => {
        render(<VerifyBulkDetails onBack={mockOnBack} onContinue={mockOnContinue} />);

        fireEvent.click(screen.getByTestId("filter-valid"));

        expect(mockDispatch).toHaveBeenCalledWith(updateFilterState({ filter: "Valid" }));
    });

    it("allows changing network filter", () => {
        render(<VerifyBulkDetails onBack={mockOnBack} onContinue={mockOnContinue} />);

        fireEvent.change(screen.getByTestId("network-dropdown"), { target: { value: "MTN" } });

        expect(mockDispatch).toHaveBeenCalledWith(updateFilterState({ networkFilter: "MTN" }));
    });

    it("allows sorting entries by amount", () => {
        render(<VerifyBulkDetails onBack={mockOnBack} onContinue={mockOnContinue} />);

        fireEvent.click(screen.getByTestId("toggle-sort"));

        expect(mockDispatch).toHaveBeenCalledWith(updateFilterState({ sortOrder: "asc" }));
    });

    it("toggles sort order from asc to desc", () => {
        // Override the default mock implementation for this specific test
        useAppSelector.mockImplementation((selectorFn) => {
            const modifiedState = {
                bulkAirtime: {
                    entries: mockEntries,
                    validEntriesCount: 2,
                    totalValidAmount: 8000,
                    filterState: {
                        filter: "All",
                        networkFilter: "Network",
                        sortOrder: "asc", // Already set to "asc"
                    },
                },
            };

            return selectorFn(modifiedState);
        });

        render(<VerifyBulkDetails onBack={mockOnBack} onContinue={mockOnContinue} />);

        // Verify the current sort order is shown as "asc"
        expect(screen.getByTestId("toggle-sort").textContent).toContain("asc");

        // Click to toggle sort
        fireEvent.click(screen.getByTestId("toggle-sort"));

        // Verify it switched to "desc"
        expect(mockDispatch).toHaveBeenCalledWith(updateFilterState({ sortOrder: "desc" }));
    });

    it("toggles sort order from desc to null", () => {
        // Test the third state of sorting (desc -> null)
        useAppSelector.mockImplementation((selectorFn) => {
            const modifiedState = {
                bulkAirtime: {
                    entries: mockEntries,
                    validEntriesCount: 2,
                    totalValidAmount: 8000,
                    filterState: {
                        filter: "All",
                        networkFilter: "Network",
                        sortOrder: "desc", // Already set to "desc"
                    },
                },
            };

            return selectorFn(modifiedState);
        });

        render(<VerifyBulkDetails onBack={mockOnBack} onContinue={mockOnContinue} />);

        // Verify the current sort order is shown as "desc"
        expect(screen.getByTestId("toggle-sort").textContent).toContain("desc");

        // Click to toggle sort (should go to null)
        fireEvent.click(screen.getByTestId("toggle-sort"));

        // Verify it switched to null (no sort)
        expect(mockDispatch).toHaveBeenCalledWith(updateFilterState({ sortOrder: null }));
    });

    it("sorts entries in descending order when sortOrder is 'desc'", () => {
        // Create entries with distinct amounts for clear sorting
        const entriesForSorting = [
            { id: "1", phoneNumber: "07012345678", network: "MTN", status: "Valid", amount: 5000 },
            { id: "2", phoneNumber: "08023456789", network: "Airtel", status: "Valid", amount: 3000 },
            { id: "3", phoneNumber: "09034567890", network: "Glo", status: "Valid", amount: 10000 },
        ];

        // Override the default mock implementation for this specific test
        useAppSelector.mockImplementation((selectorFn) => {
            const modifiedState = {
                bulkAirtime: {
                    entries: entriesForSorting,
                    validEntriesCount: 3,
                    totalValidAmount: 18000,
                    filterState: {
                        filter: "All",
                        networkFilter: "Network",
                        sortOrder: "desc", // Set to "desc" to test descending sort
                    },
                },
            };

            return selectorFn(modifiedState);
        });

        render(<VerifyBulkDetails onBack={mockOnBack} onContinue={mockOnContinue} />);

        // Get all entries from the rendered component
        const entryElements = screen.getAllByTestId(/^entry-/);

        // Verify entries are sorted in descending order by checking the order of IDs
        // The highest amount (10000) has ID "3" and should be first
        // The lowest amount (3000) has ID "2" and should be last
        expect(entryElements[0]).toHaveAttribute("data-testid", "entry-3");
        expect(entryElements[1]).toHaveAttribute("data-testid", "entry-1");
        expect(entryElements[2]).toHaveAttribute("data-testid", "entry-2");

        // Verify the sort order indicator shows "desc"
        expect(screen.getByTestId("toggle-sort").textContent).toContain("desc");
    });

    it("handles adding a new entry", () => {
        render(<VerifyBulkDetails onBack={mockOnBack} onContinue={mockOnContinue} />);

        fireEvent.click(screen.getByTestId("button-primary-ghost"));

        expect(screen.getByTestId("airtime-form-add")).toBeInTheDocument();

        fireEvent.click(screen.getByTestId("submit-form"));

        expect(mockDispatch).toHaveBeenCalledWith(
            setEntries([
                ...mockEntries,
                expect.objectContaining({
                    phoneNumber: "07012345678",
                    network: "MTN",
                    amount: 5000,
                }),
            ])
        );
    });

    it("handles editing an entry", () => {
        render(<VerifyBulkDetails onBack={mockOnBack} onContinue={mockOnContinue} />);

        fireEvent.click(screen.getByTestId("edit-entry-1"));

        expect(screen.getByTestId("airtime-form-edit")).toBeInTheDocument();

        fireEvent.click(screen.getByTestId("submit-form"));

        expect(mockDispatch).toHaveBeenCalledWith(
            setEntries(
                expect.arrayContaining([
                    expect.objectContaining({
                        id: "1",
                        phoneNumber: "07012345678",
                        network: "MTN",
                        amount: 5000,
                    }),
                ])
            )
        );
    });

    it("handles deleting an entry", () => {
        render(<VerifyBulkDetails onBack={mockOnBack} onContinue={mockOnContinue} />);

        fireEvent.click(screen.getByTestId("delete-entry-1"));

        expect(screen.getByTestId("delete-confirmation")).toBeInTheDocument();

        fireEvent.click(screen.getByTestId("confirm-delete"));

        expect(mockDispatch).toHaveBeenCalledWith(
            setEntries(
                expect.arrayContaining([expect.objectContaining({ id: "2" }), expect.objectContaining({ id: "3" })])
            )
        );
    });

    it("disables continue button when there are no valid entries", () => {
        // Override the default mock implementation for this specific test
        useAppSelector.mockImplementation((selectorFn) => {
            const modifiedState = {
                bulkAirtime: {
                    entries: mockEntries,
                    validEntriesCount: 0, // Set to 0 to disable the button
                    totalValidAmount: 0,
                    filterState: {
                        filter: "All",
                        networkFilter: "Network",
                        sortOrder: null,
                    },
                },
            };

            return selectorFn(modifiedState);
        });

        render(<VerifyBulkDetails onBack={mockOnBack} onContinue={mockOnContinue} />);

        expect(screen.getByTestId("button-primary")).toBeDisabled();
    });

    it("handles navigation buttons correctly", () => {
        render(<VerifyBulkDetails onBack={mockOnBack} onContinue={mockOnContinue} />);

        fireEvent.click(screen.getByTestId("button-outline"));
        expect(mockOnBack).toHaveBeenCalled();

        fireEvent.click(screen.getByTestId("button-primary"));
        expect(mockOnContinue).toHaveBeenCalled();
    });

    it("displays 'Next' as button text instead of 'Continue'", () => {
        render(<VerifyBulkDetails onBack={mockOnBack} onContinue={mockOnContinue} />);

        const nextButton = screen.getByTestId("button-primary");
        expect(nextButton).toHaveTextContent("Next");
        expect(nextButton).not.toHaveTextContent("Continue");
    });

    it("displays 'No entries found' when filtered entries is empty", () => {
        // Override the default mock implementation for this specific test
        useAppSelector.mockImplementation((selectorFn) => {
            const modifiedState = {
                bulkAirtime: {
                    entries: [], // Empty entries array
                    validEntriesCount: 0,
                    totalValidAmount: 0,
                    filterState: {
                        filter: "All",
                        networkFilter: "Network",
                        sortOrder: null,
                    },
                },
            };

            return selectorFn(modifiedState);
        });

        render(<VerifyBulkDetails onBack={mockOnBack} onContinue={mockOnContinue} />);

        expect(screen.getByText("No entries found")).toBeInTheDocument();
    });

    it("handles exit confirmation", () => {
        const { rerender } = render(<VerifyBulkDetails onBack={mockOnBack} onContinue={mockOnContinue} />);

        // Trigger exit confirmation
        fireEvent.click(screen.getByTestId("drawer-close"));

        // Create a new mock state with showExitConfirmation set to true
        const mockedExitHandlers =
            require("@/components/page-components/dashboard/bill-payments/hooks/useExitHandlers").useExitHandlers;
        mockedExitHandlers.mockReturnValue({
            isOpen: true,
            showExitConfirmation: true,
            handleClose: jest.fn(),
            handleConfirmExit: jest.fn(),
            handleCancelExit: jest.fn(),
        });

        rerender(<VerifyBulkDetails onBack={mockOnBack} onContinue={mockOnContinue} />);

        expect(screen.getByTestId("exit-confirmation")).toBeInTheDocument();
    });

    it("displays correct page title with custom category name", () => {
        render(<VerifyBulkDetails onBack={mockOnBack} onContinue={mockOnContinue} categoryName="Data" />);

        expect(screen.getByTestId("drawer-title")).toHaveTextContent("Data bulk bill payment");
        expect(screen.getByText("Verify bulk data details")).toBeInTheDocument();
    });

    it("handles multiple entry actions in sequence", () => {
        render(<VerifyBulkDetails onBack={mockOnBack} onContinue={mockOnContinue} />);

        // Clear any previous calls
        mockDispatch.mockClear();

        // Add a new entry
        fireEvent.click(screen.getByTestId("button-primary-ghost"));
        fireEvent.click(screen.getByTestId("submit-form"));

        // Edit an entry
        fireEvent.click(screen.getByTestId("edit-entry-1"));
        fireEvent.click(screen.getByTestId("submit-form"));

        // Delete an entry
        fireEvent.click(screen.getByTestId("delete-entry-2"));
        fireEvent.click(screen.getByTestId("confirm-delete"));

        // Verify that setEntries was called for each action (3 times for add, edit, delete)
        expect(mockDispatch).toHaveBeenCalledTimes(3);

        // Verify the specific actions were called
        expect(mockDispatch).toHaveBeenCalledWith(setEntries(expect.any(Array)));
    });

    it("handles entry editing with validation", () => {
        // Mock entries to ensure we have data to edit
        const entriesForEditing = [
            { id: "1", phoneNumber: "07012345678", network: "MTN", status: "Valid", amount: 5000 },
        ];

        useAppSelector.mockImplementation((selectorFn) => {
            const fakeState = {
                bulkAirtime: {
                    entries: entriesForEditing,
                    validEntriesCount: 1,
                    totalValidAmount: 5000,
                    filterState: {
                        filter: "All",
                        networkFilter: "Network",
                        sortOrder: null,
                    },
                },
            };
            return selectorFn(fakeState);
        });

        // Mock getEntryStatus to simulate validation
        getEntryStatus.mockImplementation((phoneNumber, network, billers, amount) => {
            if (phoneNumber === "08087654321") {
                return "Invalid"; // Mock new phone number as invalid
            }
            return "Valid";
        });

        render(<VerifyBulkDetails onBack={mockOnBack} onContinue={mockOnContinue} />);

        // Edit the entry
        fireEvent.click(screen.getByTestId("edit-entry-1"));

        // Submit form with different values
        fireEvent.click(screen.getByTestId("submit-form"));

        // Verify that the entry was updated with new status
        expect(mockDispatch).toHaveBeenCalledWith(
            setEntries([
                expect.objectContaining({
                    id: "1",
                    phoneNumber: "07012345678", // From mock form data
                    network: "MTN",
                    status: "Valid", // Result of getEntryStatus
                    amount: 5000,
                }),
            ])
        );
    });

    it("handles empty entries array gracefully", () => {
        // Mock empty entries
        useAppSelector.mockImplementation((selectorFn) => {
            const fakeState = {
                bulkAirtime: {
                    entries: [],
                    validEntriesCount: 0,
                    totalValidAmount: 0,
                    filterState: {
                        filter: "All",
                        networkFilter: "Network",
                        sortOrder: null,
                    },
                },
            };
            return selectorFn(fakeState);
        });

        render(<VerifyBulkDetails onBack={mockOnBack} onContinue={mockOnContinue} />);

        // Should show no entries message and disabled continue button
        expect(screen.getByText("No entries found")).toBeInTheDocument();
        expect(screen.getByTestId("button-primary")).toBeDisabled();
    });

    it("filters entries by specific status and network", () => {
        // Test specific filter conditions to cover branch conditions
        const entriesForFiltering = [
            { id: "1", phoneNumber: "07012345678", network: "MTN", status: "Valid", amount: 5000 },
            { id: "2", phoneNumber: "08023456789", network: "Airtel", status: "Invalid", amount: 3000 },
            { id: "3", phoneNumber: "09034567890", network: "MTN", status: "Valid", amount: 10000 },
        ];

        // Set specific filter conditions (not "All" and not "Network")
        useAppSelector.mockImplementation((selectorFn) => {
            const fakeState = {
                bulkAirtime: {
                    entries: entriesForFiltering,
                    validEntriesCount: 2,
                    totalValidAmount: 15000,
                    filterState: {
                        filter: "Valid", // Specific status filter
                        networkFilter: "MTN", // Specific network filter
                        sortOrder: null,
                    },
                },
            };
            return selectorFn(fakeState);
        });

        render(<VerifyBulkDetails onBack={mockOnBack} onContinue={mockOnContinue} />);

        // Should only show entries that match both Valid status and MTN network
        // This will trigger the branch conditions where filter !== "All" and networkFilter !== "Network"
        expect(screen.getByTestId("entries-count")).toHaveTextContent("2");
        expect(screen.getByTestId("network-filter")).toHaveTextContent("MTN");
    });

    it("applies combined filtering with no matches", () => {
        // Test case where no entries match the combined filters
        const entriesForFiltering = [
            { id: "1", phoneNumber: "07012345678", network: "MTN", status: "Valid", amount: 5000 },
            { id: "2", phoneNumber: "08023456789", network: "Airtel", status: "Valid", amount: 3000 },
        ];

        // Set filter conditions that won't match any entries
        useAppSelector.mockImplementation((selectorFn) => {
            const fakeState = {
                bulkAirtime: {
                    entries: entriesForFiltering,
                    validEntriesCount: 2,
                    totalValidAmount: 8000,
                    filterState: {
                        filter: "Invalid", // No entries have this status
                        networkFilter: "Glo", // No entries have this network
                        sortOrder: null,
                    },
                },
            };
            return selectorFn(fakeState);
        });

        render(<VerifyBulkDetails onBack={mockOnBack} onContinue={mockOnContinue} />);

        // Should show no entries found due to filtering
        expect(screen.getByText("No entries found")).toBeInTheDocument();
        // Don't check entries-count since the table is not rendered when there are no filtered entries
        expect(screen.queryByTestId("airtime-table")).not.toBeInTheDocument();
    });
});
