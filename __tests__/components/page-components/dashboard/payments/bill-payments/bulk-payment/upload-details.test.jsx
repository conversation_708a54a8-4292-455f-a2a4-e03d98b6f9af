import React from "react";
import { render, screen, fireEvent, waitFor, act } from "@testing-library/react";
import { Provider } from "react-redux";
import { configureStore } from "@reduxjs/toolkit";
import { useRouter } from "next/navigation";

// Mock the module functions
import * as fileProcessingModule from "@/components/page-components/dashboard/bill-payments/bulk-payment/utils/file-processing";
import * as tableUtils from "@/components/page-components/dashboard/bill-payments/bulk-payment/table/utils";
import * as feedback from "@/functions/feedback";

// Component to test
import UploadBulkDetails from "@/components/page-components/dashboard/bill-payments/bulk-payment/upload-details";

// Mock all the imports
jest.mock("next/navigation", () => ({
    useRouter: jest.fn(),
}));

jest.mock("@/redux/hooks", () => ({
    useAppDispatch: jest.fn().mockReturnValue(jest.fn()),
    useAppSelector: jest.fn().mockImplementation((selector) => {
        const state = {
            bulkAirtime: {
                fileInfo: null,
                entries: [],
                filter: "All",
                networkFilter: "Network",
                sortOrder: null,
            },
        };
        return selector(state);
    }),
}));

// Mock Redux actions
jest.mock("@/redux/features/bulkAirtime", () => ({
    resetBulkAirtime: jest.fn(),
    setEntries: jest.fn(),
    updateFilterState: jest.fn(),
}));

// We need to track the onExit callback for testing
let mockExitHandler = null;

jest.mock("@/components/page-components/dashboard/bill-payments/hooks/useExitHandlers", () => ({
    useExitHandlers: jest.fn(({ onExit }) => {
        // Store the onExit handler for testing
        mockExitHandler = onExit;

        return {
            isOpen: true,
            showExitConfirmation: false,
            handleClose: jest.fn(),
            handleConfirmExit: jest.fn(() => {
                if (onExit) onExit();
            }),
            handleCancelExit: jest.fn(),
        };
    }),
}));

jest.mock("@/components/page-components/dashboard/bill-payments/bulk-payment/hooks/use-file-restoration", () => ({
    useFileRestoration: jest.fn().mockReturnValue({
        uploadedFile: null,
        isUploadComplete: false,
        clearFile: jest.fn(),
        storeFile: jest.fn(),
    }),
}));

jest.mock("@/components/common/stepper", () => ({
    __esModule: true,
    default: jest.fn().mockImplementation(({ steps, currentStep }) => (
        <div data-testid="stepper">
            <span>Current step: {currentStep}</span>
            <span>Total steps: {steps.length}</span>
        </div>
    )),
}));

jest.mock("@/components/common/buttonv3", () => ({
    Button: jest.fn().mockImplementation(({ children, onClick, disabled, variant }) => (
        <button data-testid={`button-${variant}`} onClick={onClick} disabled={disabled}>
            {children}
        </button>
    )),
}));

jest.mock("@/components/common/file-attachment", () => ({
    __esModule: true,
    default: jest.fn().mockImplementation(({ onFilesSelected, onFileRemoved, headerText }) => (
        <div data-testid="file-attachment">
            <span>{headerText}</span>
            <button
                data-testid="select-file-button"
                onClick={() => onFilesSelected([new File(["file content"], "test.csv", { type: "text/csv" })])}
            >
                Select File
            </button>
            <button data-testid="remove-file-button" onClick={() => onFileRemoved()}>
                Remove File
            </button>
        </div>
    )),
}));

jest.mock("@/components/common/full-screen-drawer", () => ({
    __esModule: true,
    default: jest
        .fn()
        .mockImplementation(
            ({ children, isOpen, title, onClose, showExitConfirmation, onConfirmExit, onCancelExit }) =>
                isOpen ? (
                    <div data-testid="full-screen-drawer">
                        <div data-testid="drawer-title">{title}</div>
                        {showExitConfirmation && (
                            <div data-testid="exit-confirmation">
                                <button data-testid="confirm-exit" onClick={onConfirmExit}>
                                    Yes, exit
                                </button>
                                <button data-testid="cancel-exit" onClick={onCancelExit}>
                                    No, stay
                                </button>
                            </div>
                        )}
                        <button data-testid="close-drawer" onClick={onClose}>
                            Close
                        </button>
                        {children}
                    </div>
                ) : null
        ),
}));

// Mock icons
jest.mock("@/components/icons/bill-payment-icons", () => ({
    LayoutIcon: () => <div data-testid="layout-icon" />,
    FileIcon: () => <div data-testid="file-icon" />,
    UploadIcon: () => <div data-testid="upload-icon" />,
    CsvFileIcon: () => <div data-testid="csv-file-icon" />,
}));

// Mock the utility functions
jest.mock("@/components/page-components/dashboard/bill-payments/bulk-payment/utils/file-processing", () => ({
    validateAndProcessFile: jest.fn().mockResolvedValue({
        entries: [{ id: 1, name: "Test Entry" }],
        fileInfo: { name: "test.csv", size: 100, type: "text/csv" },
    }),
}));

jest.mock("@/components/page-components/dashboard/bill-payments/bulk-payment/table/utils", () => ({
    generateSampleCSV: jest.fn().mockReturnValue("header1,header2\nvalue1,value2"),
}));

jest.mock("@/functions/feedback", () => ({
    sendFeedback: jest.fn(),
    sendCatchFeedback: jest.fn(),
}));

// Create a mock store
const mockStore = configureStore({
    reducer: {
        bulkAirtime: (
            state = {
                fileInfo: null,
                entries: [],
                filter: "All",
                networkFilter: "Network",
                sortOrder: null,
            }
        ) => state,
    },
});

describe("UploadBulkDetails Component", () => {
    const mockRouter = {
        push: jest.fn(),
    };

    beforeEach(() => {
        jest.clearAllMocks();
        useRouter.mockReturnValue(mockRouter);
        mockExitHandler = null;

        // Mock the useFileRestoration hook with default values
        // eslint-disable-next-line @typescript-eslint/no-require-imports
        require("@/components/page-components/dashboard/bill-payments/bulk-payment/hooks/use-file-restoration").useFileRestoration.mockReturnValue(
            {
                uploadedFile: null,
                isUploadComplete: false,
                clearFile: jest.fn(),
                storeFile: jest.fn(),
            }
        );

        // Reset the mocks to default values
        fileProcessingModule.validateAndProcessFile.mockResolvedValue({
            entries: [{ id: 1, name: "Test Entry" }],
            fileInfo: { name: "test.csv", size: 100, type: "text/csv" },
        });
    });

    it("renders component with correct title and category", () => {
        render(
            <Provider store={mockStore}>
                <UploadBulkDetails categoryId={5} categoryName="Data" />
            </Provider>
        );

        expect(screen.getByTestId("drawer-title")).toHaveTextContent("Bulk Data bill payment");
        expect(screen.getByText("Upload bulk Data details")).toBeInTheDocument();
    });

    it("continues to the next page when button is clicked and file is uploaded", async () => {
        // Override hook to return a valid file and upload state
        // eslint-disable-next-line @typescript-eslint/no-require-imports
        require("@/components/page-components/dashboard/bill-payments/bulk-payment/hooks/use-file-restoration").useFileRestoration.mockReturnValue(
            {
                uploadedFile: new File(["file content"], "test.csv", { type: "text/csv" }),
                isUploadComplete: true,
                clearFile: jest.fn(),
                storeFile: jest.fn(),
            }
        );

        render(
            <Provider store={mockStore}>
                <UploadBulkDetails categoryId={4} categoryName="Airtime" />
            </Provider>
        );

        const continueButton = screen.getByTestId("button-primary");

        // Button should be enabled since we have a file
        expect(continueButton).not.toBeDisabled();

        fireEvent.click(continueButton);

        // Should navigate to the verification page
        expect(mockRouter.push).toHaveBeenCalledWith("/payments/bill-payments/4/bulk/verify");
    });

    it("calls the onContinue prop when provided instead of routing", async () => {
        // Override hook to return a valid file and upload state
        // eslint-disable-next-line @typescript-eslint/no-require-imports
        require("@/components/page-components/dashboard/bill-payments/bulk-payment/hooks/use-file-restoration").useFileRestoration.mockReturnValue(
            {
                uploadedFile: new File(["file content"], "test.csv", { type: "text/csv" }),
                isUploadComplete: true,
                clearFile: jest.fn(),
                storeFile: jest.fn(),
            }
        );

        const mockOnContinue = jest.fn();

        render(
            <Provider store={mockStore}>
                <UploadBulkDetails categoryId={4} categoryName="Airtime" onContinue={mockOnContinue} />
            </Provider>
        );

        const continueButton = screen.getByTestId("button-primary");
        fireEvent.click(continueButton);

        // Should call the custom onContinue handler instead of routing
        expect(mockOnContinue).toHaveBeenCalled();
        expect(mockRouter.push).not.toHaveBeenCalled();
    });

    it("displays continue button as disabled when file is not uploaded", () => {
        render(
            <Provider store={mockStore}>
                <UploadBulkDetails />
            </Provider>
        );

        const continueButton = screen.getByTestId("button-primary");
        expect(continueButton).toBeDisabled();
    });

    it("displays 'Next' as button text instead of 'Continue'", () => {
        render(
            <Provider store={mockStore}>
                <UploadBulkDetails />
            </Provider>
        );

        const nextButton = screen.getByTestId("button-primary");
        expect(nextButton).toHaveTextContent("Next");
        expect(nextButton).not.toHaveTextContent("Continue");
    });

    it("processes a file when selected and calls storeFile", async () => {
        const mockStoreFile = jest.fn();

        // Set up the hook with our spy
        // eslint-disable-next-line @typescript-eslint/no-require-imports
        require("@/components/page-components/dashboard/bill-payments/bulk-payment/hooks/use-file-restoration").useFileRestoration.mockReturnValue(
            {
                uploadedFile: null,
                isUploadComplete: false,
                clearFile: jest.fn(),
                storeFile: mockStoreFile,
            }
        );

        render(
            <Provider store={mockStore}>
                <UploadBulkDetails />
            </Provider>
        );

        const selectFileButton = screen.getByTestId("select-file-button");

        await act(async () => {
            fireEvent.click(selectFileButton);
        });

        // File processing and storing should be called
        await waitFor(() => {
            expect(fileProcessingModule.validateAndProcessFile).toHaveBeenCalled();
            expect(mockStoreFile).toHaveBeenCalled();
        });
    });

    it("clears the file when remove button is clicked", async () => {
        const mockClearFile = jest.fn();

        // Set up the hook with a file and our spy
        // eslint-disable-next-line @typescript-eslint/no-require-imports
        require("@/components/page-components/dashboard/bill-payments/bulk-payment/hooks/use-file-restoration").useFileRestoration.mockReturnValue(
            {
                uploadedFile: new File(["file content"], "test.csv", { type: "text/csv" }),
                isUploadComplete: true,
                clearFile: mockClearFile,
                storeFile: jest.fn(),
            }
        );

        render(
            <Provider store={mockStore}>
                <UploadBulkDetails />
            </Provider>
        );

        const removeFileButton = screen.getByTestId("remove-file-button");

        fireEvent.click(removeFileButton);

        expect(mockClearFile).toHaveBeenCalled();
    });

    it("downloads sample CSV when download button is clicked", async () => {
        // First, render the component without any DOM mocking
        render(
            <Provider store={mockStore}>
                <UploadBulkDetails categoryName="Airtime" />
            </Provider>
        );

        // Setup mocks AFTER rendering
        const mockLink = {
            setAttribute: jest.fn(),
            click: jest.fn(),
        };

        // Save original methods
        const originalCreateElement = document.createElement;
        const originalAppendChild = document.body.appendChild;
        const originalRemoveChild = document.body.removeChild;
        const originalCreateObjectURL = URL.createObjectURL;

        // Apply mocks
        document.createElement = jest.fn().mockReturnValue(mockLink);
        document.body.appendChild = jest.fn();
        document.body.removeChild = jest.fn();
        URL.createObjectURL = jest.fn().mockReturnValue("mock-url");

        // Now find and click the download button
        const downloadButtons = screen.getAllByText("Download");
        fireEvent.click(downloadButtons[0]);

        // Verify the expected behavior
        expect(tableUtils.generateSampleCSV).toHaveBeenCalled();
        expect(document.createElement).toHaveBeenCalledWith("a");
        expect(mockLink.setAttribute).toHaveBeenCalledWith("href", "mock-url");
        expect(mockLink.setAttribute).toHaveBeenCalledWith("download", "bulk_airtime_template.csv");
        expect(mockLink.click).toHaveBeenCalled();

        // Restore original methods
        document.createElement = originalCreateElement;
        document.body.appendChild = originalAppendChild;
        document.body.removeChild = originalRemoveChild;
        URL.createObjectURL = originalCreateObjectURL;
    });

    it("shows loading state while processing file", async () => {
        // Override the validateAndProcessFile mock for this test
        fileProcessingModule.validateAndProcessFile.mockImplementation(
            () =>
                new Promise((resolve) => {
                    setTimeout(() => {
                        resolve({
                            entries: [{ id: 1, name: "Test Entry" }],
                            fileInfo: { name: "test.csv", size: 100, type: "text/csv" },
                        });
                    }, 100);
                })
        );

        render(
            <Provider store={mockStore}>
                <UploadBulkDetails />
            </Provider>
        );

        const selectFileButton = screen.getByTestId("select-file-button");

        await act(async () => {
            fireEvent.click(selectFileButton);
        });

        // Should show loading state
        expect(screen.getByText("Validating your CSV file...")).toBeInTheDocument();

        // Wait for processing to complete
        await waitFor(
            () => {
                expect(fileProcessingModule.validateAndProcessFile).toHaveBeenCalled();
            },
            { timeout: 200 }
        );
    });

    it("handles file processing errors", async () => {
        // Override validateAndProcessFile for this test to simulate an error
        fileProcessingModule.validateAndProcessFile.mockRejectedValue(new Error("Invalid file format"));

        const mockClearFile = jest.fn();
        // eslint-disable-next-line @typescript-eslint/no-require-imports
        require("@/components/page-components/dashboard/bill-payments/bulk-payment/hooks/use-file-restoration").useFileRestoration.mockReturnValue(
            {
                uploadedFile: null,
                isUploadComplete: false,
                clearFile: mockClearFile,
                storeFile: jest.fn(),
            }
        );

        render(
            <Provider store={mockStore}>
                <UploadBulkDetails />
            </Provider>
        );

        const selectFileButton = screen.getByTestId("select-file-button");

        await act(async () => {
            fireEvent.click(selectFileButton);
        });

        // Should handle the error
        await waitFor(() => {
            expect(feedback.sendFeedback).toHaveBeenCalledWith("Invalid file format", "error");
            expect(mockClearFile).toHaveBeenCalled();
        });
    });

    it("calls clearFile and resetBulkAirtime when exit handler is triggered", () => {
        // Get access to dispatch function
        const mockDispatch = jest.fn();
        // eslint-disable-next-line @typescript-eslint/no-require-imports
        require("@/redux/hooks").useAppDispatch.mockReturnValue(mockDispatch);

        // Create a spy for clearFile
        const mockClearFile = jest.fn();

        // Mock the useFileRestoration hook with our spy
        // eslint-disable-next-line @typescript-eslint/no-require-imports
        require("@/components/page-components/dashboard/bill-payments/bulk-payment/hooks/use-file-restoration").useFileRestoration.mockReturnValue(
            {
                uploadedFile: new File(["file content"], "test.csv", { type: "text/csv" }),
                isUploadComplete: true,
                clearFile: mockClearFile,
                storeFile: jest.fn(),
            }
        );

        // Get the redux action
        // eslint-disable-next-line @typescript-eslint/no-require-imports
        const { resetBulkAirtime } = require("@/redux/features/bulkAirtime");

        // Render the component
        render(
            <Provider store={mockStore}>
                <UploadBulkDetails />
            </Provider>
        );

        // Verify the exit handler was registered
        expect(mockExitHandler).toBeDefined();

        // Call the exit handler directly
        mockExitHandler();

        // Verify it calls the right functions
        expect(mockClearFile).toHaveBeenCalledTimes(1);
        expect(mockDispatch).toHaveBeenCalledWith(resetBulkAirtime());
    });
});
