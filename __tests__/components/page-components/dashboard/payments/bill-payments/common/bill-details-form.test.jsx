/**
 * @file bill-details-form.test.jsx
 *
 * @purpose Test suite for the BillDetailsForm component that validates form rendering, prop passing, and component interactions.
 *
 * @functionality This test file verifies that the BillDetailsForm component correctly renders all required
 * sub-components (ServiceProviderSelect, PackageSelect, AmountInput), passes props appropriately, handles
 * package selection callbacks, renders children, and manages amount field state based on package configuration.
 * It uses comprehensive mocking to isolate the component under test and validate its behavior across different
 * scenarios including fixed-amount packages and variable-amount packages.
 *
 * @dependencies
 * - React Testing Library: For component rendering and DOM queries
 * - Jest: For test framework and mocking capabilities
 * - Redux hooks: Mocked to provide controlled state for testing
 * - Sub-components: Mocked to isolate testing focus
 *
 * @usage Run this test file as part of the Jest test suite to validate BillDetailsForm component behavior.
 * The tests cover component rendering, prop validation, user interactions, and conditional logic for amount
 * field state management based on package selection.
 */

import { render, screen } from "@testing-library/react";
import BillDetailsForm from "@/components/page-components/dashboard/bill-payments/common/bill-details-form";

// Mock the required components
jest.mock("@/redux/hooks", () => ({
    useAppSelector: jest.fn((selector) => {
        // Mock the Redux state
        const state = {
            billPayments: {
                biller: {
                    data: [
                        {
                            billername: "Test Provider",
                            billerid: "123",
                        },
                        {
                            billerName: "Another Provider",
                            billerId: "456",
                        },
                    ],
                },
            },
            singleBillPayment: {
                selectedPackage: null,
            },
        };
        return selector(state);
    }),
}));

jest.mock("@/components/page-components/dashboard/bill-payments/common/error-boundary", () => ({
    __esModule: true,
    default: ({ children, message }) => (
        <div data-testid="error-boundary" data-error-message={message}>
            {children}
        </div>
    ),
}));

jest.mock("@/components/page-components/dashboard/bill-payments/common/service-provider-select", () => ({
    __esModule: true,
    default: ({ formik, categoryId }) => (
        <div data-testid="service-provider-select" data-category-id={categoryId}>
            Service Provider Select
        </div>
    ),
}));

jest.mock("@/components/page-components/dashboard/bill-payments/common/package-select", () => ({
    __esModule: true,
    default: ({ formik, onPackageSelect, selectedBillerId }) => (
        <div
            data-testid="package-select"
            data-biller-id={selectedBillerId}
            onClick={() => onPackageSelect && onPackageSelect({ id: "123", name: "Test Package" })}
        >
            Package Select
        </div>
    ),
}));

jest.mock("@/components/common/amount-input", () => ({
    __esModule: true,
    default: ({ formik, label, name, className, disabled }) => (
        <div
            data-testid="amount-input"
            data-label={label}
            data-name={name}
            data-classname={className}
            data-disabled={disabled}
        >
            Amount Input
        </div>
    ),
}));

describe("BillDetailsForm Component", () => {
    // Common props for testing
    const formikMock = {
        values: {
            serviceProvider: "Test Provider",
            package: "Test Package",
            amount: "1000",
        },
        errors: {},
        touched: {},
        handleChange: jest.fn(),
        handleBlur: jest.fn(),
        setFieldValue: jest.fn(),
    };

    const onPackageSelectMock = jest.fn();
    const categoryId = 24;

    // Reset mocks before each test
    beforeEach(() => {
        jest.clearAllMocks();
    });

    test("renders all required components", () => {
        render(<BillDetailsForm formik={formikMock} categoryId={categoryId} onPackageSelect={onPackageSelectMock} />);

        // Check that all components are rendered
        expect(screen.getByTestId("error-boundary")).toBeInTheDocument();
        expect(screen.getByTestId("service-provider-select")).toBeInTheDocument();
        expect(screen.getByTestId("package-select")).toBeInTheDocument();
        expect(screen.getByTestId("amount-input")).toBeInTheDocument();
    });

    test("passes correct props to child components", () => {
        render(<BillDetailsForm formik={formikMock} categoryId={categoryId} onPackageSelect={onPackageSelectMock} />);

        // Check that ServiceProviderSelect gets the correct categoryId
        const serviceProviderSelect = screen.getByTestId("service-provider-select");
        expect(serviceProviderSelect).toHaveAttribute("data-category-id", categoryId.toString());

        // Check that PackageSelect gets the correct billerId
        const packageSelect = screen.getByTestId("package-select");
        expect(packageSelect).toHaveAttribute("data-biller-id", "123");

        // Check that AmountInput has correct props
        const amountInput = screen.getByTestId("amount-input");
        expect(amountInput).toHaveAttribute("data-label", "Amount");
        expect(amountInput).toHaveAttribute("data-name", "amount");

        // Check that AmountInput has no special className by default
        expect(amountInput).toHaveAttribute("data-classname", "");
    });

    test("calls onPackageSelect when package is selected", () => {
        render(<BillDetailsForm formik={formikMock} categoryId={categoryId} onPackageSelect={onPackageSelectMock} />);

        // Trigger package selection
        const packageSelect = screen.getByTestId("package-select");
        packageSelect.click();

        // Verify the callback was called with the expected data
        expect(onPackageSelectMock).toHaveBeenCalledWith({ id: "123", name: "Test Package" });
    });

    test("renders children when provided", () => {
        render(
            <BillDetailsForm formik={formikMock} categoryId={categoryId} onPackageSelect={onPackageSelectMock}>
                <div data-testid="child-element">Test Child</div>
            </BillDetailsForm>
        );

        // Check that the child element is rendered
        expect(screen.getByTestId("child-element")).toBeInTheDocument();
        expect(screen.getByText("Test Child")).toBeInTheDocument();
    });

    test("applies disabled styling and disables amount input when selected package has amountFixed set to true", () => {
        // Update the mock implementation for this specific test
        const { useAppSelector } = require("@/redux/hooks");
        useAppSelector.mockImplementation((selector) => {
            const state = {
                billPayments: {
                    biller: {
                        data: [
                            {
                                billername: "Test Provider",
                                billerid: "123",
                            },
                        ],
                    },
                },
                singleBillPayment: {
                    selectedPackage: {
                        id: "123",
                        name: "Fixed Amount Package",
                        amountFixed: true,
                        amount: 5000,
                    },
                },
            };
            return selector(state);
        });

        render(<BillDetailsForm formik={formikMock} categoryId={categoryId} onPackageSelect={onPackageSelectMock} />);

        // Check that AmountInput has disabled styling and is disabled
        const amountInput = screen.getByTestId("amount-input");
        expect(amountInput).toHaveAttribute("data-classname", "opacity-50 cursor-not-allowed");
        expect(amountInput).toHaveAttribute("data-disabled", "true");
    });

    test("does not apply disabled styling and enables amount input when selected package has amountFixed set to false", () => {
        // Update the mock implementation for this specific test
        const { useAppSelector } = require("@/redux/hooks");
        useAppSelector.mockImplementation((selector) => {
            const state = {
                billPayments: {
                    biller: {
                        data: [
                            {
                                billername: "Test Provider",
                                billerid: "123",
                            },
                        ],
                    },
                },
                singleBillPayment: {
                    selectedPackage: {
                        id: "123",
                        name: "Variable Amount Package",
                        amountFixed: false,
                    },
                },
            };
            return selector(state);
        });

        render(<BillDetailsForm formik={formikMock} categoryId={categoryId} onPackageSelect={onPackageSelectMock} />);

        // Check that AmountInput does not have disabled styling and is not disabled
        const amountInput = screen.getByTestId("amount-input");
        expect(amountInput).toHaveAttribute("data-classname", "");
        expect(amountInput).toHaveAttribute("data-disabled", "false");
    });

    test("validates that amount must be greater than 0", () => {
        // Test with zero amount
        const formikWithZeroAmount = {
            ...formikMock,
            values: {
                ...formikMock.values,
                amount: "0",
            },
            errors: {
                amount: "Amount must be greater than 0",
            },
            touched: {
                amount: true,
            },
        };

        render(
            <BillDetailsForm
                formik={formikWithZeroAmount}
                categoryId={categoryId}
                onPackageSelect={onPackageSelectMock}
            />
        );

        const amountInput = screen.getByTestId("amount-input");
        expect(amountInput).toBeInTheDocument();
    });

    test("validates that amount must be a valid number", () => {
        // Test with invalid number
        const formikWithInvalidAmount = {
            ...formikMock,
            values: {
                ...formikMock.values,
                amount: "abc",
            },
            errors: {
                amount: "Amount must be a valid number",
            },
            touched: {
                amount: true,
            },
        };

        render(
            <BillDetailsForm
                formik={formikWithInvalidAmount}
                categoryId={categoryId}
                onPackageSelect={onPackageSelectMock}
            />
        );

        const amountInput = screen.getByTestId("amount-input");
        expect(amountInput).toBeInTheDocument();
    });

    test("shows amount required error when amount field is empty", () => {
        // Test that amount field shows required error
        const formikWithErrors = {
            ...formikMock,
            values: {
                ...formikMock.values,
                amount: "",
            },
            errors: {
                amount: "Amount is required",
            },
            touched: {
                amount: true,
            },
        };

        render(
            <BillDetailsForm formik={formikWithErrors} categoryId={categoryId} onPackageSelect={onPackageSelectMock} />
        );

        const amountInput = screen.getByTestId("amount-input");
        expect(amountInput).toBeInTheDocument();
    });

    test("shows amount greater than 0 error when amount is zero", () => {
        // Test that amount field shows greater than 0 error
        const formikWithErrors = {
            ...formikMock,
            values: {
                ...formikMock.values,
                amount: "0",
            },
            errors: {
                amount: "Amount must be greater than 0",
            },
            touched: {
                amount: true,
            },
        };

        render(
            <BillDetailsForm formik={formikWithErrors} categoryId={categoryId} onPackageSelect={onPackageSelectMock} />
        );

        const amountInput = screen.getByTestId("amount-input");
        expect(amountInput).toBeInTheDocument();
    });
});
