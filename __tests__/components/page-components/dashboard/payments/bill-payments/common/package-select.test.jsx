import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import PackageSelect from "@/components/page-components/dashboard/bill-payments/common/package-select";
import { useAppSelector, useAppDispatch } from "@/redux/hooks";
import { usePaymentItems } from "@/components/page-components/dashboard/bill-payments/hooks/use-payment-items";
import { updateSelectedPackage } from "@/redux/slices/singleBillPayment";

// Mock the redux hooks
jest.mock("@/redux/hooks", () => ({
    useAppSelector: jest.fn(),
    useAppDispatch: jest.fn(),
}));

// Mock the custom hook
jest.mock("@/components/page-components/dashboard/bill-payments/hooks/use-payment-items", () => ({
    usePaymentItems: jest.fn(),
}));

// Mock the redux action
jest.mock("@/redux/slices/singleBillPayment", () => ({
    updateSelectedPackage: jest.fn(),
}));

// Mock form-field component
jest.mock("@/components/page-components/dashboard/bill-payments/common/form-field", () => ({
    __esModule: true,
    default: ({ label, name, touched, error, children }) => (
        <div data-testid={`form-field-${name}`}>
            <label>{label}</label>
            {children}
            {touched && error && <div data-testid={`error-${name}`}>{error}</div>}
        </div>
    ),
}));

// Mock dropdown component
jest.mock("@/components/common/dropdown", () => ({
    __esModule: true,
    default: ({ options, value, onChange, isDisabled, isLoading, name, placeholder }) => (
        <div data-testid={`dropdown-${name}`}>
            <select
                disabled={isDisabled}
                value={value?.label || ""}
                onChange={(e) => onChange(e.target.value)}
                data-testid={`select-${name}`}
            >
                <option value="">{placeholder}</option>
                {options.map((option, index) => (
                    <option key={index} value={option.label}>
                        {option.label}
                    </option>
                ))}
            </select>
            {isLoading && <div data-testid="loading-indicator">Loading...</div>}
        </div>
    ),
}));

describe("PackageSelect", () => {
    // Test data
    const packageOptions = [
        { value: "pkg1", label: "Package 1", amountFixed: false, amount: "500" },
        { value: "pkg2", label: "Package 2", amountFixed: true, amount: "1000" },
    ];

    const mockFormik = {
        values: {
            serviceProvider: "Test Provider",
            package: "",
        },
        errors: {},
        touched: {},
        setFieldValue: jest.fn(),
        setFieldTouched: jest.fn(),
    };

    const mockOnPackageSelect = jest.fn();
    let mockDispatch;

    beforeEach(() => {
        jest.clearAllMocks();

        // Setup mocks
        mockDispatch = jest.fn();
        useAppDispatch.mockReturnValue(mockDispatch);

        // Mock Redux state
        useAppSelector.mockImplementation((selector) => {
            // Create a mock state
            const state = {
                billPayments: {
                    biller: {
                        data: [
                            {
                                billername: "Test Provider",
                                billerid: "test123",
                            },
                        ],
                    },
                },
                singleBillPayment: {
                    selectedPackage: null,
                },
            };
            return selector(state);
        });

        // Mock usePaymentItems hook
        usePaymentItems.mockReturnValue({
            packageOptions,
            loading: false,
            findPaymentItemByName: (name) => packageOptions.find((pkg) => pkg.label === name),
        });

        // Mock setTimeout
        jest.useFakeTimers();
    });

    afterEach(() => {
        jest.useRealTimers();
    });

    it("renders correctly when service provider is valid", () => {
        render(<PackageSelect formik={mockFormik} onPackageSelect={mockOnPackageSelect} />);

        expect(screen.getByTestId("form-field-package")).toBeInTheDocument();
        expect(screen.getByTestId("dropdown-package")).toBeInTheDocument();
        expect(screen.getByText("Package")).toBeInTheDocument();
    });

    it("disables the dropdown when service provider is invalid", () => {
        // Set up invalid service provider
        const invalidFormik = {
            ...mockFormik,
            values: { ...mockFormik.values, serviceProvider: "" },
            errors: { serviceProvider: "Required" },
        };

        render(<PackageSelect formik={invalidFormik} onPackageSelect={mockOnPackageSelect} />);

        expect(screen.getByTestId("select-package")).toBeDisabled();
    });

    it("initializes with value from Redux store when available", () => {
        // Mock selected package in Redux store
        useAppSelector.mockImplementation((selector) => {
            const state = {
                billPayments: {
                    biller: {
                        data: [
                            {
                                billername: "Test Provider",
                                billerid: "test123",
                            },
                        ],
                    },
                },
                singleBillPayment: {
                    selectedPackage: {
                        paymentCode: "pkg2",
                        amountFixed: true,
                        amount: 1000,
                    },
                },
            };
            return selector(state);
        });

        render(<PackageSelect formik={mockFormik} onPackageSelect={mockOnPackageSelect} />);

        // Verify formik values are set - amount is ALWAYS populated from API
        expect(mockFormik.setFieldValue).toHaveBeenCalledWith("package", "Package 2", false);
        expect(mockFormik.setFieldValue).toHaveBeenCalledWith("amount", 1000, false);
    });

    it("handles package selection correctly", () => {
        render(<PackageSelect formik={mockFormik} onPackageSelect={mockOnPackageSelect} />);

        // Select a package
        fireEvent.change(screen.getByTestId("select-package"), { target: { value: "Package 2" } });

        // Verify form value is updated
        expect(mockFormik.setFieldValue).toHaveBeenCalledWith("package", "Package 2", false);

        // Verify package item is found and passed to callback
        expect(mockOnPackageSelect).toHaveBeenCalledWith(packageOptions[1]);

        // Verify Redux action is dispatched
        expect(updateSelectedPackage).toHaveBeenCalledWith(packageOptions[1]);

        // Verify amount is ALWAYS set from API response
        expect(mockFormik.setFieldValue).toHaveBeenCalledWith("amount", "1000", false);

        // Advance timers to trigger setFieldTouched
        jest.runAllTimers();
        expect(mockFormik.setFieldTouched).toHaveBeenCalledWith("package", true, true);
    });

    it("handles clearing package selection correctly", () => {
        render(<PackageSelect formik={mockFormik} onPackageSelect={mockOnPackageSelect} />);

        // Clear the package selection
        fireEvent.change(screen.getByTestId("select-package"), { target: { value: "" } });

        // Verify form value is updated
        expect(mockFormik.setFieldValue).toHaveBeenCalledWith("package", "", false);

        // Verify callback is called with null
        expect(mockOnPackageSelect).toHaveBeenCalledWith(null);

        // Verify Redux action is dispatched with null
        expect(updateSelectedPackage).toHaveBeenCalledWith(null);

        // Advance timers to trigger setFieldTouched
        jest.runAllTimers();
        expect(mockFormik.setFieldTouched).toHaveBeenCalledWith("package", true, true);
    });

    it("populates amount field for variable amount packages (amountFixed: false)", () => {
        render(<PackageSelect formik={mockFormik} onPackageSelect={mockOnPackageSelect} />);

        // Select Package 1 which has amountFixed: false
        fireEvent.change(screen.getByTestId("select-package"), { target: { value: "Package 1" } });

        // Verify form value is updated
        expect(mockFormik.setFieldValue).toHaveBeenCalledWith("package", "Package 1", false);

        // Verify package item is found and passed to callback
        expect(mockOnPackageSelect).toHaveBeenCalledWith(packageOptions[0]);

        // Verify Redux action is dispatched
        expect(updateSelectedPackage).toHaveBeenCalledWith(packageOptions[0]);

        // Verify amount is STILL set even though amountFixed is false
        expect(mockFormik.setFieldValue).toHaveBeenCalledWith("amount", "500", false);

        // Advance timers to trigger setFieldTouched
        jest.runAllTimers();
        expect(mockFormik.setFieldTouched).toHaveBeenCalledWith("package", true, true);
    });

    it("shows loading state when fetching payment items", () => {
        // Mock loading state
        usePaymentItems.mockReturnValue({
            packageOptions: [],
            loading: true,
            findPaymentItemByName: jest.fn(),
        });

        render(<PackageSelect formik={mockFormik} onPackageSelect={mockOnPackageSelect} />);

        expect(screen.getByTestId("loading-indicator")).toBeInTheDocument();
    });

    it("finds package by partial label match when exact match is not found", () => {
        // Setup Redux store with a package that uses partial matching
        useAppSelector.mockImplementation((selector) => {
            const state = {
                billPayments: {
                    biller: {
                        data: [
                            {
                                billername: "Test Provider",
                                billerid: "test123",
                            },
                        ],
                    },
                },
                singleBillPayment: {
                    selectedPackage: {
                        paymentCode: "My Package 1 with extra text", // This contains "Package 1" but isn't an exact match
                        amountFixed: false,
                        amount: 0,
                    },
                },
            };
            return selector(state);
        });

        render(<PackageSelect formik={mockFormik} onPackageSelect={mockOnPackageSelect} />);

        // Verify the component found the partial match and set the form value
        expect(mockFormik.setFieldValue).toHaveBeenCalledWith("package", "Package 1", false);
    });

    it("returns undefined when no matching package is found", () => {
        // Mock usePaymentItems hook with custom implementation first
        usePaymentItems.mockReturnValue({
            packageOptions,
            loading: false,
            findPaymentItemByName: jest.fn().mockReturnValue(null),
        });

        // Clear any previous calls to setFieldValue
        mockFormik.setFieldValue.mockClear();
        mockOnPackageSelect.mockClear();
        updateSelectedPackage.mockClear();

        // Create a formik object with a non-existent package
        const formikWithNonExistentPackage = {
            ...mockFormik,
            values: {
                ...mockFormik.values,
                serviceProvider: "Test Provider",
                package: "Non-existent Package",
            },
        };

        render(<PackageSelect formik={formikWithNonExistentPackage} onPackageSelect={mockOnPackageSelect} />);

        // Simulate selecting a non-existent package
        fireEvent.change(screen.getByTestId("select-package"), { target: { value: "Unknown Package" } });

        // Verify the correct callbacks are made
        expect(mockFormik.setFieldValue).toHaveBeenCalledWith("package", "", false);
        expect(mockOnPackageSelect).toHaveBeenCalledWith(null);
        expect(updateSelectedPackage).toHaveBeenCalledWith(null);
    });

    it("handles null or undefined package values correctly", () => {
        // Setup test specific mocks
        mockFormik.setFieldValue.mockClear();
        mockOnPackageSelect.mockClear();
        updateSelectedPackage.mockClear();

        // Create a simpler test that doesn't rely on accessing onChange prop
        // Test directly with the select element's change event
        render(<PackageSelect formik={mockFormik} onPackageSelect={mockOnPackageSelect} />);

        // Simulate a change with an empty value, which will clear the selection
        fireEvent.change(screen.getByTestId("select-package"), {
            target: { value: "" },
        });

        // Verify empty string selection (equivalent to null/undefined handling)
        expect(mockFormik.setFieldValue).toHaveBeenCalledWith("package", "", false);
        expect(mockOnPackageSelect).toHaveBeenCalledWith(null);
        expect(updateSelectedPackage).toHaveBeenCalledWith(null);
    });

    it("initializes form values when selectedPackage exists but formik package is empty", () => {
        // Mock selectedPackage in Redux store with a package that has formik.values.package empty
        useAppSelector.mockImplementation((selector) => {
            const state = {
                billPayments: {
                    biller: {
                        data: [
                            {
                                billername: "Test Provider",
                                billerid: "test123",
                            },
                        ],
                    },
                },
                singleBillPayment: {
                    selectedPackage: {
                        paymentCode: "Package 1",
                        amountFixed: false,
                        amount: 500,
                    },
                },
            };
            return selector(state);
        });

        // Create formik with empty package value
        const formikWithEmptyPackage = {
            ...mockFormik,
            values: {
                ...mockFormik.values,
                package: "", // Empty package to trigger the useEffect branch
            },
        };

        render(<PackageSelect formik={formikWithEmptyPackage} onPackageSelect={mockOnPackageSelect} />);

        // Verify the useEffect sets the form value when selectedPackage exists and package is empty
        expect(mockFormik.setFieldValue).toHaveBeenCalledWith("package", "Package 1", false);
    });

    it("handles dropdown onChange with object value", () => {
        // Create a custom mock for the dropdown that accepts object values
        const MockDropdownWithObject = ({ onChange, options, value }) => (
            <div data-testid="dropdown-package">
                <select
                    data-testid="select-package"
                    value={value?.label || ""}
                    onChange={(e) => {
                        // Simulate the dropdown passing an object instead of string
                        const selectedOption = options.find((opt) => opt.label === e.target.value);
                        if (selectedOption) {
                            onChange(selectedOption); // Pass object instead of string
                        } else {
                            onChange(e.target.value);
                        }
                    }}
                >
                    <option value="">Search packages...</option>
                    {options.map((option, index) => (
                        <option key={index} value={option.label}>
                            {option.label}
                        </option>
                    ))}
                </select>
            </div>
        );

        // Temporarily replace the dropdown mock
        const originalDropdownMock = require("@/components/common/dropdown").default;
        require("@/components/common/dropdown").default = MockDropdownWithObject;

        render(<PackageSelect formik={mockFormik} onPackageSelect={mockOnPackageSelect} />);

        // Select a package to trigger the object branch
        fireEvent.change(screen.getByTestId("select-package"), { target: { value: "Package 1" } });

        // Verify the object branch is handled correctly
        expect(mockFormik.setFieldValue).toHaveBeenCalledWith("package", "Package 1", false);

        // Restore original mock
        require("@/components/common/dropdown").default = originalDropdownMock;
    });

    it("handles case when findPaymentItemByName returns null", () => {
        // Mock usePaymentItems to return a findPaymentItemByName that returns null
        usePaymentItems.mockReturnValue({
            packageOptions,
            loading: false,
            findPaymentItemByName: jest.fn().mockReturnValue(null), // Always return null
        });

        render(<PackageSelect formik={mockFormik} onPackageSelect={mockOnPackageSelect} />);

        // Select a package that won't be found
        fireEvent.change(screen.getByTestId("select-package"), { target: { value: "Unknown Package" } });

        // Verify the else branch when paymentItem is null
        expect(mockOnPackageSelect).toHaveBeenCalledWith(null);
        expect(updateSelectedPackage).toHaveBeenCalledWith(null);
    });

    it("initializes form values when selectedPackage has amountFixed false", () => {
        // Mock selectedPackage in Redux store with amountFixed: false
        useAppSelector.mockImplementation((selector) => {
            const state = {
                billPayments: {
                    biller: {
                        data: [
                            {
                                billername: "Test Provider",
                                billerid: "test123",
                            },
                        ],
                    },
                },
                singleBillPayment: {
                    selectedPackage: {
                        paymentCode: "Package 1",
                        amountFixed: false,
                        amount: 500,
                    },
                },
            };
            return selector(state);
        });

        // Create formik with empty package value
        const formikWithEmptyPackage = {
            ...mockFormik,
            values: {
                ...mockFormik.values,
                package: "", // Empty package to trigger the useEffect branch
            },
        };

        render(<PackageSelect formik={formikWithEmptyPackage} onPackageSelect={mockOnPackageSelect} />);

        // Verify the useEffect sets the package AND the amount (always populated now)
        expect(mockFormik.setFieldValue).toHaveBeenCalledWith("package", "Package 1", false);
        expect(mockFormik.setFieldValue).toHaveBeenCalledWith("amount", 500, false);
    });

    it("handles dropdown onChange with null/undefined object", () => {
        // Create a custom mock that passes null/undefined as object
        const MockDropdownWithNull = ({ onChange, options, value }) => (
            <div data-testid="dropdown-package">
                <button
                    data-testid="select-null-package"
                    onClick={() => onChange(null)} // Pass null as object
                >
                    Select Null
                </button>
                <button
                    data-testid="select-undefined-package"
                    onClick={() => onChange(undefined)} // Pass undefined as object
                >
                    Select Undefined
                </button>
            </div>
        );

        // Temporarily replace the dropdown mock
        const originalDropdownMock = require("@/components/common/dropdown").default;
        require("@/components/common/dropdown").default = MockDropdownWithNull;

        render(<PackageSelect formik={mockFormik} onPackageSelect={mockOnPackageSelect} />);

        // Test null object
        fireEvent.click(screen.getByTestId("select-null-package"));
        expect(mockFormik.setFieldValue).toHaveBeenCalledWith("package", "", false);
        expect(mockOnPackageSelect).toHaveBeenCalledWith(null);

        // Test undefined object
        fireEvent.click(screen.getByTestId("select-undefined-package"));
        expect(mockFormik.setFieldValue).toHaveBeenCalledWith("package", "", false);
        expect(mockOnPackageSelect).toHaveBeenCalledWith(null);

        // Restore original mock
        require("@/components/common/dropdown").default = originalDropdownMock;
    });

    it("always populates amount field from API response when amountFixed is true", () => {
        // Mock payment items with specific amount values
        const paymentItemWithAmount = {
            value: "pkg-fixed",
            label: "Fixed Amount Package",
            amountFixed: true,
            amount: "2500.00", // API returns amount as string
        };

        usePaymentItems.mockReturnValue({
            packageOptions: [paymentItemWithAmount],
            loading: false,
            findPaymentItemByName: (name) => (name === "Fixed Amount Package" ? paymentItemWithAmount : null),
        });

        render(<PackageSelect formik={mockFormik} onPackageSelect={mockOnPackageSelect} />);

        // Select the fixed amount package
        fireEvent.change(screen.getByTestId("select-package"), {
            target: { value: "Fixed Amount Package" },
        });

        // Verify that amount field is populated with the API response value
        expect(mockFormik.setFieldValue).toHaveBeenCalledWith("amount", "2500.00", false);

        // Verify that the callback receives the correct package data
        expect(mockOnPackageSelect).toHaveBeenCalledWith(paymentItemWithAmount);

        // Verify Redux is updated with the selected package
        expect(updateSelectedPackage).toHaveBeenCalledWith(paymentItemWithAmount);
    });
});
