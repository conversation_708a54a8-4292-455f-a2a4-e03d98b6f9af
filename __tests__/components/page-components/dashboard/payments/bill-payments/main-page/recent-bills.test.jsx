import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import RecentBills from "@/components/page-components/dashboard/bill-payments/main-page/recent-bills";
import * as RecentBillsContext from "@/components/page-components/dashboard/bill-payments/context/RecentBillsContext";

// Mock Next.js router
const mockPush = jest.fn();
jest.mock("next/navigation", () => ({
    useRouter: jest.fn(() => ({
        push: mockPush,
    })),
}));

// Mock the hooks and components
jest.mock("@/components/page-components/dashboard/bill-payments/context/RecentBillsContext", () => ({
    useRecentBills: jest.fn(),
}));

jest.mock("@/components/page-components/dashboard/bill-payments/main-page/transaction-details-dialog", () => ({
    TransactionDetailsDialog: jest.fn(({ isOpen, onClose, transaction }) =>
        isOpen ? (
            <div data-testid="transaction-details-dialog">
                <button data-testid="close-dialog" onClick={onClose}>
                    Close
                </button>
                <div data-testid="transaction-reference">{transaction?.reference}</div>
            </div>
        ) : null
    ),
}));

jest.mock("@/components/page-components/dashboard/bill-payments/main-page/empty-bills-state", () => ({
    EmptyBillsState: jest.fn(() => <div data-testid="empty-bills-state">No recent bills</div>),
}));

// Mock the icons
jest.mock("@/components/icons/bill-payment-icons", () => ({
    ChevronRightIcon: () => <div data-testid="chevron-right-icon">ChevronRight</div>,
}));

jest.mock("@/components/icons/bill-payment-categories", () => ({
    ListIcon: () => <div data-testid="list-icon">List</div>,
    ReceiptIcon: () => <div data-testid="receipt-icon">Receipt</div>,
}));

jest.mock("@/components/ui/skeleton", () => ({
    Skeleton: ({ className }) => <div data-testid="skeleton" className={className}></div>,
}));

describe("RecentBills", () => {
    afterEach(() => {
        jest.clearAllMocks();
    });

    it("should render loading state when data is being fetched", () => {
        // Mock the loading state
        RecentBillsContext.useRecentBills.mockReturnValue({
            bills: [],
            loading: true,
            error: null,
        });

        render(<RecentBills />);

        // Verify loading state is shown - each row has 4 skeletons (icon, title, subtitle, chevron)
        // and there are 5 rows, so 5*4=20 skeletons total
        expect(screen.getAllByTestId("skeleton")).toHaveLength(20);
    });

    it("should render error state when API call fails", () => {
        // Mock the error state
        RecentBillsContext.useRecentBills.mockReturnValue({
            bills: [],
            loading: false,
            error: "Failed to fetch recent bills",
        });

        render(<RecentBills />);

        // Verify error state is shown
        expect(screen.getByText("Error loading recent bills")).toBeInTheDocument();
    });

    it("should render empty state when there are no bills", () => {
        // Mock the empty state
        RecentBillsContext.useRecentBills.mockReturnValue({
            bills: [],
            loading: false,
            error: null,
        });

        render(<RecentBills />);

        // Verify empty state is shown
        expect(screen.getByTestId("empty-bills-state")).toBeInTheDocument();
    });

    it("should render empty state (NOT error) when no bills are available", () => {
        // This test specifically verifies the bug fix:
        // When there are no bills and no error, show empty state, not error state
        RecentBillsContext.useRecentBills.mockReturnValue({
            bills: [],
            loading: false,
            error: null, // No error should mean empty state, not error state
        });

        render(<RecentBills />);

        // Should show empty state
        expect(screen.getByTestId("empty-bills-state")).toBeInTheDocument();

        // Should NOT show error state
        expect(screen.queryByText("Error loading recent bills")).not.toBeInTheDocument();
    });

    it("should render recent bills when data is available", () => {
        // Mock bills data
        const mockBills = [
            {
                id: "1",
                iconType: "airtime",
                provider: "MTN",
                type: "Airtime",
                categoryName: "Mobile/Recharge",
                reference: "REF12345",
                amount: "₦1,000.00",
                date: "2023-06-01T12:00:00Z",
                status: "Completed",
            },
            {
                id: "2",
                iconType: "electricity",
                provider: "Ikeja Electric",
                type: "Electricity",
                categoryName: "Utility Bills",
                reference: "REF67890",
                amount: "₦5,000.00",
                date: "2023-06-02T14:30:00Z",
                status: "Completed",
                isBulkPayment: true,
            },
        ];

        RecentBillsContext.useRecentBills.mockReturnValue({
            bills: mockBills,
            loading: false,
            error: null,
        });

        render(<RecentBills />);

        // Verify bills are rendered
        expect(screen.getByText("MTN")).toBeInTheDocument();
        expect(screen.getByText("Ikeja Electric")).toBeInTheDocument();

        // Verify list icon is used for bulk payment
        expect(screen.getByTestId("list-icon")).toBeInTheDocument();
    });

    it("should open the transaction details dialog when a bill is clicked", () => {
        // Mock bills data
        const mockBills = [
            {
                id: "1",
                iconType: "airtime",
                provider: "MTN",
                type: "Airtime",
                categoryName: "Mobile/Recharge",
                reference: "REF12345",
                amount: "₦1,000.00",
                date: "2023-06-01T12:00:00Z",
                status: "Completed",
            },
        ];

        RecentBillsContext.useRecentBills.mockReturnValue({
            bills: mockBills,
            loading: false,
            error: null,
        });

        render(<RecentBills />);

        // Find and click the chevron button - using role to find the button directly
        const chevronButton = screen.getByRole("button", { name: /view transaction details/i });
        fireEvent.click(chevronButton);

        // Verify dialog is opened
        expect(screen.getByTestId("transaction-details-dialog")).toBeInTheDocument();
        expect(screen.getByTestId("transaction-reference")).toHaveTextContent("REF12345");

        // Close the dialog
        fireEvent.click(screen.getByTestId("close-dialog"));

        // Verify dialog is closed
        expect(screen.queryByTestId("transaction-details-dialog")).not.toBeInTheDocument();
    });

    it("should navigate to transactions page when View all is clicked", () => {
        // Mock bills data
        const mockBills = [
            {
                id: "1",
                iconType: "airtime",
                provider: "MTN",
                type: "Airtime",
                categoryName: "Mobile/Recharge",
                reference: "REF12345",
                amount: "₦1,000.00",
                date: "2023-06-01T12:00:00Z",
                status: "Completed",
            },
        ];

        RecentBillsContext.useRecentBills.mockReturnValue({
            bills: mockBills,
            loading: false,
            error: null,
        });

        render(<RecentBills />);

        // Find and click the "View all" link
        const viewAllButton = screen.getByText("View all");
        fireEvent.click(viewAllButton);

        // Verify router.push was called with the correct path
        expect(mockPush).toHaveBeenCalledWith("/transactions");
    });
});
