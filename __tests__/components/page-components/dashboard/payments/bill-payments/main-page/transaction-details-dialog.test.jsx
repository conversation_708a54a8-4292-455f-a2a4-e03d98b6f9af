import React from "react";
import { render, screen, fireEvent, act, cleanup } from "@testing-library/react";
import { TransactionDetailsDialog } from "@/components/page-components/dashboard/bill-payments/main-page/transaction-details-dialog";
import { TransactionId } from "@/components/common/transaction-id";
import { formatDateWithTime } from "@/functions/date";

// Mock Redux hooks
jest.mock("@/redux/hooks", () => ({
    useAppDispatch: jest.fn().mockReturnValue(jest.fn()),
    useAppSelector: jest.fn((selector) => {
        // Mock the security state
        if (selector.toString().includes("security")) {
            return { success: false, pin: null };
        }
        // Mock the corporate ID
        if (selector.toString().includes("corporate")) {
            return "CORP123";
        }
        return {};
    }),
}));

// Mock the Redux actions/thunks
jest.mock("@/redux/actions/billPaymentThunks", () => ({
    initiateBillPayment: jest.fn().mockReturnValue({ type: "mock/initiateBillPayment" }),
}));

// Mock the security slice actions
jest.mock("@/redux/slices/securitySlice", () => ({
    openVerifyPinModal: jest.fn().mockReturnValue({ type: "mock/openVerifyPinModal" }),
}));

// Mock the RecentBillsContext
jest.mock("@/components/page-components/dashboard/bill-payments/context/RecentBillsContext", () => ({
    useRecentBills: jest.fn().mockReturnValue({
        refetchRecentBills: jest.fn().mockResolvedValue({}),
    }),
    // Make sure the type is exported for props in the component
    RecentBill: {},
}));

// Mock Badge component to handle the data-testid properly
jest.mock("@/components/common/badge", () => ({
    __esModule: true,
    default: ({ text, color, "data-testid": dataTestId }) => (
        <div data-testid={dataTestId || "badge"} className={`badge-${color}`}>
            {text}
        </div>
    ),
    BadgeColor: {},
}));

// Mock the date functions
jest.mock("@/functions/date", () => ({
    formatDateWithTime: jest.fn().mockReturnValue("Jan 1, 2023, 12:00 PM"),
}));

// Mock the TransactionId component
jest.mock("@/components/common/transaction-id", () => ({
    TransactionId: ({ id, textSize }) => <div data-testid="transaction-id">{id}</div>,
}));

// Mock icons used in the component
jest.mock("@/components/icons/bill-payment-icons", () => ({
    RepeatIcon: (props) => (
        <div data-testid="repeat-icon" {...props}>
            RepeatIcon
        </div>
    ),
    PhoneCallIcon: (props) => (
        <div data-testid="phone-icon" {...props}>
            PhoneIcon
        </div>
    ),
    ZapIcon: () => <div data-testid="zap-icon">Zap Icon</div>,
    CableIcon: (props) => (
        <div data-testid="cable-icon" {...props}>
            CableIcon
        </div>
    ),
    WifiIcon: () => <div data-testid="wifi-icon">Wifi Icon</div>,
}));

// Mock category icons
jest.mock("@/components/icons/bill-payment-categories", () => ({
    ListIcon: (props) => (
        <div data-testid="list-icon" {...props}>
            ListIcon
        </div>
    ),
    ReceiptIcon: (props) => (
        <div data-testid="receipt-icon" {...props}>
            ReceiptIcon
        </div>
    ),
}));

// Mock Button component
jest.mock("@/components/common/buttonv3", () => ({
    Button: ({ children, onClick, disabled, variant, size, leftIcon, className, ...props }) => (
        <button
            data-testid={`button-${variant || "default"}`}
            onClick={onClick ? onClick : undefined}
            disabled={disabled}
            className={className}
            {...props}
        >
            {leftIcon}
            {children}
        </button>
    ),
}));

// Mock Lucide icons
jest.mock("lucide-react", () => ({
    X: (props) => (
        <div data-testid="x-icon" {...props}>
            X
        </div>
    ),
}));

// Mock SideDrawer component
jest.mock("@/components/common/drawer", () => ({
    __esModule: true,
    default: ({ isOpen, children, className }) =>
        isOpen ? (
            <div data-testid="side-drawer" className={className}>
                {children}
            </div>
        ) : null,
}));

describe("TransactionDetailsDialog", () => {
    const mockTransaction = {
        provider: "MTN",
        categoryName: "Mobile/Recharge",
        reference: "REF12345",
        amount: "₦1,000.00",
        date: "2023-01-01T12:00:00Z",
        status: "Successful",
        account: "**********",
        narration: "Airtime purchase",
        customerId: "CUST123",
        paymentCode: "PAY123",
        customerMobile: "***********",
    };

    const mockBulkTransaction = {
        provider: "Ikeja Electric",
        categoryName: "Utility Bills",
        reference: "REF67890",
        amount: "₦5,000.00",
        date: "2023-01-02T14:30:00Z",
        status: "Awaiting Approval",
        account: "**********",
        narration: "Bill payment",
        isBulkPayment: true,
    };

    const mockOnClose = jest.fn();
    const mockOnReport = jest.fn();

    beforeEach(() => {
        jest.clearAllMocks();
    });

    test("should not render when isOpen is false", () => {
        render(
            <TransactionDetailsDialog
                isOpen={false}
                onClose={mockOnClose}
                onReport={mockOnReport}
                transaction={mockTransaction}
            />
        );

        expect(screen.queryByTestId("side-drawer")).not.toBeInTheDocument();
    });

    test("should not render when transaction is null", () => {
        render(
            <TransactionDetailsDialog isOpen={true} onClose={mockOnClose} transaction={null} onReport={mockOnReport} />
        );

        expect(screen.queryByTestId("side-drawer")).not.toBeInTheDocument();
    });

    test("should render with transaction data when isOpen is true", () => {
        render(
            <TransactionDetailsDialog
                isOpen={true}
                onClose={mockOnClose}
                onReport={mockOnReport}
                transaction={mockTransaction}
            />
        );

        // Header information
        expect(screen.getByTestId("transaction-title")).toHaveTextContent("MTN");
        expect(screen.getByTestId("transaction-amount")).toHaveTextContent("₦1,000.00");
        expect(screen.getByTestId("transaction-status")).toHaveTextContent("Successful");

        // Details section
        expect(screen.getByText("Bill type").nextSibling).toHaveTextContent("Mobile/Recharge");
        expect(screen.getByText("Biller").nextSibling).toHaveTextContent("MTN");

        // Verify TransactionId is present
        expect(screen.getByTestId("transaction-id")).toHaveTextContent("REF12345");
    });

    test("should show the correct icon based on transaction type", () => {
        // Test with single payment (should use ReceiptIcon)
        render(
            <TransactionDetailsDialog
                isOpen={true}
                onClose={mockOnClose}
                onReport={mockOnReport}
                transaction={mockTransaction}
            />
        );

        expect(screen.getByTestId("receipt-icon")).toBeInTheDocument();
        expect(screen.queryByTestId("list-icon")).not.toBeInTheDocument();

        // Cleanup
        cleanup();

        // Test with bulk payment (should use ListIcon)
        render(
            <TransactionDetailsDialog
                isOpen={true}
                onClose={mockOnClose}
                transaction={mockBulkTransaction}
                onReport={mockOnReport}
            />
        );

        expect(screen.getByTestId("list-icon")).toBeInTheDocument();
        expect(screen.queryByTestId("receipt-icon")).not.toBeInTheDocument();
    });

    test("should close when the X button is clicked", () => {
        render(
            <TransactionDetailsDialog
                isOpen={true}
                onClose={mockOnClose}
                onReport={mockOnReport}
                transaction={mockTransaction}
            />
        );

        fireEvent.click(screen.getByTestId("close-dialog-button"));
        expect(mockOnClose).toHaveBeenCalledTimes(1);
    });

    test("should close when pressing the Escape key", () => {
        render(
            <TransactionDetailsDialog
                isOpen={true}
                onClose={mockOnClose}
                onReport={mockOnReport}
                transaction={mockTransaction}
            />
        );

        // Simulate Escape key press
        fireEvent.keyDown(document, { key: "Escape" });
        expect(mockOnClose).toHaveBeenCalledTimes(1);
    });

    test("should call onReport when Report button is clicked", () => {
        render(
            <TransactionDetailsDialog
                isOpen={true}
                onClose={mockOnClose}
                onReport={mockOnReport}
                transaction={mockTransaction}
            />
        );

        fireEvent.click(screen.getByText("Report"));
        expect(mockOnReport).toHaveBeenCalledTimes(1);
    });

    test("should be unmounted properly when closed", () => {
        const { rerender } = render(
            <TransactionDetailsDialog
                isOpen={true}
                onClose={mockOnClose}
                onReport={mockOnReport}
                transaction={mockTransaction}
            />
        );

        expect(screen.getByTestId("transaction-title")).toBeInTheDocument();

        // Close the dialog by changing isOpen to false
        rerender(
            <TransactionDetailsDialog
                isOpen={false}
                onClose={mockOnClose}
                onReport={mockOnReport}
                transaction={mockTransaction}
            />
        );

        expect(screen.queryByTestId("transaction-title")).not.toBeInTheDocument();
    });

    test("should clean up event listeners when unmounted", () => {
        const removeEventListenerSpy = jest.spyOn(document, "removeEventListener");

        const { unmount } = render(
            <TransactionDetailsDialog
                isOpen={true}
                onClose={mockOnClose}
                onReport={mockOnReport}
                transaction={mockTransaction}
            />
        );

        unmount();

        expect(removeEventListenerSpy).toHaveBeenCalledWith("keydown", expect.any(Function));
        removeEventListenerSpy.mockRestore();
    });

    test("should render bulk payment with disabled repeat button", () => {
        render(
            <TransactionDetailsDialog
                isOpen={true}
                onClose={mockOnClose}
                onReport={mockOnReport}
                transaction={mockBulkTransaction}
            />
        );

        // Check header shows correct icon for bulk payment
        expect(screen.getByTestId("list-icon")).toBeInTheDocument();

        // Check bulk payment has disabled repeat button
        const repeatButton = screen.getByText("Repeat").closest("button");
        expect(repeatButton).toBeDisabled();
        expect(repeatButton).toHaveAttribute("title", "Bulk payments cannot be repeated");
    });

    test("should handle null/undefined values gracefully", () => {
        const incompleteTransaction = {
            provider: "Unknown Provider",
            reference: "UNKNOWN",
            // Ensure status is defined to avoid toLowerCase error
            status: "-",
        };

        render(
            <TransactionDetailsDialog
                isOpen={true}
                onClose={mockOnClose}
                onReport={mockOnReport}
                transaction={incompleteTransaction}
            />
        );

        // Should fall back to ReceiptIcon for regular payments
        expect(screen.getByTestId("receipt-icon")).toBeInTheDocument();

        // Should handle missing data gracefully
        expect(screen.getByTestId("transaction-title")).toHaveTextContent("Unknown Provider");

        // Check for dash placeholders for missing data
        const dashElements = screen.getAllByText("-");
        expect(dashElements.length).toBeGreaterThan(0);
    });

    test("should format account number correctly", () => {
        // Test with a raw account number
        const transactionWithRawAccount = {
            ...mockTransaction,
            account: "**********",
        };

        render(
            <TransactionDetailsDialog
                isOpen={true}
                onClose={mockOnClose}
                onReport={mockOnReport}
                transaction={transactionWithRawAccount}
            />
        );

        // Should format as ****7890
        const accountText = screen.getByText(/\*\*\*\*7890/);
        expect(accountText).toBeInTheDocument();
    });

    // Additional tests to increase branch coverage for the getStatusBadgeColor function

    describe("Status badge colors", () => {
        // Test for each possible case in the switch statement

        test("should show success badge for 'successful' status", () => {
            render(
                <TransactionDetailsDialog
                    isOpen={true}
                    onClose={mockOnClose}
                    onReport={mockOnReport}
                    transaction={{ ...mockTransaction, status: "Successful" }}
                />
            );

            expect(screen.getByTestId("transaction-status").className).toContain("badge-success");
        });

        test("should show error badge for 'failed' status", () => {
            render(
                <TransactionDetailsDialog
                    isOpen={true}
                    onClose={mockOnClose}
                    onReport={mockOnReport}
                    transaction={{ ...mockTransaction, status: "Failed" }}
                />
            );

            expect(screen.getByTestId("transaction-status").className).toContain("badge-error");
        });

        test("should show brand badge for 'in progress' status", () => {
            render(
                <TransactionDetailsDialog
                    isOpen={true}
                    onClose={mockOnClose}
                    onReport={mockOnReport}
                    transaction={{ ...mockTransaction, status: "In Progress" }}
                />
            );

            expect(screen.getByTestId("transaction-status").className).toContain("badge-brand");
        });

        test("should show warning badge for 'pending' status", () => {
            render(
                <TransactionDetailsDialog
                    isOpen={true}
                    onClose={mockOnClose}
                    onReport={mockOnReport}
                    transaction={{ ...mockTransaction, status: "Pending" }}
                />
            );

            expect(screen.getByTestId("transaction-status").className).toContain("badge-warning");
        });

        test("should show warning badge for 'awaiting approval' status", () => {
            render(
                <TransactionDetailsDialog
                    isOpen={true}
                    onClose={mockOnClose}
                    onReport={mockOnReport}
                    transaction={{ ...mockTransaction, status: "Awaiting Approval" }}
                />
            );

            expect(screen.getByTestId("transaction-status").className).toContain("badge-warning");
        });

        test("should show error badge for 'rejected approval' status", () => {
            render(
                <TransactionDetailsDialog
                    isOpen={true}
                    onClose={mockOnClose}
                    onReport={mockOnReport}
                    transaction={{ ...mockTransaction, status: "Rejected Approval" }}
                />
            );

            expect(screen.getByTestId("transaction-status").className).toContain("badge-error");
        });

        test("should show neutral badge for unknown status", () => {
            render(
                <TransactionDetailsDialog
                    isOpen={true}
                    onClose={mockOnClose}
                    onReport={mockOnReport}
                    transaction={{ ...mockTransaction, status: "Some Unknown Status" }}
                />
            );

            expect(screen.getByTestId("transaction-status").className).toContain("badge-neutral");
        });

        test("should respect status case insensitivity and handle underscores", () => {
            render(
                <TransactionDetailsDialog
                    isOpen={true}
                    onClose={mockOnClose}
                    onReport={mockOnReport}
                    transaction={{ ...mockTransaction, status: "AWAITING_APPROVAL" }}
                />
            );

            expect(screen.getByTestId("transaction-status").className).toContain("badge-warning");
        });

        test("should show neutral badge for undefined status", () => {
            render(
                <TransactionDetailsDialog
                    isOpen={true}
                    onClose={mockOnClose}
                    onReport={mockOnReport}
                    transaction={{ ...mockTransaction, status: undefined }}
                />
            );

            expect(screen.getByTestId("transaction-status").className).toContain("badge-neutral");
        });
    });

    // Test PIN verification and repeat payment logic

    describe("Payment processing functionality", () => {
        const mockDispatch = jest.fn();
        const mockRefetch = jest.fn().mockResolvedValue({});

        beforeEach(() => {
            require("@/redux/hooks").useAppDispatch.mockReturnValue(mockDispatch);
            require("@/components/page-components/dashboard/bill-payments/context/RecentBillsContext").useRecentBills.mockReturnValue(
                { refetchRecentBills: mockRefetch }
            );
        });

        test("should trigger PIN verification on Repeat button click", () => {
            render(
                <TransactionDetailsDialog
                    isOpen={true}
                    onClose={mockOnClose}
                    onReport={mockOnReport}
                    transaction={mockTransaction}
                />
            );

            // Find and click the repeat payment button
            const repeatButton = screen.getByText("Repeat").closest("button");
            fireEvent.click(repeatButton);

            // Check that openVerifyPinModal action was dispatched
            expect(mockDispatch).toHaveBeenCalledWith(expect.any(Object));
        });

        test("should process payment when PIN is verified", () => {
            // Mock successful PIN verification
            const { useAppSelector } = require("@/redux/hooks");
            useAppSelector.mockImplementation((selector) => {
                if (selector.toString().includes("security")) {
                    return { success: true, pin: "1234" };
                }
                if (selector.toString().includes("corporate")) {
                    return "CORP123";
                }
                return {};
            });

            render(
                <TransactionDetailsDialog
                    isOpen={true}
                    onClose={mockOnClose}
                    onReport={mockOnReport}
                    transaction={mockTransaction}
                />
            );

            // The effect should trigger payment processing automatically
            expect(mockDispatch).toHaveBeenCalledWith(expect.any(Object));

            // Reset the mock implementation
            useAppSelector.mockImplementation((selector) => {
                if (selector.toString().includes("security")) {
                    return { success: false, pin: null };
                }
                if (selector.toString().includes("corporate")) {
                    return "CORP123";
                }
                return {};
            });
        });

        test("should handle payment processing without try-catch wrapper", async () => {
            // Mock successful PIN verification
            const { useAppSelector } = require("@/redux/hooks");
            useAppSelector.mockImplementation((selector) => {
                if (selector.toString().includes("security")) {
                    return { success: true, pin: "1234" };
                }
                if (selector.toString().includes("corporate")) {
                    return "CORP123";
                }
                return {};
            });

            // Mock successful payment initiation (no error throwing)
            const { initiateBillPayment } = require("@/redux/actions/billPaymentThunks");
            initiateBillPayment.mockImplementationOnce(() => Promise.resolve());

            // Use act to handle async state updates
            await act(async () => {
                render(
                    <TransactionDetailsDialog
                        isOpen={true}
                        onClose={mockOnClose}
                        onReport={mockOnReport}
                        transaction={mockTransaction}
                    />
                );
            });

            // Reset mocks to their original implementation
            useAppSelector.mockImplementation((selector) => {
                if (selector.toString().includes("security")) {
                    return { success: false, pin: null };
                }
                if (selector.toString().includes("corporate")) {
                    return "CORP123";
                }
                return {};
            });
        });
    });

    // Test handling of useEffect cleanup and initialization
    describe("Component lifecycle", () => {
        test("should reset hasProcessedPin when dialog opens", () => {
            // First render with isOpen=false
            const { rerender } = render(
                <TransactionDetailsDialog
                    isOpen={false}
                    onClose={mockOnClose}
                    transaction={mockTransaction}
                    onReport={mockOnReport}
                />
            );

            // Then rerender with isOpen=true
            rerender(
                <TransactionDetailsDialog
                    isOpen={true}
                    onClose={mockOnClose}
                    transaction={mockTransaction}
                    onReport={mockOnReport}
                />
            );

            // Check that the component renders properly after opening
            expect(screen.getByTestId("transaction-status")).toBeInTheDocument();
        });
    });

    // Test edge cases for formatAccountDisplay
    describe("Account formatting", () => {
        test("should return dash for empty account", () => {
            render(
                <TransactionDetailsDialog
                    isOpen={true}
                    onClose={mockOnClose}
                    onReport={mockOnReport}
                    transaction={{ ...mockTransaction, account: "" }}
                />
            );

            // Find the account element
            const accountElements = screen.getAllByText("-");
            expect(accountElements.length).toBeGreaterThan(0);
        });

        test("should format short account numbers correctly", () => {
            render(
                <TransactionDetailsDialog
                    isOpen={true}
                    onClose={mockOnClose}
                    onReport={mockOnReport}
                    transaction={{ ...mockTransaction, account: "123" }}
                />
            );

            // Should still show a masked account even if less than 4 digits
            const accountText = screen.getByText(/\*\*\*\*123/);
            expect(accountText).toBeInTheDocument();
        });
    });

    // Test skip condition for payment processing
    describe("Payment processing conditions", () => {
        test("should not process payment for bulk transactions", () => {
            // Mock successful PIN verification
            const { useAppSelector } = require("@/redux/hooks");
            useAppSelector.mockImplementation((selector) => {
                if (selector.toString().includes("security")) {
                    return { success: true, pin: "1234" };
                }
                if (selector.toString().includes("corporate")) {
                    return "CORP123";
                }
                return {};
            });

            const mockDispatch = jest.fn();
            require("@/redux/hooks").useAppDispatch.mockReturnValue(mockDispatch);

            render(
                <TransactionDetailsDialog
                    isOpen={true}
                    onClose={mockOnClose}
                    onReport={mockOnReport}
                    transaction={{ ...mockTransaction, isBulkPayment: true }}
                />
            );

            // The initiateBillPayment action should not be dispatched for bulk payments
            expect(mockDispatch).not.toHaveBeenCalledWith(
                expect.objectContaining({
                    type: "mock/initiateBillPayment",
                })
            );

            // Reset mocks
            useAppSelector.mockImplementation((selector) => {
                if (selector.toString().includes("security")) {
                    return { success: false, pin: null };
                }
                if (selector.toString().includes("corporate")) {
                    return "CORP123";
                }
                return {};
            });
        });

        test("should not process payment without PIN", () => {
            // Mock PIN verification with success but no PIN
            const { useAppSelector } = require("@/redux/hooks");
            useAppSelector.mockImplementation((selector) => {
                if (selector.toString().includes("security")) {
                    return { success: true, pin: null };
                }
                if (selector.toString().includes("corporate")) {
                    return "CORP123";
                }
                return {};
            });

            const mockDispatch = jest.fn();
            require("@/redux/hooks").useAppDispatch.mockReturnValue(mockDispatch);

            render(
                <TransactionDetailsDialog
                    isOpen={true}
                    onClose={mockOnClose}
                    onReport={mockOnReport}
                    transaction={mockTransaction}
                />
            );

            // The initiateBillPayment action should not be dispatched without a PIN
            expect(mockDispatch).not.toHaveBeenCalledWith(
                expect.objectContaining({
                    type: "mock/initiateBillPayment",
                })
            );

            // Reset mocks
            useAppSelector.mockImplementation((selector) => {
                if (selector.toString().includes("security")) {
                    return { success: false, pin: null };
                }
                if (selector.toString().includes("corporate")) {
                    return "CORP123";
                }
                return {};
            });
        });
    });

    // Add tests for formatAccountDisplay and getStatusBadgeColor edge cases
    describe("Edge cases for utility functions", () => {
        // Test all the remaining cases for getStatusBadgeColor
        test("should handle non-standard status formats properly", () => {
            // Test with status that has underscores and mixed case
            render(
                <TransactionDetailsDialog
                    isOpen={true}
                    onClose={mockOnClose}
                    onReport={mockOnReport}
                    transaction={{ ...mockTransaction, status: "In_PROGRESS" }}
                />
            );

            // Should interpret "In_PROGRESS" as "in progress" and use brand color
            expect(screen.getByTestId("transaction-status").className).toContain("badge-brand");

            cleanup();

            // Test with status that doesn't match any switch case
            render(
                <TransactionDetailsDialog
                    isOpen={true}
                    onClose={mockOnClose}
                    onReport={mockOnReport}
                    transaction={{ ...mockTransaction, status: "custom status" }}
                />
            );

            // Should default to neutral for unknown status
            expect(screen.getByTestId("transaction-status").className).toContain("badge-neutral");
        });
    });

    // Extra tests for payment processing with different conditions
    describe("Payment processing with empty values", () => {
        test("should handle transactions with missing fields properly when processing payment", () => {
            // Mock successful PIN verification
            const { useAppSelector } = require("@/redux/hooks");
            useAppSelector.mockImplementation((selector) => {
                if (selector.toString().includes("security")) {
                    return { success: true, pin: "1234" };
                }
                if (selector.toString().includes("corporate")) {
                    return "CORP123";
                }
                return {};
            });

            const mockDispatch = jest.fn();
            require("@/redux/hooks").useAppDispatch.mockReturnValue(mockDispatch);

            // Minimal transaction with only required fields
            const minimalTransaction = {
                amount: "₦500.00",
                provider: "Test Provider",
                // Missing customerId, paymentCode, etc.
            };

            render(
                <TransactionDetailsDialog
                    isOpen={true}
                    onClose={mockOnClose}
                    onReport={mockOnReport}
                    transaction={minimalTransaction}
                />
            );

            // The payment params should include empty strings for missing fields
            expect(mockDispatch).toHaveBeenCalledWith(
                expect.objectContaining({
                    type: "mock/initiateBillPayment",
                })
            );

            // Reset mocks
            useAppSelector.mockImplementation((selector) => {
                if (selector.toString().includes("security")) {
                    return { success: false, pin: null };
                }
                if (selector.toString().includes("corporate")) {
                    return "CORP123";
                }
                return {};
            });
        });

        // test("should handle transaction with non-numeric amount", async () => {
        //     // Mock successful PIN verification
        //     const { useAppSelector } = require("@/redux/hooks");
        //     useAppSelector.mockImplementation((selector) => {
        //         if (selector.toString().includes("security")) {
        //             return { success: true, pin: "1234" };
        //         }
        //         if (selector.toString().includes("corporate")) {
        //             return "CORP123";
        //         }
        //         return {};
        //     });

        //     const mockDispatch = jest.fn();
        //     require("@/redux/hooks").useAppDispatch.mockReturnValue(mockDispatch);

        //     // Transaction with non-standard amount format
        //     const nonNumericAmountTransaction = {
        //         ...mockTransaction,
        //         amount: "not a number",
        //     };

        //     // Mock console.error to prevent test output pollution
        //     const originalConsoleError = console.error;
        //     console.error = jest.fn();

        //     // Use act to handle async state updates
        //     await act(async () => {
        //         render(
        //             <TransactionDetailsDialog
        //                 isOpen={true}
        //                 onClose={mockOnClose}
        //                 onReport={mockOnReport}
        //                 transaction={nonNumericAmountTransaction}
        //             />
        //         );
        //     });

        //     // Reset mocks
        //     useAppSelector.mockImplementation((selector) => {
        //         if (selector.toString().includes("security")) {
        //             return { success: false, pin: null };
        //         }
        //         if (selector.toString().includes("corporate")) {
        //             return "CORP123";
        //         }
        //         return {};
        //     });

        //     // Restore console.error
        //     console.error = originalConsoleError;
        // });
    });

    // Test transaction processing without refetch capability
    describe("Payment processing without refetch", () => {
        test("should handle cases where refetchRecentBills is not available", async () => {
            // Mock successful PIN verification
            const { useAppSelector } = require("@/redux/hooks");
            useAppSelector.mockImplementation((selector) => {
                if (selector.toString().includes("security")) {
                    return { success: true, pin: "1234" };
                }
                if (selector.toString().includes("corporate")) {
                    return "CORP123";
                }
                return {};
            });

            // Mock context without refetchRecentBills
            require("@/components/page-components/dashboard/bill-payments/context/RecentBillsContext").useRecentBills.mockReturnValue(
                {}
            );

            const mockDispatch = jest.fn();
            require("@/redux/hooks").useAppDispatch.mockReturnValue(mockDispatch);

            // Use act to handle async state updates
            await act(async () => {
                render(
                    <TransactionDetailsDialog
                        isOpen={true}
                        onClose={mockOnClose}
                        onReport={mockOnReport}
                        transaction={mockTransaction}
                    />
                );
            });

            // Payment should still be dispatched even without refetch
            expect(mockDispatch).toHaveBeenCalledWith(
                expect.objectContaining({
                    type: "mock/initiateBillPayment",
                })
            );

            // Reset mocks
            useAppSelector.mockImplementation((selector) => {
                if (selector.toString().includes("security")) {
                    return { success: false, pin: null };
                }
                if (selector.toString().includes("corporate")) {
                    return "CORP123";
                }
                return {};
            });

            // Restore mock context
            require("@/components/page-components/dashboard/bill-payments/context/RecentBillsContext").useRecentBills.mockReturnValue(
                {
                    refetchRecentBills: jest.fn().mockResolvedValue({}),
                }
            );
        });
    });

    describe("Bulk payment display", () => {
        it("should display '3 billers' in header for bulk payment with multiple billers", () => {
            const bulkTransaction = {
                id: "35",
                iconType: "bulk-payment",
                provider: "Multiple billers",
                type: "Mobile/Recharge",
                categoryName: "Mobile/Recharge",
                reference: "BULK-REF-001",
                amount: "₦15,000.00",
                date: "2023-01-01T10:00:00Z",
                status: "SUCCESSFUL",
                account: "**********",
                narration: "Bulk payment for multiple billers",
                isBulkPayment: true,
                uniqueBillerCount: 3,
                recipientCount: 10,
                paymentCode: "PAY001",
                customerId: "CUST001",
                customerMobile: "***********",
            };

            render(<TransactionDetailsDialog isOpen={true} onClose={jest.fn()} transaction={bulkTransaction} />);

            // Check that the header shows "3 billers"
            expect(screen.getByTestId("transaction-title")).toHaveTextContent("3 billers");

            // Check that the biller field shows "Multiple billers"
            expect(screen.getByTestId("details-biller")).toHaveTextContent("Multiple billers");

            // Check that the amount is correctly displayed
            expect(screen.getByTestId("transaction-amount")).toHaveTextContent("₦15,000.00");
        });

        it("should display provider name in header for bulk payment with single biller", () => {
            const bulkTransaction = {
                id: "42",
                iconType: "bulk-payment",
                provider: "GLO",
                type: "Mobile/Recharge",
                categoryName: "Mobile/Recharge",
                reference: "BULK-REF-002",
                amount: "₦8,000.00",
                date: "2023-01-02T11:00:00Z",
                status: "SUCCESSFUL",
                account: "**********",
                narration: "Bulk GLO recharge",
                isBulkPayment: true,
                uniqueBillerCount: 1,
                recipientCount: 5,
                paymentCode: "PAY002",
                customerId: "CUST002",
                customerMobile: "***********",
            };

            render(<TransactionDetailsDialog isOpen={true} onClose={jest.fn()} transaction={bulkTransaction} />);

            // Check that the header shows the provider name "GLO"
            expect(screen.getByTestId("transaction-title")).toHaveTextContent("GLO");

            // Check that the biller field shows "GLO"
            expect(screen.getByTestId("details-biller")).toHaveTextContent("GLO");

            // Check that the amount is correctly displayed
            expect(screen.getByTestId("transaction-amount")).toHaveTextContent("₦8,000.00");
        });

        it("should display correct header for bulk payment with large number of billers", () => {
            const bulkTransaction = {
                id: "100",
                iconType: "bulk-payment",
                provider: "Multiple billers",
                type: "Mixed",
                categoryName: "Mixed",
                reference: "BULK-REF-003",
                amount: "₦150,000.00",
                date: "2023-01-03T12:00:00Z",
                status: "SUCCESSFUL",
                account: "**********",
                narration: "Large bulk payment",
                isBulkPayment: true,
                uniqueBillerCount: 45,
                recipientCount: 150,
                paymentCode: "PAY003",
                customerId: "CUST003",
                customerMobile: "***********",
            };

            render(<TransactionDetailsDialog isOpen={true} onClose={jest.fn()} transaction={bulkTransaction} />);

            // Check that the header shows "45 billers"
            expect(screen.getByTestId("transaction-title")).toHaveTextContent("45 billers");

            // Check that the biller field shows "Multiple billers"
            expect(screen.getByTestId("details-biller")).toHaveTextContent("Multiple billers");
        });

        it("should handle edge case where uniqueBillerCount is undefined for bulk payment", () => {
            const bulkTransaction = {
                id: "200",
                iconType: "bulk-payment",
                provider: "Unknown",
                type: "Mobile/Recharge",
                categoryName: "Mobile/Recharge",
                reference: "BULK-REF-004",
                amount: "₦5,000.00",
                date: "2023-01-04T13:00:00Z",
                status: "SUCCESSFUL",
                account: "**********",
                narration: "Legacy bulk payment",
                isBulkPayment: true,
                // uniqueBillerCount is undefined
                paymentCode: "PAY004",
                customerId: "CUST004",
                customerMobile: "***********",
            };

            render(<TransactionDetailsDialog isOpen={true} onClose={jest.fn()} transaction={bulkTransaction} />);

            // Should fall back to showing provider name when uniqueBillerCount is undefined
            expect(screen.getByTestId("transaction-title")).toHaveTextContent("Unknown");
        });
    });
});
