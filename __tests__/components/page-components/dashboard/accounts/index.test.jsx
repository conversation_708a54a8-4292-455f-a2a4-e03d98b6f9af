import AccountOverview from "@/components/page-components/dashboard/accounts";
import { copyToClipboard } from "@/functions/stringManipulations";
import { closeTransferFundDialog, openAddTransferFundsDialog } from "@/redux/features/uiDialogSlice";
import { closeTransferSuccessModal } from "@/redux/slices/accountSlice";
import "@testing-library/jest-dom";
import { fireEvent, render, screen, waitFor, within } from "@testing-library/react";
import { Provider } from "react-redux";
import configureStore from "redux-mock-store";

// Mocks for child components
jest.mock("@/components/page-components/dashboard/accounts/account-switcher", () => () => (
    <div data-testid="account-switcher">Mock AccountSwitcher</div>
));
jest.mock("@/components/page-components/dashboard/accounts/account-chart", () => () => (
    <div data-testid="account-chart">Mock AccountChart</div>
));
jest.mock("@/components/page-components/dashboard/accounts/recent-transaction", () => () => (
    <div data-testid="recent-transaction">Mock RecentTransaction</div>
));
jest.mock("@/components/page-components/dashboard/accounts/column-data", () => ({
    DownloadStatementDialog: () => <div data-testid="download-dialog">Mock DownloadStatementDialog</div>,
}));

// Mocks for modal components defined in the same file
jest.mock("@/components/page-components/dashboard/accounts/payments/add-transfer-funds", () => ({
    __esModule: true,
    default: ({ isOpen, onClose }) =>
        isOpen ? (
            <div data-testid="add-transfer-funds">
                Mock AddTransferFunds <button onClick={onClose}>Close</button>
            </div>
        ) : null,
}));
jest.mock("@/components/page-components/dashboard/accounts/payments/add-transfer-funds-review", () => ({
    __esModule: true,
    default: ({ isOpen, onClose, onBack }) =>
        isOpen ? (
            <div data-testid="add-transfer-funds-review">
                Mock AddTransferFundsReview
                <button onClick={onClose}>Close</button>
                <button onClick={onBack}>Back</button>
            </div>
        ) : null,
}));
// New mock for TransferSuccessModal
jest.mock("@/components/page-components/dashboard/accounts/payments/transfer-success-modal", () => ({
    __esModule: true,
    default: ({ isOpen, onClose }) =>
        isOpen ? (
            <div data-testid="transfer-success-modal">
                Mock TransferSuccessModal <button onClick={onClose}>Close</button>
            </div>
        ) : null,
}));

// Mock next/link
jest.mock("next/link", () => ({ children, href }) => <a href={href}>{children}</a>);

// Mock copyToClipboard as an async function
jest.mock("@/functions/stringManipulations", () => ({
    copyToClipboard: jest.fn(() => Promise.resolve()),
}));

// Mock the LoadingIndicator to provide a test id.
jest.mock("@/components/common/loading-indicator", () => () => <div data-testid="loading-indicator">Loading...</div>);

const mockStore = configureStore([]);

// Helper that renders the component with an optionally overridden redux state.
// We supply the uiDialog and account states as needed.
const renderComponent = (uiDialogOverrides = {}, accountOverrides = {}) => {
    const store = mockStore({
        uiDialog: {
            transferFunds: {
                isAddTransferFundsOpen: uiDialogOverrides.isAddTransferFundsOpen || false,
                isReviewTransferFundsOpen: uiDialogOverrides.isReviewTransferFundsOpen || false,
            },
        },
        account: {
            loadingStatus: accountOverrides.loadingStatus || "loaded",
            selectedAccount: accountOverrides.selectedAccount || {
                accountName: "Main Account",
                accountNumber: "********",
                preferredName: "Preferred Name",
                schemeType: "Savings",
                // Bank name is hard-coded as "First city monument bank" in the component
            },
            transferFundsAccount: {
                successModalOpen: accountOverrides.successModalOpen || false,
            },
        },
    });
    store.dispatch = jest.fn();
    return {
        store,
        ...render(
            <Provider store={store}>
                <AccountOverview />
            </Provider>
        ),
    };
};

describe("AccountOverview Component", () => {
    let store;

    beforeEach(() => {
        // Default store state if not overridden.
        store = mockStore({
            uiDialog: {
                transferFunds: { isAddTransferFundsOpen: false, isReviewTransferFundsOpen: false },
            },
            account: {
                loadingStatus: "loaded",
                selectedAccount: {
                    accountName: "Main Account",
                    accountNumber: "********",
                    preferredName: "Preferred Name",
                    schemeType: "Savings",
                },
                transferFundsAccount: {
                    successModalOpen: false,
                },
            },
        });
        store.dispatch = jest.fn();
    });

    test("renders AccountOverview correctly", () => {
        renderComponent();
        // Check for static texts
        expect(screen.getByText("Recent transactions")).toBeInTheDocument();
        expect(screen.getByText("Transfer funds")).toBeInTheDocument();
        // Check that child components are rendered
        expect(screen.getByTestId("account-switcher")).toBeInTheDocument();
        expect(screen.getByTestId("account-chart")).toBeInTheDocument();
        expect(screen.getByTestId("recent-transaction")).toBeInTheDocument();
        expect(screen.getByTestId("download-dialog")).toBeInTheDocument();
        // Check the transactions link
        const transactionsLink = screen.getByRole("link", { name: /see all transactions/i });
        expect(transactionsLink).toHaveAttribute("href", "/transactions");
    });

    test("dispatches openAddTransferFundsDialog when Transfer funds button is clicked", () => {
        const { store } = renderComponent();
        fireEvent.click(screen.getByText("Transfer funds"));
        expect(store.dispatch).toHaveBeenCalledWith(openAddTransferFundsDialog());
    });

    test("renders AddTransferFunds when isAddTransferFundsOpen is true", () => {
        renderComponent({ isAddTransferFundsOpen: true });
        expect(screen.getByTestId("add-transfer-funds")).toBeInTheDocument();
    });

    test("dispatches closeTransferFundDialog when AddTransferFunds is closed", () => {
        const { store } = renderComponent({ isAddTransferFundsOpen: true });
        // Verify that the AddTransferFunds modal is visible
        expect(screen.getByTestId("add-transfer-funds")).toBeInTheDocument();
        // Click the Close button inside the modal
        fireEvent.click(screen.getByText("Close", { selector: "button" }));
        expect(store.dispatch).toHaveBeenCalledWith(closeTransferFundDialog());
    });

    test("dispatches closeTransferFundDialog when AddTransferFundsReview is closed", () => {
        const { store } = renderComponent({ isReviewTransferFundsOpen: true });
        expect(screen.getByTestId("add-transfer-funds-review")).toBeInTheDocument();
        fireEvent.click(screen.getByText("Close", { selector: "button" }));
        expect(store.dispatch).toHaveBeenCalledWith(closeTransferFundDialog());
    });

    test("dispatches openAddTransferFundsDialog when back is clicked in AddTransferFundsReview", () => {
        const { store } = renderComponent({ isReviewTransferFundsOpen: true });
        expect(screen.getByTestId("add-transfer-funds-review")).toBeInTheDocument();
        fireEvent.click(screen.getByText("Back", { selector: "button" }));
        expect(store.dispatch).toHaveBeenCalledWith(openAddTransferFundsDialog());
    });

    test("calls copyToClipboard for each copy button with the correct value", async () => {
        // The copy buttons appear for:
        // "Bank name" with value "First city monument bank" (hard-coded in component)
        // "Account number" with value from selectedAccount.accountNumber (default "********")
        const expectedValues = ["First city monument bank", "********"];
        renderComponent();
        const copyButtons = screen.getAllByTestId("copy-authenticator");
        expect(copyButtons).toHaveLength(expectedValues.length);

        // Click each copy button and verify that copyToClipboard is called with the correct value
        for (let i = 0; i < copyButtons.length; i++) {
            fireEvent.click(copyButtons[i]);
            await waitFor(() => {
                expect(copyToClipboard).toHaveBeenCalledWith(expectedValues[i]);
            });
        }
    });

    test("handles async copy operation correctly", async () => {
        renderComponent();
        const copyButton = screen.getAllByTestId("copy-authenticator")[0];
        fireEvent.click(copyButton);
        await waitFor(() => {
            expect(copyToClipboard).toHaveBeenCalledTimes(1);
        });
    });

    // Test to cover the loading state branch
    test("renders LoadingIndicator when loadingStatus is 'loading'", () => {
        renderComponent({}, { loadingStatus: "loading", selectedAccount: null });
        // Expect the LoadingIndicator (mocked with data-testid "loading-indicator") to be present.
        expect(screen.getByTestId("loading-indicator")).toBeInTheDocument();
        // Since selectedAccount is null, account details should not be rendered.
        expect(screen.queryByText("First city monument bank")).not.toBeInTheDocument();
    });

    // New test for TransferSuccessModal
    test("renders TransferSuccessModal when successModalOpen is true", () => {
        const { store } = renderComponent({}, { successModalOpen: true });
        const modal = screen.getByTestId("transfer-success-modal");
        expect(modal).toBeInTheDocument();
        // Scope the search to the modal container only
        const closeButton = within(modal).getByText("Close", { selector: "button" });
        fireEvent.click(closeButton);
        expect(store.dispatch).toHaveBeenCalledWith(closeTransferSuccessModal());
    });

    test("renders correctly when selectedAccount is present", () => {
        renderComponent();
        // AccountSwitcher and Chart
        expect(screen.getByTestId("account-switcher")).toBeInTheDocument();
        expect(screen.getByTestId("account-chart")).toBeInTheDocument();

        // Account details
        expect(screen.getByText("Bank name")).toBeInTheDocument();
        expect(screen.getByText("********")).toBeInTheDocument(); // Account number
        expect(screen.getByText("Main Account")).toBeInTheDocument(); // Account name
        expect(screen.getByText("Preferred Name")).toBeInTheDocument(); // Alias
        expect(screen.getByText("Savings")).toBeInTheDocument(); // Account type

        // Buttons and dialogs
        expect(screen.getByText("Transfer funds")).toBeInTheDocument();
        expect(screen.getByTestId("download-dialog")).toBeInTheDocument();
        expect(screen.getByTestId("recent-transaction")).toBeInTheDocument();
    });

    test("renders LoadingIndicator when loadingStatus is 'loading'", () => {
        renderComponent({}, { loadingStatus: "loading" });
        expect(screen.getByTestId("loading-indicator")).toBeInTheDocument();
    });

    test("clicking Transfer funds dispatches openAddTransferFundsDialog", () => {
        const { store } = renderComponent();
        fireEvent.click(screen.getByText("Transfer funds"));
        expect(store.dispatch).toHaveBeenCalledWith(openAddTransferFundsDialog());
    });

    test("renders AddTransferFunds when isAddTransferFundsOpen is true", () => {
        renderComponent({ isAddTransferFundsOpen: true });
        expect(screen.getByTestId("add-transfer-funds")).toBeInTheDocument();
    });

    test("dispatches closeTransferFundDialog when AddTransferFunds is closed", () => {
        const { store } = renderComponent({ isAddTransferFundsOpen: true });
        fireEvent.click(screen.getByText("Close"));
        expect(store.dispatch).toHaveBeenCalledWith(closeTransferFundDialog());
    });

    test("dispatches closeTransferFundDialog when AddTransferFundsReview is closed", () => {
        const { store } = renderComponent({ isReviewTransferFundsOpen: true });
        fireEvent.click(screen.getByText("Close"));
        expect(store.dispatch).toHaveBeenCalledWith(closeTransferFundDialog());
    });

    test("dispatches openAddTransferFundsDialog when back button is clicked in review", () => {
        const { store } = renderComponent({ isReviewTransferFundsOpen: true });
        fireEvent.click(screen.getByText("Back"));
        expect(store.dispatch).toHaveBeenCalledWith(openAddTransferFundsDialog());
    });

    test("renders TransferSuccessModal and closes it on button click", () => {
        const { store } = renderComponent({}, { successModalOpen: true });
        const modal = screen.getByTestId("transfer-success-modal");
        const closeButton = within(modal).getByText("Close");
        fireEvent.click(closeButton);
        expect(store.dispatch).toHaveBeenCalledWith(closeTransferSuccessModal());
    });

    test("calls copyToClipboard for both copy buttons", async () => {
        renderComponent();
        const expectedValues = ["First city monument bank", "********"];
        const copyButtons = screen.getAllByTestId("copy-authenticator");
        expect(copyButtons).toHaveLength(expectedValues.length);

        for (let i = 0; i < copyButtons.length; i++) {
            fireEvent.click(copyButtons[i]);
            await waitFor(() => {
                expect(copyToClipboard).toHaveBeenCalledWith(expectedValues[i]);
            });
        }
    });
});
