/* eslint-disable @typescript-eslint/no-require-imports */
// DownloadStatementModal.test.js
import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import DownloadStatementModal from "../../../../../src/components/page-components/dashboard/accounts/download-statement-modal";

// --- Mocks for Redux hooks ---
const mockDispatch = jest.fn();
jest.mock("@/redux/hooks", () => ({
    useAppDispatch: () => mockDispatch,
    useAppSelector: jest.fn(),
}));

// Set a default Redux selector state.
const { useAppSelector } = require("@/redux/hooks");
useAppSelector.mockImplementation((callback) =>
    callback({
        dashboard: { getAccountStatement: { loading: false, success: false } },
        account: { selectedAccount: { accountNumber: "**********" } },
    })
);

// --- Mocks for helper functions ---
const mockSendFeedback = jest.fn();
jest.mock("@/functions/feedback", () => ({
    sendFeedback: (...args) => mockSendFeedback(...args),
}));

const mockFDateNumeric = jest.fn((dateStr, format) => {
    // For testing, if "yyyy-MM-dd" is requested, format properly.
    if (format === "yyyy-MM-dd") {
        const date = new Date(dateStr);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        return `${year}-${month}-${day}`;
    }
    return dateStr.slice(0, 10);
});
jest.mock("@/functions/date", () => ({
    fDateNumeric: (...args) => mockFDateNumeric(...args),
}));

// --- Mock the action creator ---
const mockGetAccountStatement = jest.fn(() => ({ type: "GET_ACCOUNT_STATEMENT" }));
jest.mock("@/redux/actions/dashboardActions", () => ({
    getAccountStatement: (...args) => mockGetAccountStatement(...args),
}));

// --- Dummy dateFilterValues ---
jest.mock("../../../../../src/components/page-components/dashboard/accounts/data", () => ({
    dateFilterValues: [
        {
            label: "Custom date",
            startDate: new Date("2023-01-01"),
            endDate: new Date("2023-01-02"),
        },
        {
            label: "Last week",
            startDate: new Date("2022-12-25"),
            endDate: new Date("2022-12-31"),
        },
        {
            label: "Last month",
            startDate: new Date("2022-12-01"),
            endDate: new Date("2022-12-31"),
        },
    ],
}));

// --- Mock DatePicker ---
jest.mock("@/components/common/date-picker", () => {
    return function MockDatePicker(props) {
        const { label, value, onChange } = props;
        const formattedValue = value ? value.toISOString().substring(0, 10) : "";
        return (
            <input
                type="date"
                aria-label={label}
                value={formattedValue}
                onChange={(e) => onChange && onChange(new Date(e.target.value))}
                disabled={props.disable || false}
                data-testid={props["data-testid"]}
            />
        );
    };
});

// --- Mock Dropdown ---
jest.mock("@/components/common/dropdown", () => {
    return function MockDropdown(props) {
        return (
            <select
                aria-label={props.label}
                value={props.value.value}
                onChange={(e) => {
                    props.onChange({ label: e.target.value, value: e.target.value });
                }}
            >
                {props.options.map((option) => (
                    <option key={option.value} value={option.value}>
                        {option.label}
                    </option>
                ))}
            </select>
        );
    };
});

// --- Mock LabelInput ---
jest.mock("@/components/common/label-input", () => {
    return function MockLabelInput(props) {
        return (
            <input
                aria-label={props.label}
                value={props.value}
                onChange={props.onChange}
                data-testid={props["data-testid"]}
            />
        );
    };
});

// --- Mock CustomModal ---
jest.mock("@/components/common/custom-modal", () => {
    return function MockCustomModal({ children, isOpen, onRequestClose, title }) {
        if (!isOpen) return null;
        return (
            <div data-testid="modal">
                <div data-testid="modal-title">{title}</div>
                <button onClick={onRequestClose} data-testid="modal-close">
                    ×
                </button>
                {children}
            </div>
        );
    };
});

// --- Mock Button ---
jest.mock("@/components/common/buttonv3", () => ({
    Button: function MockButton(props) {
        return (
            <button
                type={props.type}
                onClick={props.onClick}
                disabled={props.disabled}
                data-testid={props.variant === "outline" ? "cancel-button" : "submit-button"}
            >
                {props.loading ? "Loading..." : props.children}
            </button>
        );
    },
}));

describe("DownloadStatementModal", () => {
    const onCloseMock = jest.fn();

    beforeEach(() => {
        jest.clearAllMocks();
        useAppSelector.mockImplementation((callback) =>
            callback({
                dashboard: { getAccountStatement: { loading: false, success: false } },
                account: { selectedAccount: { accountNumber: "**********" } },
            })
        );
    });

    describe("Modal Rendering", () => {
        it("renders modal when open is true", () => {
            render(<DownloadStatementModal open={true} onClose={onCloseMock} />);
            expect(screen.getByTestId("modal")).toBeInTheDocument();
            expect(screen.getByTestId("modal-title")).toHaveTextContent("Get account statement");
        });

        it("does not render modal when open is false", () => {
            render(<DownloadStatementModal open={false} onClose={onCloseMock} />);
            expect(screen.queryByTestId("modal")).not.toBeInTheDocument();
        });

        it("renders all form elements correctly", () => {
            render(<DownloadStatementModal open={true} onClose={onCloseMock} />);

            expect(screen.getByLabelText("Date")).toBeInTheDocument();
            expect(screen.getByLabelText("Start date")).toBeInTheDocument();
            expect(screen.getByLabelText("End date")).toBeInTheDocument();
            expect(screen.getByLabelText("Email address")).toBeInTheDocument();
            expect(screen.getByTestId("cancel-button")).toBeInTheDocument();
            expect(screen.getByTestId("submit-button")).toBeInTheDocument();
        });
    });

    describe("Modal Closing", () => {
        it("calls onClose when Cancel button is clicked", () => {
            render(<DownloadStatementModal open={true} onClose={onCloseMock} />);
            const cancelButton = screen.getByTestId("cancel-button");
            fireEvent.click(cancelButton);
            expect(onCloseMock).toHaveBeenCalled();
        });

        it("calls onClose when modal close button is clicked", () => {
            render(<DownloadStatementModal open={true} onClose={onCloseMock} />);
            const closeButton = screen.getByTestId("modal-close");
            fireEvent.click(closeButton);
            expect(onCloseMock).toHaveBeenCalled();
        });
    });

    describe("Date Option Changes", () => {
        it("updates dateOption when a different option is selected", async () => {
            render(<DownloadStatementModal open={true} onClose={onCloseMock} />);
            const dropdown = screen.getByLabelText("Date");

            fireEvent.change(dropdown, { target: { value: "Last week" } });

            await waitFor(() => {
                expect(dropdown.value).toBe("Last week");
            });
        });

        it("disables date pickers when non-custom date is selected", async () => {
            render(<DownloadStatementModal open={true} onClose={onCloseMock} />);
            const dropdown = screen.getByLabelText("Date");

            fireEvent.change(dropdown, { target: { value: "Last week" } });

            await waitFor(() => {
                const startDateInput = screen.getByLabelText("Start date");
                const endDateInput = screen.getByLabelText("End date");
                expect(startDateInput).toBeDisabled();
                expect(endDateInput).toBeDisabled();
            });
        });

        it("enables date pickers when custom date is selected", async () => {
            render(<DownloadStatementModal open={true} onClose={onCloseMock} />);
            const dropdown = screen.getByLabelText("Date");

            // First select non-custom, then back to custom
            fireEvent.change(dropdown, { target: { value: "Last week" } });
            fireEvent.change(dropdown, { target: { value: "Custom date" } });

            await waitFor(() => {
                const startDateInput = screen.getByLabelText("Start date");
                const endDateInput = screen.getByLabelText("End date");
                expect(startDateInput).not.toBeDisabled();
                expect(endDateInput).not.toBeDisabled();
            });
        });

        it("updates dates when date option changes to predefined option", async () => {
            render(<DownloadStatementModal open={true} onClose={onCloseMock} />);
            const dropdown = screen.getByLabelText("Date");

            fireEvent.change(dropdown, { target: { value: "Last week" } });

            await waitFor(() => {
                const startDateInput = screen.getByLabelText("Start date");
                const endDateInput = screen.getByLabelText("End date");
                expect(startDateInput.value).toBe("2022-12-25");
                expect(endDateInput.value).toBe("2022-12-31");
            });
        });
    });

    describe("Date Picker Changes", () => {
        it("updates start date when custom date is selected and start date picker changes", async () => {
            render(<DownloadStatementModal open={true} onClose={onCloseMock} />);
            const startDateInput = screen.getByLabelText("Start date");

            // Ensure we're in custom date mode
            const dropdown = screen.getByLabelText("Date");
            fireEvent.change(dropdown, { target: { value: "Custom date" } });

            fireEvent.change(startDateInput, { target: { value: "2023-02-01" } });

            await waitFor(() => {
                expect(startDateInput.value).toBe("2023-02-01");
            });
        });

        it("updates end date when custom date is selected and end date picker changes", async () => {
            render(<DownloadStatementModal open={true} onClose={onCloseMock} />);
            const endDateInput = screen.getByLabelText("End date");

            // Ensure we're in custom date mode
            const dropdown = screen.getByLabelText("Date");
            fireEvent.change(dropdown, { target: { value: "Custom date" } });

            fireEvent.change(endDateInput, { target: { value: "2023-02-05" } });

            await waitFor(() => {
                expect(endDateInput.value).toBe("2023-02-05");
            });
        });

        it("does not update start date when non-custom date is selected", async () => {
            render(<DownloadStatementModal open={true} onClose={onCloseMock} />);
            const dropdown = screen.getByLabelText("Date");
            fireEvent.change(dropdown, { target: { value: "Last week" } });

            const startDateInput = screen.getByLabelText("Start date");
            const originalValue = startDateInput.value;

            fireEvent.change(startDateInput, { target: { value: "2023-02-01" } });

            // Value should remain the same
            expect(startDateInput.value).toBe(originalValue);
        });

        it("does not update end date when non-custom date is selected", async () => {
            render(<DownloadStatementModal open={true} onClose={onCloseMock} />);
            const dropdown = screen.getByLabelText("Date");
            fireEvent.change(dropdown, { target: { value: "Last week" } });

            const endDateInput = screen.getByLabelText("End date");
            const originalValue = endDateInput.value;

            fireEvent.change(endDateInput, { target: { value: "2023-02-05" } });

            // Value should remain the same
            expect(endDateInput.value).toBe(originalValue);
        });
    });

    describe("Email Address Input", () => {
        it("updates email address when input changes", async () => {
            render(<DownloadStatementModal open={true} onClose={onCloseMock} />);
            const emailInput = screen.getByLabelText("Email address");

            fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });

            expect(emailInput.value).toBe("<EMAIL>");
        });

        it("enables submit button when email is provided", async () => {
            render(<DownloadStatementModal open={true} onClose={onCloseMock} />);
            const emailInput = screen.getByLabelText("Email address");
            const submitButton = screen.getByTestId("submit-button");

            expect(submitButton).toBeDisabled();

            fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });

            expect(submitButton).not.toBeDisabled();
        });

        it("disables submit button when email is empty", () => {
            render(<DownloadStatementModal open={true} onClose={onCloseMock} />);
            const submitButton = screen.getByTestId("submit-button");

            expect(submitButton).toBeDisabled();
        });
    });

    describe("Form Submission", () => {
        it("dispatches getAccountStatement action on form submission", async () => {
            render(<DownloadStatementModal open={true} onClose={onCloseMock} />);

            const emailInput = screen.getByLabelText("Email address");
            fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });

            const form = screen.getByTestId("submit-button").closest("form");
            fireEvent.submit(form);

            await waitFor(() => {
                expect(mockDispatch).toHaveBeenCalled();
                expect(mockGetAccountStatement).toHaveBeenCalledWith({
                    startDate: expect.any(String),
                    endDate: expect.any(String),
                    accountNumber: "**********",
                    emailAddress: "<EMAIL>",
                });
            });
        });

        it("prevents default form submission", async () => {
            render(<DownloadStatementModal open={true} onClose={onCloseMock} />);

            const emailInput = screen.getByLabelText("Email address");
            fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });

            const form = screen.getByTestId("submit-button").closest("form");
            const submitEvent = new Event("submit", { bubbles: true, cancelable: true });
            const preventDefaultSpy = jest.spyOn(submitEvent, "preventDefault");

            fireEvent(form, submitEvent);

            expect(preventDefaultSpy).toHaveBeenCalled();
        });

        it("uses empty string for accountNumber when selectedAccount is null", async () => {
            useAppSelector.mockImplementation((callback) =>
                callback({
                    dashboard: { getAccountStatement: { loading: false, success: false } },
                    account: { selectedAccount: null },
                })
            );

            render(<DownloadStatementModal open={true} onClose={onCloseMock} />);

            const emailInput = screen.getByLabelText("Email address");
            fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });

            const form = screen.getByTestId("submit-button").closest("form");
            fireEvent.submit(form);

            await waitFor(() => {
                expect(mockGetAccountStatement).toHaveBeenCalledWith({
                    startDate: expect.any(String),
                    endDate: expect.any(String),
                    accountNumber: "",
                    emailAddress: "<EMAIL>",
                });
            });
        });
    });

    describe("Loading State", () => {
        it("shows loading state and disables submit button when loading", () => {
            useAppSelector.mockImplementation((callback) =>
                callback({
                    dashboard: { getAccountStatement: { loading: true, success: false } },
                    account: { selectedAccount: { accountNumber: "**********" } },
                })
            );

            render(<DownloadStatementModal open={true} onClose={onCloseMock} />);

            const submitButton = screen.getByTestId("submit-button");
            expect(submitButton).toBeDisabled();
            expect(submitButton).toHaveTextContent("Loading...");
        });

        it("disables submit button when both loading and no email", () => {
            useAppSelector.mockImplementation((callback) =>
                callback({
                    dashboard: { getAccountStatement: { loading: true, success: false } },
                    account: { selectedAccount: { accountNumber: "**********" } },
                })
            );

            render(<DownloadStatementModal open={true} onClose={onCloseMock} />);

            const submitButton = screen.getByTestId("submit-button");
            expect(submitButton).toBeDisabled();
        });
    });

    describe("Success State", () => {
        it("calls sendFeedback and onClose when success becomes true", async () => {
            const { rerender } = render(<DownloadStatementModal open={true} onClose={onCloseMock} />);

            useAppSelector.mockImplementation((callback) =>
                callback({
                    dashboard: { getAccountStatement: { loading: false, success: true } },
                    account: { selectedAccount: { accountNumber: "**********" } },
                })
            );

            rerender(<DownloadStatementModal open={true} onClose={onCloseMock} />);

            await waitFor(() => {
                expect(mockSendFeedback).toHaveBeenCalledWith(
                    "Account statement request submitted successfully",
                    "success"
                );
                expect(onCloseMock).toHaveBeenCalled();
            });
        });

        it("does not call sendFeedback and onClose when success is false", async () => {
            render(<DownloadStatementModal open={true} onClose={onCloseMock} />);

            // Wait a bit to ensure useEffect doesn't trigger
            await new Promise((resolve) => setTimeout(resolve, 100));

            expect(mockSendFeedback).not.toHaveBeenCalled();
            expect(onCloseMock).not.toHaveBeenCalled();
        });
    });
});

describe("DownloadStatementModal - Edge Cases", () => {
    const onCloseMock = jest.fn();

    beforeEach(() => {
        jest.clearAllMocks();
        useAppSelector.mockImplementation((callback) =>
            callback({
                dashboard: { getAccountStatement: { loading: false, success: false } },
                account: { selectedAccount: { accountNumber: "**********" } },
            })
        );
    });

    describe("Date Option Edge Cases", () => {
        it("handles date option without startDate and endDate", async () => {
            // Mock data with incomplete date option
            const data = require("../../../../../src/components/page-components/dashboard/accounts/data");
            data.dateFilterValues = [
                {
                    label: "Custom date",
                    startDate: new Date("2023-01-01"),
                    endDate: new Date("2023-01-02"),
                },
                {
                    label: "Incomplete option",
                    // Missing startDate and endDate
                },
            ];

            render(<DownloadStatementModal open={true} onClose={onCloseMock} />);
            const dropdown = screen.getByLabelText("Date");

            fireEvent.change(dropdown, { target: { value: "Incomplete option" } });

            // Should not crash and dates should remain unchanged
            await waitFor(() => {
                expect(dropdown.value).toBe("Incomplete option");
            });
        });
    });

    describe("Fallback Date Handling", () => {
        beforeEach(() => {
            // Override dateFilterValues to be empty for fallback testing
            const data = require("../../../../../src/components/page-components/dashboard/accounts/data");
            data.dateFilterValues = [];
            jest.useFakeTimers().setSystemTime(new Date("2023-03-15T00:00:00Z"));
        });

        afterEach(() => {
            jest.useRealTimers();
        });

        it("uses fallback dates when no matching option is found", () => {
            render(<DownloadStatementModal open={true} onClose={onCloseMock} />);

            const startDateInput = screen.getByLabelText("Start date");
            const endDateInput = screen.getByLabelText("End date");

            expect(startDateInput.value).toBe("2023-03-15");
            expect(endDateInput.value).toBe("2023-03-15");
        });
    });
});

describe("DownloadStatementModal - Component Integration", () => {
    const onCloseMock = jest.fn();

    beforeEach(() => {
        jest.clearAllMocks();
        useAppSelector.mockImplementation((callback) =>
            callback({
                dashboard: { getAccountStatement: { loading: false, success: false } },
                account: { selectedAccount: { accountNumber: "**********" } },
            })
        );
    });

    it("handles modal open/close cycle", () => {
        const { rerender } = render(<DownloadStatementModal open={false} onClose={onCloseMock} />);

        expect(screen.queryByTestId("modal")).not.toBeInTheDocument();

        rerender(<DownloadStatementModal open={true} onClose={onCloseMock} />);
        expect(screen.getByTestId("modal")).toBeInTheDocument();

        rerender(<DownloadStatementModal open={false} onClose={onCloseMock} />);
        expect(screen.queryByTestId("modal")).not.toBeInTheDocument();
    });
});
