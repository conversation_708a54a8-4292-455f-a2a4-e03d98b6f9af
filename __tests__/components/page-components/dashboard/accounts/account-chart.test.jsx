/* eslint-disable @typescript-eslint/no-require-imports */
import Account<PERSON>hart from "@/components/page-components/dashboard/accounts/account-chart";
import { fireEvent, render, screen, waitFor } from "@testing-library/react";

jest.mock("recharts", () => ({
    ResponsiveContainer: ({ children }) => <div data-testid="responsive-container">{children}</div>,
    AreaChart: ({ children, data }) => (
        <div data-testid="area-chart" data-chart-data={JSON.stringify(data)}>
            {children}
        </div>
    ),
    XAxis: (props) => <div data-testid="x-axis" {...props} />,
    YAxis: ({ tickFormatter }) => (
        <div data-testid="y-axis">
            {tickFormatter ? tickFormatter(0) : ""} {tickFormatter ? tickFormatter(1000) : ""}
        </div>
    ),
    CartesianGrid: () => <div data-testid="cartesian-grid" />,
    Tooltip: () => <div data-testid="tooltip" />,
    Area: () => <div data-testid="area" />,
}));

// --- Mock for the Dropdown component ---
jest.mock("@/components/common/dropdown", () => ({
    __esModule: true,
    default: ({ value, options, onChange = () => {} }) => (
        <select
            data-testid="dropdown"
            value={value.value}
            onChange={(e) => onChange({ label: e.target.value, value: e.target.value })}
        >
            {options.map((option) => (
                <option key={option.value} value={option.value}>
                    {option.label}
                </option>
            ))}
        </select>
    ),
}));

// --- Mock for dateFilterValues ---
// We provide two options ("Last 30 days" and "Last 3 months") and one "Custom date" (which is filtered out).
jest.mock("@/components/page-components/dashboard/accounts/data", () => ({
    dateFilterValues: [
        {
            label: "Last 30 days",
            startDate: new Date("2025-02-23"),
            endDate: new Date("2025-03-25"),
        },
        {
            label: "Last 3 months",
            startDate: new Date("2025-01-01"),
            endDate: new Date("2025-03-25"),
        },
        { label: "Custom date", startDate: new Date(), endDate: new Date() },
    ],
}));

// --- Mock for LoadingIndicator ---
jest.mock("@/components/common/loading-indicator", () => ({
    __esModule: true,
    default: () => <div data-testid="loading-indicator">Loading...</div>,
}));

// --- Mocks for Redux hooks and actions ---
const mockDispatch = jest.fn();
jest.mock("@/redux/hooks", () => ({
    useAppDispatch: () => mockDispatch,
    useAppSelector: jest.fn(),
}));
jest.mock("@/redux/actions/dashboardActions", () => ({
    getTransactions: jest.fn().mockImplementation((params) => ({
        type: "GET_TRANSACTIONS",
        payload: params,
    })),
}));

describe("AccountChart Component", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("displays the correct default dropdown value", () => {
        require("@/redux/hooks").useAppSelector.mockImplementation((selector) =>
            selector({
                dashboard: { getTransactions: { loading: false, data: [] } },
                account: { selectedAccount: { accountNumber: "**********" } },
            })
        );
        render(<AccountChart />);
        expect(screen.getByTestId("dropdown")).toHaveValue("Last 30 days");
    });

    it("updates dropdown value when changed", () => {
        require("@/redux/hooks").useAppSelector.mockImplementation((selector) =>
            selector({
                dashboard: { getTransactions: { loading: false, data: [] } },
                account: { selectedAccount: { accountNumber: "**********" } },
            })
        );
        render(<AccountChart />);
        const dropdown = screen.getByTestId("dropdown");

        // Change dropdown value to "Last 3 months"
        fireEvent.change(dropdown, { target: { value: "Last 3 months" } });
        expect(dropdown).toHaveValue("Last 3 months");
    });

    it("calls getTransactions on mount with correctly formatted dates", async () => {
        const { getTransactions } = require("@/redux/actions/dashboardActions");
        require("@/redux/hooks").useAppSelector.mockImplementation((selector) =>
            selector({
                dashboard: { getTransactions: { loading: false, data: [] } },
                account: { selectedAccount: { accountNumber: "**********" } },
            })
        );
        render(<AccountChart />);

        // Wait for the useEffect to trigger dispatch.
        await waitFor(() => {
            expect(mockDispatch).toHaveBeenCalled();
        });

        // Default is "Last 30 days" so we expect dates "2025-02-23" and "2025-03-25"
        expect(getTransactions).toHaveBeenCalledWith({
            startDate: "2025-02-23",
            endDate: "2025-03-25",
            accountNumber: "**********",
        });
    });

    it("triggers a new dispatch call when dropdown value changes", async () => {
        const { getTransactions } = require("@/redux/actions/dashboardActions");
        require("@/redux/hooks").useAppSelector.mockImplementation((selector) =>
            selector({
                dashboard: { getTransactions: { loading: false, data: [] } },
                account: { selectedAccount: { accountNumber: "**********" } },
            })
        );
        render(<AccountChart />);
        const dropdown = screen.getByTestId("dropdown");

        // Change to "Last 3 months" option.
        fireEvent.change(dropdown, { target: { value: "Last 3 months" } });

        // Wait for the effect triggered by the date change.
        await waitFor(() => {
            // Two dispatch calls: one for initial mount and one for the update.
            expect(mockDispatch).toHaveBeenCalledTimes(2);
        });
        // Expect the dispatch call to have been made with dates from "Last 3 months".
        expect(getTransactions).toHaveBeenCalledWith({
            startDate: "2025-01-01",
            endDate: "2025-03-25",
            accountNumber: "**********",
        });
    });

    it("computes chart data correctly when data exists", () => {
        // Provide sample data with createdDate and amount.
        const sampleData = [
            { createdDate: "2025-03-01T00:00:00.000Z", amount: 5000 },
            { createdDate: "2025-03-02T00:00:00.000Z", amount: 10000 },
        ];
        require("@/redux/hooks").useAppSelector.mockReturnValue({
            loading: false,
            data: sampleData,
        });
        render(<AccountChart />);
        const areaChart = screen.getByTestId("area-chart");
        const chartData = JSON.parse(areaChart.getAttribute("data-chart-data") || "[]");
        expect(chartData).toHaveLength(2);
        expect(chartData[0]).toEqual({ name: "Mar 1", value: 5000 });
        expect(chartData[1]).toEqual({ name: "Mar 2", value: 10000 });
    });

    it("uses fallback dates when dropdown value is not found in dateFilterValues", async () => {
        // Use fake timers to control the system time.
        const fixedDate = new Date("2025-04-01T00:00:00.000Z");
        jest.useFakeTimers("modern");
        jest.setSystemTime(fixedDate);

        const { getTransactions } = require("@/redux/actions/dashboardActions");
        require("@/redux/hooks").useAppSelector.mockImplementation((selector) =>
            selector({
                dashboard: { getTransactions: { loading: false, data: [] } },
                account: { selectedAccount: { accountNumber: "**********" } },
            })
        );

        render(<AccountChart />);
        const dropdown = screen.getByTestId("dropdown");

        // Simulate a dropdown change to a value not in the available options.
        fireEvent.change(dropdown, { target: { value: "Non-existent" } });

        // Wait for the useEffect to trigger a new dispatch.
        await waitFor(() => {
            expect(mockDispatch).toHaveBeenCalledTimes(2);
        });

        // When no matching date option is found, the fallback uses new Date(), which is fixed to "2025-04-01".
        // fDateNumeric should then format this date as "2025-04-01".
        expect(getTransactions).toHaveBeenCalledWith({
            startDate: "2025-04-01",
            endDate: "2025-04-01",
            accountNumber: "**********",
        });

        // Restore timers.
        jest.useRealTimers();
    });

    it("renders LoadingIndicator when loading is true", async () => {
        // Set Redux state with loading: true.
        require("@/redux/hooks").useAppSelector.mockReturnValue({
            loading: true,
            data: [],
        });

        render(<AccountChart />);

        // Check that the loading indicator is rendered.
        expect(screen.getByTestId("loading-indicator")).toBeInTheDocument();

        // Verify that the chart is not rendered.
        expect(screen.queryByTestId("area-chart")).not.toBeInTheDocument();
    });

    it("does not render LoadingIndicator when loading is false", async () => {
        // Set Redux state with loading: false.
        require("@/redux/hooks").useAppSelector.mockReturnValue({
            loading: false,
            data: [], // No chart data
        });

        render(<AccountChart />);

        // Ensure the loading indicator is not present.
        expect(screen.queryByTestId("loading-indicator")).not.toBeInTheDocument();
    });
});
