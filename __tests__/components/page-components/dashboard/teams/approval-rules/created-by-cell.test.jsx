import React from "react";
import { render, screen } from "@testing-library/react";
import { Provider } from "react-redux";
import configureS<PERSON> from "redux-mock-store";
import "@testing-library/jest-dom";

// Import the component directly
import { CreatedByCell } from "@/components/page-components/dashboard/teams/approval-rules/column-data.tsx";

// Mock the getNameInitials function
jest.mock("@/functions/stringManipulations", () => ({
    getNameInitials: (name) => {
        if (!name) return "??";
        return name
            .split(" ")
            .map((n) => n[0])
            .join("");
    },
    capitalizeUserName: (name) => {
        if (!name) return "";
        return name.charAt(0).toUpperCase() + name.slice(1).toLowerCase();
    },
}));

// Create a mock store
const mockStore = configureStore([]);

describe("CreatedByCell Component", () => {
    // Test case 1: Creator found with role in roles array
    test("renders correctly when creator is found with role in roles array", () => {
        // Mock team members and roles data
        const teamMembers = [
            {
                id: 123,
                firstName: "John",
                lastName: "Doe",
                roleId: 456,
                email: "<EMAIL>",
            },
        ];

        const roles = [
            {
                id: 456,
                name: "Team Lead",
            },
        ];

        // Create store with mock data
        const store = mockStore({
            teamMembers: {
                getTeamMembers: {
                    data: teamMembers,
                    loading: false,
                    error: null,
                },
            },
            roles: {
                getAllRoles: {
                    data: roles,
                    loading: false,
                    error: null,
                },
            },
        });

        // Render the component with the store
        render(
            <Provider store={store}>
                <CreatedByCell creatorId="123" />
            </Provider>
        );

        // Check if the component renders correctly
        expect(screen.getByText("John Doe")).toBeInTheDocument();
        expect(screen.getByText("Team Lead")).toBeInTheDocument();
        expect(screen.getByTestId("member-initials")).toHaveTextContent("JD");
    });

    // Test case 2: Creator found with Super Admin role (roleId = 0)
    test("renders correctly when creator is a Super Admin", () => {
        // Mock team members data with Super Admin role
        const teamMembers = [
            {
                id: 123,
                firstName: "Jane",
                lastName: "Smith",
                roleId: 0, // Super Admin
                email: "<EMAIL>",
            },
        ];

        // Create store with mock data
        const store = mockStore({
            teamMembers: {
                getTeamMembers: {
                    data: teamMembers,
                    loading: false,
                    error: null,
                },
            },
            roles: {
                getAllRoles: {
                    data: [],
                    loading: false,
                    error: null,
                },
            },
        });

        // Render the component with the store
        render(
            <Provider store={store}>
                <CreatedByCell creatorId="123" />
            </Provider>
        );

        // Check if the component renders correctly
        expect(screen.getByText("Jane Smith")).toBeInTheDocument();
        expect(screen.getByText("Super Admin")).toBeInTheDocument();
        expect(screen.getByTestId("member-initials")).toHaveTextContent("JS");
    });

    // Test case 3: Creator found but role not found in roles array
    test("renders with Unknown Role when creator's role is not found", () => {
        // Mock team members data with a role that doesn't exist in roles array
        const teamMembers = [
            {
                id: 123,
                firstName: "Bob",
                lastName: "Johnson",
                roleId: 789, // This role doesn't exist in the roles array
                email: "<EMAIL>",
            },
        ];

        // Create store with mock data
        const store = mockStore({
            teamMembers: {
                getTeamMembers: {
                    data: teamMembers,
                    loading: false,
                    error: null,
                },
            },
            roles: {
                getAllRoles: {
                    data: [],
                    loading: false,
                    error: null,
                },
            },
        });

        // Render the component with the store
        render(
            <Provider store={store}>
                <CreatedByCell creatorId="123" />
            </Provider>
        );

        // Check if the component renders correctly
        expect(screen.getByText("Bob Johnson")).toBeInTheDocument();
        expect(screen.getByText("Unknown Role")).toBeInTheDocument();
        expect(screen.getByTestId("member-initials")).toHaveTextContent("BJ");
    });

    // Test case 4: Creator not found
    test("renders with creator ID when creator is not found", () => {
        // Create store with empty team members array
        const store = mockStore({
            teamMembers: {
                getTeamMembers: {
                    data: [],
                    loading: false,
                    error: null,
                },
            },
            roles: {
                getAllRoles: {
                    data: [],
                    loading: false,
                    error: null,
                },
            },
        });

        // Render the component with the store
        render(
            <Provider store={store}>
                <CreatedByCell creatorId="456" />
            </Provider>
        );

        // Check if the component renders with the creator ID
        expect(screen.getByText("456")).toBeInTheDocument();
        expect(screen.getByText("User ID: 456")).toBeInTheDocument();
        // The initials should be derived from the creator ID
        expect(screen.getByTestId("member-initials")).toBeInTheDocument();
    });

    // Test case 5: Empty or null creatorId
    test("renders with 'Unknown' when creatorId is empty or null", () => {
        // Create store with empty team members array
        const store = mockStore({
            teamMembers: {
                getTeamMembers: {
                    data: [],
                    loading: false,
                    error: null,
                },
            },
            roles: {
                getAllRoles: {
                    data: [],
                    loading: false,
                    error: null,
                },
            },
        });

        // Render the component with null creatorId
        render(
            <Provider store={store}>
                <CreatedByCell creatorId={null} />
            </Provider>
        );

        // Check if the component renders with "Unknown"
        expect(screen.getByText("Unknown")).toBeInTheDocument();
        // Use a more flexible approach to find the element containing "User ID:"
        const userIdElements = screen.getAllByText((content, element) => {
            return element.textContent.includes("User ID:");
        });
        expect(userIdElements.length).toBeGreaterThan(0);
        expect(screen.getByTestId("member-initials")).toBeInTheDocument();
    });

    // Test case 6: Non-numeric creatorId
    test("renders with creator ID when creatorId is not a number", () => {
        // Create store with team members array
        const store = mockStore({
            teamMembers: {
                getTeamMembers: {
                    data: [
                        {
                            id: 123,
                            firstName: "John",
                            lastName: "Doe",
                            roleId: 456,
                        },
                    ],
                    loading: false,
                    error: null,
                },
            },
            roles: {
                getAllRoles: {
                    data: [],
                    loading: false,
                    error: null,
                },
            },
        });

        // Render the component with non-numeric creatorId
        render(
            <Provider store={store}>
                <CreatedByCell creatorId="abc" />
            </Provider>
        );

        // Check if the component renders with the creator ID
        expect(screen.getByText("abc")).toBeInTheDocument();
        expect(screen.getByText("User ID: abc")).toBeInTheDocument();
        expect(screen.getByTestId("member-initials")).toBeInTheDocument();
    });
});
