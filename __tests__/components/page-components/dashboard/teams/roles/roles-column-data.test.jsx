import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { columns } from "@/components/page-components/dashboard/teams/roles/roles-column-data.tsx";
import { createColumnHelper } from "@tanstack/react-table";
import "@testing-library/jest-dom";
import { Provider } from "react-redux";
import configureMockStore from "redux-mock-store";
import thunk from "redux-thunk";

// Mock the imported components
jest.mock("@/components/common/checkbox", () => ({
    __esModule: true,
    default: jest.fn(({ checked, onCheckedChange, size, indeterminate, ...props }) => (
        <div
            data-testid={props["data-testid"] || "checkbox"}
            data-checked={checked}
            data-indeterminate={indeterminate}
            data-size={size}
            onClick={() => onCheckedChange(!checked)}
            {...props}
        />
    )),
}));

jest.mock("@/components/common/table/table-more-action", () => ({
    __esModule: true,
    default: jest.fn(({ data, menuItems, ...props }) => (
        <div>
            <div
                data-testid={props["data-testid"] || "table-more-action"}
                data-role-id={data.id}
                onClick={() => menuItems[0].onClick(data)}
                {...props}
            >
                More Actions
            </div>
            <button data-testid="edit-role-action" onClick={() => menuItems[1].onClick(data)}>
                Edit Role
            </button>
        </div>
    )),
}));

jest.mock("@/components/icons/team", () => ({
    __esModule: true,
    EyeIcon: jest.fn(() => <div data-testid="eye-icon">Eye Icon</div>),
    EditIcon: jest.fn(() => <div data-testid="edit-icon">Edit Icon</div>),
}));

jest.mock("@/functions/stringManipulations", () => ({
    capitalizeUserName: jest.fn((name) => {
        if (!name) return "";
        return name.charAt(0).toUpperCase() + name.slice(1).toLowerCase();
    }),
}));

// Set up mock store
const middlewares = [thunk];
const mockStore = configureMockStore(middlewares);
const initialState = {
    teamMembers: {
        getTeamMembers: {
            loading: false,
            success: true,
            error: null,
            data: [
                { id: "1", firstName: "John", lastName: "Doe", email: "<EMAIL>", roleId: 1 },
                { id: "2", firstName: "Jane", lastName: "Smith", email: "<EMAIL>", roleId: 2 },
            ],
        },
    },
    roles: {
        getAllRoles: {
            loading: false,
            success: true,
            error: null,
            data: [
                { id: 1, name: "Admin" },
                { id: 2, name: "Super Admin" },
                { id: 3, name: "Staff" },
            ],
        },
    },
    corporate: {
        corporateId: "123",
    },
};

// Helper function to render with Redux Provider
const renderWithProvider = (ui, state = {}) => {
    const store = mockStore({
        ...initialState,
        ...state,
    });
    return render(<Provider store={store}>{ui}</Provider>);
};

describe("Roles Table Columns", () => {
    const mockData = {
        id: "123",
        createdBy: "John Doe",
        type: "System",
        teamMembers: "5",
        name: "Admin",
        dateCreated: "2023-01-01",
    };

    const mockSetRoleToView = jest.fn();
    const mockSetIsViewPermissionsSideDrawerOpen = jest.fn();
    const mockSetIsEditMode = jest.fn();
    const mockSetIsCreateCustomRoleModalOpen = jest.fn();

    const mockTable = {
        getIsAllPageRowsSelected: jest.fn().mockReturnValue(false),
        toggleAllPageRowsSelected: jest.fn(),
        getIsSomePageRowsSelected: jest.fn().mockReturnValue(false),
        options: {
            meta: {
                setRoleToView: mockSetRoleToView,
                setIsViewPermissionsSideDrawerOpen: mockSetIsViewPermissionsSideDrawerOpen,
                setIsEditMode: mockSetIsEditMode,
                setIsCreateCustomRoleModalOpen: mockSetIsCreateCustomRoleModalOpen,
            },
        },
    };

    const mockRow = {
        getIsSelected: jest.fn().mockReturnValue(false),
        toggleSelected: jest.fn(),
        getValue: jest.fn((key) => mockData[key]),
        original: mockData,
        id: "123",
    };

    afterEach(() => {
        jest.clearAllMocks();
    });

    describe("Column Structure", () => {
        test("columns structure is correct", () => {
            // Verify total number of columns
            expect(columns.length).toBe(7);

            // Verify column ids/accessorKeys
            const columnIds = columns.map((col) => col.id || col.accessorKey);
            expect(columnIds).toContain("select");
            expect(columnIds).toContain("name");
            expect(columnIds).toContain("dateCreated");
            expect(columnIds).toContain("createdBy");
            expect(columnIds).toContain("type");
            expect(columnIds).toContain("teamMembers");
            expect(columnIds).toContain("actions");

            // Verify specific column properties
            const selectColumn = columns.find((col) => col.id === "select");
            expect(selectColumn?.enableSorting).toBe(false);
            expect(selectColumn?.enableHiding).toBe(false);
        });
    });

    describe("Select Column", () => {
        test("select column header renders correctly", () => {
            const selectColumn = columns.find((column) => column.id === "select");
            if (!selectColumn || !selectColumn.header) throw new Error("Select column not found");

            const headerFn = selectColumn.header;
            const HeaderComponent = () => headerFn({ table: mockTable });

            renderWithProvider(<HeaderComponent />);

            const checkbox = screen.getByTestId("checkbox-select-all");
            expect(checkbox).toBeInTheDocument();
            expect(checkbox).toHaveAttribute("data-checked", "false");
            expect(checkbox).toHaveAttribute("data-indeterminate", "false");
        });

        test("select column header with all selected", () => {
            const selectColumn = columns.find((column) => column.id === "select");
            if (!selectColumn || !selectColumn.header) throw new Error("Select column not found");

            const tableWithAllSelected = {
                ...mockTable,
                getIsAllPageRowsSelected: jest.fn().mockReturnValue(true),
                getIsSomePageRowsSelected: jest.fn().mockReturnValue(false),
            };

            const headerFn = selectColumn.header;
            const HeaderComponent = () => headerFn({ table: tableWithAllSelected });

            renderWithProvider(<HeaderComponent />);

            const checkbox = screen.getByTestId("checkbox-select-all");
            expect(checkbox).toHaveAttribute("data-checked", "true");
            expect(checkbox).toHaveAttribute("data-indeterminate", "false");
        });

        test("checkbox indeterminate state works correctly", () => {
            const selectColumn = columns.find((column) => column.id === "select");
            if (!selectColumn || !selectColumn.header) throw new Error("Select column not found");

            // Set up mocks for indeterminate state
            const indeterminateTable = {
                ...mockTable,
                getIsSomePageRowsSelected: jest.fn().mockReturnValue(true),
            };

            const headerFn = selectColumn.header;
            const HeaderComponent = () => headerFn({ table: indeterminateTable });

            renderWithProvider(<HeaderComponent />);

            const checkbox = screen.getByTestId("checkbox-select-all");
            expect(checkbox).toHaveAttribute("data-indeterminate", "true");
        });

        test("select column cell renders correctly", () => {
            const selectColumn = columns.find((column) => column.id === "select");
            if (!selectColumn || !selectColumn.cell) throw new Error("Select column not found");

            const cellFn = selectColumn.cell;
            const CellComponent = () => cellFn({ row: mockRow });

            renderWithProvider(<CellComponent />);

            const checkbox = screen.getByTestId("checkbox-select-row");
            expect(checkbox).toBeInTheDocument();
            expect(checkbox).toHaveAttribute("data-checked", "false");
        });

        test("select column cell with selected row", () => {
            const selectColumn = columns.find((column) => column.id === "select");
            if (!selectColumn || !selectColumn.cell) throw new Error("Select column not found");

            const selectedRow = {
                ...mockRow,
                getIsSelected: jest.fn().mockReturnValue(true),
            };

            const cellFn = selectColumn.cell;
            const CellComponent = () => cellFn({ row: selectedRow });

            renderWithProvider(<CellComponent />);

            const checkbox = screen.getByTestId("checkbox-select-row");
            expect(checkbox).toHaveAttribute("data-checked", "true");
        });

        test("select column header click toggles all rows", () => {
            const selectColumn = columns.find((column) => column.id === "select");
            if (!selectColumn || !selectColumn.header) throw new Error("Select column not found");

            const headerFn = selectColumn.header;
            const HeaderComponent = () => headerFn({ table: mockTable });

            renderWithProvider(<HeaderComponent />);

            const checkbox = screen.getByTestId("checkbox-select-all");
            fireEvent.click(checkbox);

            expect(mockTable.toggleAllPageRowsSelected).toHaveBeenCalledWith(true);
        });

        test("select column cell click toggles row selection", () => {
            const selectColumn = columns.find((column) => column.id === "select");
            if (!selectColumn || !selectColumn.cell) throw new Error("Select column not found");

            const cellFn = selectColumn.cell;
            const CellComponent = () => cellFn({ row: mockRow });

            renderWithProvider(<CellComponent />);

            const checkbox = screen.getByTestId("checkbox-select-row");
            fireEvent.click(checkbox);

            expect(mockRow.toggleSelected).toHaveBeenCalledWith(true);
        });
    });

    describe("Text Columns", () => {
        test("name column renders correctly", () => {
            const nameColumn = columns.find((column) => column.accessorKey === "name");
            if (!nameColumn || !nameColumn.cell) throw new Error("Name column not found");

            const cellFn = nameColumn.cell;
            const CellComponent = () => cellFn({ row: mockRow });

            renderWithProvider(<CellComponent />);

            expect(screen.getByText("Admin")).toBeInTheDocument();
            expect(mockRow.getValue).toHaveBeenCalledWith("name");
        });

        test("name column with empty value", () => {
            const nameColumn = columns.find((column) => column.accessorKey === "name");
            if (!nameColumn || !nameColumn.cell) throw new Error("Name column not found");

            const rowWithEmptyName = {
                ...mockRow,
                getValue: jest.fn((key) => (key === "name" ? "" : mockData[key])),
            };

            const cellFn = nameColumn.cell;
            const CellComponent = () => cellFn({ row: rowWithEmptyName });

            renderWithProvider(<CellComponent />);

            // Check for empty paragraph element
            const emptyElement = screen.getByText((content, element) => {
                return element.tagName.toLowerCase() === "p" && element.textContent === "";
            });
            expect(emptyElement).toBeInTheDocument();
        });

        test("type column renders correctly", () => {
            const typeColumn = columns.find((column) => column.accessorKey === "type");
            if (!typeColumn || !typeColumn.cell) throw new Error("Type column not found");

            const cellFn = typeColumn.cell;
            const CellComponent = () => cellFn({ row: mockRow });

            renderWithProvider(<CellComponent />);

            expect(screen.getByText("System")).toBeInTheDocument();
            expect(mockRow.getValue).toHaveBeenCalledWith("type");
        });

        test("type column with different values", () => {
            const typeColumn = columns.find((column) => column.accessorKey === "type");
            if (!typeColumn || !typeColumn.cell) throw new Error("Type column not found");

            const rowWithCustomType = {
                ...mockRow,
                getValue: jest.fn((key) => (key === "type" ? "Custom" : mockData[key])),
            };

            const cellFn = typeColumn.cell;
            const CellComponent = () => cellFn({ row: rowWithCustomType });

            renderWithProvider(<CellComponent />);

            expect(screen.getByText("Custom")).toBeInTheDocument();
        });
    });

    describe("Date Column", () => {
        test("dateCreated column renders correctly with ISO date string", () => {
            const dateColumn = columns.find((column) => column.accessorKey === "dateCreated");
            if (!dateColumn || !dateColumn.cell) throw new Error("Date column not found");

            const cellFn = dateColumn.cell;
            const CellComponent = () => cellFn({ row: mockRow });

            renderWithProvider(<CellComponent />);

            expect(screen.getByText("Sun Jan 01 2023")).toBeInTheDocument();
            expect(mockRow.getValue).toHaveBeenCalledWith("dateCreated");
        });

        test("dateCreated column handles timestamp string correctly", () => {
            const dateColumn = columns.find((column) => column.accessorKey === "dateCreated");
            if (!dateColumn || !dateColumn.cell) throw new Error("Date column not found");

            // Create a mock row with a timestamp string
            const timestampMockRow = {
                ...mockRow,
                getValue: jest.fn((key) => (key === "dateCreated" ? "1672531200000" : mockData[key])), // Jan 1, 2023 as timestamp
            };

            const cellFn = dateColumn.cell;
            const CellComponent = () => cellFn({ row: timestampMockRow });

            renderWithProvider(<CellComponent />);

            expect(screen.getByText("Sun Jan 01 2023")).toBeInTheDocument();
            expect(timestampMockRow.getValue).toHaveBeenCalledWith("dateCreated");
        });

        test("dateCreated column handles invalid date gracefully", () => {
            const dateColumn = columns.find((column) => column.accessorKey === "dateCreated");
            if (!dateColumn || !dateColumn.cell) throw new Error("Date column not found");

            // Create a mock row with an invalid date string
            const invalidDateMockRow = {
                ...mockRow,
                getValue: jest.fn((key) => (key === "dateCreated" ? "not-a-date" : mockData[key])),
            };

            const cellFn = dateColumn.cell;
            const CellComponent = () => cellFn({ row: invalidDateMockRow });

            renderWithProvider(<CellComponent />);

            // It should just display the original string if it can't be parsed
            expect(screen.getByText("not-a-date")).toBeInTheDocument();
            expect(invalidDateMockRow.getValue).toHaveBeenCalledWith("dateCreated");
        });

        test("dateCreated column handles empty string", () => {
            const dateColumn = columns.find((column) => column.accessorKey === "dateCreated");
            if (!dateColumn || !dateColumn.cell) throw new Error("Date column not found");

            const emptyDateMockRow = {
                ...mockRow,
                getValue: jest.fn((key) => (key === "dateCreated" ? "" : mockData[key])),
            };

            const cellFn = dateColumn.cell;
            const CellComponent = () => cellFn({ row: emptyDateMockRow });

            renderWithProvider(<CellComponent />);

            // Check for empty paragraph element
            const emptyElement = screen.getByText((content, element) => {
                return element.tagName.toLowerCase() === "p" && element.textContent === "";
            });
            expect(emptyElement).toBeInTheDocument();
        });
    });

    describe("CreatedBy Column", () => {
        test("createdBy column renders correctly when creator is found in team members", () => {
            const createdByColumn = columns.find((column) => column.accessorKey === "createdBy");
            if (!createdByColumn || !createdByColumn.cell) throw new Error("CreatedBy column not found");

            // Create a row with a creator ID that matches a team member
            const rowWithMatchingCreator = {
                ...mockRow,
                getValue: jest.fn((key) => (key === "createdBy" ? "1" : mockData[key])),
            };

            const cellFn = createdByColumn.cell;
            const CellComponent = () => cellFn({ row: rowWithMatchingCreator });

            renderWithProvider(<CellComponent />);

            // Should display the capitalized name of the creator found in team members
            expect(screen.getByText("1")).toBeInTheDocument();
            expect(rowWithMatchingCreator.getValue).toHaveBeenCalledWith("createdBy");
        });

        test("createdBy column renders correctly when creator is not found", () => {
            const createdByColumn = columns.find((column) => column.accessorKey === "createdBy");
            if (!createdByColumn || !createdByColumn.cell) throw new Error("CreatedBy column not found");

            const cellFn = createdByColumn.cell;
            const CellComponent = () => cellFn({ row: mockRow });

            renderWithProvider(<CellComponent />);

            // Should display the original creator ID when not found in team members
            expect(screen.getByText("John Doe")).toBeInTheDocument();
            expect(mockRow.getValue).toHaveBeenCalledWith("createdBy");
        });

        test("createdBy column with creator not found in team members", () => {
            const createdByColumn = columns.find((column) => column.accessorKey === "createdBy");
            if (!createdByColumn || !createdByColumn.cell) throw new Error("CreatedBy column not found");

            const rowWithUnknownCreator = {
                ...mockRow,
                getValue: jest.fn((key) => (key === "createdBy" ? "999" : mockData[key])),
            };

            const cellFn = createdByColumn.cell;
            const CellComponent = () => cellFn({ row: rowWithUnknownCreator });

            renderWithProvider(<CellComponent />);

            expect(screen.getByText("999")).toBeInTheDocument();
        });

        test("createdBy column with invalid creator ID", () => {
            const createdByColumn = columns.find((column) => column.accessorKey === "createdBy");
            if (!createdByColumn || !createdByColumn.cell) throw new Error("CreatedBy column not found");

            const rowWithInvalidId = {
                ...mockRow,
                getValue: jest.fn((key) => (key === "createdBy" ? "invalid-id" : mockData[key])),
            };

            const cellFn = createdByColumn.cell;
            const CellComponent = () => cellFn({ row: rowWithInvalidId });

            renderWithProvider(<CellComponent />);

            expect(screen.getByText("invalid-id")).toBeInTheDocument();
        });

        test("createdBy column with empty team members data", () => {
            const createdByColumn = columns.find((column) => column.accessorKey === "createdBy");
            if (!createdByColumn || !createdByColumn.cell) throw new Error("CreatedBy column not found");

            const rowWithCreatorId = {
                ...mockRow,
                getValue: jest.fn((key) => (key === "createdBy" ? "1" : mockData[key])),
            };

            const cellFn = createdByColumn.cell;
            const CellComponent = () => cellFn({ row: rowWithCreatorId });

            const emptyState = {
                teamMembers: {
                    getTeamMembers: {
                        loading: false,
                        success: true,
                        error: null,
                        data: [],
                    },
                },
            };

            renderWithProvider(<CellComponent />, emptyState);

            expect(screen.getByText("1")).toBeInTheDocument();
        });

        test("createdBy column with null team members data", () => {
            const createdByColumn = columns.find((column) => column.accessorKey === "createdBy");
            if (!createdByColumn || !createdByColumn.cell) throw new Error("CreatedBy column not found");

            const rowWithCreatorId = {
                ...mockRow,
                getValue: jest.fn((key) => (key === "createdBy" ? "1" : mockData[key])),
            };

            const cellFn = createdByColumn.cell;
            const CellComponent = () => cellFn({ row: rowWithCreatorId });

            const nullState = {
                teamMembers: {
                    getTeamMembers: {
                        loading: false,
                        success: true,
                        error: null,
                        data: null,
                    },
                },
            };

            renderWithProvider(<CellComponent />, nullState);

            expect(screen.getByText("1")).toBeInTheDocument();
        });

        test("createdBy column with empty creator ID shows Unknown", () => {
            const createdByColumn = columns.find((column) => column.accessorKey === "createdBy");
            if (!createdByColumn || !createdByColumn.cell) throw new Error("CreatedBy column not found");

            const rowWithEmptyCreator = {
                ...mockRow,
                getValue: jest.fn((key) => (key === "createdBy" ? "" : mockData[key])),
            };

            const cellFn = createdByColumn.cell;
            const CellComponent = () => cellFn({ row: rowWithEmptyCreator });

            renderWithProvider(<CellComponent />);

            expect(screen.getByText("Unknown")).toBeInTheDocument();
            expect(rowWithEmptyCreator.getValue).toHaveBeenCalledWith("createdBy");
        });

        test("createdBy column with null creator ID shows Unknown", () => {
            const createdByColumn = columns.find((column) => column.accessorKey === "createdBy");
            if (!createdByColumn || !createdByColumn.cell) throw new Error("CreatedBy column not found");

            const rowWithNullCreator = {
                ...mockRow,
                getValue: jest.fn((key) => (key === "createdBy" ? null : mockData[key])),
            };

            const cellFn = createdByColumn.cell;
            const CellComponent = () => cellFn({ row: rowWithNullCreator });

            renderWithProvider(<CellComponent />);

            expect(screen.getByText("Unknown")).toBeInTheDocument();
            expect(rowWithNullCreator.getValue).toHaveBeenCalledWith("createdBy");
        });
    });

    describe("Team Members Column", () => {
        test("teamMembers column renders correctly", () => {
            const teamMembersColumn = columns.find((column) => column.accessorKey === "teamMembers");
            if (!teamMembersColumn || !teamMembersColumn.cell) throw new Error("TeamMembers column not found");

            const cellFn = teamMembersColumn.cell;
            const CellComponent = () => cellFn({ row: mockRow });

            renderWithProvider(<CellComponent />);

            // The component shows 0 because it's using useAppSelector which is mocked
            expect(screen.getByText("0")).toBeInTheDocument();
        });

        test("teamMembers column with matching team members", () => {
            const teamMembersColumn = columns.find((column) => column.accessorKey === "teamMembers");
            if (!teamMembersColumn || !teamMembersColumn.cell) throw new Error("TeamMembers column not found");

            const cellFn = teamMembersColumn.cell;
            const CellComponent = () => cellFn({ row: mockRow });

            const customState = {
                teamMembers: {
                    getTeamMembers: {
                        loading: false,
                        success: true,
                        error: null,
                        data: [
                            { id: "1", firstName: "John", lastName: "Doe", email: "<EMAIL>", roleId: 123 },
                            { id: "2", firstName: "Jane", lastName: "Smith", email: "<EMAIL>", roleId: 123 },
                            { id: "3", firstName: "Bob", lastName: "Johnson", email: "<EMAIL>", roleId: 456 },
                        ],
                    },
                },
            };

            renderWithProvider(<CellComponent />, customState);

            expect(screen.getByText("0")).toBeInTheDocument();
        });

        test("teamMembers column with empty team members data", () => {
            const teamMembersColumn = columns.find((column) => column.accessorKey === "teamMembers");
            if (!teamMembersColumn || !teamMembersColumn.cell) throw new Error("TeamMembers column not found");

            const cellFn = teamMembersColumn.cell;
            const CellComponent = () => cellFn({ row: mockRow });

            const emptyState = {
                teamMembers: {
                    getTeamMembers: {
                        loading: false,
                        success: true,
                        error: null,
                        data: [],
                    },
                },
            };

            renderWithProvider(<CellComponent />, emptyState);

            expect(screen.getByText("0")).toBeInTheDocument();
        });

        test("teamMembers column with null team members data", () => {
            const teamMembersColumn = columns.find((column) => column.accessorKey === "teamMembers");
            if (!teamMembersColumn || !teamMembersColumn.cell) throw new Error("TeamMembers column not found");

            const cellFn = teamMembersColumn.cell;
            const CellComponent = () => cellFn({ row: mockRow });

            const nullState = {
                teamMembers: {
                    getTeamMembers: {
                        loading: false,
                        success: true,
                        error: null,
                        data: null,
                    },
                },
            };

            renderWithProvider(<CellComponent />, nullState);

            expect(screen.getByText("0")).toBeInTheDocument();
        });
    });

    describe("Actions Column", () => {
        test("actions column renders and handles click correctly", () => {
            const actionsColumn = columns.find((column) => column.id === "actions");
            if (!actionsColumn || !actionsColumn.cell) throw new Error("Actions column not found");

            const cellFn = actionsColumn.cell;
            const CellComponent = () => cellFn({ row: mockRow, table: mockTable });

            renderWithProvider(<CellComponent />);

            // Click the "View role permissions" action
            const moreAction = screen.getByTestId("more-actions");
            fireEvent.click(moreAction);

            expect(mockSetRoleToView).toHaveBeenCalledWith(mockData);
            expect(mockSetIsViewPermissionsSideDrawerOpen).toHaveBeenCalledWith(true);

            // Click the "Edit role" action
            const editRoleAction = screen.getByTestId("edit-role-action");
            fireEvent.click(editRoleAction);

            expect(mockSetRoleToView).toHaveBeenCalledWith(mockData);
            expect(mockSetIsEditMode).toHaveBeenCalledWith(true);
            expect(mockSetIsCreateCustomRoleModalOpen).toHaveBeenCalledWith(true);
        });

        test("actions column with missing meta functions", () => {
            const actionsColumn = columns.find((column) => column.id === "actions");
            if (!actionsColumn || !actionsColumn.cell) throw new Error("Actions column not found");

            const tableWithoutMeta = {
                ...mockTable,
                options: {
                    meta: {},
                },
            };

            const cellFn = actionsColumn.cell;
            const CellComponent = () => cellFn({ row: mockRow, table: tableWithoutMeta });

            // Should render without throwing an error
            expect(() => renderWithProvider(<CellComponent />)).not.toThrow();
        });

        test("actions column menu items structure", () => {
            const actionsColumn = columns.find((column) => column.id === "actions");
            if (!actionsColumn || !actionsColumn.cell) throw new Error("Actions column not found");

            const cellFn = actionsColumn.cell;
            const CellComponent = () => cellFn({ row: mockRow, table: mockTable });

            renderWithProvider(<CellComponent />);

            // Check that the menu items are properly structured
            const moreAction = screen.getByTestId("more-actions");
            expect(moreAction).toBeInTheDocument();
        });
    });

    describe("Edge Cases and Error Handling", () => {
        test("handles missing column definitions gracefully", () => {
            // Test that we can find all expected columns
            const expectedColumns = ["select", "name", "dateCreated", "createdBy", "type", "teamMembers", "actions"];

            expectedColumns.forEach((columnId) => {
                const column = columns.find((col) => col.id === columnId || col.accessorKey === columnId);
                expect(column).toBeDefined();
            });
        });

        test("handles missing cell renderers gracefully", () => {
            // Test that all columns have cell renderers
            columns.forEach((column) => {
                expect(column.cell).toBeDefined();
                expect(typeof column.cell).toBe("function");
            });
        });
    });
});
