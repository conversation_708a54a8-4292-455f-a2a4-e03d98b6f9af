"use client";
import { render, screen, fireEvent } from "@testing-library/react";
import { columns } from "@/components/page-components/dashboard/teams/people/column-data.tsx";

import { getCoreRowModel, useReactTable } from "@tanstack/react-table";

// Mock dependencies
jest.mock("@/components/common/badge", () => {
    return function MockBadge({ text, color }) {
        return (
            <div data-testid={`badge-${text}`} data-color={color}>
                {text}
            </div>
        );
    };
});

jest.mock("@/components/common/checkbox", () => {
    return function MockCheckbox({
        checked,
        onCheckedChange,
        "aria-label": ariaLabel,
        "data-testid": testId,
        indeterminate,
    }) {
        return (
            <input
                type="checkbox"
                checked={checked}
                onChange={() => onCheckedChange(!checked)}
                aria-label={ariaLabel}
                data-testid={testId}
                data-indeterminate={indeterminate ? "true" : "false"}
            />
        );
    };
});

jest.mock("@/components/common/table/table-more-action", () => {
    return function MockTableMoreAction({ "data-testid": testId, data, menuItems }) {
        return (
            <div data-testid={testId || "more-actions"}>
                <button onClick={() => menuItems[0].onClick(data)} data-testid="edit-action">
                    Edit
                </button>
                <button onClick={() => menuItems[1].onClick(data)} data-testid="remove-action">
                    Remove
                </button>
            </div>
        );
    };
});

jest.mock("@/components/icons/team", () => ({
    EditIcon: () => <div data-testid="edit-icon">EditIcon</div>,
    DeleteIcon: () => <div data-testid="delete-icon">DeleteIcon</div>,
}));

jest.mock("@/functions/stringManipulations", () => ({
    getNameInitials: jest.fn().mockImplementation((name) => {
        const nameParts = name.split(" ");
        return nameParts.length > 1 ? `${nameParts[0][0]}${nameParts[1][0]}` : nameParts[0][0];
    }),
    capitalizeUserName: jest.fn().mockImplementation((name) => {
        if (!name) return "";
        return name.charAt(0).toUpperCase() + name.slice(1).toLowerCase();
    }),
}));

jest.mock("@/components/page-components/dashboard/teams/people/modals/edit-team-member-drawer.tsx", () => {
    return function MockEditTeamMemberDrawer({ handleClose, meta }) {
        return meta.isRoleEditOpen ? (
            <div data-testid="edit-team-member-drawer">
                <button onClick={handleClose} data-testid="close-drawer">
                    Close
                </button>
            </div>
        ) : null;
    };
});

// Mock data
const mockTeamMembers = [
    {
        id: "1",
        firstName: "John",
        lastName: "Doe",
        email: "<EMAIL>",
        role: "Admin",
        roleId: "1",
        lastLogin: "2023-01-01",
        twoFaStatus: "Enabled",
        mfaStatus: true,
        phoneNumber: "+1234567890",
    },
    {
        id: "2",
        firstName: "Jane",
        lastName: "Smith",
        email: "<EMAIL>",
        role: "User",
        roleId: "2",
        lastLogin: "2023-01-02",
        twoFaStatus: "Disabled",
        mfaStatus: false,
        phoneNumber: "+0987654321",
    },
];

// Mock table meta
const mockTableMeta = {
    setIsOpen: jest.fn(),
    setMemberId: jest.fn(),
    setIsRoleEditOpen: jest.fn(),
    setMemberToEdit: jest.fn(),
    setIsRemoveModalOpen: jest.fn(),
    setMemberToRemove: jest.fn(),
    isRoleEditOpen: false,
    memberToEdit: null,
    setPhoneNumber: jest.fn(),
    // Add new properties for role mapping
    roleMap: {
        1: "Admin",
        2: "User",
    },
    rolesLoading: false,
};

// Create a test component that renders the table
const TestTable = ({ data }) => {
    const table = useReactTable({
        data,
        columns,
        getCoreRowModel: getCoreRowModel(),
        meta: mockTableMeta,
    });

    return (
        <table>
            <thead>
                {table.getHeaderGroups().map((headerGroup) => (
                    <tr key={headerGroup.id}>
                        {headerGroup.headers.map((header) => (
                            <th key={header.id}>
                                {header.isPlaceholder
                                    ? null
                                    : header.column.columnDef.header &&
                                        typeof header.column.columnDef.header === "function"
                                      ? header.column.columnDef.header({
                                            header,
                                            column: header.column,
                                            table,
                                        })
                                      : header.column.columnDef.header}
                            </th>
                        ))}
                    </tr>
                ))}
            </thead>
            <tbody>
                {table.getRowModel().rows.map((row) => (
                    <tr key={row.id} data-testid={`row-${row.id}`}>
                        {row.getVisibleCells().map((cell) => (
                            <td key={cell.id}>
                                {cell.column.columnDef.cell && typeof cell.column.columnDef.cell === "function"
                                    ? cell.column.columnDef.cell({
                                          cell,
                                          row,
                                          column: cell.column,
                                          table,
                                      })
                                    : cell.getValue()}
                            </td>
                        ))}
                    </tr>
                ))}
            </tbody>
        </table>
    );
};

describe("Team Members Table Columns", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("renders the table with all columns", () => {
        render(<TestTable data={mockTeamMembers} />);

        // Check if all columns are rendered
        expect(screen.getByTestId("checkbox-select-all")).toBeInTheDocument();
        expect(screen.getByText("John Doe")).toBeInTheDocument();
        expect(screen.getByText("Admin")).toBeInTheDocument();
        // Use a regex to match the date format but allow for timezone differences
        // The Intl.DateTimeFormat will format the date according to the locale and timezone
        // Since there are multiple dates in the table, we use getAllByText and check the first one
        const dateElements = screen.getAllByText(/\d{1,2} Jan 2023, \d{2}:\d{2}/);
        expect(dateElements.length).toBeGreaterThan(0);
        expect(screen.getByTestId("badge-Enabled")).toBeInTheDocument();
        expect(screen.getAllByTestId("more-actions")[0]).toBeInTheDocument();
    });

    it("renders member initials correctly", () => {
        render(<TestTable data={mockTeamMembers} />);

        const initials = screen.getAllByTestId("member-initials");
        expect(initials[0]).toHaveTextContent("JD");
        expect(initials[1]).toHaveTextContent("JS");
    });

    it("renders member email correctly", () => {
        render(<TestTable data={mockTeamMembers} />);

        expect(screen.getByText("<EMAIL>")).toBeInTheDocument();
        expect(screen.getByText("<EMAIL>")).toBeInTheDocument();
    });

    it("renders 2FA status badges with correct colors", () => {
        render(<TestTable data={mockTeamMembers} />);

        const enabledBadge = screen.getByTestId("badge-Enabled");
        const disabledBadge = screen.getByTestId("badge-Disabled");

        expect(enabledBadge).toHaveAttribute("data-color", "success");
        expect(disabledBadge).toHaveAttribute("data-color", "error");
    });

    it("handles checkbox selection for all rows", () => {
        render(<TestTable data={mockTeamMembers} />);

        const selectAllCheckbox = screen.getByTestId("checkbox-select-all");
        fireEvent.click(selectAllCheckbox);

        // Since we're mocking the checkbox behavior, we can't directly test the state change
        // But we can verify the checkbox was clicked
        expect(selectAllCheckbox).toBeInTheDocument();
    });

    it("handles checkbox selection for individual rows", () => {
        render(<TestTable data={mockTeamMembers} />);

        const rowCheckboxes = screen.getAllByTestId("checkbox-select-row");
        expect(rowCheckboxes.length).toBe(2);

        fireEvent.click(rowCheckboxes[0]);
        // Again, we can't directly test the state change due to mocking
        expect(rowCheckboxes[0]).toBeInTheDocument();
    });

    it("opens edit drawer when edit action is clicked", () => {
        render(<TestTable data={mockTeamMembers} />);

        const editButton = screen.getAllByTestId("edit-action")[0];
        fireEvent.click(editButton);

        expect(mockTableMeta.setIsRoleEditOpen).toHaveBeenCalledWith(true);
    });

    it("opens remove modal when remove action is clicked", () => {
        render(<TestTable data={mockTeamMembers} />);

        const removeButton = screen.getAllByTestId("remove-action")[0];
        fireEvent.click(removeButton);

        expect(mockTableMeta.setIsRemoveModalOpen).toHaveBeenCalledWith(true);
    });

    // The drawer is now handled in the parent component, not in the column definition
    it("sets up drawer state when edit action is clicked", () => {
        render(<TestTable data={mockTeamMembers} />);

        const editButton = screen.getAllByTestId("edit-action")[0];
        fireEvent.click(editButton);

        expect(mockTableMeta.setIsRoleEditOpen).toHaveBeenCalledWith(true);
        expect(mockTableMeta.setMemberToEdit).toHaveBeenCalledWith(mockTeamMembers[0]);
    });

    // The drawer is now handled in the parent component, not in the column definition
    it("sets up remove modal state when remove action is clicked", () => {
        render(<TestTable data={mockTeamMembers} />);

        const removeButton = screen.getAllByTestId("remove-action")[1];
        fireEvent.click(removeButton);

        expect(mockTableMeta.setIsRemoveModalOpen).toHaveBeenCalledWith(true);
        expect(mockTableMeta.setMemberToRemove).toHaveBeenCalledWith(mockTeamMembers[1]);
    });
});
