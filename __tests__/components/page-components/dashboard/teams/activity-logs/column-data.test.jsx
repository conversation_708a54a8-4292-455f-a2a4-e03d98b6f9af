import React from "react";
import { render, screen } from "@testing-library/react";
import { Provider } from "react-redux";
import { configureStore } from "@reduxjs/toolkit";
import {
    columns,
    createOptimizedColumns,
} from "@/components/page-components/dashboard/teams/activity-logs/column-data.tsx";
import Checkbox from "@/components/common/checkbox";
import TableMoreAction from "@/components/common/table/table-more-action";
import { getNameInitials, capitalizeUserName } from "@/functions/stringManipulations";

// Mock dependencies
jest.mock("@/components/common/checkbox", () => ({
    __esModule: true,
    default: jest.fn(() => <div data-testid="checkbox" />),
}));

jest.mock("@/components/common/table/table-more-action", () => ({
    __esModule: true,
    default: jest.fn(() => <div data-testid="table-more-action" />),
}));

jest.mock("@/functions/stringManipulations", () => ({
    getNameInitials: jest.fn(() => "SO"),
    capitalizeUserName: jest.fn((name) => {
        if (!name) return "";
        return name.charAt(0).toUpperCase() + name.slice(1).toLowerCase();
    }),
}));

jest.mock("@/components/icons/team", () => ({
    DeleteIcon: () => <div data-testid="delete-icon" />,
    DisableIcon: () => <div data-testid="disable-icon" />,
    EditIcon: () => <div data-testid="edit-icon" />,
}));

// Create mock store
const createMockStore = (initialState = {}) => {
    const defaultState = {
        roles: {
            getAllRoles: {
                data: [],
                loading: false,
                error: null,
                success: false,
            },
        },
        teamMembers: {
            getTeamMembers: {
                data: [],
                loading: false,
                error: null,
                success: false,
            },
        },
    };

    // Deep merge the state
    const mergedState = {
        roles: {
            ...defaultState.roles,
            ...initialState.roles,
        },
        teamMembers: {
            ...defaultState.teamMembers,
            ...initialState.teamMembers,
        },
    };

    return configureStore({
        reducer: {
            roles: (state = mergedState.roles) => state,
            teamMembers: (state = mergedState.teamMembers) => state,
        },
    });
};

describe("Column-data", () => {
    let mockStore;
    // Sample data for testing
    const sampleRow = {
        original: {
            id: "1",
            role: "Team Member", // Default role for fallback
            performedBy: {
                name: "Samantha Okeke",
                role: "Admin",
            },
            action: "Added team member",
            timestamp: "2024-11-08T15:06:00Z", // ISO format that will format to "8 Nov 2024, 15:06"
            location: "Abuja, Nigeria",
            ipAddress: "***********",
        },
        getValue: jest.fn((key) => sampleRow.original[key]),
        getIsSelected: jest.fn(() => false),
        toggleSelected: jest.fn(),
    };

    const sampleTable = {
        getIsAllPageRowsSelected: jest.fn(() => false),
        getIsSomePageRowsSelected: jest.fn(() => false),
        toggleAllPageRowsSelected: jest.fn(),
        options: {
            meta: {
                setSelectedLog: jest.fn(),
            },
        },
    };

    beforeEach(() => {
        mockStore = createMockStore();
        jest.clearAllMocks();
    });

    const renderWithProvider = (component, store = mockStore) => render(<Provider store={store}>{component}</Provider>);

    describe("columns definition", () => {
        test("should have the correct number of columns", () => {
            expect(columns).toHaveLength(5);
        });

        test("should have the correct column keys", () => {
            const columnKeys = columns.map((col) => col.id || col.accessorKey);
            expect(columnKeys).toEqual(["select", "performedBy", "action", "timestamp", "location"]);
        });

        test("should have correct column properties", () => {
            expect(columns[0]).toHaveProperty("id", "select");
            expect(columns[0]).toHaveProperty("enableSorting", false);
            expect(columns[0]).toHaveProperty("enableHiding", false);
            expect(columns[1]).toHaveProperty("accessorKey", "performedBy");
            expect(columns[2]).toHaveProperty("accessorKey", "action");
            expect(columns[3]).toHaveProperty("accessorKey", "timestamp");
            expect(columns[4]).toHaveProperty("accessorKey", "location");
        });
    });

    describe("select column", () => {
        test("renders header checkbox correctly", () => {
            const SelectHeader = columns[0].header;
            render(<SelectHeader table={sampleTable} />);

            expect(Checkbox).toHaveBeenCalledWith(
                expect.objectContaining({
                    checked: false,
                    indeterminate: false,
                    "aria-label": "Select all",
                    "data-testid": "checkbox-select-all",
                    size: "sm",
                }),
                expect.anything()
            );
        });

        test("renders header checkbox with indeterminate state", () => {
            const tableWithIndeterminate = {
                ...sampleTable,
                getIsAllPageRowsSelected: jest.fn(() => false),
                getIsSomePageRowsSelected: jest.fn(() => true),
            };
            const SelectHeader = columns[0].header;
            render(<SelectHeader table={tableWithIndeterminate} />);

            expect(Checkbox).toHaveBeenCalledWith(
                expect.objectContaining({
                    checked: false,
                    indeterminate: true,
                }),
                expect.anything()
            );
        });

        test("renders header checkbox with all selected", () => {
            const tableWithAllSelected = {
                ...sampleTable,
                getIsAllPageRowsSelected: jest.fn(() => true),
                getIsSomePageRowsSelected: jest.fn(() => false),
            };
            const SelectHeader = columns[0].header;
            render(<SelectHeader table={tableWithAllSelected} />);

            expect(Checkbox).toHaveBeenCalledWith(
                expect.objectContaining({
                    checked: true,
                    indeterminate: false,
                }),
                expect.anything()
            );
        });

        test("renders cell checkbox correctly", () => {
            const SelectCell = columns[0].cell;
            render(<SelectCell row={sampleRow} />);

            expect(Checkbox).toHaveBeenCalledWith(
                expect.objectContaining({
                    checked: false,
                    "aria-label": "Select row",
                    "data-testid": "checkbox-select-row",
                    size: "sm",
                    role: "checkbox-row",
                }),
                expect.anything()
            );
        });

        test("renders cell checkbox with selected state", () => {
            const rowWithSelected = {
                ...sampleRow,
                getIsSelected: jest.fn(() => true),
            };
            const SelectCell = columns[0].cell;
            render(<SelectCell row={rowWithSelected} />);

            expect(Checkbox).toHaveBeenCalledWith(
                expect.objectContaining({
                    checked: true,
                }),
                expect.anything()
            );
        });

        test("checkbox toggles row selection", () => {
            const SelectCell = columns[0].cell;
            render(<SelectCell row={sampleRow} />);

            // Extract the onCheckedChange callback
            const checkboxProps = Checkbox.mock.calls[0][0];
            checkboxProps.onCheckedChange();

            expect(sampleRow.toggleSelected).toHaveBeenCalledWith(true);
        });

        test("header checkbox toggles all rows selection", () => {
            const SelectHeader = columns[0].header;
            render(<SelectHeader table={sampleTable} />);

            // Extract the onCheckedChange callback
            const checkboxProps = Checkbox.mock.calls[0][0];
            checkboxProps.onCheckedChange();

            expect(sampleTable.toggleAllPageRowsSelected).toHaveBeenCalledWith(true);
        });
    });

    describe("performedBy column", () => {
        test("renders performedBy cell correctly", () => {
            const PerformedByCell = columns[1].cell;
            const { container } = renderWithProvider(<PerformedByCell row={sampleRow} />);

            expect(getNameInitials).toHaveBeenCalledWith("Samantha Okeke");
            expect(container).toHaveTextContent("Samantha Okeke");
            // The component shows the role from row.original.role
            expect(container).toHaveTextContent("Team Member");
        });

        test("displays initials from getNameInitials function", () => {
            const PerformedByCell = columns[1].cell;
            renderWithProvider(<PerformedByCell row={sampleRow} />);

            expect(screen.getByTestId("member-initials")).toHaveTextContent("SO");
        });

        test("renders with correct CSS classes", () => {
            const PerformedByCell = columns[1].cell;
            const { container } = renderWithProvider(<PerformedByCell row={sampleRow} />);

            // Check for the avatar container - use a more flexible selector
            const avatarContainer = container.querySelector('[class*="rounded-full"][class*="bg-[#F9F0FE]"]');
            expect(avatarContainer).toBeInTheDocument();

            // Check for the text container - use a more flexible selector
            const textContainer = container.querySelector('[class*="flex"][class*="flex-col"]');
            expect(textContainer).toBeInTheDocument();
        });
    });

    describe("action, date, and location columns", () => {
        test("renders action cell correctly", () => {
            const ActionCell = columns[2].cell;
            const { container } = render(<ActionCell row={sampleRow} />);

            expect(container).toHaveTextContent("Added team member");
        });

        test("renders date cell correctly", () => {
            const DateCell = columns[3].cell;
            const { container } = render(<DateCell row={sampleRow} />);

            // Check that the date is formatted correctly (allowing for timezone differences)
            expect(container.textContent).toMatch(/\d{2} Nov 2024, \d{2}:\d{2}/);
        });

        test("renders date cell with different timestamp formats", () => {
            const rowWithDifferentTimestamp = {
                ...sampleRow,
                original: {
                    ...sampleRow.original,
                    timestamp: "2024-12-25T10:30:00.000Z",
                },
            };
            const DateCell = columns[3].cell;
            const { container } = render(<DateCell row={rowWithDifferentTimestamp} />);

            expect(container.textContent).toMatch(/\d{2} Dec 2024, \d{2}:\d{2}/);
        });

        test("renders location cell correctly", () => {
            const LocationCell = columns[4].cell;
            const { container } = render(<LocationCell row={sampleRow} />);

            expect(container).toHaveTextContent("Abuja, Nigeria");
            expect(container).toHaveTextContent("***********");
        });

        test("renders location cell with empty location", () => {
            const rowWithEmptyLocation = {
                ...sampleRow,
                original: {
                    ...sampleRow.original,
                    location: "",
                    ipAddress: "",
                },
            };
            const LocationCell = columns[4].cell;
            const { container } = render(<LocationCell row={rowWithEmptyLocation} />);

            expect(container).toHaveTextContent("");
        });

        test("renders location cell with only location", () => {
            const rowWithOnlyLocation = {
                ...sampleRow,
                original: {
                    ...sampleRow.original,
                    ipAddress: "",
                },
            };
            const LocationCell = columns[4].cell;
            const { container } = render(<LocationCell row={rowWithOnlyLocation} />);

            expect(container).toHaveTextContent("Abuja, Nigeria");
            expect(container).not.toHaveTextContent("***********");
        });
    });

    describe("PerformedByCell with team member data", () => {
        test("renders correctly when creator is found in team members", () => {
            const mockStore = createMockStore({
                roles: {
                    getAllRoles: {
                        data: [
                            { id: 1, name: "Admin" },
                            { id: 2, name: "User" },
                        ],
                        loading: false,
                        error: null,
                        success: true,
                    },
                },
                teamMembers: {
                    getTeamMembers: {
                        data: [
                            {
                                id: 123,
                                firstName: "John",
                                lastName: "Doe",
                                roleId: 1,
                            },
                        ],
                        loading: false,
                        error: null,
                        success: true,
                    },
                },
            });

            const rowWithCreatorId = {
                original: {
                    ...sampleRow.original,
                    performedBy: {
                        id: 123,
                        name: "John Doe",
                        role: "Admin",
                    },
                },
                getValue: jest.fn((key) => rowWithCreatorId.original[key]),
                getIsSelected: jest.fn(() => false),
                toggleSelected: jest.fn(),
            };

            const PerformedByCell = columns[1].cell;
            const { container } = renderWithProvider(<PerformedByCell row={rowWithCreatorId} />, mockStore);

            expect(container).toHaveTextContent("John Doe");
            expect(container).toHaveTextContent("Admin"); // Component uses row.original.role
            expect(getNameInitials).toHaveBeenCalledWith("John Doe");
        });

        test("renders correctly when creator has roleId 0 (Super Admin)", () => {
            const mockStore = createMockStore({
                roles: {
                    getAllRoles: {
                        data: [
                            { id: 1, name: "Admin" },
                            { id: 2, name: "User" },
                        ],
                        loading: false,
                        error: null,
                        success: true,
                    },
                },
                teamMembers: {
                    getTeamMembers: {
                        data: [
                            {
                                id: 456,
                                firstName: "Super",
                                lastName: "Admin",
                                roleId: 0,
                            },
                        ],
                        loading: false,
                        error: null,
                        success: true,
                    },
                },
            });

            const rowWithSuperAdmin = {
                original: {
                    ...sampleRow.original,
                    performedBy: {
                        id: 456,
                        name: "Super Admin",
                        role: "Super Admin",
                    },
                },
                getValue: jest.fn((key) => rowWithSuperAdmin.original[key]),
                getIsSelected: jest.fn(() => false),
                toggleSelected: jest.fn(),
            };

            const PerformedByCell = columns[1].cell;
            const { container } = renderWithProvider(<PerformedByCell row={rowWithSuperAdmin} />, mockStore);

            expect(container).toHaveTextContent("Super Admin");
            expect(container).toHaveTextContent("Super Admin");
        });

        test("renders correctly when creator role is found in roles array", () => {
            const mockStore = createMockStore({
                roles: {
                    getAllRoles: {
                        data: [
                            { id: 1, name: "Admin" },
                            { id: 2, name: "Manager" },
                        ],
                        loading: false,
                        error: null,
                        success: true,
                    },
                },
                teamMembers: {
                    getTeamMembers: {
                        data: [
                            {
                                id: 789,
                                firstName: "Jane",
                                lastName: "Smith",
                                roleId: 2,
                            },
                        ],
                        loading: false,
                        error: null,
                        success: true,
                    },
                },
            });

            const rowWithManager = {
                original: {
                    ...sampleRow.original,
                    performedBy: {
                        id: 789,
                        name: "Jane Smith",
                        role: "Manager",
                    },
                },
                getValue: jest.fn((key) => rowWithManager.original[key]),
                getIsSelected: jest.fn(() => false),
                toggleSelected: jest.fn(),
            };

            const PerformedByCell = columns[1].cell;
            const { container } = renderWithProvider(<PerformedByCell row={rowWithManager} />, mockStore);

            expect(container).toHaveTextContent("Jane Smith");
            expect(container).toHaveTextContent("Manager"); // Component uses row.original.role
        });
    });

    describe("createOptimizedColumns function", () => {
        test("returns the same column structure as the default columns", () => {
            const optimizedColumns = createOptimizedColumns();

            expect(optimizedColumns).toHaveLength(5);
            expect(optimizedColumns[0].id).toBe("select");
            expect(optimizedColumns[1].accessorKey).toBe("performedBy");
            expect(optimizedColumns[2].accessorKey).toBe("action");
            expect(optimizedColumns[3].accessorKey).toBe("timestamp");
            expect(optimizedColumns[4].accessorKey).toBe("location");
        });

        test("optimized columns have the same cell renderers", () => {
            const optimizedColumns = createOptimizedColumns();

            // Test that the cell renderers are the same type
            expect(typeof optimizedColumns[1].cell).toBe("function");
            expect(typeof optimizedColumns[2].cell).toBe("function");
            expect(typeof optimizedColumns[3].cell).toBe("function");
            expect(typeof optimizedColumns[4].cell).toBe("function");
        });

        test("optimized columns have the same header renderers", () => {
            const optimizedColumns = createOptimizedColumns();

            // Test that the header renderers are the same type
            expect(typeof optimizedColumns[0].header).toBe("function");
        });

        test("optimized columns maintain enableSorting and enableHiding properties", () => {
            const optimizedColumns = createOptimizedColumns();

            expect(optimizedColumns[0].enableSorting).toBe(false);
            expect(optimizedColumns[0].enableHiding).toBe(false);
        });
    });

    describe("PerformedByCell edge cases", () => {
        test("renders correctly when creator is not found in team members", () => {
            const mockStore = createMockStore({
                roles: {
                    getAllRoles: {
                        data: [
                            { id: 1, name: "Admin" },
                            { id: 2, name: "User" },
                        ],
                        loading: false,
                        error: null,
                        success: true,
                    },
                },
                teamMembers: {
                    getTeamMembers: {
                        data: [
                            {
                                id: 999, // Different ID
                                firstName: "Other",
                                lastName: "User",
                                roleId: 1,
                            },
                        ],
                        loading: false,
                        error: null,
                        success: true,
                    },
                },
            });

            const rowWithUnknownCreator = {
                original: {
                    ...sampleRow.original,
                    performedBy: {
                        id: 123, // Not in team members
                        name: "Unknown User",
                        role: "Unknown Role",
                    },
                },
                getValue: jest.fn((key) => rowWithUnknownCreator.original[key]),
                getIsSelected: jest.fn(() => false),
                toggleSelected: jest.fn(),
            };

            const PerformedByCell = columns[1].cell;
            const { container } = renderWithProvider(<PerformedByCell row={rowWithUnknownCreator} />, mockStore);

            expect(container).toHaveTextContent("Unknown User");
            expect(container).toHaveTextContent("Team Member"); // Component uses row.original.role
        });

        test("renders correctly when roles data is empty", () => {
            const mockStore = createMockStore({
                roles: {
                    getAllRoles: {
                        data: [],
                        loading: false,
                        error: null,
                        success: true,
                    },
                },
                teamMembers: {
                    getTeamMembers: {
                        data: [
                            {
                                id: 123,
                                firstName: "John",
                                lastName: "Doe",
                                roleId: 1,
                            },
                        ],
                        loading: false,
                        error: null,
                        success: true,
                    },
                },
            });

            const rowWithCreator = {
                original: {
                    ...sampleRow.original,
                    performedBy: {
                        id: 123,
                        name: "John Doe",
                        role: "Admin",
                    },
                },
                getValue: jest.fn((key) => rowWithCreator.original[key]),
                getIsSelected: jest.fn(() => false),
                toggleSelected: jest.fn(),
            };

            const PerformedByCell = columns[1].cell;
            const { container } = renderWithProvider(<PerformedByCell row={rowWithCreator} />, mockStore);

            expect(container).toHaveTextContent("John Doe");
        });

        test("renders correctly when team members data is empty", () => {
            const mockStore = createMockStore({
                roles: {
                    getAllRoles: {
                        data: [{ id: 1, name: "Admin" }],
                        loading: false,
                        error: null,
                        success: true,
                    },
                },
                teamMembers: {
                    getTeamMembers: {
                        data: [],
                        loading: false,
                        error: null,
                        success: true,
                    },
                },
            });

            const rowWithCreator = {
                original: {
                    ...sampleRow.original,
                    performedBy: {
                        id: 123,
                        name: "John Doe",
                        role: "Admin",
                    },
                },
                getValue: jest.fn((key) => rowWithCreator.original[key]),
                getIsSelected: jest.fn(() => false),
                toggleSelected: jest.fn(),
            };

            const PerformedByCell = columns[1].cell;
            const { container } = renderWithProvider(<PerformedByCell row={rowWithCreator} />, mockStore);

            expect(container).toHaveTextContent("John Doe");
            expect(container).toHaveTextContent("Team Member"); // Component uses row.original.role
        });

        test("handles null/undefined performedBy data", () => {
            const mockStore = createMockStore({
                roles: {
                    getAllRoles: {
                        data: [],
                        loading: false,
                        error: null,
                        success: true,
                    },
                },
                teamMembers: {
                    getTeamMembers: {
                        data: [],
                        loading: false,
                        error: null,
                        success: true,
                    },
                },
            });

            const rowWithNullPerformedBy = {
                original: {
                    ...sampleRow.original,
                    performedBy: {
                        id: null,
                        name: "Unknown User",
                        role: "Unknown Role",
                    },
                },
                getValue: jest.fn((key) => rowWithNullPerformedBy.original[key]),
                getIsSelected: jest.fn(() => false),
                toggleSelected: jest.fn(),
            };

            const PerformedByCell = columns[1].cell;

            // Should render without throwing an error
            const { container } = renderWithProvider(<PerformedByCell row={rowWithNullPerformedBy} />, mockStore);
            expect(container).toHaveTextContent("Unknown User");
        });
    });

    describe("PerformedByCell branch coverage improvements", () => {
        test("handles performedBy as string", () => {
            const mockStore = createMockStore();

            const rowWithStringPerformedBy = {
                original: {
                    ...sampleRow.original,
                    role: "Manager", // Role to use as fallback when performedBy is string
                    performedBy: "John Doe", // String instead of object
                },
                getValue: jest.fn((key) => rowWithStringPerformedBy.original[key]),
                getIsSelected: jest.fn(() => false),
                toggleSelected: jest.fn(),
            };

            const PerformedByCell = columns[1].cell;
            const { container } = renderWithProvider(<PerformedByCell row={rowWithStringPerformedBy} />, mockStore);

            expect(container).toHaveTextContent("John Doe");
            expect(container).toHaveTextContent("Manager"); // Uses row.original.role when performedBy is string
        });

        test("handles performedBy object without id", () => {
            const mockStore = createMockStore();

            const rowWithNoId = {
                original: {
                    ...sampleRow.original,
                    performedBy: {
                        name: "Jane Smith",
                        // No id property
                    },
                },
                getValue: jest.fn((key) => rowWithNoId.original[key]),
                getIsSelected: jest.fn(() => false),
                toggleSelected: jest.fn(),
            };

            const PerformedByCell = columns[1].cell;
            const { container } = renderWithProvider(<PerformedByCell row={rowWithNoId} />, mockStore);

            expect(container).toHaveTextContent("Jane Smith");
            expect(container).toHaveTextContent("Team Member"); // Uses row.original.role when performedBy.role is undefined
        });

        test("handles performedBy object with id but no matching team member", () => {
            const mockStore = createMockStore({
                teamMembers: {
                    getTeamMembers: {
                        data: [
                            {
                                id: 999, // Different ID
                                firstName: "Other",
                                lastName: "User",
                                roleId: 1,
                            },
                        ],
                        loading: false,
                        error: null,
                        success: true,
                    },
                },
            });

            const rowWithUnmatchedId = {
                original: {
                    ...sampleRow.original,
                    performedBy: {
                        id: 123, // ID not in team members
                        name: "Unknown User",
                    },
                },
                getValue: jest.fn((key) => rowWithUnmatchedId.original[key]),
                getIsSelected: jest.fn(() => false),
                toggleSelected: jest.fn(),
            };

            const PerformedByCell = columns[1].cell;
            const { container } = renderWithProvider(<PerformedByCell row={rowWithUnmatchedId} />, mockStore);

            expect(container).toHaveTextContent("Unknown User");
            expect(container).toHaveTextContent("Team Member"); // Uses row.original.role when performedBy.role is undefined
        });

        test("handles invalid id that cannot be parsed to number", () => {
            const mockStore = createMockStore({
                teamMembers: {
                    getTeamMembers: {
                        data: [
                            {
                                id: 123,
                                firstName: "John",
                                lastName: "Doe",
                                roleId: 1,
                            },
                        ],
                        loading: false,
                        error: null,
                        success: true,
                    },
                },
            });

            const rowWithInvalidId = {
                original: {
                    ...sampleRow.original,
                    performedBy: {
                        id: "invalid-id", // Non-numeric ID
                        name: "Test User",
                    },
                },
                getValue: jest.fn((key) => rowWithInvalidId.original[key]),
                getIsSelected: jest.fn(() => false),
                toggleSelected: jest.fn(),
            };

            const PerformedByCell = columns[1].cell;
            const { container } = renderWithProvider(<PerformedByCell row={rowWithInvalidId} />, mockStore);

            expect(container).toHaveTextContent("Test User");
            expect(container).toHaveTextContent("Team Member"); // Uses row.original.role when performedBy.role is undefined
        });

        test("handles creator found but role not found in roles array", () => {
            const mockStore = createMockStore({
                roles: {
                    getAllRoles: {
                        data: [
                            { id: 999, name: "Other Role" }, // Different role ID
                        ],
                        loading: false,
                        error: null,
                        success: true,
                    },
                },
                teamMembers: {
                    getTeamMembers: {
                        data: [
                            {
                                id: 123,
                                firstName: "John",
                                lastName: "Doe",
                                roleId: 1, // Role ID not in roles array
                            },
                        ],
                        loading: false,
                        error: null,
                        success: true,
                    },
                },
            });

            const rowWithUnmatchedRole = {
                original: {
                    ...sampleRow.original,
                    performedBy: {
                        id: 123,
                        name: "John Doe",
                    },
                },
                getValue: jest.fn((key) => rowWithUnmatchedRole.original[key]),
                getIsSelected: jest.fn(() => false),
                toggleSelected: jest.fn(),
            };

            const PerformedByCell = columns[1].cell;
            const { container } = renderWithProvider(<PerformedByCell row={rowWithUnmatchedRole} />, mockStore);

            expect(container).toHaveTextContent("John Doe");
            expect(container).toHaveTextContent("Team Member"); // Should default to Team Member when role not found
        });

        test("handles roles data as non-array", () => {
            const mockStore = createMockStore({
                roles: {
                    getAllRoles: {
                        data: "not-an-array", // Non-array data
                        loading: false,
                        error: null,
                        success: true,
                    },
                },
                teamMembers: {
                    getTeamMembers: {
                        data: [
                            {
                                id: 123,
                                firstName: "John",
                                lastName: "Doe",
                                roleId: 1,
                            },
                        ],
                        loading: false,
                        error: null,
                        success: true,
                    },
                },
            });

            const rowWithCreator = {
                original: {
                    ...sampleRow.original,
                    performedBy: {
                        id: 123,
                        name: "John Doe",
                    },
                },
                getValue: jest.fn((key) => rowWithCreator.original[key]),
                getIsSelected: jest.fn(() => false),
                toggleSelected: jest.fn(),
            };

            const PerformedByCell = columns[1].cell;
            const { container } = renderWithProvider(<PerformedByCell row={rowWithCreator} />, mockStore);

            expect(container).toHaveTextContent("John Doe");
            expect(container).toHaveTextContent("Team Member"); // Should default when roles is not array
        });

        test("handles teamMembers data as non-array", () => {
            const mockStore = createMockStore({
                teamMembers: {
                    getTeamMembers: {
                        data: "not-an-array", // Non-array data
                        loading: false,
                        error: null,
                        success: true,
                    },
                },
            });

            const rowWithId = {
                original: {
                    ...sampleRow.original,
                    performedBy: {
                        id: 123,
                        name: "John Doe",
                    },
                },
                getValue: jest.fn((key) => rowWithId.original[key]),
                getIsSelected: jest.fn(() => false),
                toggleSelected: jest.fn(),
            };

            const PerformedByCell = columns[1].cell;
            const { container } = renderWithProvider(<PerformedByCell row={rowWithId} />, mockStore);

            expect(container).toHaveTextContent("John Doe");
            expect(container).toHaveTextContent("Team Member");
        });

        test("handles null teamMembers data", () => {
            const mockStore = createMockStore({
                teamMembers: {
                    getTeamMembers: {
                        data: null, // Null data
                        loading: false,
                        error: null,
                        success: true,
                    },
                },
            });

            const rowWithId = {
                original: {
                    ...sampleRow.original,
                    performedBy: {
                        id: 123,
                        name: "John Doe",
                    },
                },
                getValue: jest.fn((key) => rowWithId.original[key]),
                getIsSelected: jest.fn(() => false),
                toggleSelected: jest.fn(),
            };

            const PerformedByCell = columns[1].cell;
            const { container } = renderWithProvider(<PerformedByCell row={rowWithId} />, mockStore);

            expect(container).toHaveTextContent("John Doe");
            expect(container).toHaveTextContent("Team Member");
        });

        test("handles null roles data", () => {
            const mockStore = createMockStore({
                roles: {
                    getAllRoles: {
                        data: null, // Null data
                        loading: false,
                        error: null,
                        success: true,
                    },
                },
                teamMembers: {
                    getTeamMembers: {
                        data: [
                            {
                                id: 123,
                                firstName: "John",
                                lastName: "Doe",
                                roleId: 1,
                            },
                        ],
                        loading: false,
                        error: null,
                        success: true,
                    },
                },
            });

            const rowWithCreator = {
                original: {
                    ...sampleRow.original,
                    performedBy: {
                        id: 123,
                        name: "John Doe",
                    },
                },
                getValue: jest.fn((key) => rowWithCreator.original[key]),
                getIsSelected: jest.fn(() => false),
                toggleSelected: jest.fn(),
            };

            const PerformedByCell = columns[1].cell;
            const { container } = renderWithProvider(<PerformedByCell row={rowWithCreator} />, mockStore);

            expect(container).toHaveTextContent("John Doe");
            expect(container).toHaveTextContent("Team Member");
        });

        test("handles undefined performedBy", () => {
            const mockStore = createMockStore();

            const rowWithUndefinedPerformedBy = {
                original: {
                    ...sampleRow.original,
                    performedBy: undefined,
                },
                getValue: jest.fn((key) => rowWithUndefinedPerformedBy.original[key]),
                getIsSelected: jest.fn(() => false),
                toggleSelected: jest.fn(),
            };

            const PerformedByCell = columns[1].cell;
            const { container } = renderWithProvider(<PerformedByCell row={rowWithUndefinedPerformedBy} />, mockStore);

            expect(container).toHaveTextContent("Team Member"); // Should show role from row.original.role
        });

        test("handles empty string performedBy", () => {
            const mockStore = createMockStore();

            const rowWithEmptyPerformedBy = {
                original: {
                    ...sampleRow.original,
                    performedBy: "",
                },
                getValue: jest.fn((key) => rowWithEmptyPerformedBy.original[key]),
                getIsSelected: jest.fn(() => false),
                toggleSelected: jest.fn(),
            };

            const PerformedByCell = columns[1].cell;
            const { container } = renderWithProvider(<PerformedByCell row={rowWithEmptyPerformedBy} />, mockStore);

            expect(container).toHaveTextContent("Team Member"); // Should show role from row.original.role
        });

        test("handles performedBy with empty name", () => {
            const mockStore = createMockStore();

            const rowWithEmptyName = {
                original: {
                    ...sampleRow.original,
                    performedBy: {
                        id: 123,
                        name: "",
                    },
                },
                getValue: jest.fn((key) => rowWithEmptyName.original[key]),
                getIsSelected: jest.fn(() => false),
                toggleSelected: jest.fn(),
            };

            const PerformedByCell = columns[1].cell;
            const { container } = renderWithProvider(<PerformedByCell row={rowWithEmptyName} />, mockStore);

            expect(container).toHaveTextContent("Team Member"); // Should show role from row.original.role
        });
    });

    describe("Timestamp formatting", () => {
        // test("formats different timestamp formats correctly", () => {
        //     const testCases = [
        //         { input: "2024-01-15T09:30:00Z", expected: /15 Jan 2024, \d{2}:\d{2}/ },
        //         { input: "2024-12-31T23:59:59Z", expected: /01 Jan 2025, \d{2}:\d{2}/ },
        //         { input: "2024-06-01T00:00:00.000Z", expected: /01 Jun 2024, \d{2}:\d{2}/ },
        //     ];

        //     testCases.forEach(({ input, expected }) => {
        //         const rowWithTimestamp = {
        //             ...sampleRow,
        //             original: {
        //                 ...sampleRow.original,
        //                 timestamp: input,
        //             },
        //         };
        //         const DateCell = columns[3].cell;
        //         const { container } = render(<DateCell row={rowWithTimestamp} />);
        //         expect(container.textContent).toMatch(expected);
        //     });
        // });

        test("handles invalid timestamp gracefully", () => {
            const rowWithInvalidTimestamp = {
                ...sampleRow,
                original: {
                    ...sampleRow.original,
                    timestamp: "invalid-date",
                },
            };
            const DateCell = columns[3].cell;
            const { container } = render(<DateCell row={rowWithInvalidTimestamp} />);

            // Should still render something, even if it's the invalid date
            expect(container.textContent).toBeTruthy();
        });
    });

    describe("Column accessibility", () => {
        test("select column has proper ARIA labels", () => {
            const SelectHeader = columns[0].header;
            const SelectCell = columns[0].cell;

            render(<SelectHeader table={sampleTable} />);
            expect(Checkbox).toHaveBeenCalledWith(
                expect.objectContaining({
                    "aria-label": "Select all",
                }),
                expect.anything()
            );

            render(<SelectCell row={sampleRow} />);
            expect(Checkbox).toHaveBeenCalledWith(
                expect.objectContaining({
                    "aria-label": "Select row",
                }),
                expect.anything()
            );
        });

        test("performedBy cell has proper test ID for initials", () => {
            const PerformedByCell = columns[1].cell;
            renderWithProvider(<PerformedByCell row={sampleRow} />);

            expect(screen.getByTestId("member-initials")).toBeInTheDocument();
        });
    });
});
