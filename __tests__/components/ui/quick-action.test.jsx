import { fireEvent, render, screen } from "@testing-library/react";
import MenuCard from "../../../src/components/ui/quick-action/menu-card";

// Mock next/navigation
const mockPush = jest.fn();
jest.mock("next/navigation", () => ({
    useRouter: () => ({
        push: mockPush,
    }),
}));

// Mock icon component
jest.mock("../../../src/components/ui/icons", () => ({
    SendIcon: () => <svg data-testid="mock-icon">SendIcon</svg>,
}));

describe("MenuCard", () => {
    const mockOnClick = jest.fn();
    const mockCloseMenu = jest.fn();

    beforeEach(() => {
        mockOnClick.mockClear();
        mockPush.mockClear();
        mockCloseMenu.mockClear();
    });

    it("should call router.push and closeMenu when menu.destination is defined", () => {
        const mockMenuItem = {
            label: "Send NGN",
            icon: <div data-testid="mock-icon">SendIcon</div>,
            destination: "/send-ngn",
        };

        render(<MenuCard menu={mockMenuItem} closeMenu={mockCloseMenu} />);

        const button = screen.getByRole("button");
        fireEvent.click(button);

        expect(mockPush).toHaveBeenCalledTimes(1);
        expect(mockPush).toHaveBeenCalledWith("/send-ngn");
        expect(mockCloseMenu).toHaveBeenCalledTimes(1);
    });

    it("should call both onClick and closeMenu when menu.onClick is defined", () => {
        const mockMenuItem = {
            label: "Send NGN",
            icon: <div data-testid="mock-icon">SendIcon</div>,
            onClick: mockOnClick,
        };

        render(<MenuCard menu={mockMenuItem} closeMenu={mockCloseMenu} />);

        const button = screen.getByRole("button");
        fireEvent.click(button);

        expect(mockOnClick).toHaveBeenCalledTimes(1);
        expect(mockCloseMenu).toHaveBeenCalledTimes(1);
    });

    it("should call onClick, router.push, and closeMenu when both are defined", () => {
        const mockMenuItem = {
            label: "Send NGN",
            icon: <div data-testid="mock-icon">SendIcon</div>,
            onClick: mockOnClick,
            destination: "/send-ngn",
        };

        render(<MenuCard menu={mockMenuItem} closeMenu={mockCloseMenu} />);

        const button = screen.getByRole("button");
        fireEvent.click(button);

        expect(mockOnClick).toHaveBeenCalledTimes(1);
        expect(mockPush).toHaveBeenCalledTimes(1);
        expect(mockPush).toHaveBeenCalledWith("/send-ngn");
        expect(mockCloseMenu).toHaveBeenCalledTimes(2); // Called once for onClick, once for destination
    });

    it("should not throw error or call closeMenu when neither onClick nor destination is defined", () => {
        const mockMenuItem = {
            label: "Send NGN",
            icon: <div data-testid="mock-icon">SendIcon</div>,
        };

        render(<MenuCard menu={mockMenuItem} closeMenu={mockCloseMenu} />);

        const button = screen.getByRole("button");
        expect(() => fireEvent.click(button)).not.toThrow();
        expect(mockOnClick).not.toHaveBeenCalled();
        expect(mockPush).not.toHaveBeenCalled();
        expect(mockCloseMenu).not.toHaveBeenCalled();
    });
});
