import React from "react";
import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";
import withDynamicPermissionCheck, { PERMISSION_CHECKERS } from "@/components/hoc/withDynamicPermissionCheck";
import { usePermissions } from "@/contexts/PermissionContext";
import { usePermissionConstants } from "@/hooks/usePermissionConstants";

// Mock Next.js router
jest.mock("next/navigation", () => ({
    useRouter: jest.fn(() => ({
        push: jest.fn(),
        replace: jest.fn(),
        back: jest.fn(),
        forward: jest.fn(),
        refresh: jest.fn(),
        prefetch: jest.fn(),
    })),
    usePathname: jest.fn(() => "/test-path"),
    useSearchParams: jest.fn(() => new URLSearchParams()),
}));

// Mock dependencies
jest.mock("@/contexts/PermissionContext", () => ({
    usePermissions: jest.fn(),
}));

jest.mock("@/hooks/usePermissionConstants", () => ({
    usePermissionConstants: jest.fn(),
}));

// Mock the underlying withPermissionCheck HOC
jest.mock("@/components/hoc/withPermissionCheck", () =>
    jest.fn((Component, config) => {
        const WrappedComponent = (props: React.JSX.IntrinsicAttributes) => {
            const mockPermissionContext = (usePermissions as jest.MockedFunction<typeof usePermissions>)();

            // Simulate the complex logic of withPermissionCheck
            const { requiredPermissions, requireAll = false } = config;

            // If no permissions required, render component
            if (!requiredPermissions || requiredPermissions.length === 0) {
                return <Component {...props} />;
            }

            // Check if permission system is ready
            if (!mockPermissionContext.isPermissionSystemReady) {
                return <div>Loading permissions...</div>;
            }

            // Check if permissions are loading
            if (mockPermissionContext.isLoadingPermissions) {
                return <div>Loading permissions...</div>;
            }

            // Check if using cached permissions (deny access for security)
            if (mockPermissionContext.isUsingCachedPermissions) {
                return null;
            }

            // Check if permission fetch failed (deny access)
            if (mockPermissionContext.permissionFetchFailed) {
                return null;
            }

            // Check permissions
            let hasAccess = false;
            if (requireAll) {
                hasAccess = mockPermissionContext.hasAllPermissions(requiredPermissions);
            } else {
                hasAccess = mockPermissionContext.hasAnyPermission(requiredPermissions);
            }

            // Only render if user has access
            return hasAccess ? <Component {...props} /> : null;
        };

        WrappedComponent.displayName = `WithPermissionCheck(${Component.displayName || Component.name})`;
        return WrappedComponent;
    })
);

const mockUsePermissions = usePermissions as jest.MockedFunction<typeof usePermissions>;
const mockUsePermissionConstants = usePermissionConstants as jest.MockedFunction<typeof usePermissionConstants>;

// Test component
const TestComponent: React.FC<{ testProp?: string }> = ({ testProp = "default" }) => (
    <div data-testid="test-component">Test Component: {testProp}</div>
);

describe("withDynamicPermissionCheck", () => {
    const mockPermissions = {
        TRANSACTIONS: {
            VIEW_ALL: "View all transactions (CIB + external channels)",
            VIEW_OWN: "View own transactions",
            CREATE: "Create transactions",
            EDIT: "Edit transactions",
            DELETE: "Delete transactions",
            EXPORT: "Generate transaction receipts",
            APPROVE: "Approve transactions",
            VIEW_DETAILS: "View transaction details",
            GENERATE_RECEIPTS: "Generate transaction receipts",
        },
        TRANSFERS: {
            CREATE: "Create transfers",
            APPROVE: "Approve transfers",
            VIEW_ALL: "View all transfers",
            VIEW_OWN: "View own transfers",
            CANCEL: "Cancel transfers",
            SEND_MONEY: "Send money to beneficiaries",
        },
        USERS: {
            VIEW_ALL: "View all users",
            CREATE: "Create users",
            EDIT: "Edit users",
            DELETE: "Delete users",
            MANAGE_ROLES: "Manage user roles",
            VIEW_PROFILE: "View user profile",
        },
        ROLES: {
            VIEW_ALL: "View all roles",
            CREATE: "Create roles",
            EDIT: "Edit roles",
            DELETE: "Delete roles",
            ASSIGN: "Assign roles",
        },
        SETTINGS: {
            VIEW: "View settings",
            EDIT: "Edit settings",
            MANAGE_LIMITS: "Manage limits",
            CHANGE_PASSWORD: "Change password",
            CHANGE_PIN: "Change PIN",
        },
        DASHBOARD: {
            VIEW: "View dashboard",
            VIEW_ANALYTICS: "View analytics",
            VIEW_REPORTS: "View reports",
        },
        ACCOUNTS: {
            VIEW_ALL: "View all accounts",
            VIEW_OWN: "View own accounts",
            MANAGE: "Manage accounts",
            VIEW_DETAILS: "View account details",
            DOWNLOAD_STATEMENTS: "Download statements",
            VIEW_TRANSACTION_HISTORY: "View transaction history",
        },
        REPORTS: {
            VIEW: "View reports",
            GENERATE: "Generate reports",
            EXPORT: "Export reports",
        },
        TEAM_MANAGEMENT: {
            VIEW_TEAM_MEMBERS: "View team members",
            INVITE_TEAM_MEMBERS: "Invite team members",
            EDIT_TEAM_MEMBERS: "Edit team members",
            REMOVE_TEAM_MEMBERS: "Remove team members",
            VIEW_AUTHENTICATION_STATUS: "View authentication status",
            VIEW_ROLES: "View and edit roles",
            CREATE_ROLES: "Create custom roles",
            MANAGE_PERMISSIONS: "Manage permissions",
            SET_ROLE_RESTRICTIONS: "Set role restrictions",
            VIEW_APPROVAL_RULES: "View approval rules",
            MANAGE_APPROVAL_RULES: "Manage approval rules",
            FILTER_ACTIVITY_LOGS: "Filter activity logs",
        },
        SEND_MONEY: {
            INITIATE_SINGLE_PAYMENTS: "Initiate single payments",
            INITIATE_BULK_PAYMENTS: "Initiate bulk payments",
            VIEW_SCHEDULED_PAYMENTS: "View scheduled payments",
            VIEW_PAYMENT_LIMITS: "View payment limits",
            CANCEL_PENDING_PAYMENTS: "Cancel pending payments",
            SCHEDULE_PAYMENTS: "Schedule payments",
        },
        BILL_PAYMENTS: {
            VIEW_CATEGORIES: "View bill categories",
            INITIATE_BILL_PAYMENT: "Initiate bill payment",
            BULK_AIRTIME_PAYMENTS: "Bulk airtime payments",
            CANCEL_SCHEDULED_BILL: "Cancel scheduled bill",
            VIEW_PAYMENT_HISTORY: "View payment history",
            GENERATE_RECEIPTS: "Generate receipts",
        },
        BENEFICIARIES: {
            VIEW_LIST: "View beneficiary list",
            ADD_NEW: "Add new beneficiary",
            EDIT_DETAILS: "Edit beneficiary details",
            DELETE: "Delete beneficiary",
            VIEW_ACTIVITY_HISTORY: "View activity history",
        },
        OUTGOING_PAYMENTS: {
            VIEW_LIST: "View outgoing payments list",
            VIEW_STATUS: "View payment status",
            FILTER_BY_TYPE: "Filter by payment type",
            MANAGE_APPROVAL_ACTIONS: "Manage approval actions",
        },
        ONBOARDING: {
            COMPLETE_DIGITAL_FORM: "Complete digital form",
            SUBMIT_MANUAL_FORMS: "Submit manual forms",
            UPLOAD_DOCUMENTS: "Upload documents",
        },
        OVERVIEW: {
            VIEW_ACCOUNT_BALANCES: "View account balances",
            VIEW_PAYMENT_APPROVAL_REQUESTS: "View payment approval requests",
        },
    };

    const mockPermissionContext = {
        userPermissions: ["View all transactions (CIB + external channels)", "View team members"],
        isLoadingPermissions: false,
        hasPermission: jest.fn(),
        hasAnyPermission: jest.fn(),
        hasAllPermissions: jest.fn(),
        systemPermissions: [],
        isLoadingSystemPermissions: false,
        systemPermissionsError: null,
        refreshPermissions: jest.fn(),
        isUsingCachedPermissions: false,
        permissionFetchFailed: false,
        isPermissionSystemReady: true,
        refetchPermissions: jest.fn(),
        refreshSystemPermissions: jest.fn(),
    };

    beforeEach(() => {
        jest.clearAllMocks();

        mockUsePermissionConstants.mockReturnValue({
            PERMISSIONS: mockPermissions,
            getPermission: jest.fn((name) => name),
            permissionExists: jest.fn(() => true),
            getPermissionsByModule: jest.fn(() => []),
            validatePermissions: jest.fn(() => ({ valid: [], invalid: [] })),
            systemPermissions: [],
            isLoaded: true,
            isLoading: false,
            getAllPermissionNames: jest.fn(() => []),
            getPermissionCount: jest.fn(() => 0),
        });

        // Reset permission context mocks to default behavior
        mockPermissionContext.hasPermission.mockReturnValue(true);
        mockPermissionContext.hasAnyPermission.mockReturnValue(true);
        mockPermissionContext.hasAllPermissions.mockReturnValue(true);
        mockPermissionContext.isLoadingPermissions = false;
        mockPermissionContext.isPermissionSystemReady = true;
        mockPermissionContext.isUsingCachedPermissions = false;
        mockPermissionContext.permissionFetchFailed = false;

        mockUsePermissions.mockReturnValue(mockPermissionContext);
    });

    describe("Basic HOC functionality", () => {
        it("should render component when user has required permission", () => {
            mockPermissionContext.hasPermission.mockReturnValue(true);

            const WrappedComponent = withDynamicPermissionCheck(TestComponent, {
                permissions: PERMISSION_CHECKERS.VIEW_ALL_TRANSACTIONS,
            });

            render(<WrappedComponent testProp="test-value" />);

            expect(screen.getByTestId("test-component")).toBeInTheDocument();
            expect(screen.getByText("Test Component: test-value")).toBeInTheDocument();
        });

        it("should not render component when user lacks required permission", () => {
            // Since VIEW_ALL_TRANSACTIONS returns an array, hasAnyPermission will be called
            mockPermissionContext.hasAnyPermission.mockReturnValue(false);

            const WrappedComponent = withDynamicPermissionCheck(TestComponent, {
                permissions: PERMISSION_CHECKERS.VIEW_ALL_TRANSACTIONS,
            });

            render(<WrappedComponent />);

            expect(screen.queryByTestId("test-component")).not.toBeInTheDocument();
        });

        it("should use fallback permissions when dynamic permissions fail", () => {
            mockPermissionContext.hasPermission.mockReturnValue(true);
            // Mock the permissions function to throw an error
            const mockPermissionsFunction = jest.fn(() => {
                throw new Error("Dynamic permissions failed");
            });

            const WrappedComponent = withDynamicPermissionCheck(TestComponent, {
                permissions: mockPermissionsFunction,
                fallbackPermissions: ["fallback-permission"],
            });

            render(<WrappedComponent />);

            // Should render the component using fallback permissions
            expect(screen.getByTestId("test-component")).toBeInTheDocument();
        });

        it("should show loading state when permissions are loading", () => {
            mockUsePermissionConstants.mockReturnValue({
                PERMISSIONS: mockPermissions,
                getPermission: jest.fn((name) => name),
                permissionExists: jest.fn(() => true),
                getPermissionsByModule: jest.fn(() => []),
                validatePermissions: jest.fn(() => ({ valid: [], invalid: [] })),
                systemPermissions: [],
                isLoaded: false,
                isLoading: true,
                getAllPermissionNames: jest.fn(() => []),
                getPermissionCount: jest.fn(() => 0),
            });

            const WrappedComponent = withDynamicPermissionCheck(TestComponent, {
                permissions: PERMISSION_CHECKERS.VIEW_ALL_TRANSACTIONS,
                showLoadingState: true,
            });

            render(<WrappedComponent />);

            expect(screen.getByText("Resolving permissions...")).toBeInTheDocument();
            expect(screen.queryByTestId("test-component")).not.toBeInTheDocument();
        });
    });

    describe("Permission checker functions", () => {
        it("should handle single permission checker", () => {
            // VIEW_ALL_TRANSACTIONS returns an array, so hasAnyPermission will be called
            mockPermissionContext.hasAnyPermission.mockReturnValue(true);

            const WrappedComponent = withDynamicPermissionCheck(TestComponent, {
                permissions: PERMISSION_CHECKERS.VIEW_ALL_TRANSACTIONS,
            });

            render(<WrappedComponent />);

            expect(mockPermissionContext.hasAnyPermission).toHaveBeenCalledWith([
                "View all transactions (CIB + external channels)",
            ]);
            expect(screen.getByTestId("test-component")).toBeInTheDocument();
        });

        it("should handle ANY permission checker", () => {
            mockPermissionContext.hasAnyPermission.mockReturnValue(true);

            const WrappedComponent = withDynamicPermissionCheck(TestComponent, {
                permissions: PERMISSION_CHECKERS.TEAM_MANAGEMENT_PERMISSIONS,
            });

            render(<WrappedComponent />);

            expect(mockPermissionContext.hasAnyPermission).toHaveBeenCalled();
            expect(screen.getByTestId("test-component")).toBeInTheDocument();
        });

        it("should handle ALL permission checker", () => {
            mockPermissionContext.hasAllPermissions.mockReturnValue(true);

            // Create a custom ALL checker for testing
            const allChecker = (P: typeof mockPermissions) => [P.TRANSACTIONS.VIEW_ALL, P.TRANSACTIONS.VIEW_DETAILS];

            const WrappedComponent = withDynamicPermissionCheck(TestComponent, {
                permissions: allChecker,
                requireAll: true,
            });

            render(<WrappedComponent />);

            expect(mockPermissionContext.hasAllPermissions).toHaveBeenCalledWith([
                "View all transactions (CIB + external channels)",
                "View transaction details",
            ]);
            expect(screen.getByTestId("test-component")).toBeInTheDocument();
        });
    });

    describe("PERMISSION_CHECKERS", () => {
        it("should have VIEW_ALL_TRANSACTIONS checker", () => {
            expect(PERMISSION_CHECKERS.VIEW_ALL_TRANSACTIONS).toBeDefined();

            const result = PERMISSION_CHECKERS.VIEW_ALL_TRANSACTIONS(mockPermissions);
            expect(Array.isArray(result)).toBe(true);
            expect(result).toContain("View all transactions (CIB + external channels)");
        });

        it("should have TEAM_MANAGEMENT_PERMISSIONS checker", () => {
            expect(PERMISSION_CHECKERS.TEAM_MANAGEMENT_PERMISSIONS).toBeDefined();

            const result = PERMISSION_CHECKERS.TEAM_MANAGEMENT_PERMISSIONS(mockPermissions);
            expect(Array.isArray(result)).toBe(true);
            expect(result).toContain("View team members");
            expect(result).toContain("Create custom roles");
        });

        it("should have SEND_MONEY_ACCESS checker", () => {
            expect(PERMISSION_CHECKERS.SEND_MONEY_ACCESS).toBeDefined();

            const result = PERMISSION_CHECKERS.SEND_MONEY_ACCESS(mockPermissions);
            expect(Array.isArray(result)).toBe(true);
            expect(result).toContain("Initiate single payments");
            expect(result).toContain("Initiate bulk payments");
        });
    });

    describe("Error handling", () => {
        it("should handle permission loading errors gracefully", () => {
            mockUsePermissionConstants.mockReturnValue({
                PERMISSIONS: {
                    TRANSACTIONS: {
                        VIEW_ALL: "",
                        VIEW_OWN: "",
                        CREATE: "",
                        EDIT: "",
                        DELETE: "",
                        EXPORT: "",
                        APPROVE: "",
                        VIEW_DETAILS: "",
                        GENERATE_RECEIPTS: "",
                    },
                    TRANSFERS: {
                        CREATE: "",
                        APPROVE: "",
                        VIEW_ALL: "",
                        VIEW_OWN: "",
                        CANCEL: "",
                        SEND_MONEY: "",
                    },
                    USERS: {
                        VIEW_ALL: "",
                        CREATE: "",
                        EDIT: "",
                        DELETE: "",
                        MANAGE_ROLES: "",
                        VIEW_PROFILE: "",
                    },
                    ROLES: {
                        VIEW_ALL: "",
                        CREATE: "",
                        EDIT: "",
                        DELETE: "",
                        ASSIGN: "",
                    },
                    SETTINGS: {
                        VIEW: "",
                        EDIT: "",
                        MANAGE_LIMITS: "",
                        CHANGE_PASSWORD: "",
                        CHANGE_PIN: "",
                    },
                    DASHBOARD: {
                        VIEW: "",
                        VIEW_ANALYTICS: "",
                        VIEW_REPORTS: "",
                    },
                    ACCOUNTS: {
                        VIEW_ALL: "",
                        VIEW_OWN: "",
                        MANAGE: "",
                        VIEW_DETAILS: "",
                        DOWNLOAD_STATEMENTS: "",
                        VIEW_TRANSACTION_HISTORY: "",
                    },
                    REPORTS: {
                        VIEW: "",
                        GENERATE: "",
                        EXPORT: "",
                    },
                    TEAM_MANAGEMENT: {
                        VIEW_TEAM_MEMBERS: "",
                        INVITE_TEAM_MEMBERS: "",
                        EDIT_TEAM_MEMBERS: "",
                        REMOVE_TEAM_MEMBERS: "",
                        VIEW_AUTHENTICATION_STATUS: "",
                        VIEW_ROLES: "",
                        CREATE_ROLES: "",
                        MANAGE_PERMISSIONS: "",
                        SET_ROLE_RESTRICTIONS: "",
                        VIEW_APPROVAL_RULES: "",
                        MANAGE_APPROVAL_RULES: "",
                        FILTER_ACTIVITY_LOGS: "",
                    },
                    SEND_MONEY: {
                        INITIATE_SINGLE_PAYMENTS: "",
                        INITIATE_BULK_PAYMENTS: "",
                        VIEW_SCHEDULED_PAYMENTS: "",
                        VIEW_PAYMENT_LIMITS: "",
                        CANCEL_PENDING_PAYMENTS: "",
                        SCHEDULE_PAYMENTS: "",
                    },
                    BILL_PAYMENTS: {
                        VIEW_CATEGORIES: "",
                        INITIATE_BILL_PAYMENT: "",
                        BULK_AIRTIME_PAYMENTS: "",
                        CANCEL_SCHEDULED_BILL: "",
                        VIEW_PAYMENT_HISTORY: "",
                        GENERATE_RECEIPTS: "",
                    },
                    BENEFICIARIES: {
                        VIEW_LIST: "",
                        ADD_NEW: "",
                        EDIT_DETAILS: "",
                        DELETE: "",
                        VIEW_ACTIVITY_HISTORY: "",
                    },
                    OUTGOING_PAYMENTS: {
                        VIEW_LIST: "",
                        VIEW_STATUS: "",
                        FILTER_BY_TYPE: "",
                        MANAGE_APPROVAL_ACTIONS: "",
                    },
                    ONBOARDING: {
                        COMPLETE_DIGITAL_FORM: "",
                        SUBMIT_MANUAL_FORMS: "",
                        UPLOAD_DOCUMENTS: "",
                    },
                    OVERVIEW: {
                        VIEW_ACCOUNT_BALANCES: "",
                        VIEW_PAYMENT_APPROVAL_REQUESTS: "",
                    },
                }, // Empty permissions to cause resolution failure
                getPermission: jest.fn((name) => name),
                permissionExists: jest.fn(() => true),
                getPermissionsByModule: jest.fn(() => []),
                validatePermissions: jest.fn(() => ({ valid: [], invalid: [] })),
                systemPermissions: [],
                isLoaded: false,
                isLoading: false, // Not loading but also not loaded (error state)
                getAllPermissionNames: jest.fn(() => []),
                getPermissionCount: jest.fn(() => 0),
            });

            const WrappedComponent = withDynamicPermissionCheck(TestComponent, {
                permissions: PERMISSION_CHECKERS.VIEW_ALL_TRANSACTIONS,
            });

            render(<WrappedComponent />);

            // Should show loading state from PermissionResolutionLoader when permissions can't be resolved
            expect(screen.getByText("Resolving permissions...")).toBeInTheDocument();
        });

        it("should handle missing permission constants", () => {
            mockUsePermissionConstants.mockReturnValue({
                PERMISSIONS: {} as typeof mockPermissions, // Empty permissions
                getPermission: jest.fn(() => "fallback-permission"),
                permissionExists: jest.fn(() => false),
                getPermissionsByModule: jest.fn(() => []),
                validatePermissions: jest.fn(() => ({ valid: [], invalid: [] })),
                systemPermissions: [],
                isLoaded: false, // Not loaded to trigger PermissionResolutionLoader
                isLoading: false,
                getAllPermissionNames: jest.fn(() => []),
                getPermissionCount: jest.fn(() => 0),
            });

            const WrappedComponent = withDynamicPermissionCheck(TestComponent, {
                permissions: PERMISSION_CHECKERS.VIEW_ALL_TRANSACTIONS,
            });

            render(<WrappedComponent />);

            // Should show loading state when permissions can't be resolved
            expect(screen.getByText("Resolving permissions...")).toBeInTheDocument();
            expect(screen.queryByTestId("test-component")).not.toBeInTheDocument();
        });
    });

    describe("Component props forwarding", () => {
        it("should forward all props to wrapped component", () => {
            mockPermissionContext.hasAnyPermission.mockReturnValue(true);

            const WrappedComponent = withDynamicPermissionCheck(TestComponent, {
                permissions: PERMISSION_CHECKERS.VIEW_ALL_TRANSACTIONS,
            });

            render(<WrappedComponent testProp="forwarded-value" />);

            expect(screen.getByText("Test Component: forwarded-value")).toBeInTheDocument();
        });

        it("should preserve component display name", () => {
            const WrappedComponent = withDynamicPermissionCheck(TestComponent, {
                permissions: PERMISSION_CHECKERS.VIEW_ALL_TRANSACTIONS,
            });

            expect(WrappedComponent.displayName).toBe("withDynamicPermissionCheck(TestComponent)");
        });
    });

    describe("Memoization", () => {
        it("should memoize permission checking results", () => {
            mockPermissionContext.hasPermission.mockReturnValue(true);

            const WrappedComponent = withDynamicPermissionCheck(TestComponent, {
                permissions: PERMISSION_CHECKERS.VIEW_ALL_TRANSACTIONS,
            });

            const { rerender } = render(<WrappedComponent />);

            // Clear mock calls
            mockPermissionContext.hasPermission.mockClear();

            // Rerender with same props
            rerender(<WrappedComponent />);

            // Should not call permission check again due to memoization
            expect(mockPermissionContext.hasPermission).not.toHaveBeenCalled();
        });

        it("should recalculate when permissions change", () => {
            mockPermissionContext.hasAnyPermission.mockReturnValue(true);

            const WrappedComponent = withDynamicPermissionCheck(TestComponent, {
                permissions: PERMISSION_CHECKERS.VIEW_ALL_TRANSACTIONS,
            });

            const { rerender } = render(<WrappedComponent />);

            // Change permissions
            mockUsePermissionConstants.mockReturnValue({
                ...mockUsePermissionConstants(),
                PERMISSIONS: {
                    ...mockPermissions,
                    TRANSACTIONS: {
                        ...mockPermissions.TRANSACTIONS,
                        VIEW_ALL: "Updated permission name",
                    },
                },
            });

            rerender(<WrappedComponent />);

            // Should recalculate with new permissions
            expect(mockPermissionContext.hasAnyPermission).toHaveBeenCalled();
        });
    });
});

