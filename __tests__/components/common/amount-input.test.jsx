import { fireEvent, render, screen } from "@testing-library/react";
import AmountInput from "../../../src/components/common/amount-input";

// Mock stringManipulations functions
jest.mock("@/functions/stringManipulations", () => ({
    formatNumberToNaira: jest.fn(
        (num, decimals) => `₦${num.toLocaleString("en-US", { minimumFractionDigits: decimals })}`
    ),
    formatNumber: jest.fn((num, decimals) => num.toLocaleString("en-US", { minimumFractionDigits: decimals })),
}));

// Mock LabelInput component
jest.mock("../../../src/components/common/label-input", () => {
    return function MockLabelInput({ label, value, onChange, onBlur, onFocus, error, showError, disabled, className }) {
        return (
            <div>
                <label>{label}</label>
                <input
                    type="text"
                    value={value}
                    onChange={onChange}
                    onBlur={onBlur}
                    onFocus={onFocus}
                    disabled={disabled}
                    className={className}
                    data-testid="amount-input"
                />
                {showError && error && <span data-testid="error">{error}</span>}
            </div>
        );
    };
});

describe("AmountInput Component", () => {
    const setFieldValueMock = jest.fn();
    const getFieldPropsMock = jest.fn();
    const handleBlurMock = jest.fn();
    let formatNumberToNaira;
    let formatNumber;

    beforeEach(() => {
        jest.clearAllMocks();
        getFieldPropsMock.mockClear();
        setFieldValueMock.mockClear();
        handleBlurMock.mockClear();
        const stringManipulations = jest.requireMock("@/functions/stringManipulations");
        formatNumberToNaira = stringManipulations.formatNumberToNaira;
        formatNumber = stringManipulations.formatNumber;
        formatNumberToNaira.mockClear();
        formatNumber.mockClear();
    });

    const renderComponent = (props = {}) => {
        const defaultProps = {
            formik: {
                values: { amount: "" },
                touched: {},
                errors: {},
                setFieldValue: setFieldValueMock,
                getFieldProps: getFieldPropsMock.mockReturnValue({
                    value: props.formik?.values?.amount || "",
                }),
                handleBlur: handleBlurMock,
            },
            className: "test-class",
            label: "Amount",
            name: "amount",
            showCurrency: true,
            disabled: false,
        };

        return render(<AmountInput {...defaultProps} {...props} />);
    };

    test("renders input with label and correct className", () => {
        renderComponent();
        expect(screen.getByText("Amount")).toBeInTheDocument();
        expect(screen.getByTestId("amount-input")).toHaveClass("test-class");
    });

    test("shows error message when validation fails", () => {
        renderComponent({
            formik: {
                values: { amount: "" },
                touched: { amount: true },
                errors: { amount: "Required" },
                setFieldValue: setFieldValueMock,
                getFieldProps: getFieldPropsMock.mockReturnValue({ value: "" }),
                handleBlur: handleBlurMock,
            },
        });
        expect(screen.getByTestId("error")).toHaveTextContent("Required");
    });

    test("formats value with currency symbol when showCurrency is true and not focused", () => {
        formatNumberToNaira.mockReturnValue("₦1,000.00");
        renderComponent({
            formik: {
                values: { amount: "1000" },
                touched: {},
                errors: {},
                setFieldValue: setFieldValueMock,
                getFieldProps: getFieldPropsMock.mockReturnValue({ value: "1000" }),
                handleBlur: handleBlurMock,
            },
        });
        expect(screen.getByTestId("amount-input")).toHaveValue("₦1,000.00");
        expect(formatNumberToNaira).toHaveBeenCalledWith(1000, 2);
    });

    test("formats value without currency symbol when showCurrency is false and not focused", () => {
        formatNumber.mockReturnValue("1,000.00");
        renderComponent({
            showCurrency: false,
            formik: {
                values: { amount: "1000" },
                touched: {},
                errors: {},
                setFieldValue: setFieldValueMock,
                getFieldProps: getFieldPropsMock.mockReturnValue({ value: "1000" }),
                handleBlur: handleBlurMock,
            },
        });
        expect(screen.getByTestId("amount-input")).toHaveValue("1,000.00");
        expect(formatNumber).toHaveBeenCalledWith(1000, 2);
    });

    test("handles empty input correctly", () => {
        renderComponent({
            formik: {
                values: { amount: "1000" },
                touched: {},
                errors: {},
                setFieldValue: setFieldValueMock,
                getFieldProps: getFieldPropsMock.mockReturnValue({ value: "1000" }),
                handleBlur: handleBlurMock,
            },
        });
        const input = screen.getByTestId("amount-input");
        fireEvent.change(input, { target: { value: "" } });
        expect(setFieldValueMock).toHaveBeenCalledWith("amount", "");
    });

    test("removes non-numeric characters from input", () => {
        renderComponent();
        const input = screen.getByTestId("amount-input");
        fireEvent.change(input, { target: { value: "abc123.45def" } });
        expect(setFieldValueMock).toHaveBeenCalledWith("amount", "123.45");
    });

    test("removes commas from input value but allows them in display", () => {
        renderComponent();
        const input = screen.getByTestId("amount-input");
        fireEvent.change(input, { target: { value: "1,234.56" } });
        expect(setFieldValueMock).toHaveBeenCalledWith("amount", "1234.56");
    });

    test("converts value to number with two decimal places on blur", () => {
        renderComponent({
            formik: {
                values: { amount: "1000.5" },
                touched: {},
                errors: {},
                setFieldValue: setFieldValueMock,
                getFieldProps: getFieldPropsMock.mockReturnValue({ value: "1000.5" }),
                handleBlur: handleBlurMock,
            },
        });
        const input = screen.getByTestId("amount-input");
        fireEvent.blur(input);
        expect(setFieldValueMock).toHaveBeenCalledWith("amount", "1000.50");
    });

    test("clears input when value is zero on blur", () => {
        renderComponent({
            formik: {
                values: { amount: "0" },
                touched: {},
                errors: {},
                setFieldValue: setFieldValueMock,
                getFieldProps: getFieldPropsMock.mockReturnValue({ value: "0" }),
                handleBlur: handleBlurMock,
            },
        });
        const input = screen.getByTestId("amount-input");
        fireEvent.blur(input);
        expect(setFieldValueMock).toHaveBeenCalledWith("amount", "");
    });

    test("ignores input with multiple decimal points", () => {
        renderComponent();
        const input = screen.getByTestId("amount-input");
        fireEvent.change(input, { target: { value: "12.34.56" } });
        expect(setFieldValueMock).not.toHaveBeenCalled();
    });

    test("limits decimal places to two", () => {
        renderComponent();
        const input = screen.getByTestId("amount-input");
        fireEvent.change(input, { target: { value: "12.3456" } });
        expect(setFieldValueMock).toHaveBeenCalledWith("amount", "12.34");
    });

    test("displays empty string when value is null or undefined", () => {
        renderComponent({
            formik: {
                values: { amount: null },
                touched: {},
                errors: {},
                setFieldValue: setFieldValueMock,
                getFieldProps: getFieldPropsMock.mockReturnValue({ value: null }),
                handleBlur: handleBlurMock,
            },
        });
        expect(screen.getByTestId("amount-input")).toHaveValue("");
    });

    test("displays empty string when value is undefined", () => {
        renderComponent({
            formik: {
                values: { amount: undefined },
                touched: {},
                errors: {},
                setFieldValue: setFieldValueMock,
                getFieldProps: getFieldPropsMock.mockReturnValue({ value: undefined }),
                handleBlur: handleBlurMock,
            },
        });
        expect(screen.getByTestId("amount-input")).toHaveValue("");
    });

    test("handles non-numeric input gracefully", () => {
        renderComponent({
            formik: {
                values: { amount: "abc" },
                touched: {},
                errors: {},
                setFieldValue: setFieldValueMock,
                getFieldProps: getFieldPropsMock.mockReturnValue({ value: "abc" }),
                handleBlur: handleBlurMock,
            },
        });
        const input = screen.getByTestId("amount-input");
        expect(input).toHaveValue("");
        fireEvent.blur(input);
        expect(setFieldValueMock).not.toHaveBeenCalled();
    });

    test("disables input when disabled prop is true", () => {
        renderComponent({ disabled: true });
        expect(screen.getByTestId("amount-input")).toBeDisabled();
    });

    test("enables input when disabled prop is false", () => {
        renderComponent({ disabled: false });
        expect(screen.getByTestId("amount-input")).not.toBeDisabled();
    });

    test("prevents input changes when disabled", () => {
        renderComponent({
            disabled: true,
            formik: {
                values: { amount: "1000" },
                touched: {},
                errors: {},
                setFieldValue: setFieldValueMock,
                getFieldProps: getFieldPropsMock.mockReturnValue({ value: "1000" }),
                handleBlur: handleBlurMock,
            },
        });
        const input = screen.getByTestId("amount-input");
        fireEvent.change(input, { target: { value: "2000" } });
        expect(setFieldValueMock).not.toHaveBeenCalled();
    });

    test("calls handleBlur from formik on blur", () => {
        renderComponent();
        const input = screen.getByTestId("amount-input");
        fireEvent.blur(input);
        expect(handleBlurMock).toHaveBeenCalled();
    });

    test("handles negative numbers by removing minus sign", () => {
        renderComponent();
        const input = screen.getByTestId("amount-input");
        fireEvent.change(input, { target: { value: "-123.45" } });
        expect(setFieldValueMock).toHaveBeenCalledWith("amount", "123.45");
    });

    test("handles input with only decimal point", () => {
        renderComponent();
        const input = screen.getByTestId("amount-input");
        fireEvent.change(input, { target: { value: "." } });
        expect(setFieldValueMock).toHaveBeenCalledWith("amount", ".");
    });

    test("handles input with trailing decimal point", () => {
        renderComponent();
        const input = screen.getByTestId("amount-input");
        fireEvent.change(input, { target: { value: "123." } });
        expect(setFieldValueMock).toHaveBeenCalledWith("amount", "123.");
    });

    test("handles input with leading decimal point", () => {
        renderComponent();
        const input = screen.getByTestId("amount-input");
        fireEvent.change(input, { target: { value: ".123" } });
        expect(setFieldValueMock).toHaveBeenCalledWith("amount", ".12");
    });

    test("handles rapid input changes correctly", () => {
        renderComponent();
        const input = screen.getByTestId("amount-input");
        fireEvent.change(input, { target: { value: "12" } });
        fireEvent.change(input, { target: { value: "123" } });
        fireEvent.change(input, { target: { value: "123.4" } });
        expect(setFieldValueMock).toHaveBeenCalledTimes(3);
        expect(setFieldValueMock).toHaveBeenLastCalledWith("amount", "123.4");
    });

    test("handles blur with invalid number gracefully", () => {
        renderComponent({
            formik: {
                values: { amount: "." },
                touched: {},
                errors: {},
                setFieldValue: setFieldValueMock,
                getFieldProps: getFieldPropsMock.mockReturnValue({ value: "." }),
                handleBlur: handleBlurMock,
            },
        });
        const input = screen.getByTestId("amount-input");
        fireEvent.blur(input);
        expect(setFieldValueMock).not.toHaveBeenCalled();
    });

    test("shows raw unformatted value when input is focused", () => {
        renderComponent({
            formik: {
                values: { amount: "1000" },
                touched: {},
                errors: {},
                setFieldValue: setFieldValueMock,
                getFieldProps: getFieldPropsMock.mockReturnValue({ value: "1000" }),
                handleBlur: handleBlurMock,
            },
        });
        const input = screen.getByTestId("amount-input");
        fireEvent.focus(input);
        expect(input).toHaveValue("1000"); // raw string, no formatting
    });

    test("displayValue returns empty string for non-numeric unfocused value", () => {
        renderComponent({
            formik: {
                values: { amount: "abc" },
                touched: {},
                errors: {},
                setFieldValue: setFieldValueMock,
                getFieldProps: getFieldPropsMock.mockReturnValue({ value: "abc" }),
                handleBlur: handleBlurMock,
            },
        });
        const input = screen.getByTestId("amount-input");
        // since not focused, displayValue returns "" for non-numbers
        expect(input).toHaveValue("");
    });

    test("shows '0' when focused and value is zero string '0'", () => {
        renderComponent({
            formik: {
                values: { amount: "0" },
                touched: {},
                errors: {},
                setFieldValue: setFieldValueMock,
                getFieldProps: getFieldPropsMock.mockReturnValue({ value: "0" }),
                handleBlur: handleBlurMock,
            },
        });
        const input = screen.getByTestId("amount-input");
        fireEvent.focus(input);
        expect(input).toHaveValue("0");
    });
    test("shows empty string when focused and value is empty string", () => {
        renderComponent({
            formik: {
                values: { amount: "" },
                touched: {},
                errors: {},
                setFieldValue: setFieldValueMock,
                getFieldProps: getFieldPropsMock.mockReturnValue({ value: "" }),
                handleBlur: handleBlurMock,
            },
        });
        const input = screen.getByTestId("amount-input");
        fireEvent.focus(input);
        expect(input).toHaveValue("");
    });
    test("shows empty string when focused and value is null", () => {
        renderComponent({
            formik: {
                values: { amount: null },
                touched: {},
                errors: {},
                setFieldValue: setFieldValueMock,
                getFieldProps: getFieldPropsMock.mockReturnValue({ value: null }),
                handleBlur: handleBlurMock,
            },
        });
        const input = screen.getByTestId("amount-input");
        fireEvent.focus(input);
        expect(input).toHaveValue("");
    });
    test("shows empty string when focused and value is undefined", () => {
        renderComponent({
            formik: {
                values: { amount: undefined },
                touched: {},
                errors: {},
                setFieldValue: setFieldValueMock,
                getFieldProps: getFieldPropsMock.mockReturnValue({ value: undefined }),
                handleBlur: handleBlurMock,
            },
        });
        const input = screen.getByTestId("amount-input");
        fireEvent.focus(input);
        expect(input).toHaveValue("");
    });
    test("shows empty string when focused and value is NaN", () => {
        renderComponent({
            formik: {
                values: { amount: NaN },
                touched: {},
                errors: {},
                setFieldValue: setFieldValueMock,
                getFieldProps: getFieldPropsMock.mockReturnValue({ value: NaN }),
                handleBlur: handleBlurMock,
            },
        });
        const input = screen.getByTestId("amount-input");
        fireEvent.focus(input);
        expect(input).toHaveValue("");
    });
});
