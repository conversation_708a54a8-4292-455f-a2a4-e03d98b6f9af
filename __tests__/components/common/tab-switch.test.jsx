import { fireEvent, render, screen } from "@testing-library/react";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import TabSwitch from "../../../src/components/common/tab-switch";

jest.mock("@formkit/auto-animate", () => jest.fn(() => {}));

// Mocking Next.js hooks
jest.mock("next/navigation", () => ({
    useRouter: jest.fn(),
    usePathname: jest.fn(),
    useSearchParams: jest.fn(),
}));

describe("TabSwitch Component", () => {
    const tabs = ["Tab 1", "Tab 2", "Tab 3"];
    const panels = [
        <div key="panel-1">Panel 1</div>,
        <div key="panel-2">Panel 2</div>,
        <div key="panel-3">Panel 3</div>,
    ];

    beforeEach(() => {
        // Reset mocks before each test
        useRouter.mockReturnValue({ push: jest.fn() });
        usePathname.mockReturnValue("/current-page");
        useSearchParams.mockReturnValue(new URLSearchParams());
    });

    test("should render the tabs correctly", () => {
        render(<TabSwitch tabs={tabs} panels={panels} />);

        // Check if all tabs are rendered
        tabs.forEach((tab) => {
            expect(screen.getByText(tab)).toBeInTheDocument();
        });
    });

    test("should highlight the active tab", () => {
        render(<TabSwitch tabs={tabs} panels={panels} />);

        // Initially, the first tab should be active
        expect(screen.getByText("Tab 1")).toHaveClass("text-primary");

        // Simulate a click on Tab 2
        fireEvent.click(screen.getByText("Tab 2"));

        // Tab 2 should now be active, and Tab 1 should not
        expect(screen.getByText("Tab 2")).toHaveClass("text-primary");
        expect(screen.getByText("Tab 1")).not.toHaveClass("text-primary");
    });

    test("should display the correct panel based on the selected tab", () => {
        render(<TabSwitch tabs={tabs} panels={panels} />);

        // Initially, Panel 1 should be visible
        expect(screen.getByText("Panel 1")).toBeInTheDocument();

        // Simulate a click on Tab 2, and check if Panel 2 is shown
        fireEvent.click(screen.getByText("Tab 2"));
        expect(screen.getByText("Panel 2")).toBeInTheDocument();
    });

    test("should update the URL query when a tab is clicked", () => {
        render(<TabSwitch tabs={tabs} panels={panels} />);

        const routerPush = useRouter().push;

        // Simulate a click on Tab 2
        fireEvent.click(screen.getByText("Tab 2"));

        // Ensure the URL's query string is updated correctly
        expect(routerPush).toHaveBeenCalledWith(
            "/current-page?tab=1" // The tab index is 1 for Tab 2
        );
    });

    test("should select the tab based on URL query params", () => {
        const searchParams = new URLSearchParams({ tab: "2" }); // Simulate the URL having a query for Tab 3

        useSearchParams.mockReturnValue(searchParams);

        render(<TabSwitch tabs={tabs} panels={panels} />);

        // Tab 3 should be active because the query parameter `tab=2` corresponds to the 3rd tab (index 2)
        expect(screen.getByText("Tab 3")).toHaveClass("text-primary");
        expect(screen.getByText("Panel 3")).toBeInTheDocument();
    });

    test("should update selected tab when URL parameters change", () => {
        // Start with tab=0
        const initialSearchParams = new URLSearchParams({ tab: "0" });
        useSearchParams.mockReturnValue(initialSearchParams);

        const { rerender } = render(<TabSwitch tabs={tabs} panels={panels} />);

        // Initially, Tab 1 should be active
        expect(screen.getByText("Tab 1")).toHaveClass("text-primary");
        expect(screen.getByText("Panel 1")).toBeInTheDocument();

        // Simulate URL parameter change to tab=1
        const updatedSearchParams = new URLSearchParams({ tab: "1" });
        useSearchParams.mockReturnValue(updatedSearchParams);

        // Re-render with new search params
        rerender(<TabSwitch tabs={tabs} panels={panels} />);

        // Now Tab 2 should be active
        expect(screen.getByText("Tab 2")).toHaveClass("text-primary");
        expect(screen.getByText("Panel 2")).toBeInTheDocument();
        expect(screen.queryByText("Panel 1")).not.toBeInTheDocument();
    });

    test("should handle URL parameter changes from external navigation", () => {
        // Start with no tab parameter
        const initialSearchParams = new URLSearchParams();
        useSearchParams.mockReturnValue(initialSearchParams);

        const { rerender } = render(<TabSwitch tabs={tabs} panels={panels} />);

        // Initially, Tab 1 should be active (default)
        expect(screen.getByText("Tab 1")).toHaveClass("text-primary");

        // Simulate external navigation that updates URL to tab=2
        const updatedSearchParams = new URLSearchParams({ tab: "2" });
        useSearchParams.mockReturnValue(updatedSearchParams);

        // Re-render with new search params
        rerender(<TabSwitch tabs={tabs} panels={panels} />);

        // Now Tab 3 should be active
        expect(screen.getByText("Tab 3")).toHaveClass("text-primary");
        expect(screen.getByText("Panel 3")).toBeInTheDocument();
    });

    test("should handle invalid tab parameters gracefully", () => {
        // Test with invalid tab parameter
        const searchParams = new URLSearchParams({ tab: "invalid" });
        useSearchParams.mockReturnValue(searchParams);

        render(<TabSwitch tabs={tabs} panels={panels} />);

        // Should default to first tab when invalid parameter is provided
        expect(screen.getByText("Tab 1")).toHaveClass("text-primary");
        expect(screen.getByText("Panel 1")).toBeInTheDocument();
    });

    test("should handle out-of-bounds tab parameters gracefully", () => {
        // Test with tab index that exceeds the number of tabs
        const searchParams = new URLSearchParams({ tab: "10" });
        useSearchParams.mockReturnValue(searchParams);

        render(<TabSwitch tabs={tabs} panels={panels} />);

        // Should default to first tab when out-of-bounds parameter is provided
        expect(screen.getByText("Tab 1")).toHaveClass("text-primary");
        expect(screen.getByText("Panel 1")).toBeInTheDocument();
    });

    test("should handle negative tab parameters gracefully", () => {
        // Test with negative tab index
        const searchParams = new URLSearchParams({ tab: "-1" });
        useSearchParams.mockReturnValue(searchParams);

        render(<TabSwitch tabs={tabs} panels={panels} />);

        // Should default to first tab when negative parameter is provided
        expect(screen.getByText("Tab 1")).toHaveClass("text-primary");
        expect(screen.getByText("Panel 1")).toBeInTheDocument();
    });

    // Test for getTabKey functionality
    test("should generate unique keys for different tab types", () => {
        // This is testing the internal getTabKey method
        // We can verify it works by checking that React doesn't throw warnings about unique keys

        // Create tabs with a mix of types that would test all branches of getTabKey
        const mixedTabs = [
            "String Tab", // String case
            { id: "object-id", label: "Object Tab" }, // Object with id/label case
            <div key="react-element">React Element Tab</div>, // React element case (toString fallback)
            123, // Number case (toString fallback)
        ];

        // If getTabKey works correctly, React won't complain about duplicate keys
        // We're testing the implementation details here, but it's necessary for coverage
        const { container } = render(<TabSwitch tabs={mixedTabs} />);

        // Check that all tabs rendered without errors
        expect(screen.getByText("String Tab")).toBeInTheDocument();
        expect(screen.getByText("Object Tab")).toBeInTheDocument();
        expect(screen.getByText("React Element Tab")).toBeInTheDocument();
        expect(screen.getByText("123")).toBeInTheDocument();

        // Each button should have a unique key attribute (we can't directly test this,
        // but we can verify React didn't throw warnings in the console)
        expect(container.querySelectorAll("button").length).toBe(4);
    });
});

// New tests for the activeTab and onChange functionality
describe("TabSwitch Component with activeTab and onChange", () => {
    // Test with string tabs
    const stringTabs = ["Tab 1", "Tab 2", "Tab 3"];

    // Test with object tabs
    const objectTabs = [
        { id: "tab1", label: "Tab One" },
        { id: "tab2", label: "Tab Two" },
        { id: "tab3", label: "Tab Three" },
    ];

    beforeEach(() => {
        // Reset mocks before each test
        useRouter.mockReturnValue({ push: jest.fn() });
        usePathname.mockReturnValue("/current-page");
        useSearchParams.mockReturnValue(new URLSearchParams());
    });

    test("should render with string tabs and highlight active tab", () => {
        const handleChange = jest.fn();
        render(<TabSwitch tabs={stringTabs} activeTab="Tab 2" onChange={handleChange} />);

        // Check if all tabs are rendered
        stringTabs.forEach((tab) => {
            expect(screen.getByText(tab)).toBeInTheDocument();
        });

        // Check if the active tab is highlighted
        expect(screen.getByText("Tab 2")).toHaveClass("text-primary");
    });

    test("should call onChange when a tab is clicked with string tabs", () => {
        const handleChange = jest.fn();
        render(<TabSwitch tabs={stringTabs} activeTab="Tab 1" onChange={handleChange} />);

        // Click on Tab 3
        fireEvent.click(screen.getByText("Tab 3"));

        // Check if onChange was called with the correct tab
        expect(handleChange).toHaveBeenCalledWith("Tab 3");
    });

    test("should render with object tabs and highlight active tab", () => {
        const handleChange = jest.fn();
        render(<TabSwitch tabs={objectTabs} activeTab="tab2" onChange={handleChange} />);

        // Check if all tab labels are rendered
        objectTabs.forEach((tab) => {
            expect(screen.getByText(tab.label)).toBeInTheDocument();
        });

        // Check if the active tab is highlighted
        expect(screen.getByText("Tab Two")).toHaveClass("text-primary");
    });

    test("should call onChange when a tab is clicked with object tabs", () => {
        const handleChange = jest.fn();
        render(<TabSwitch tabs={objectTabs} activeTab="tab1" onChange={handleChange} />);

        // Click on Tab Three
        fireEvent.click(screen.getByText("Tab Three"));

        // Check if onChange was called with the correct tab id
        expect(handleChange).toHaveBeenCalledWith("tab3");
    });

    test("should not update URL when using activeTab and onChange", () => {
        const handleChange = jest.fn();
        const routerPush = useRouter().push;

        render(<TabSwitch tabs={objectTabs} activeTab="tab1" onChange={handleChange} />);

        // Click on Tab Two
        fireEvent.click(screen.getByText("Tab Two"));

        // Check that router.push was not called
        expect(routerPush).not.toHaveBeenCalled();

        // But onChange should have been called
        expect(handleChange).toHaveBeenCalledWith("tab2");
    });
});

const createQueryString = (key, value) => `${key}=${value}`;

const TestComponent = ({ pathname }) => {
    const router = useRouter();
    const [selectedTab, setSelectedTab] = useState("tab1");

    useEffect(() => {
        router.push(pathname + "?" + createQueryString("tab", selectedTab));
    }, [pathname, router, selectedTab]);

    return (
        <div>
            <button onClick={() => setSelectedTab("tab2")}>Change Tab</button>
        </div>
    );
};

describe("Change Tab on switch", () => {
    const mockRouter = {
        push: jest.fn(),
    };

    beforeEach(() => {
        useRouter.mockReturnValue(mockRouter);
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    test("calls router.push with the correct query string on mount", () => {
        render(<TestComponent pathname="/test" />);

        expect(mockRouter.push).toHaveBeenCalledWith("/test?tab=tab1");
    });
});
