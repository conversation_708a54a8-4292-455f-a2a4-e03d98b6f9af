import { transferAxios } from "@/api/axios";
import { useTransactionFees } from "@/hooks/useTransactionFees";
import { renderHook, waitFor } from "@testing-library/react";

// Mock useDebounce to return value immediately
jest.mock("@/hooks/useDebounce", () => ({
    useDebounce: (value) => value,
}));

jest.mock("@/api/axios", () => ({
    transferAxios: {
        get: jest.fn(),
    },
}));

describe("useTransactionFees", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("should initialize with 0 fees and not loading", () => {
        const { result } = renderHook(() => useTransactionFees({ amount: 0 }));
        expect(result.current.fees).toBe(0);
        expect(result.current.loading).toBe(false);
    });

    it("should fetch fees when amount is set and bankCode is not FCMB", async () => {
        transferAxios.get.mockResolvedValue({ data: { message: 100.005 } });

        const { result } = renderHook(() => useTransactionFees({ amount: 5000, bankCode: "000001" }));

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(transferAxios.get).toHaveBeenCalledWith("/fee?type=transfer&amount=5000");
        expect(result.current.fees).toBe(100.01); // Test rounding up
    });

    it("should not fetch fees when bankCode is FCMB (000003)", async () => {
        transferAxios.get.mockResolvedValue({ data: { message: 100.005 } });

        const { result } = renderHook(() => useTransactionFees({ amount: 5000, bankCode: "000003" }));

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(transferAxios.get).not.toHaveBeenCalled();
        expect(result.current.fees).toBe(0);
    });

    it("should handle API errors gracefully", async () => {
        transferAxios.get.mockRejectedValue(new Error("Network Error"));

        const { result } = renderHook(() => useTransactionFees({ amount: 5000, bankCode: "000001" }));

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.fees).toBe(0);
    });

    it("should use custom type if provided and bankCode is not FCMB", async () => {
        transferAxios.get.mockResolvedValue({ data: { message: 20.007 } });

        const { result } = renderHook(() => useTransactionFees({ amount: 2000, type: "bill", bankCode: "000002" }));

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(transferAxios.get).toHaveBeenCalledWith("/fee?type=bill&amount=2000");
        expect(result.current.fees).toBe(20.01); // Test rounding up
    });

    it("should not fetch fees if amount is 0, regardless of bankCode", () => {
        renderHook(() => useTransactionFees({ amount: 0, bankCode: "000001" }));
        expect(transferAxios.get).not.toHaveBeenCalled();
    });

    it("should round fees correctly when fee is exactly divisible by 0.01 and bankCode is not FCMB", async () => {
        transferAxios.get.mockResolvedValue({ data: { message: 100.0 } });

        const { result } = renderHook(() => useTransactionFees({ amount: 5000, bankCode: "000001" }));

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.fees).toBe(100.0);
    });

    it("should round fees correctly when fee has more than 2 decimal places and bankCode is not FCMB", async () => {
        transferAxios.get.mockResolvedValue({ data: { message: 100.009 } });

        const { result } = renderHook(() => useTransactionFees({ amount: 5000, bankCode: "000001" }));

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.fees).toBe(100.01);
    });
});
