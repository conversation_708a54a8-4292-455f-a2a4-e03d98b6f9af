import { configureStore, EnhancedStore } from "@reduxjs/toolkit";
import { signOut } from "@/redux/features/user";
import billPaymentClearMiddleware from "@/redux/middleware/billPaymentClearMiddleware";
import billPaymentsReducer from "@/redux/slices/billPaymentSlice";

// Mock the clearBillCategoriesCache function
jest.mock("@/redux/actions/billPaymentThunks", () => ({
    ...jest.requireActual("@/redux/actions/billPaymentThunks"),
    clearBillCategoriesCache: jest.fn(),
}));

import { clearBillCategoriesCache } from "@/redux/actions/billPaymentThunks";

// Mock session storage
const sessionStorageMock = (() => {
    let store: Record<string, string> = {};
    return {
        getItem: (key: string) => store[key] || null,
        setItem: (key: string, value: string) => {
            store[key] = value.toString();
        },
        removeItem: (key: string) => {
            delete store[key];
        },
        clear: () => {
            store = {};
        },
    };
})();

Object.defineProperty(window, "sessionStorage", {
    value: sessionStorageMock,
});

const mockClearBillCategoriesCache = clearBillCategoriesCache as jest.MockedFunction<typeof clearBillCategoriesCache>;

describe("Bill Payment Clear Middleware", () => {
    let store: EnhancedStore<{ billPayments: ReturnType<typeof billPaymentsReducer> }>;

    beforeEach(() => {
        // Clear mocks and session storage before each test
        jest.clearAllMocks();
        sessionStorage.clear();

        // Create fresh store with middleware
        store = configureStore({
            reducer: {
                billPayments: billPaymentsReducer,
            },
            middleware: (getDefaultMiddleware) => getDefaultMiddleware().concat(billPaymentClearMiddleware),
        });
    });

    test("should call clearBillCategoriesCache on user signOut", () => {
        // Arrange - Ensure function is mocked
        expect(mockClearBillCategoriesCache).not.toHaveBeenCalled();

        // Act - Dispatch signOut action
        store.dispatch(signOut());

        // Assert - clearBillCategoriesCache should be called
        expect(mockClearBillCategoriesCache).toHaveBeenCalledTimes(1);
    });

    test("should clear bill categories from Redux state on user signOut", () => {
        // Act - Dispatch signOut action
        store.dispatch(signOut());

        // Assert - Categories should be reset in Redux state
        const finalState = store.getState();
        expect(finalState.billPayments.categories.content).toEqual([]);
        expect(finalState.billPayments.categories.loading).toBe(false);
        expect(finalState.billPayments.categories.error).toBeUndefined();
    });

    test("should only react to user/signOut action type", () => {
        // Act - Dispatch a different action
        store.dispatch({ type: "user/updateUser", payload: { user: {} } });

        // Assert - clearBillCategoriesCache should not be called
        expect(mockClearBillCategoriesCache).not.toHaveBeenCalled();
    });

    test("should handle signOut gracefully even if clearBillCategoriesCache throws", () => {
        // Arrange - Mock the function to throw an error
        mockClearBillCategoriesCache.mockImplementationOnce(() => {
            throw new Error("Storage error");
        });

        // Act - Dispatch signOut action (should not throw error)
        expect(() => {
            store.dispatch(signOut());
        }).not.toThrow();

        // Assert - Function was still called despite the error
        expect(mockClearBillCategoriesCache).toHaveBeenCalledTimes(1);
    });
});
