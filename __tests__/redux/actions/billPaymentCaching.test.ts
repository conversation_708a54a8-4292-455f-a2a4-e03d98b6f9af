import { configureStore, ThunkDispatch, AnyAction } from "@reduxjs/toolkit";
import { fetchBillCategories, clearBillCategoriesCache } from "@/redux/actions/billPaymentThunks";
import billPaymentsReducer from "@/redux/slices/billPaymentSlice";
import { BillCategoriesResponse } from "@/redux/types/billPayments";
import { billAxios } from "@/api/axios";

// Mock the axios instance
jest.mock("@/api/axios", () => ({
    billAxios: {
        get: jest.fn(),
    },
}));

// Mock session storage
const sessionStorageMock = (() => {
    let store: Record<string, string> = {};
    return {
        getItem: (key: string) => store[key] || null,
        setItem: (key: string, value: string) => {
            store[key] = value.toString();
        },
        removeItem: (key: string) => {
            delete store[key];
        },
        clear: () => {
            store = {};
        },
    };
})();

Object.defineProperty(window, "sessionStorage", {
    value: sessionStorageMock,
});

const mockBillAxios = billAxios as jest.Mocked<typeof billAxios>;

describe("Bill Payment Categories Caching", () => {
    let store: ReturnType<typeof configureStore>;

    type AppDispatch = ThunkDispatch<ReturnType<typeof billPaymentsReducer>, undefined, AnyAction>;

    const mockCategoriesResponse: BillCategoriesResponse = {
        totalPages: 1,
        size: 10,
        totalElements: 3,
        hasNext: false,
        hasPrevious: false,
        content: [
            { id: 1, categoryName: "Airtime", description: "Mobile airtime recharge" },
            { id: 2, categoryName: "Electricity", description: "Electric utility bills" },
            { id: 3, categoryName: "Cable TV", description: "Cable and satellite TV subscriptions" },
        ],
    };

    beforeEach(() => {
        // Clear mocks and session storage before each test
        jest.clearAllMocks();
        sessionStorage.clear();

        // Create fresh store for each test
        store = configureStore({
            reducer: {
                billPayments: billPaymentsReducer,
            },
        });
    });

    describe("Session-based caching", () => {
        test("should fetch categories from API on first call in session", async () => {
            // Arrange
            mockBillAxios.get.mockResolvedValueOnce({ data: mockCategoriesResponse });

            // Act
            const resultAction = await (store.dispatch as AppDispatch)(fetchBillCategories({ page: 1, size: 50 }));

            // Assert
            expect(mockBillAxios.get).toHaveBeenCalledTimes(1);
            expect(mockBillAxios.get).toHaveBeenCalledWith("/api/v1/vas/biller-categories", {
                params: { page: 1, size: 50 },
            });
            expect(resultAction.meta.requestStatus).toBe("fulfilled");
            expect(resultAction.payload).toEqual(mockCategoriesResponse);

            // Verify categories are stored in session storage
            const cachedCategories = sessionStorage.getItem("billCategories");
            expect(cachedCategories).toBeDefined();
            expect(JSON.parse(cachedCategories!)).toEqual(mockCategoriesResponse);
        });

        test("should return cached categories on subsequent calls without API call", async () => {
            // Arrange - Set up cached categories in session storage
            sessionStorage.setItem("billCategories", JSON.stringify(mockCategoriesResponse));

            // Act
            const resultAction = await (store.dispatch as AppDispatch)(fetchBillCategories({ page: 1, size: 50 }));

            // Assert
            expect(mockBillAxios.get).not.toHaveBeenCalled();
            expect(resultAction.meta.requestStatus).toBe("fulfilled");
            expect(resultAction.payload).toEqual(mockCategoriesResponse);
        });

        test("should fetch from API when forceRefresh is true even with cache", async () => {
            // Arrange - Set up cached categories
            sessionStorage.setItem("billCategories", JSON.stringify(mockCategoriesResponse));
            mockBillAxios.get.mockResolvedValueOnce({ data: mockCategoriesResponse });

            // Act
            const resultAction = await (store.dispatch as AppDispatch)(
                fetchBillCategories({ page: 1, size: 50, forceRefresh: true })
            );

            // Assert
            expect(mockBillAxios.get).toHaveBeenCalledTimes(1);
            expect(resultAction.meta.requestStatus).toBe("fulfilled");
            expect(resultAction.payload).toEqual(mockCategoriesResponse);
        });

        test("should not use cache when categories are empty array", async () => {
            // Arrange - Set up empty categories cache
            const emptyResponse: BillCategoriesResponse = {
                ...mockCategoriesResponse,
                content: [],
                totalElements: 0,
            };
            sessionStorage.setItem("billCategories", JSON.stringify(emptyResponse));
            mockBillAxios.get.mockResolvedValueOnce({ data: mockCategoriesResponse });

            // Act
            const resultAction = await (store.dispatch as AppDispatch)(fetchBillCategories({ page: 1, size: 50 }));

            // Assert
            expect(mockBillAxios.get).toHaveBeenCalledTimes(1);
            expect(resultAction.meta.requestStatus).toBe("fulfilled");
        });
    });

    describe("Cache clearing on logout", () => {
        test("should clear categories from session storage using clearBillCategoriesCache", () => {
            // Arrange - Set up cached categories
            sessionStorage.setItem("billCategories", JSON.stringify(mockCategoriesResponse));
            expect(sessionStorage.getItem("billCategories")).toBeDefined();

            // Act - Clear cache using the utility function
            clearBillCategoriesCache();

            // Assert
            expect(sessionStorage.getItem("billCategories")).toBeNull();
        });

        test("should fetch fresh categories after logout when cache is cleared", async () => {
            // Arrange - Simulate post-logout state with no cache
            expect(sessionStorage.getItem("billCategories")).toBeNull();
            mockBillAxios.get.mockResolvedValueOnce({ data: mockCategoriesResponse });

            // Act
            const resultAction = await (store.dispatch as AppDispatch)(fetchBillCategories({ page: 1, size: 50 }));

            // Assert
            expect(mockBillAxios.get).toHaveBeenCalledTimes(1);
            expect(resultAction.meta.requestStatus).toBe("fulfilled");
            expect(resultAction.payload).toEqual(mockCategoriesResponse);
        });
    });

    describe("Error handling with caching", () => {
        test("should return cached data when API fails and cache exists", async () => {
            // Arrange - Set up cached categories and mock API failure
            sessionStorage.setItem("billCategories", JSON.stringify(mockCategoriesResponse));
            mockBillAxios.get.mockRejectedValueOnce(new Error("Network error"));

            // Act
            const resultAction = await (store.dispatch as AppDispatch)(fetchBillCategories({ forceRefresh: true }));

            // Assert - Should still return cached data as fallback
            expect(mockBillAxios.get).toHaveBeenCalledTimes(1);
            expect(resultAction.meta.requestStatus).toBe("fulfilled");
            expect(resultAction.payload).toEqual(mockCategoriesResponse);
        });

        test("should fail when API fails and no cache exists", async () => {
            // Arrange - No cache and mock API failure
            expect(sessionStorage.getItem("billCategories")).toBeNull();
            mockBillAxios.get.mockRejectedValueOnce(new Error("Network error"));

            // Act
            const resultAction = await (store.dispatch as AppDispatch)(fetchBillCategories());

            // Assert
            expect(mockBillAxios.get).toHaveBeenCalledTimes(1);
            expect(resultAction.meta.requestStatus).toBe("rejected");
        });
    });

    describe("Session storage integration", () => {
        test("should handle malformed cache data gracefully", async () => {
            // Arrange - Set up malformed cache data
            sessionStorage.setItem("billCategories", "invalid-json");
            mockBillAxios.get.mockResolvedValueOnce({ data: mockCategoriesResponse });

            // Act
            const resultAction = await (store.dispatch as AppDispatch)(fetchBillCategories());

            // Assert - Should fetch from API when cache is invalid
            expect(mockBillAxios.get).toHaveBeenCalledTimes(1);
            expect(resultAction.meta.requestStatus).toBe("fulfilled");
            expect(resultAction.payload).toEqual(mockCategoriesResponse);
        });

        test("should update cache after successful API call", async () => {
            // Arrange
            mockBillAxios.get.mockResolvedValueOnce({ data: mockCategoriesResponse });

            // Act
            await (store.dispatch as AppDispatch)(fetchBillCategories());

            // Assert
            const cachedCategories = sessionStorage.getItem("billCategories");
            expect(cachedCategories).toBeDefined();
            expect(JSON.parse(cachedCategories!)).toEqual(mockCategoriesResponse);
        });
    });
});
