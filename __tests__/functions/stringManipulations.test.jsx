import {
    formatNumber,
    formatNumberToNaira,
    getNameInitials,
    copyToClipboard,
    generateAuthQRCodeValue,
    convertCamelCaseToWords,
    convertFirstLetterToUppercase,
    maskNumber,
    convertToCamelCase,
    enumToCapitalizedPhrase,
    convertDateToMonthYear,
    findOptionByValue,
    convertToSnakeCase,
    capitalizeUserName,
    capitalizeFullName,
} from "@/functions/stringManipulations";

// Mock the feedback function
jest.mock("@/functions/feedback", () => ({
    sendFeedback: jest.fn(),
}));

describe("String Manipulation Functions", () => {
    describe("formatNumber", () => {
        it("formats numbers with Nigerian locale", () => {
            expect(formatNumber(1234567)).toBe("1,234,567");
            expect(formatNumber(1000)).toBe("1,000");
            expect(formatNumber(0)).toBe("0");
        });

        it("formats numbers with custom fraction digits", () => {
            expect(formatNumber(1234.567, 2)).toBe("1,234.57");
            expect(formatNumber(1234.567, 0)).toBe("1,235");
        });
    });

    describe("formatNumberToNaira", () => {
        it("formats numbers as Nigerian currency", () => {
            expect(formatNumberToNaira(1234567)).toBe("₦1,234,567.00");
            expect(formatNumberToNaira(1000)).toBe("₦1,000.00");
        });

        it("formats numbers with custom fraction digits", () => {
            expect(formatNumberToNaira(1234.567, 3)).toBe("₦1,234.567");
            expect(formatNumberToNaira(1234.567, 0)).toBe("₦1,235");
        });
    });

    describe("getNameInitials", () => {
        it("extracts initials from full name", () => {
            expect(getNameInitials("John Doe")).toBe("JD");
            expect(getNameInitials("Mary Jane Smith")).toBe("MJ");
            expect(getNameInitials("A")).toBe("A");
        });

        it("handles edge cases", () => {
            expect(getNameInitials("")).toBe("");
            expect(getNameInitials("   ")).toBe("");
            expect(getNameInitials(null)).toBeUndefined();
            expect(getNameInitials(undefined)).toBeUndefined();
        });
    });

    describe("copyToClipboard", () => {
        it("copies text to clipboard", async () => {
            const mockClipboard = {
                writeText: jest.fn().mockResolvedValue(undefined),
            };
            Object.assign(navigator, { clipboard: mockClipboard });

            await copyToClipboard("test text");
            expect(mockClipboard.writeText).toHaveBeenCalledWith("test text");
        });
    });

    describe("generateAuthQRCodeValue", () => {
        it("generates QR code value for authentication", () => {
            const result = generateAuthQRCodeValue();
            expect(result).toBe("otpauth://totp/CIB?secret=CIB");
        });
    });

    describe("convertCamelCaseToWords", () => {
        it("converts camelCase to words", () => {
            expect(convertCamelCaseToWords("firstName")).toBe("First Name");
            expect(convertCamelCaseToWords("userName")).toBe("User Name");
            expect(convertCamelCaseToWords("emailAddress")).toBe("Email Address");
        });
    });

    describe("convertFirstLetterToUppercase", () => {
        it("converts first letter to uppercase", () => {
            expect(convertFirstLetterToUppercase("hello")).toBe("Hello");
            expect(convertFirstLetterToUppercase("world")).toBe("World");
            expect(convertFirstLetterToUppercase("")).toBe("");
        });
    });

    describe("maskNumber", () => {
        it("masks numbers with asterisks", () => {
            expect(maskNumber("1234567890")).toBe("******7890");
            expect(maskNumber("1234567890", 6)).toBe("****567890");
            expect(maskNumber("1234")).toBe("1234");
        });
    });

    describe("convertToCamelCase", () => {
        it("converts snake_case to camelCase", () => {
            expect(convertToCamelCase("first_name")).toBe("firstName");
            expect(convertToCamelCase("user_name")).toBe("userName");
            expect(convertToCamelCase("email_address")).toBe("emailAddress");
        });
    });

    describe("enumToCapitalizedPhrase", () => {
        it("converts enum strings to capitalized phrases", () => {
            expect(enumToCapitalizedPhrase("FIRST_NAME")).toBe("First Name");
            expect(enumToCapitalizedPhrase("USER_NAME")).toBe("User Name");
            expect(enumToCapitalizedPhrase("EMAIL_ADDRESS")).toBe("Email Address");
        });

        it("handles edge cases", () => {
            expect(enumToCapitalizedPhrase("")).toBe("");
            expect(() => enumToCapitalizedPhrase(null)).toThrow("Input must be a string");
            expect(() => enumToCapitalizedPhrase(undefined)).toThrow("Input must be a string");
        });
    });

    describe("convertDateToMonthYear", () => {
        it("converts date to month year format", () => {
            expect(convertDateToMonthYear("2024-01-15")).toBe("January, 2024");
            expect(convertDateToMonthYear("2024-12-25")).toBe("December, 2024");
        });

        it("handles invalid dates", () => {
            expect(convertDateToMonthYear("invalid-date")).toContain("Error:");
        });
    });

    describe("findOptionByValue", () => {
        it("finds option by value", () => {
            const options = [
                { label: "Option 1", value: "opt1" },
                { label: "Option 2", value: "opt2" },
            ];
            expect(findOptionByValue("opt1", options)).toEqual({ label: "Option 1", value: "opt1" });
            expect(findOptionByValue("opt3", options)).toBeNull();
        });
    });

    describe("convertToSnakeCase", () => {
        it("converts strings to snake_case", () => {
            expect(convertToSnakeCase("firstName")).toBe("first_name");
            expect(convertToSnakeCase("User Name")).toBe("user_name");
            expect(convertToSnakeCase("email-address")).toBe("email_address");
        });
    });

    describe("capitalizeUserName", () => {
        it("capitalizes simple names", () => {
            expect(capitalizeUserName("john")).toBe("John");
            expect(capitalizeUserName("mary")).toBe("Mary");
            expect(capitalizeUserName("jane")).toBe("Jane");
        });

        it("capitalizes names with multiple words", () => {
            expect(capitalizeUserName("john doe")).toBe("John Doe");
            expect(capitalizeUserName("mary jane smith")).toBe("Mary Jane Smith");
            expect(capitalizeUserName("  john   doe  ")).toBe("John Doe");
        });

        it("handles hyphenated names", () => {
            expect(capitalizeUserName("jean-pierre")).toBe("Jean-Pierre");
            expect(capitalizeUserName("mary-jane")).toBe("Mary-Jane");
            expect(capitalizeUserName("o'connor")).toBe("O'Connor");
        });

        it("handles names with prefixes", () => {
            expect(capitalizeUserName("van der berg")).toBe("Van Der Berg");
            expect(capitalizeUserName("mc donald")).toBe("Mc Donald");
            expect(capitalizeUserName("de la cruz")).toBe("De La Cruz");
            expect(capitalizeUserName("von trapp")).toBe("Von Trapp");
        });

        it("handles edge cases", () => {
            expect(capitalizeUserName("")).toBe("");
            expect(capitalizeUserName("   ")).toBe("");
            expect(capitalizeUserName(null)).toBe("");
            expect(capitalizeUserName(undefined)).toBe("");
            expect(capitalizeUserName("123")).toBe("123");
            expect(capitalizeUserName("a")).toBe("A");
        });

        it("handles special characters and numbers", () => {
            expect(capitalizeUserName("john123")).toBe("John123");
            expect(capitalizeUserName("<EMAIL>")).toBe("<EMAIL>");
            expect(capitalizeUserName("o'brien")).toBe("O'Brien");
        });

        it("handles multiple spaces and formatting", () => {
            expect(capitalizeUserName("  john   doe  ")).toBe("John Doe");
            expect(capitalizeUserName("mary\tjane")).toBe("Mary Jane");
            expect(capitalizeUserName("john\ndoe")).toBe("John Doe");
        });
    });

    describe("capitalizeFullName", () => {
        it("capitalizes full names with first and last name", () => {
            expect(capitalizeFullName("john", "doe")).toBe("John Doe");
            expect(capitalizeFullName("mary", "jane")).toBe("Mary Jane");
        });

        it("capitalizes full names with middle name", () => {
            expect(capitalizeFullName("john", "doe", "michael")).toBe("John Michael Doe");
            expect(capitalizeFullName("mary", "jane", "elizabeth")).toBe("Mary Elizabeth Jane");
        });

        it("handles edge cases", () => {
            expect(capitalizeFullName("", "")).toBe("");
            expect(capitalizeFullName("john", "")).toBe("John");
            expect(capitalizeFullName("", "doe")).toBe("Doe");
            expect(capitalizeFullName("john", "doe", "")).toBe("John Doe");
        });

        it("handles special name formats", () => {
            expect(capitalizeFullName("jean-pierre", "dupont")).toBe("Jean-Pierre Dupont");
            expect(capitalizeFullName("van der", "berg")).toBe("Van Der Berg");
            expect(capitalizeFullName("o'connor", "smith")).toBe("O'Connor Smith");
        });
    });
});
