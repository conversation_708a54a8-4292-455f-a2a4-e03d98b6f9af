// This file contains validation rule engine definitions.
// It defines the requirements for each ValidationSchema in formik for the CIB project.

import * as Yup from "yup";

export const LoginValidationSchema = Yup.object({
    username: Yup.string().required("Email or username is required"),
    password: Yup.string().required("Password is required"),
});

export const PreLoginValidationSchema = Yup.object({
    username: Yup.string().required("Email or username is required"),
    action: Yup.string().required("Login action is required"),
});

export const TwoFaValidationSchema = Yup.object({
    otp: Yup.string()
        .max(6, "OTP cannot exceed 6 characters")
        .min(6, "OTP must be at least 6 characters")
        .required("OTP is required"),
});

export const ForgotPasswordSchema = Yup.object({
    email: Yup.string().email("Enter a valid email").required("Email is required"),
});

export const ResetPasswordValidationSchema = Yup.object({
    password: Yup.string()
        .required("Password is required")
        .min(14, "Password must be at least 14 characters")
        .matches(/[A-Z]/, "Password must contain at least one uppercase letter")
        .matches(/[0-9]/, "Password must contain at least one number")
        .matches(/[!@#$%^&*(),.?":{}|<>]/, "Password must contain at least one special character"),
    confirmPassword: Yup.string()
        .oneOf([Yup.ref("password")], "Passwords must match")
        .required("Required"),
});

export const SettingsPasswordSchema = Yup.object().shape({
    currentPassword: Yup.string().required("Current password is required"),
    newPassword: Yup.string()
        .required("New password is required")
        .min(14, "Password must be at least 14 characters")
        .matches(/[A-Z]/, "Password must contain at least one uppercase letter")
        .matches(/[0-9]/, "Password must contain at least one number")
        .matches(/[!@#$%^&*(),.?":{}|<>]/, "Password must contain at least one special character")
        .test("not-same-as-current", "New password must be different from your current password", function (value) {
            return value !== this.parent.currentPassword;
        }),
    confirmPassword: Yup.string()
        .oneOf([Yup.ref("newPassword")], "Passwords must match")
        .required("Please confirm your password"),
});

export const SettingsPinSchema = Yup.object().shape({
    currentPIN: Yup.string()
        .required("Current PIN is required")
        .matches(/^\d{6}$/, "PIN must be exactly 6 digits"),
    newPIN: Yup.string()
        .required("New PIN is required")
        .matches(/^\d{6}$/, "PIN must be exactly 6 digits")
        .test("not-same-as-current", "New PIN must be different from your current PIN", function (value) {
            return value !== this.parent.currentPIN;
        }),
});
