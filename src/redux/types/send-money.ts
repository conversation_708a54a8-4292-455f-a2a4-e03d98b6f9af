import { ExtractedBulkFileData } from "@/components/page-components/dashboard/send-money/data";
import { TransferApprovalType } from "@/types/user";

export type SendSingleTransferBody = {
    sourceAccount: string;
    sourceAccountName: string;
    sourceAccountCurrency: string;
    destinationAccount: string;
    destinationAccountName: string;
    destinationAccountCurrency: string;
    bankName: string;
    bankCode: string;
    amount: number;
    requestType: string;
    narration: string;
    needsApproval: boolean;
    frequencyType?: string;
    firstPaymentDate: string;
    lastPaymentDate: string;
    frequencyValue: number;
    transactionPin?: string;
    token: string;
};

export type SendMultipleTransferBody = {
    transferSource: string;
    amount: string;
    transferType: string;
    narration: string;
    scheduledDate: string;
    reoccurringFrequency: string;
    reoccurringStartDate: string;
    reoccurringReminderSchedule: string;
    reoccurringEndDate: string;
    reoccurringEndOccurrences: string;
};

export interface ISendMoneyInitialState {
    sendSingleTransfer: {
        error: string | null;
        loading: boolean;
        success: boolean;
    };
    sendMultipleTransfer: {
        error: string | null;
        loading: boolean;
        reference?: string;
        success: boolean;
    };
    sendBulkTransfer: {
        error: string | null;
        loading: boolean;
        success: boolean;
        reference?: string;
    };
    saveBulkTransferTemplate: {
        error: string | null;
        loading: boolean;
        success: boolean;
    };
    updateBulkPaymentTemplate: {
        error: string | null;
        loading: boolean;
        success: boolean;
    };
    downloadBulkTemplate: {
        error: string | null;
        loading: boolean;
        success: boolean;
        data: string | null;
    };
    getBulkTemplates: {
        error: string | null;
        loading: boolean;
        success: boolean;
        data: GetBulkTemplateType[] | null;
    };
    getBulkTemplateCSV: {
        error: string | null;
        loading: boolean;
        success: boolean;
        data: ExtractedBulkFileData[] | null;
    };
    getTransferApprovers: {
        error: string | null;
        loading: boolean;
        success: boolean;
        data: TransferApprovalType[] | null;
    };
}

export interface GetBulkTemplateType {
    filename: string;
    recipient: number;
    lastUsed: string;
}

export type BulkTransferBody = {
    sourceAccount: string;
    sourceAccountName: string;
    destinationAccount: string;
    destinationAccountName: string;
    bankCode: string;
    bankName: string;
    sourceAccountCurrency: string;
    destinationAccountCurrency: string;
    amount: number;
    requestType: string;
    needsApproval: boolean;
    narration: string;
    firstPaymentDate: string;
    lastPaymentDate: string;
    frequencyType: string;
    frequencyValue: number;
    token: string;
};

export type SaveBulkTransferTemplateBody = {
    templateName: string;
    templateFile: File;
};
