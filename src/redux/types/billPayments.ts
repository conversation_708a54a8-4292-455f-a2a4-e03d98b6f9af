export interface BillCategory {
    id?: number;
    categoryName?: string;
    description?: string;
}

export interface Biller {
    billerName?: string;
    billerId?: number;
    billerCode?: string;
    categoryId?: number;
    categoryName?: string;
    categoryDescription?: string;
    shortName?: string;
    isActive?: boolean;
    paymentItem?: PaymentItem;
}

export interface BillCategoriesResponse {
    totalPages: number;
    size: number;
    totalElements: number;
    hasNext: boolean;
    hasPrevious: boolean;
    content: BillCategory[];
}

export interface BillCategoriesState {
    content: BillCategory[];
    loading: boolean;
    error?: string;
    totalPages: number;
    size: number;
    totalElements: number;
    hasNext: boolean;
    hasPrevious: boolean;
}

export interface PaymentItem {
    categoryId: string;
    billerId: string;
    paymentItemId: string;
    paymentItemName: string;
    amount: string;
    code: string;
    paymentCode: string;
    amountFixed: boolean;
}

export interface PaymentItemsResponse {
    totalPages: number;
    size: number;
    totalElements: number;
    hasNext: boolean;
    hasPrevious: boolean;
    content: PaymentItem[];
}

export interface PaymentItemsState {
    data: PaymentItem[];
    loading: boolean;
    error: string | null;
    totalPages?: number;
    size?: number;
    totalElements?: number;
    hasNext?: boolean;
    hasPrevious?: boolean;
}

/**
 * Interface for recent bill payment API response
 * The content is now a grouped object where:
 * - Keys containing only numbers represent bulk payments
 * - Keys containing letters (alphanumeric) represent single payments
 */
export interface RecentBillsResponse {
    totalPages: number;
    size: number;
    totalElements: number;
    hasNext: boolean;
    hasPrevious: boolean;
    content: Record<string, RecentBillApiItem[]> | RecentBillApiItem[];
}

/**
 * Interface for a single recent bill payment item from API
 */
export interface RecentBillApiItem {
    id: number;
    version?: number;
    createdAt: string | null;
    lastModifiedAt?: string | null;
    createdBy?: string | null;
    lastModifiedBy?: string | null;
    paymentCode: string;
    customerId: string | null;
    customerMobile: string | null;
    customerEmail: string | null;
    amount: string;
    requestReference: string;
    status: string;
    accountNumber: string;
    requiresApproval: boolean;
    corporateId: number;
    bulkPaymentAdviceDetails: unknown | null;
    narration: string | null;
    biller: string | null;
    billType: string | null;
}

export interface BillerState {
    data: Biller[] | null;
    loading: boolean;
    error: string | null;
}

export interface CustomerValidationRequest {
    customers: Array<{
        customerId: string;
        paymentCode: string;
    }>;
}

export interface CustomerValidationResponse {
    customers: Array<{
        paymentCode: string;
        customerId: string;
        responseCode: string;
        fullName: string;
        amount: string;
        amountType: string;
        amountTypeDescription: string;
    }>;
}

export interface CustomerValidationState {
    data: CustomerValidationResponse | null;
    loading: boolean;
    error: string | null;
}
