import { setSignupProgress, SignupStep } from "@/lib/session-utils";
import {
    fetchTeamMemberData,
    registerUser,
    resendOtp,
    sendOTP,
    setSecurityQuestions,
    setupAuthenticator,
    updatePreferredMFA,
    validateOtp,
} from "@/redux/actions/auth/signupActions";
import { ISignupState } from "@/redux/types/auth/signup";
import { getSliceError } from "@/redux/utils";
import { createSlice, type PayloadAction } from "@reduxjs/toolkit";

// Initial state
const initialState: ISignupState = {
    personalInfo: null,
    businessInfo: null,
    directorInfo: null,
    personalAndBusinessInfo: null,
    teamMemberInfo: null,
    mfaInfo: null,
    currentStep: 1,
    isComplete: false,
    userSignupOption: null,
    corporateId: null,
    userId: null,
    loading: false,
    error: null,
    success: false,
    teamMember: null,
    sendOTP: {
        error: null,
        loading: false,
        success: false,
    },
    validateOTP: {
        error: null,
        loading: false,
        success: false,
    },
    resendOTP: {
        error: null,
        loading: false,
        success: false,
    },
    setSecurityQuestions: {
        error: null,
        loading: false,
        success: false,
    },
    setupAuthenticator: {
        error: null,
        loading: false,
        success: false,
    },
    updatePreferredMFA: {
        error: null,
        loading: false,
        success: false,
    },
    teamMemberData: {
        error: null,
        loading: false,
        success: false,
    },
};

// Create the slice
const signupSlice = createSlice({
    name: "signup",
    initialState,
    reducers: {
        // Save personal information
        savePersonalInfo: (state, action: PayloadAction<ISignupState["personalInfo"]>) => {
            state.personalInfo = action.payload;
            state.currentStep = 2;
            updateSignupProgress(SignupStep.VERIFY_PERSONAL_EMAIL);
        },

        // Save business information
        saveBusinessInfo: (state, action: PayloadAction<ISignupState["businessInfo"]>) => {
            state.businessInfo = action.payload;
            state.currentStep = 5;
            updateSignupProgress(SignupStep.VALIDATE_BUSINESS);
        },

        // Save director information
        saveDirectorInfo: (state, action: PayloadAction<ISignupState["directorInfo"]>) => {
            state.directorInfo = action.payload;
            state.currentStep = 6;
            updateSignupProgress(SignupStep.VERIFY_BUSINESS_EMAIL);
        },

        savePersonalAndBusinessInfo: (state, action: PayloadAction<ISignupState["personalAndBusinessInfo"]>) => {
            state.personalAndBusinessInfo = action.payload;
            // state.currentStep = 3;

            if (state.personalAndBusinessInfo?.emailAddress === state.personalAndBusinessInfo?.corporateEmailAddress) {
                state.currentStep = 4;
            } else {
                state.currentStep = 3;
            }

            updateSignupProgress(state.currentStep);
        },

        saveTeamMemberInfo: (state, action: PayloadAction<ISignupState["teamMemberInfo"]>) => {
            state.teamMemberInfo = action.payload;
            state.currentStep = 3;
            updateSignupProgress(SignupStep.VERIFY_PHONE_NUMBER);
        },

        // Save MFA information
        saveMfaInfo: (state, action: PayloadAction<ISignupState["mfaInfo"]>) => {
            state.mfaInfo = action.payload;
        },

        // Reset the form (for when user abandons signup or completes it)
        resetSignupForm: () => initialState,

        // Go to a specific step (for navigation)
        setCurrentStep: (state, action: PayloadAction<number>) => {
            state.currentStep = action.payload;
            updateSignupProgress(action.payload);
        },

        setUserState: (state, action: PayloadAction<"existing" | "new" | "team-invite">) => {
            state.userSignupOption = action.payload;

            if (action.payload === "existing") {
                state.personalAndBusinessInfo = null;
                state.teamMemberInfo = null;
            }

            if (action.payload === "new") {
                state.personalInfo = null;
                state.businessInfo = null;
                state.directorInfo = null;
                state.teamMemberInfo = null;
            }

            if (action.payload === "team-invite") {
                state.personalInfo = null;
                state.businessInfo = null;
                state.directorInfo = null;
                state.personalAndBusinessInfo = null;
            }
        },

        // Clear error and loading state for a specific action
        clearOTPState: (state, action: PayloadAction<"sendOTP" | "validateOTP" | "resendOTP">) => {
            state[action.payload] = {
                error: null,
                loading: false,
                success: false,
            };
        },

        // Clear error and loading state for a register user
        clearRegisterUserState: (state) => {
            state.loading = false;
            state.error = null;
            state.success = false;
        },

        clearState: (
            state,
            action: PayloadAction<
                | "sendOTP"
                | "validateOTP"
                | "resendOTP"
                | "setSecurityQuestions"
                | "setupAuthenticator"
                | "updatePreferredMFA"
            >
        ) => {
            state[action.payload] = {
                error: null,
                loading: false,
                success: false,
            };
        },
    },
    extraReducers: (builder) => {
        builder
            // Register user
            .addCase(registerUser.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(registerUser.fulfilled, (state, action) => {
                state.loading = false;
                state.success = true;
                state.isComplete = true;
                state.corporateId = action.payload.corporateId || action.payload.id;
                state.userId = action.payload.userId || action.payload.id;
            })
            .addCase(registerUser.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload as string;
            })

            // Send OTP
            .addCase(sendOTP.pending, (state) => {
                state.sendOTP.loading = true;
            })
            .addCase(sendOTP.fulfilled, (state) => {
                state.sendOTP.loading = false;
                state.sendOTP.success = true;
                state.sendOTP.error = null;
            })
            .addCase(sendOTP.rejected, (state, action) => {
                state.sendOTP.error = action.payload as string;
                state.sendOTP.success = false;
                state.sendOTP.loading = false;
            })

            // Validate OTP
            .addCase(validateOtp.pending, (state) => {
                state.validateOTP.loading = true;
            })
            .addCase(validateOtp.fulfilled, (state) => {
                state.validateOTP.loading = false;
                state.validateOTP.success = true;
                state.validateOTP.error = null;
            })
            .addCase(validateOtp.rejected, (state, action) => {
                state.validateOTP.error = action.payload as string;
                state.validateOTP.success = false;
                state.validateOTP.loading = false;
            })

            // Resend OTP
            .addCase(resendOtp.pending, (state) => {
                state.resendOTP.loading = true;
            })
            .addCase(resendOtp.fulfilled, (state) => {
                state.resendOTP.loading = false;
                state.resendOTP.success = true;
                state.resendOTP.error = null;
            })
            .addCase(resendOtp.rejected, (state, action) => {
                state.resendOTP.error = action.payload as string;
                state.resendOTP.success = false;
                state.resendOTP.loading = false;
            })

            // Set security question
            .addCase(setSecurityQuestions.pending, (state) => {
                state.setSecurityQuestions.loading = true;
            })
            .addCase(setSecurityQuestions.fulfilled, (state) => {
                state.setSecurityQuestions.loading = false;
                state.setSecurityQuestions.success = true;
                state.setSecurityQuestions.error = null;
            })
            .addCase(setSecurityQuestions.rejected, (state, action) => {
                state.setSecurityQuestions.error = action.payload as string;
                state.setSecurityQuestions.success = false;
                state.setSecurityQuestions.loading = false;
            })

            // Setup Authenticator
            .addCase(setupAuthenticator.pending, (state) => {
                state.setupAuthenticator.loading = true;
            })
            .addCase(setupAuthenticator.fulfilled, (state) => {
                state.setupAuthenticator.loading = false;
                state.setupAuthenticator.success = true;
                state.setupAuthenticator.error = null;
            })
            .addCase(setupAuthenticator.rejected, (state, action) => {
                state.setupAuthenticator.error = action.payload as string;
                state.setupAuthenticator.success = false;
                state.setupAuthenticator.loading = false;
            })

            // Update Preferred MFA
            .addCase(updatePreferredMFA.pending, (state) => {
                state.updatePreferredMFA.loading = true;
            })
            .addCase(updatePreferredMFA.fulfilled, (state, action) => {
                state.updatePreferredMFA.loading = false;
                state.updatePreferredMFA.success = true;
                state.updatePreferredMFA.error = null;
                state.corporateId = action.payload.corporateId;
            })
            .addCase(updatePreferredMFA.rejected, (state, action) => {
                state.updatePreferredMFA.error = getSliceError(action);
                state.updatePreferredMFA.success = false;
                state.updatePreferredMFA.loading = false;
            })

            // Fetch team member data
            .addCase(fetchTeamMemberData.pending, (state) => {
                state.teamMemberData.loading = true;
            })
            .addCase(fetchTeamMemberData.fulfilled, (state, action) => {
                state.teamMemberData.loading = false;
                state.teamMemberData.success = true;
                state.teamMemberData.error = null;
                state.teamMember = action.payload;
            })
            .addCase(fetchTeamMemberData.rejected, (state, action) => {
                state.teamMemberData.error = action.payload as string;
                state.teamMemberData.success = false;
                state.teamMemberData.loading = false;
            });
    },
});

// Export actions
export const {
    savePersonalInfo,
    saveBusinessInfo,
    saveDirectorInfo,
    savePersonalAndBusinessInfo,
    saveTeamMemberInfo,
    saveMfaInfo,
    resetSignupForm,
    setCurrentStep,
    setUserState,
    clearOTPState,
    clearRegisterUserState,
    clearState,
} = signupSlice.actions;

// Export reducer
export default signupSlice.reducer;

/**
 * Updates the signup progress in the cookie
 * @param step The current signup step
 */
const updateSignupProgress = (step: SignupStep) => {
    if (typeof window !== "undefined") {
        try {
            console.log(`Updating signup progress to step: ${step}`);

            // Validate the step value
            if (typeof step !== "number" || step < 1 || step > 8) {
                console.warn(`Invalid signup step value: ${step}, defaulting to PERSONAL_INFO`);
                step = SignupStep.PERSONAL_INFO;
            }

            // Set the cookie
            setSignupProgress(step);

            // Also set a localStorage backup for debugging
            localStorage.setItem("debug_signup_step", step.toString());
        } catch (error) {
            console.error("Error updating signup progress:", error);
        }
    }
};
