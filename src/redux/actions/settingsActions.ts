/**
 * Settings module API actions
 *
 * Contains thunks for fetching and managing user settings data.
 * Uses the userAxios instance for all API calls to the user service.
 */
import { createAsyncThunk } from "@reduxjs/toolkit";
import { userAxios } from "@/api/axios";
import { RootState } from "..";
import { sendCatchFeedback, sendFeedback } from "@/functions/feedback";
import { getSessionDetails } from "@/functions/userSession";
import { capitalizeUserName } from "@/functions/stringManipulations";
import {
    BusinessInfo,
    UserPersonalInfo,
    UploadBusinessLogoResponse,
    DeleteBusinessLogoResponse,
} from "../types/settings";

/**
 * Fetches the personal information of the logged-in user
 *
 * @remarks
 * This thunk uses the userAxios instance to make a GET request to /v1/corporate-user
 * No query parameters are needed as the API identifies the user through authentication
 * Error feedback is not shown for this background data loading operation
 * as per API Integration Guideline #4.
 *
 * @returns The user's personal information data
 */
export const fetchPersonalInfo = createAsyncThunk<UserPersonalInfo, void, { state: RootState; rejectValue: string }>(
    "settings/fetchPersonalInfo",
    async (_, { getState, rejectWithValue }) => {
        try {
            // Using userAxios for user-related API calls
            const response = await userAxios.get("/v1/corporate-user");

            return response.data;
        } catch (error: unknown) {
            // Don't show error feedback for background data loading
            return rejectWithValue("Failed to fetch personal information");
        }
    }
);

/**
 * Interface for personal information update request
 *
 * Only includes fields that can be updated by the user
 * Follows the API requirement to use empty strings instead of null/undefined
 * Note: Role field is not included as the API doesn't support updating it
 */
interface UpdatePersonalInfoRequest {
    username: string;
    phoneNumber?: string;
    dateOfBirth?: string;
    preferredName?: string;
    address?:
        | {
              number: string;
              street: string;
              city: string;
              state: string;
              country: string;
          }
        | Record<string, never>;
}

/**
 * Helper function to build the address object for updatePersonalInfo
 * @param data - The input data containing address fields
 * @returns The formatted address object or undefined if no address fields are provided
 */
const buildAddressObject = (
    data: Partial<{
        address?: string | object;
        number?: string;
        street?: string;
        city?: string;
        state?: string;
        country?: string;
    }>
):
    | {
          number: string;
          street: string;
          city: string;
          state: string;
          country: string;
      }
    | undefined => {
    if (
        !("address" in data) &&
        !("number" in data) &&
        !("street" in data) &&
        !("city" in data) &&
        !("state" in data) &&
        !("country" in data)
    ) {
        return undefined;
    }

    const address = {
        number: "",
        street: "",
        city: "",
        state: "",
        country: "",
    };

    if ("address" in data && data.address) {
        if (typeof data.address === "object") {
            const addressObj = data.address as Record<string, string>;
            address.number = addressObj.number || "";
            address.street = addressObj.street || "";
            address.city = addressObj.city || "";
            address.state = addressObj.state || "";
            address.country = addressObj.country || "";
        } else {
            address.street = data.address;
        }
    }

    if ("number" in data) address.number = data.number || "";
    if ("street" in data) address.street = data.street || "";
    if ("city" in data) address.city = data.city || "";
    if ("state" in data) address.state = data.state || "";
    if ("country" in data) address.country = data.country || "";

    return address;
};

/**
 * Updates the personal information of the logged-in user
 *
 * @remarks
 * This thunk uses the userAxios instance to make a PATCH request to /v1/corporate-user
 * It follows the API integration guidelines by:
 * - Using the userAxios instance from api/axios.ts
 * - Including the required username field (user's email) in the request body
 * - Using sendCatchFeedback for user-initiated action errors
 *
 * @param data - Object containing the fields to update (can include address fields directly)
 * @returns The updated user's personal information
 */
export const updatePersonalInfo = createAsyncThunk<
    UserPersonalInfo,
    Partial<{
        phoneNumber?: string;
        dateOfBirth?: string;
        address?: string | object;
        number?: string;
        street?: string;
        city?: string;
        state?: string;
        country?: string;
        preferredName?: string;
        // Note: 'role' field has been removed as the API doesn't support updating it
    }>,
    { state: RootState; rejectValue: string }
>("settings/updatePersonalInfo", async (data, { getState, rejectWithValue }) => {
    try {
        const state = getState();
        // Use optional chaining to safely access nested properties
        const email = state.settings?.personalInfo?.data?.email;

        if (!email) {
            return rejectWithValue("User email not found");
        }

        // Create the request body with the required username field
        const requestBody: UpdatePersonalInfoRequest = {
            username: email, // Required field as per API guidelines
        };

        // Handle individual fields with null checks
        if ("phoneNumber" in data) requestBody.phoneNumber = data.phoneNumber || "";
        if ("dateOfBirth" in data) requestBody.dateOfBirth = data.dateOfBirth || "";
        if ("preferredName" in data) requestBody.preferredName = capitalizeUserName(data.preferredName || "");
        // Note: 'role' field is not processed as the API doesn't support updating it
        // The UI has this field disabled to prevent user editing

        // If the user is updating an address field, use the address object from buildAddressObject
        if (
            "number" in data ||
            "street" in data ||
            "city" in data ||
            "state" in data ||
            "country" in data ||
            ("address" in data && data.address && Object.keys(data.address).length > 0)
        ) {
            const addressObject = buildAddressObject(data);
            if (addressObject) {
                requestBody.address = addressObject;
            }
        }
        // If address is explicitly provided as an empty object, use it as is
        else if (
            "address" in data &&
            (data.address === undefined ||
                data.address === null ||
                (typeof data.address === "object" && Object.keys(data.address).length === 0))
        ) {
            requestBody.address = {}; // Empty object with no fields
        }

        // Make the API call with only the request body
        const response = await userAxios.patch("/v1/corporate-user", requestBody);

        return response.data;
    } catch (error: unknown) {
        // This is a user-initiated action, so show error feedback
        sendCatchFeedback(error);
        return rejectWithValue((error as Error)?.message || "Failed to update personal information");
    }
});

/**
 * Updates the business information
 *
 * @remarks
 * This thunk uses the userAxios instance to make a PUT request to /v1/self-services/corporates
 * No path parameters are needed as the API identifies the user through authentication
 * It follows the API integration guidelines by:
 * - Using the userAxios instance from api/axios.ts
 * - Passing empty strings instead of null/undefined values
 * - Using sendCatchFeedback for user-initiated action errors
 *
 * @param data - Object containing the fields to update
 * @returns The updated business information
 */
export const updateBusinessInfo = createAsyncThunk<
    BusinessInfo,
    Partial<{
        tradingName?: string;
        businessEmail?: string;
        phoneNumber?: string;
        number?: string;
        street?: string;
        city?: string;
        state?: string;
        country?: string;
    }>,
    { state: RootState; rejectValue: string }
>("settings/updateBusinessInfo", async (data, { getState, rejectWithValue }) => {
    try {
        const state = getState();

        // Create the request body with empty strings instead of null/undefined
        const requestBody: {
            tradingName: string;
            businessEmail: string;
            phoneNumber: string;
            address?: {
                number: string;
                street: string;
                city: string;
                state: string;
                country: string;
            };
        } = {
            tradingName: data.tradingName ?? state.settings?.businessInfo?.data?.tradingName ?? "",
            businessEmail: data.businessEmail ?? state.settings?.businessInfo?.data?.email ?? "",
            phoneNumber: data.phoneNumber ?? state.settings?.businessInfo?.data?.phoneNumber ?? "",
        };

        // Add address object if any address component is provided
        if (
            data.number !== undefined ||
            data.street !== undefined ||
            data.city !== undefined ||
            data.state !== undefined ||
            data.country !== undefined
        ) {
            requestBody.address = {
                number: data.number ?? "",
                street: data.street ?? "",
                city: data.city ?? "",
                state: data.state ?? "",
                country: data.country ?? "",
            };
        }

        // Following API Integration Guideline #2: use empty strings instead of null/undefined
        // If no address fields are provided but we need to preserve existing address
        if (
            !data.number &&
            !data.street &&
            !data.city &&
            !data.state &&
            !data.country &&
            state.settings?.businessInfo?.data?.address
        ) {
            requestBody.address = {
                number: state.settings?.businessInfo?.data?.address?.number || "",
                street: state.settings?.businessInfo?.data?.address?.street || "",
                city: state.settings?.businessInfo?.data?.address?.city || "",
                state: state.settings?.businessInfo?.data?.address?.state || "",
                country: state.settings?.businessInfo?.data?.address?.country || "",
            };
        }

        // Use the userAxios instance from api/axios.ts (API Integration Guideline #1 & #2)
        const response = await userAxios.put("/v1/self-services/corporates", requestBody);

        return response.data;
    } catch (error: unknown) {
        // This is a user-initiated action, so show error feedback
        sendCatchFeedback(error);
        return rejectWithValue((error as Error)?.message || "Failed to update business information");
    }
});

/**
 * Fetches the business information of the logged-in user's company
 *
 * @remarks
 * This thunk uses the userAxios instance to make a GET request to /v1/corporate
 * No query parameters or path IDs are needed as the API identifies the user through authentication
 * Error feedback is not shown for this background data loading operation
 *
 * @returns The business information data
 */
export const fetchBusinessInfo = createAsyncThunk<BusinessInfo, void, { state: RootState; rejectValue: string }>(
    "settings/fetchBusinessInfo",
    async (_, { getState, rejectWithValue }) => {
        try {
            // Using userAxios for user-related API calls (API Integration Guideline #1)
            const response = await userAxios.get("/v1/corporate");

            // Get the response data
            const data = response.data;

            // Ensure the tradingName field is set (use empty string if null or undefined)
            data.tradingName = data.tradingName ?? "";

            // Process the address field to ensure consistent format
            if (data.address) {
                // Handle string format (legacy data)
                if (typeof data.address === "string") {
                    data.address = {
                        street: data.address,
                        city: "",
                        state: "",
                        country: "",
                    };
                }
                // Handle object format but ensure all required fields exist
                else if (typeof data.address === "object") {
                    // Use empty strings instead of null/undefined (API Integration Guideline #2)
                    data.address = {
                        street: data.address.street ?? "",
                        city: data.address.city ?? "",
                        state: data.address.state ?? "",
                        country: data.address.country ?? "",
                        // Keep number if it exists in the response
                        ...(data.address.number !== undefined && { number: data.address.number ?? "" }),
                    };
                }
            } else {
                // Initialize with empty object if address is null/undefined
                data.address = {
                    number: "", // Always include number field in empty address
                    street: "",
                    city: "",
                    state: "",
                    country: "",
                };
            }

            // Get state for logo URL handling if needed
            const state = getState();

            // If the API indicates a logo exists but doesn't provide URL, construct one
            if (data.hasLogo || data.logoId) {
                const corporateId = state.corporate.corporateId;
                data.logoUrl = `/api/v1/corporate/${corporateId}/logo?t=${new Date().getTime()}`;
            }
            // Otherwise, use the stored logo URL from state if available
            else {
                const storedLogoUrl = state.settings?.businessInfo?.data?.logoUrl;
                if (storedLogoUrl) {
                    data.logoUrl = storedLogoUrl;
                }
            }

            return data;
        } catch (error: unknown) {
            // Don't show error feedback for background data loading (API Integration Guideline #4)
            return rejectWithValue("Failed to fetch business information");
        }
    }
);

/**
 * Uploads a business logo for the company
 *
 * @remarks
 * This thunk uses the userAxios instance to make a POST request to /v1/corporate/upload
 * No path parameters are needed as the API identifies the user through authentication
 * It sends the file as a FormData object with a 'logo' query parameter
 * Uses sendCatchFeedback for user-initiated action errors
 * Uses sendFeedback for successful operations
 *
 * @param file - The logo file to upload
 * @returns The response containing a success message and possibly the logo URL
 */
export const uploadBusinessLogo = createAsyncThunk<
    UploadBusinessLogoResponse,
    File,
    { state: RootState; rejectValue: string }
>("settings/uploadBusinessLogo", async (file, { getState, rejectWithValue, dispatch }) => {
    try {
        // Validate file type
        const validTypes = ["image/png", "image/jpeg", "image/jpg"];
        if (!validTypes.includes(file.type)) {
            const errorMessage = "Invalid file type. Please upload a PNG or JPG file.";
            sendCatchFeedback(new Error(errorMessage));
            return rejectWithValue(errorMessage);
        }

        // Validate file size (10MB max)
        const maxSize = 10 * 1024 * 1024; // 10MB in bytes
        if (file.size > maxSize) {
            const errorMessage = "File size exceeds 10MB limit. Please upload a smaller file.";
            sendCatchFeedback(new Error(errorMessage));
            return rejectWithValue(errorMessage);
        }

        // Create FormData object to send the file
        const formData = new FormData();
        formData.append("logo", file);

        // Convert file to data URL for local storage
        const reader = new FileReader();
        const logoUrlPromise = new Promise<string>((resolve) => {
            reader.onload = () => {
                resolve(reader.result as string);
            };
            reader.readAsDataURL(file);
        });

        // Get the data URL before making the API call
        const logoUrl = await logoUrlPromise;

        // Make the API call
        const response = await userAxios.post("/v1/corporate/upload", formData, {
            headers: {
                "Content-Type": "multipart/form-data",
            },
        });

        // Get the response data and log it for debugging
        const data = response.data;

        // Success feedback to user
        sendFeedback("Business logo uploaded successfully", "success", undefined, "Success");

        return {
            message: data.message ?? "Logo uploaded successfully",
            logoUrl: logoUrl, // Use the data URL for the logo
        };
    } catch (error: unknown) {
        // This is a user-initiated action, so show error feedback
        sendCatchFeedback(error);
        return rejectWithValue((error as Error)?.message || "Failed to upload business logo");
    }
});

/**
 * Deletes the business logo for the company
 *
 * @remarks
 * This thunk uses the userAxios instance to make a POST request to /v1/corporate/delete
 * No path parameters are needed as the API identifies the user through authentication
 * Uses sendCatchFeedback for user-initiated action errors
 * Uses sendFeedback for successful operations
 *
 * @returns The response containing a success message
 */
export const deleteBusinessLogo = createAsyncThunk<
    DeleteBusinessLogoResponse,
    void,
    { state: RootState; rejectValue: string }
>("settings/deleteBusinessLogo", async (_, { getState, rejectWithValue }) => {
    try {
        // Make the API call
        const response = await userAxios.post("/v1/corporate/delete");

        // Success feedback to user
        sendFeedback("Business logo removed successfully", "success", undefined, "Success");

        return {
            message: response.data.message ?? "Logo deleted successfully",
            logoUrl: null, // Clear the logo URL
        };
    } catch (error: unknown) {
        // This is a user-initiated action, so show error feedback
        sendCatchFeedback(error);
        return rejectWithValue((error as Error)?.message || "Failed to delete business logo");
    }
});

/**
 * Fetches the business logo for the company
 *
 * @remarks
 * This thunk uses the userAxios instance to make a GET request to /v1/corporate/download
 * No path parameters are needed as the API identifies the user through authentication
 * It follows the API integration guidelines by:
 * - Using the userAxios instance from api/axios.ts
 * - Handling binary response for image data
 * - Not showing error feedback for background data loading
 *
 * @returns The logo data as a base64 URL
 */
export const fetchBusinessLogo = createAsyncThunk<{ logoUrl: string }, void, { state: RootState; rejectValue: string }>(
    "settings/fetchBusinessLogo",
    async (_, { getState, rejectWithValue }) => {
        try {
            // Using userAxios for the request as per API guidelines
            const response = await userAxios.get("/v1/corporate/download", {
                responseType: "arraybuffer",
            });

            // Convert binary data to base64 URL for image display
            const base64String = btoa(
                new Uint8Array(response.data).reduce((data, byte) => data + String.fromCharCode(byte), "")
            );
            const logoUrl = `data:image/png;base64,${base64String}`;

            return { logoUrl };
        } catch {
            // Don't show error feedback for background data loading
            return rejectWithValue("Failed to fetch business logo");
        }
    }
);

/**
 * Changes the user's password using direct password validation
 *
 * @remarks
 * This thunk uses the userAxios instance to make a POST request to /v1/credentials?command=change-password
 * It follows the API integration guidelines by:
 * - Using the userAxios instance from api/axios.ts
 * - Getting the user's email from Redux state with multiple fallbacks for reliability
 * - Using sendCatchFeedback for user-initiated action failures
 * - Using sendFeedback for successful operations
 *
 * @param data - Object containing the new password and current password for validation
 * @returns A success message or error
 */
export const changePassword = createAsyncThunk<
    { status: string; message: string },
    { newPassword: string; oldPassword: string },
    { state: RootState; rejectValue: string }
>("settings/changePassword", async (data, { getState, rejectWithValue }) => {
    try {
        const state = getState();

        // Get user email from Redux state with multiple fallbacks for reliability
        // Priority order: user slice -> signin slice -> settings slice -> session storage
        const email =
            state.user?.user?.email || // From user slice (most reliable)
            state.signin?.user?.email || // From signin slice
            state.settings?.personalInfo?.data?.email || // From settings personal info
            getSessionDetails()?.email; // Fallback to session storage

        if (!email) {
            const errorMessage = "User email not found. Please log in again.";
            sendCatchFeedback(new Error(errorMessage));
            return rejectWithValue(errorMessage);
        }

        const response = await userAxios.post("/v1/credentials?command=change-password", {
            email: email,
            newPassword: data.newPassword,
            oldPassword: data.oldPassword,
        });

        // Show success feedback for any successful response
        // Use the API message if available, otherwise use a default message
        const successMessage = response.data.message || "Password changed successfully";
        sendFeedback(successMessage, "success", undefined, "Success");

        return response.data;
    } catch (error: unknown) {
        sendCatchFeedback(error);
        return rejectWithValue((error as Error)?.message || "Failed to change password");
    }
});

/**
 * Changes the user's password with MFA token
 */
export const changePasswordWithToken = createAsyncThunk<
    { status: string; message: string },
    { newPassword: string; oldPassword: string; token: string },
    { state: RootState; rejectValue: string }
>("settings/changePasswordWithToken", async (data, { getState, rejectWithValue }) => {
    try {
        const state = getState();

        // Get user email from Redux state with multiple fallbacks for reliability
        const email =
            state.user?.user?.email ||
            state.signin?.user?.email ||
            state.settings?.personalInfo?.data?.email ||
            getSessionDetails()?.email;

        if (!email) {
            const errorMessage = "User email not found. Please log in again.";
            sendCatchFeedback(new Error(errorMessage));
            return rejectWithValue(errorMessage);
        }

        // Add token as query parameter
        const url = `/v1/credentials?command=change-password&token=${encodeURIComponent(data.token)}`;
        const response = await userAxios.post(url, {
            email: email,
            newPassword: data.newPassword,
            oldPassword: data.oldPassword,
        });

        const successMessage = response.data.message || "Password changed successfully";
        sendFeedback(successMessage, "success", undefined, "Success");

        return response.data;
    } catch (error: unknown) {
        sendCatchFeedback(error);
        return rejectWithValue((error as Error)?.message || "Failed to change password");
    }
});

/**
 * Updates the personal information of the logged-in user with MFA token
 *
 * @remarks
 * This thunk uses the userAxios instance to make a PATCH request to /v1/corporate-user
 * It follows the API integration guidelines by:
 * - Using the userAxios instance from api/axios.ts
 * - Including the required username field (user's email) in the request body
 * - Adding the MFA token as a query parameter
 * - Using sendCatchFeedback for user-initiated action errors
 *
 * @param data - Object containing the fields to update and the MFA token
 * @returns The updated user's personal information
 */
export const updatePersonalInfoWithToken = createAsyncThunk<
    UserPersonalInfo,
    Partial<{
        phoneNumber?: string;
        dateOfBirth?: string;
        address?: string | object;
        number?: string;
        street?: string;
        city?: string;
        state?: string;
        country?: string;
        preferredName?: string;
        token: string;
    }>,
    { state: RootState; rejectValue: string }
>("settings/updatePersonalInfoWithToken", async (data, { getState, rejectWithValue }) => {
    try {
        const state = getState();
        // Use optional chaining to safely access nested properties
        const email = state.settings?.personalInfo?.data?.email;

        if (!email) {
            return rejectWithValue("User email not found");
        }

        // Extract token from data
        const { token, ...updateData } = data;

        if (!token) {
            return rejectWithValue("MFA token is required");
        }

        // Create the request body with the required username field
        const requestBody: UpdatePersonalInfoRequest = {
            username: email, // Required field as per API guidelines
        };

        // Handle individual fields with null checks
        if ("phoneNumber" in updateData) requestBody.phoneNumber = updateData.phoneNumber || "";
        if ("dateOfBirth" in updateData) requestBody.dateOfBirth = updateData.dateOfBirth || "";
        if ("preferredName" in updateData)
            requestBody.preferredName = capitalizeUserName(updateData.preferredName || "");

        // If the user is updating an address field, use the address object from buildAddressObject
        if (
            "number" in updateData ||
            "street" in updateData ||
            "city" in updateData ||
            "state" in updateData ||
            "country" in updateData ||
            ("address" in updateData && updateData.address && Object.keys(updateData.address).length > 0)
        ) {
            const addressObject = buildAddressObject(updateData);
            if (addressObject) {
                requestBody.address = addressObject;
            }
        }
        // If address is explicitly provided as an empty object, use it as is
        else if (
            "address" in updateData &&
            (updateData.address === undefined ||
                updateData.address === null ||
                (typeof updateData.address === "object" && Object.keys(updateData.address).length === 0))
        ) {
            requestBody.address = {}; // Empty object with no fields
        }

        // Add token as query parameter
        const url = `/v1/corporate-user?token=${encodeURIComponent(token)}`;

        // Make the API call with the request body and token in URL
        const response = await userAxios.patch(url, requestBody);

        return response.data;
    } catch (error: unknown) {
        // This is a user-initiated action, so show error feedback
        sendCatchFeedback(error);
        return rejectWithValue((error as Error)?.message || "Failed to update personal information");
    }
});

/**
 * Updates the business information with MFA token
 *
 * @remarks
 * This thunk uses the userAxios instance to make a PUT request to /v1/self-services/corporates
 * It follows the API integration guidelines by:
 * - Using the userAxios instance from api/axios.ts
 * - Passing empty strings instead of null/undefined values
 * - Adding the MFA token as a query parameter
 * - Using sendCatchFeedback for user-initiated action errors
 *
 * @param data - Object containing the fields to update and the MFA token
 * @returns The updated business information
 */
export const updateBusinessInfoWithToken = createAsyncThunk<
    BusinessInfo,
    Partial<{
        tradingName?: string;
        businessEmail?: string;
        phoneNumber?: string;
        number?: string;
        street?: string;
        city?: string;
        state?: string;
        country?: string;
        token: string;
    }>,
    { state: RootState; rejectValue: string }
>("settings/updateBusinessInfoWithToken", async (data, { getState, rejectWithValue }) => {
    try {
        const state = getState();

        // Extract token from data
        const { token, ...updateData } = data;

        if (!token) {
            return rejectWithValue("MFA token is required");
        }

        // Create the request body with empty strings instead of null/undefined
        const requestBody: {
            tradingName: string;
            businessEmail: string;
            phoneNumber: string;
            address?: {
                number: string;
                street: string;
                city: string;
                state: string;
                country: string;
            };
        } = {
            tradingName: updateData.tradingName ?? state.settings?.businessInfo?.data?.tradingName ?? "",
            businessEmail: updateData.businessEmail ?? state.settings?.businessInfo?.data?.email ?? "",
            phoneNumber: updateData.phoneNumber ?? state.settings?.businessInfo?.data?.phoneNumber ?? "",
        };

        // Add address object if any address component is provided
        if (
            updateData.number !== undefined ||
            updateData.street !== undefined ||
            updateData.city !== undefined ||
            updateData.state !== undefined ||
            updateData.country !== undefined
        ) {
            requestBody.address = {
                number: updateData.number ?? "",
                street: updateData.street ?? "",
                city: updateData.city ?? "",
                state: updateData.state ?? "",
                country: updateData.country ?? "",
            };
        }

        // Following API Integration Guideline #2: use empty strings instead of null/undefined
        // If no address fields are provided but we need to preserve existing address
        if (
            !updateData.number &&
            !updateData.street &&
            !updateData.city &&
            !updateData.state &&
            !updateData.country &&
            state.settings?.businessInfo?.data?.address
        ) {
            requestBody.address = {
                number: state.settings?.businessInfo?.data?.address?.number || "",
                street: state.settings?.businessInfo?.data?.address?.street || "",
                city: state.settings?.businessInfo?.data?.address?.city || "",
                state: state.settings?.businessInfo?.data?.address?.state || "",
                country: state.settings?.businessInfo?.data?.address?.country || "",
            };
        }

        // Add token as query parameter
        const url = `/v1/self-services/corporates?token=${encodeURIComponent(token)}`;

        // Use the userAxios instance from api/axios.ts (API Integration Guideline #1 & #2)
        const response = await userAxios.put(url, requestBody);

        return response.data;
    } catch (error: unknown) {
        // This is a user-initiated action, so show error feedback
        sendCatchFeedback(error);
        return rejectWithValue((error as Error)?.message || "Failed to update business information");
    }
});
