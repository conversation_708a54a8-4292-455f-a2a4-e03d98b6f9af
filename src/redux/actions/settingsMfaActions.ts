import { createAsyncThunk } from "@reduxjs/toolkit";
import { userAxios } from "@/api/axios";
import { sendCatchFeedback } from "@/functions/feedback";
import {
    ValidateSecurityQuestionRequest,
    ValidateAuthenticatorRequest,
    ValidateSmsOtpRequest,
    MfaValidationResponse,
} from "../types/settingsMfa";

/**
 * Validate security question for settings MFA
 * Returns a token that can be used for settings operations
 */
export const validateSettingsSecurityQuestion = createAsyncThunk<
    MfaValidationResponse,
    ValidateSecurityQuestionRequest,
    { rejectValue: string }
>("settingsMfa/validateSecurityQuestion", async (body, { rejectWithValue }) => {
    try {
        // Normalize answer to lowercase for case-insensitive validation
        const normalizedBody = {
            ...body,
            answer: body.answer.trim().toLowerCase(),
        };
        const response = await userAxios.post("/v1/user-security-questions/validate", normalizedBody);
        return response.data;
    } catch (error) {
        sendCatchFeedback(error);
        return rejectWithValue((error as Error)?.message || "Failed to validate security question");
    }
});

/**
 * Validate authenticator app OTP for settings MFA
 * Returns a token that can be used for settings operations
 */
export const validateSettingsAuthenticator = createAsyncThunk<
    MfaValidationResponse,
    ValidateAuthenticatorRequest,
    { rejectValue: string }
>("settingsMfa/validateAuthenticator", async (body, { rejectWithValue }) => {
    try {
        const response = await userAxios.post("/v1/mfa?command=verify-otp&verificationType=mfa-internal", body);
        return response.data;
    } catch (error) {
        sendCatchFeedback(error);
        return rejectWithValue((error as Error)?.message || "Failed to validate authenticator");
    }
});

/**
 * Validate SMS OTP for settings MFA
 * Returns a token that can be used for settings operations
 */
export const validateSettingsSmsOtp = createAsyncThunk<
    MfaValidationResponse,
    ValidateSmsOtpRequest,
    { rejectValue: string }
>("settingsMfa/validateSmsOtp", async (body, { rejectWithValue }) => {
    try {
        const response = await userAxios.post("/v1/otp-manager/validate", body);
        return response.data;
    } catch (error) {
        sendCatchFeedback(error);
        return rejectWithValue((error as Error)?.message || "Failed to validate SMS OTP");
    }
});

/**
 * Clear MFA verification state
 */
export const clearSettingsMfaState = createAsyncThunk<
    void,
    "securityQuestion" | "authenticator" | "smsOtp" | "all",
    { rejectValue: never }
>("settingsMfa/clearState", async () => {
    // This is handled in the slice reducers
    return;
});
