import { Middleware } from "@reduxjs/toolkit";
import { resetBillCategories } from "../slices/billPaymentSlice";
import { clearBillCategoriesCache } from "../actions/billPaymentThunks";

/**
 * Middleware that listens for user logout actions and clears bill payment categories
 * from both Redux state and session storage cache.
 * This ensures categories are refetched fresh on next login.
 */
const billPaymentClearMiddleware: Middleware = (store) => (next) => (action) => {
    const result = next(action);

    // Listen for logout action from user slice
    if (action.type === "user/signOut") {
        // Clear bill payment categories from Redux state
        store.dispatch(resetBillCategories());

        // Clear bill payment categories from session storage cache
        // Wrap in try-catch to prevent errors from breaking logout flow
        try {
            clearBillCategoriesCache();
        } catch {
            // Silently handle error to prevent breaking logout flow
        }
    }

    return result;
};

export default billPaymentClearMiddleware;
