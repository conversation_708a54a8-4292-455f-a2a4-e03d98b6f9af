/**
 * Centralized Permissions Service
 *
 * This service manages the fetching, caching, and synchronization of all available
 * permissions from the backend API. It replaces the static permissions file with
 * a dynamic system that stays in sync with the backend.
 *
 * Now integrated with Redux for centralized state management.
 */

import { userAxios } from "@/api/axios";
import type { Permission } from "@/utils/server-permission-check";
import type { AppDispatch, RootState } from "@/redux/index";
import {
    initializePermissions,
    clearPermissions,
} from "@/redux/actions/permissionsActions";

// Types for the permissions service
export interface PermissionModule {
    [key: string]: string;
}

export interface AllPermissions {
    [moduleName: string]: PermissionModule;
}

export interface PermissionsServiceState {
    allPermissions: AllPermissions;
    permissionsList: Permission[];
    isLoaded: boolean;
    isLoading: boolean;
    lastFetched: number | null;
    error: string | null;
}

// Cache configuration
const PERMISSIONS_CACHE_KEY = "system_permissions_cache";
const PERMISSIONS_CACHE_TIMESTAMP_KEY = "system_permissions_timestamp";
const FALLBACK_CACHE_KEY = "system_permissions_fallback";
const CACHE_DURATION = 30 * 60 * 1000; // 30 minutes
const BACKGROUND_SYNC_INTERVAL = 5 * 60 * 1000; // 5 minutes
const FALLBACK_CACHE_DURATION = 7 * 24 * 60 * 60 * 1000; // 7 days
const MAX_RETRY_ATTEMPTS = 3;
const RETRY_DELAY_BASE = 1000; // 1 second

class PermissionsService {
    private state: PermissionsServiceState = {
        allPermissions: {},
        permissionsList: [],
        isLoaded: false,
        isLoading: false,
        lastFetched: null,
        error: null,
    };

    private listeners: Set<(state: PermissionsServiceState) => void> = new Set();
    private backgroundSyncTimer: NodeJS.Timeout | null = null;
    private retryCount: number = 0;

    // Redux integration
    private dispatch: AppDispatch | null = null;
    private getReduxState: (() => RootState) | null = null;

    /**
     * Initialize Redux integration
     */
    setReduxIntegration(dispatch: AppDispatch, getState: () => RootState): void {
        this.dispatch = dispatch;
        this.getReduxState = getState;
    }

    /**
     * Subscribe to permissions state changes
     */
    subscribe(listener: (state: PermissionsServiceState) => void): () => void {
        this.listeners.add(listener);
        return () => this.listeners.delete(listener);
    }

    /**
     * Get current permissions state (legacy compatibility)
     */
    getState(): PermissionsServiceState {
        // If Redux is available, get state from Redux
        if (this.getReduxState && this.dispatch) {
            const reduxState = this.getReduxState();
            const permissionsState = reduxState.permissions;

            return {
                allPermissions: this.transformPermissionsToModules(permissionsState.systemPermissions),
                permissionsList: permissionsState.systemPermissions,
                isLoaded: permissionsState.isReady,
                isLoading: permissionsState.systemPermissionsLoading || permissionsState.userPermissionsLoading,
                lastFetched: permissionsState.systemPermissionsLastFetched,
                error: permissionsState.systemPermissionsError || permissionsState.userPermissionsError,
            };
        }

        // Fallback to local state
        return { ...this.state };
    }

    /**
     * Initialize permissions using Redux
     */
    async initializeWithRedux(forceRefresh = false): Promise<void> {
        if (!this.dispatch) {
            throw new Error("Redux integration not initialized. Call setReduxIntegration first.");
        }

        try {
            await this.dispatch(initializePermissions({ forceRefresh })).unwrap();
        } catch (error) {
            console.error("Failed to initialize permissions via Redux:", error);
            throw error;
        }
    }

    /**
     * Clear permissions on logout
     */
    async clearPermissionsOnLogout(): Promise<void> {
        if (this.dispatch) {
            await this.dispatch(clearPermissions());
        }

        // Clear local state as well
        this.setState({
            allPermissions: {},
            permissionsList: [],
            isLoaded: false,
            isLoading: false,
            lastFetched: null,
            error: null,
        });

        this.stopBackgroundSync();
    }

    /**
     * Initialize the permissions service
     */
    async initialize(): Promise<void> {
        if (this.state.isLoading) {
            return;
        }

        // Try to load from cache first
        const cachedData = this.loadFromCache();
        if (cachedData && !this.isCacheExpired(cachedData.timestamp)) {
            this.setState({
                allPermissions: cachedData.permissions,
                permissionsList: cachedData.permissionsList,
                isLoaded: true,
                lastFetched: cachedData.timestamp,
                error: null,
            });

            // Start background sync for fresh data
            this.startBackgroundSync();
            return;
        }

        // Fetch fresh data
        await this.fetchPermissions();
    }

    /**
     * Fetch permissions from the API with retry logic and fallback mechanisms
     */
    async fetchPermissions(isBackgroundFetch = false, retryAttempt = 0): Promise<void> {
        if (!isBackgroundFetch) {
            this.setState({ isLoading: true, error: null });
            this.retryCount = 0;
        }

        try {
            const response = await userAxios.get("/v1/permissions");
            const permissionsList: Permission[] = response.data;

            if (!Array.isArray(permissionsList)) {
                throw new Error("Invalid permissions response format");
            }

            // Transform permissions into organized modules
            const allPermissions = this.transformPermissionsToModules(permissionsList);

            // Update state
            const newState: Partial<PermissionsServiceState> = {
                allPermissions,
                permissionsList,
                isLoaded: true,
                lastFetched: Date.now(),
                error: null,
            };

            if (!isBackgroundFetch) {
                newState.isLoading = false;
            }

            this.setState(newState);

            // Cache the data
            this.saveToCache(allPermissions, permissionsList);

            // Reset retry count on success
            this.retryCount = 0;

            // Start background sync if not already running
            if (!isBackgroundFetch) {
                this.startBackgroundSync();
            }
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : "Failed to fetch permissions";

            // Implement retry logic for network errors
            if (retryAttempt < MAX_RETRY_ATTEMPTS && this.isNetworkError(error)) {
                this.retryCount = retryAttempt + 1;

                console.warn(`PermissionsService: Retry attempt ${this.retryCount}/${MAX_RETRY_ATTEMPTS}`, error);

                // Exponential backoff delay
                const delay = RETRY_DELAY_BASE * Math.pow(2, retryAttempt);

                setTimeout(() => {
                    this.fetchPermissions(isBackgroundFetch, retryAttempt + 1);
                }, delay);

                return;
            }

            // All retries exhausted or non-network error

            if (!isBackgroundFetch) {
                this.setState({
                    isLoading: false,
                    error: errorMessage,
                });

                // Try fallback mechanisms in order of preference
                this.tryFallbackMechanisms();
            }

            console.error("PermissionsService: Failed to fetch permissions after retries", error);
        }
    }

    /**
     * Try fallback mechanisms in order of preference
     */
    private tryFallbackMechanisms(): void {
        // 1. Try regular cache first
        const cachedData = this.loadFromCache();
        if (cachedData) {
            this.setState({
                allPermissions: cachedData.permissions,
                permissionsList: cachedData.permissionsList,
                isLoaded: true,
                lastFetched: cachedData.timestamp,
            });
            return;
        }

        // 2. Try fallback cache (longer duration)
        const fallbackData = this.loadFromFallbackCache();
        if (fallbackData) {
            this.setState({
                allPermissions: fallbackData.permissions,
                permissionsList: fallbackData.permissionsList,
                isLoaded: true,
                lastFetched: fallbackData.timestamp,
            });
            return;
        }

        // 3. Use minimal default permissions for critical functionality
        console.warn("PermissionsService: No cache available, using minimal default permissions");
        this.useMinimalDefaults();
    }

    /**
     * Use minimal default permissions when all else fails
     */
    private useMinimalDefaults(): void {
        const minimalPermissions = {
            GENERAL_PERMISSIONS: {
                VIEW_DASHBOARD: "View dashboard",
                ACCESS_SETTINGS: "Access settings",
            },
        };

        const minimalPermissionsList: Permission[] = [
            {
                id: 1,
                name: "View dashboard",
                appModule: "General",
                endpoint: "/dashboard",
                method: "GET",
                createdDate: null,
                createdBy: null,
                lastModifiedDate: null,
                lastModifiedBy: null,
            },
            {
                id: 2,
                name: "Access settings",
                appModule: "General",
                endpoint: "/settings",
                method: "GET",
                createdDate: null,
                createdBy: null,
                lastModifiedDate: null,
                lastModifiedBy: null,
            },
        ];

        this.setState({
            allPermissions: minimalPermissions,
            permissionsList: minimalPermissionsList,
            isLoaded: true,
            lastFetched: Date.now(),
            error: "Using minimal permissions due to API failure",
        });
    }

    /**
     * Check if error is a network error that should trigger retry
     */
    private isNetworkError(error: unknown): boolean {
        if (!error || typeof error !== "object") return false;

        type NetworkErrorLike = {
            message?: string;
            code?: string;
            name?: string;
            response?: { status?: number };
        };
        const err = error as NetworkErrorLike;

        // Check for common network error indicators
        const networkErrorCodes = ["NETWORK_ERROR", "TIMEOUT", "ECONNREFUSED", "ENOTFOUND"];
        const errorMessage = err.message?.toLowerCase() ?? "";
        const errorCode = err.code?.toUpperCase() ?? "";

        return (
            networkErrorCodes.includes(errorCode) ||
            errorMessage.includes("network") ||
            errorMessage.includes("timeout") ||
            errorMessage.includes("connection") ||
            err.name === "NetworkError" ||
            Boolean(err.response && err.response.status !== undefined && err.response.status >= 500)
        );
    }

    /**
     * Transform API permissions into organized modules
     */
    private transformPermissionsToModules(permissionsList: Permission[]): AllPermissions {
        const modules: AllPermissions = {};

        // Group permissions by appModule
        const moduleGroups = permissionsList.reduce(
            (acc, permission) => {
                const moduleName = permission.appModule || "General";
                if (!acc[moduleName]) {
                    acc[moduleName] = [];
                }
                acc[moduleName].push(permission);
                return acc;
            },
            {} as Record<string, Permission[]>
        );

        // Convert to the expected format
        Object.entries(moduleGroups).forEach(([moduleName, permissions]) => {
            const moduleKey = this.normalizeModuleName(moduleName);
            modules[moduleKey] = {};

            permissions.forEach((permission) => {
                const constantKey = this.generateConstantKey(permission.name);
                modules[moduleKey][constantKey] = permission.name;
            });
        });

        return modules;
    }

    /**
     * Normalize module name for use as object key
     */
    private normalizeModuleName(moduleName: string): string {
        return (
            moduleName
                .toUpperCase()
                .replace(/[^A-Z0-9]/g, "_")
                .replace(/_+/g, "_")
                .replace(/(^_|_$)/g, "") + "_PERMISSIONS"
        );
    }

    /**
     * Generate constant key from permission name
     */
    private generateConstantKey(permissionName: string): string {
        return permissionName
            .toUpperCase()
            .replace(/[^A-Z0-9]/g, "_")
            .replace(/_+/g, "_")
            .replace(/(^_|_$)/g, "");
    }

    /**
     * Save permissions to cache
     */
    private saveToCache(permissions: AllPermissions, permissionsList: Permission[]): void {
        try {
            const cacheData = {
                permissions,
                permissionsList,
                timestamp: Date.now(),
            };
            localStorage.setItem(PERMISSIONS_CACHE_KEY, JSON.stringify(cacheData));
            localStorage.setItem(PERMISSIONS_CACHE_TIMESTAMP_KEY, Date.now().toString());

            // Also save to fallback cache (longer duration)
            this.saveToFallbackCache(permissions, permissionsList);
        } catch (error) {
            console.error("PermissionsService: Failed to cache permissions", error);
        }
    }

    /**
     * Save permissions to long-term fallback cache
     */
    private saveToFallbackCache(permissions: AllPermissions, permissionsList: Permission[]): void {
        try {
            const fallbackData = {
                permissions,
                permissionsList,
                timestamp: Date.now(),
            };
            localStorage.setItem(FALLBACK_CACHE_KEY, JSON.stringify(fallbackData));
        } catch (error) {
            console.error("PermissionsService: Failed to save fallback cache", error);
        }
    }

    /**
     * Load permissions from cache
     */
    private loadFromCache(): { permissions: AllPermissions; permissionsList: Permission[]; timestamp: number } | null {
        try {
            const cached = localStorage.getItem(PERMISSIONS_CACHE_KEY);
            const timestamp = localStorage.getItem(PERMISSIONS_CACHE_TIMESTAMP_KEY);

            if (cached && timestamp) {
                const data = JSON.parse(cached);
                return {
                    permissions: data.permissions,
                    permissionsList: data.permissionsList,
                    timestamp: parseInt(timestamp, 10),
                };
            }
        } catch (error) {
            console.error("PermissionsService: Failed to load cached permissions", error);
        }
        return null;
    }

    /**
     * Load permissions from fallback cache
     */
    private loadFromFallbackCache(): {
        permissions: AllPermissions;
        permissionsList: Permission[];
        timestamp: number;
    } | null {
        try {
            const cached = localStorage.getItem(FALLBACK_CACHE_KEY);

            if (cached) {
                const data = JSON.parse(cached);
                const timestamp = data.timestamp;

                // Check if fallback cache is not too old
                if (Date.now() - timestamp < FALLBACK_CACHE_DURATION) {
                    return {
                        permissions: data.permissions,
                        permissionsList: data.permissionsList,
                        timestamp,
                    };
                }
            }
        } catch (error) {
            console.error("PermissionsService: Failed to load fallback cache", error);
        }
        return null;
    }

    /**
     * Check if cache is expired
     */
    private isCacheExpired(timestamp: number): boolean {
        return Date.now() - timestamp > CACHE_DURATION;
    }

    /**
     * Start background synchronization
     */
    private startBackgroundSync(): void {
        if (this.backgroundSyncTimer) {
            return;
        }

        this.backgroundSyncTimer = setInterval(() => {
            this.fetchPermissions(true);
        }, BACKGROUND_SYNC_INTERVAL);
    }

    /**
     * Stop background synchronization and cleanup
     */
    stopBackgroundSync(): void {
        if (this.backgroundSyncTimer) {
            clearInterval(this.backgroundSyncTimer);
            this.backgroundSyncTimer = null;
        }
    }

    /**
     * Cleanup all listeners and timers
     */
    cleanup(): void {
        this.stopBackgroundSync();
        this.listeners.clear();

        this.setState({
            allPermissions: {},
            permissionsList: [],
            isLoaded: false,
            lastFetched: null,
            error: null,
        });
    }

    /**
     * Force refresh of permissions
     */
    async refresh(): Promise<void> {
        await this.fetchPermissions(false);
    }

    /**
     * Update state and notify listeners
     */
    private setState(updates: Partial<PermissionsServiceState>): void {
        this.state = { ...this.state, ...updates };
        this.listeners.forEach((listener) => listener(this.state));
    }

    /**
     * Get permissions for a specific module
     */
    getModulePermissions(moduleName: string): PermissionModule | null {
        return this.state.allPermissions[moduleName] || null;
    }

    /**
     * Get all permissions as a flat list
     */
    getAllPermissionsList(): Permission[] {
        return [...this.state.permissionsList];
    }

    /**
     * Check if a permission exists
     */
    hasPermission(permissionName: string): boolean {
        return this.state.permissionsList.some((p) => p.name === permissionName);
    }

    /**
     * Clear cache and reset state
     */
    clearCache(): void {
        try {
            localStorage.removeItem(PERMISSIONS_CACHE_KEY);
            localStorage.removeItem(PERMISSIONS_CACHE_TIMESTAMP_KEY);
            localStorage.removeItem(FALLBACK_CACHE_KEY);
        } catch (error) {
            console.error("PermissionsService: Failed to clear cache", error);
        }

        this.setState({
            allPermissions: {},
            permissionsList: [],
            isLoaded: false,
            lastFetched: null,
            error: null,
        });
    }
}

// Create singleton instance
export const permissionsService = new PermissionsService();

/**
 * Redux-integrated permissions service wrapper
 */
export class ReduxPermissionsService {
    private static instance: ReduxPermissionsService | null = null;
    private service: PermissionsService;
    private isInitialized = false;

    private constructor() {
        this.service = permissionsService;
    }

    static getInstance(): ReduxPermissionsService {
        // if (!ReduxPermissionsService.instance) {
        //     ReduxPermissionsService.instance = new ReduxPermissionsService();
        // }
        
        return ReduxPermissionsService.instance;
    }

    /**
     * Initialize with Redux store
     */
    initialize(dispatch: AppDispatch, getState: () => RootState): void {
        if (this.isInitialized) return;

        this.service.setReduxIntegration(dispatch, getState);
        this.isInitialized = true;
    }

    /**
     * Initialize permissions system
     */
    async initializePermissions(forceRefresh = false): Promise<void> {
        if (!this.isInitialized) {
            throw new Error("ReduxPermissionsService not initialized. Call initialize() first.");
        }

        return this.service.initializeWithRedux(forceRefresh);
    }

    /**
     * Clear permissions on logout
     */
    async clearPermissions(): Promise<void> {
        return this.service.clearPermissionsOnLogout();
    }

    /**
     * Get legacy service for backward compatibility
     */
    getLegacyService(): PermissionsService {
        return this.service;
    }
}

// Export types and service
export default permissionsService;
