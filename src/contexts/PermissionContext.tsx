"use client";

import React, {
    createContext,
    useContext,
    useEffect,
    useState,
    type ReactNode,
    useMemo,
    useCallback,
    useRef,
} from "react";
import { useAppSelector, useAppDispatch } from "@/redux/hooks";
import { cookies } from "@/lib/cookies";
import { ReduxPermissionsService } from "@/services/permissionsService";
import { jwtDecode } from "jwt-decode";
import {
    selectUserPermissions,
    selectSystemPermissions,
    selectIsPermissionsReady,
    selectIsLoading,
    selectHasErrors,
    selectPermissionSystemStatus,
    selectHasPermission,
    selectHasAnyPermission,
    selectHasAllPermissions,
    selectIsUsingCache,
} from "@/redux/selectors/permissionSelectors";
import { initializePermissions, clearPermissions } from "@/redux/actions/permissionsActions";
import { store } from "@/redux/index";
import { Permission } from "@/redux/types/permissions";

// Type definitions - using Redux Permission type instead of local definition

// Constants for cleanup (legacy compatibility)
const PERMISSIONS_CACHE_KEY = "cached_user_permissions";
const PERMISSIONS_CACHE_TIMESTAMP_KEY = "cached_permissions_timestamp";
const CLEANUP_EXPIRY_TIME = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds
const LAST_CLEANUP_KEY = "permissions_last_cleanup";

// Define types for our permission context
type PermissionContextType = {
    userPermissions: string[];
    isLoadingPermissions: boolean;
    isUsingCachedPermissions: boolean;
    permissionFetchFailed: boolean;
    isPermissionSystemReady: boolean;
    hasPermission: (permission: string) => boolean;
    hasAnyPermission: (permissions: string[]) => boolean;
    hasAllPermissions: (permissions: string[]) => boolean;
    refetchPermissions: () => Promise<void>;
    // New properties for dynamic permissions - using Redux Permission type
    systemPermissions: Permission[];
    isLoadingSystemPermissions: boolean;
    systemPermissionsError: string | null;
    refreshSystemPermissions: () => Promise<void>;
};

// Create the context with a default value
const PermissionContext = createContext<PermissionContextType | undefined>(undefined);

// Custom hook to use the permission context
export const usePermissions = (): PermissionContextType => {
    const context = useContext(PermissionContext);

    if (!context) {
        throw new Error("usePermissions must be used within a PermissionProvider");
    }
    return context;
};

type PermissionProviderProps = {
    children: ReactNode;
};

// Permission provider component
export const PermissionProvider: React.FC<PermissionProviderProps> = ({ children }) => {
    const dispatch = useAppDispatch();
    const { user } = useAppSelector((state) => state.user);

    // Redux selectors for permissions state
    const userPermissions = useAppSelector(selectUserPermissions);
    const systemPermissions = useAppSelector(selectSystemPermissions);
    const isLoadingPermissions = useAppSelector(selectIsLoading);
    const isPermissionSystemReady = useAppSelector(selectIsPermissionsReady);
    const isUsingCachedPermissions = useAppSelector(selectIsUsingCache);
    const permissionSystemStatus = useAppSelector(selectPermissionSystemStatus);
    const hasErrors = useAppSelector(selectHasErrors);

    // Permission checking functions from selectors
    const hasPermissionSelector = useAppSelector(selectHasPermission);
    const hasAnyPermissionSelector = useAppSelector(selectHasAnyPermission);
    const hasAllPermissionsSelector = useAppSelector(selectHasAllPermissions);

    // Legacy state for backward compatibility
    const [permissionFetchFailed, setPermissionFetchFailed] = useState(false);

    // Note: Caching and retry logic is now handled by Redux

    // Function to get all permission-related cache keys from localStorage
    const getAllPermissionCacheKeys = useCallback(() => {
        const keys: string[] = [];

        try {
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && (key.startsWith(PERMISSIONS_CACHE_KEY) || key.startsWith(PERMISSIONS_CACHE_TIMESTAMP_KEY))) {
                    keys.push(key);
                }
            }
        } catch (error) {
            console.error("Error getting cache keys:", error);
        }

        return keys;
    }, []); // useCallback to avoid infinite loop

    // Function to clean up expired permission caches
    const cleanupExpiredCaches = useCallback(() => {
        try {
            const now = Date.now();
            const allCacheKeys = getAllPermissionCacheKeys();
            let cleanedCount = 0;

            // Group keys by user (timestamp and permission keys come in pairs)
            const userCaches = new Map<string, { permissionKey: string; timestampKey: string }>();

            for (const key of allCacheKeys) {
                if (key.startsWith(PERMISSIONS_CACHE_TIMESTAMP_KEY)) {
                    // Extract userId from timestamp key
                    const userId = key.replace(`${PERMISSIONS_CACHE_TIMESTAMP_KEY}_`, "");
                    const permissionKey = `${PERMISSIONS_CACHE_KEY}_${userId}`;

                    if (allCacheKeys.includes(permissionKey)) {
                        userCaches.set(userId, { permissionKey, timestampKey: key });
                    }
                }
            }

            // Check each user's cache timestamp
            for (const [userId, { permissionKey, timestampKey }] of userCaches) {
                try {
                    const timestampStr = localStorage.getItem(timestampKey);
                    if (timestampStr) {
                        const timestamp = Number.parseInt(timestampStr, 10);
                        const isExpired = now - timestamp > CLEANUP_EXPIRY_TIME;

                        if (isExpired) {
                            localStorage.removeItem(permissionKey);
                            localStorage.removeItem(timestampKey);
                            cleanedCount++;
                        }
                    }
                } catch (error) {
                    console.error(`Error processing cache for user ${userId}:`, error); // eslint-disable-line no-console
                    // If there's an error reading the timestamp, remove both keys to be safe
                    localStorage.removeItem(permissionKey);
                    localStorage.removeItem(timestampKey);
                    cleanedCount++;
                }
            }

            // Update last cleanup timestamp
            localStorage.setItem(LAST_CLEANUP_KEY, now.toString());

            if (cleanedCount > 0) {
                console.log(`Permission cache cleanup completed. Removed ${cleanedCount} expired cache(s).`); // eslint-disable-line no-console
            }

            return cleanedCount;
        } catch (error) {
            console.error("Error during permission cache cleanup:", error); // eslint-disable-line no-console
            return 0;
        }
    }, [getAllPermissionCacheKeys]); // useCallback to avoid infinite loop

    // Function to check if cleanup is needed and run it
    const checkAndRunCleanup = useCallback(() => {
        try {
            const lastCleanupStr = localStorage.getItem(LAST_CLEANUP_KEY);
            const now = Date.now();

            if (!lastCleanupStr) {
                // First time running, do cleanup and set timestamp
                cleanupExpiredCaches();
                return;
            }

            const lastCleanup = Number.parseInt(lastCleanupStr, 10);
            const timeSinceLastCleanup = now - lastCleanup;

            // Run cleanup if it's been more than 24 hours since last cleanup
            // This ensures we check regularly but not too frequently
            if (timeSinceLastCleanup > 24 * 60 * 60 * 1000) {
                cleanupExpiredCaches();
            }
        } catch (error) {
            console.error("Error checking cleanup schedule:", error); // eslint-disable-line no-console
        }
    }, [cleanupExpiredCaches]); // useCallback to avoid infinite loop

    // Initialize automatic cleanup
    const initializeAutomaticCleanup = useCallback(() => {
        // Run cleanup check on initialization
        checkAndRunCleanup();

        // Set up periodic cleanup check (every hour while app is active)
        const cleanupInterval = setInterval(
            () => {
                checkAndRunCleanup();
            },
            60 * 60 * 1000
        ); // 1 hour

        // Clear interval when page unloads
        const handleBeforeUnload = () => {
            clearInterval(cleanupInterval);
        };

        window.addEventListener("beforeunload", handleBeforeUnload);

        // Return cleanup function for manual cleanup if needed
        return () => {
            clearInterval(cleanupInterval);
            window.removeEventListener("beforeunload", handleBeforeUnload);
        };
    }, [checkAndRunCleanup]); // useCallback to avoid infinite loop

    // Initialize automatic cleanup on component mount
    useEffect(() => {
        const cleanup = initializeAutomaticCleanup();

        // Return cleanup function to be called on component unmount
        return cleanup;
    }, [initializeAutomaticCleanup]);

    // Note: Permission fetching is now handled by Redux actions

    // Initialize Redux permissions service
    useEffect(() => {
        // Initialize the Redux permissions service
        const reduxService = ReduxPermissionsService.getInstance();
        reduxService.initialize(dispatch, () => store.getState());
    }, [dispatch]);

    // Early permission loading when we have token but no user yet
    const tryEarlyPermissionLoad = useCallback(async () => {
        try {
            // Use Redux to initialize permissions early
            await dispatch(initializePermissions({})).unwrap();
        } catch (error) {
            console.error("Error in early permission load:", error);
        }
    }, [dispatch]);

    // Track if permissions have been initialized to prevent refetching
    const permissionsInitializedRef = useRef(false);
    const currentUserIdRef = useRef<string | null>(null);

    // Initialize permissions when user is authenticated
    useEffect(() => {
        const token = cookies.getToken();
        const isAuthenticated = cookies.isAuthenticated();
        // Get user ID from token instead of user object
        const getUserIdFromToken = (): string | null => {
            try {
                const token = cookies.getToken();
                if (!token) return null;
                const decoded = jwtDecode(token) as { userId?: number };
                return decoded?.userId?.toString() || null;
            } catch {
                return null;
            }
        };
        const currentUserId = getUserIdFromToken();

        if (user && isAuthenticated && token) {
            // Only initialize if we haven't done so for this user, or if the user has changed
            const shouldInitialize = !permissionsInitializedRef.current || currentUserIdRef.current !== currentUserId;

            if (shouldInitialize) {
                console.log("Initializing permissions for user:", currentUserId);

                // Initialize permissions via Redux
                dispatch(initializePermissions({ forceRefresh: false }))
                    .unwrap()
                    .then((result) => {
                        setPermissionFetchFailed(false);
                        permissionsInitializedRef.current = true;
                        currentUserIdRef.current = currentUserId;

                        // Log successful initialization with role info
                        if (result.userPermissions && result.userPermissions.length > 0) {
                            console.log(
                                `Permissions initialized successfully: ${result.userPermissions.length} permissions loaded`
                            );
                        }
                    })
                    .catch((error) => {
                        console.error("Failed to initialize permissions:", error);

                        // Check if this is a Super Admin role issue
                        if (error.message?.includes("Role with identifier 0")) {
                            console.warn(
                                "Super Admin role (ID: 0) detected but API call failed. This should be handled automatically."
                            );
                        }

                        setPermissionFetchFailed(true);
                        // Fallback to legacy method if Redux fails
                        tryEarlyPermissionLoad();
                    });
            } else {
                console.log("Permissions already initialized for user:", currentUserId, "- skipping refetch");
            }
        } else if (!isAuthenticated || !token) {
            // Clear permissions when not authenticated
            dispatch(clearPermissions());
            setPermissionFetchFailed(false);
            permissionsInitializedRef.current = false;
            currentUserIdRef.current = null;
        }
    }, [user, dispatch, tryEarlyPermissionLoad]);

    // Permission checking functions using Redux selectors
    const hasPermission = useCallback(
        (permission: string): boolean => hasPermissionSelector(permission),
        [hasPermissionSelector]
    );

    const hasAnyPermission = useCallback(
        (permissions: string[]): boolean => hasAnyPermissionSelector(permissions),
        [hasAnyPermissionSelector]
    );

    const hasAllPermissions = useCallback(
        (permissions: string[]): boolean => hasAllPermissionsSelector(permissions),
        [hasAllPermissionsSelector]
    );

    // System permissions functions
    const refreshSystemPermissions = useCallback(async () => {
        try {
            await dispatch(initializePermissions({ forceRefresh: true })).unwrap();
        } catch (error) {
            console.error("Failed to refresh system permissions:", error);
        }
    }, [dispatch]);

    // Refetch permissions function
    const refetchPermissions = useCallback(async () => {
        try {
            await dispatch(initializePermissions({ forceRefresh: true })).unwrap();
            setPermissionFetchFailed(false);
        } catch (error) {
            console.error("Failed to refetch permissions:", error);
            setPermissionFetchFailed(true);
            // No fallback needed, Redux handles everything
        }
    }, [dispatch]);

    // Provide the context value
    const value = useMemo(
        () => ({
            userPermissions,
            isLoadingPermissions,
            isUsingCachedPermissions,
            permissionFetchFailed: permissionFetchFailed || hasErrors,
            isPermissionSystemReady,
            hasPermission,
            hasAnyPermission,
            hasAllPermissions,
            refetchPermissions,
            // System permissions
            systemPermissions,
            isLoadingSystemPermissions: isLoadingPermissions,
            systemPermissionsError: permissionSystemStatus.errors.systemError,
            refreshSystemPermissions,
        }),
        [
            userPermissions,
            isLoadingPermissions,
            isUsingCachedPermissions,
            permissionFetchFailed,
            hasErrors,
            isPermissionSystemReady,
            hasPermission,
            hasAnyPermission,
            hasAllPermissions,
            refetchPermissions,
            systemPermissions,
            permissionSystemStatus.errors.systemError,
            refreshSystemPermissions,
        ]
    );

    return <PermissionContext.Provider value={value}>{children}</PermissionContext.Provider>;
};
