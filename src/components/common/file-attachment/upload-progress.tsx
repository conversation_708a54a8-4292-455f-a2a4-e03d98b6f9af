const DefaultFileIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
        <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M3.375 5.04167C3.375 4.12119 4.12119 3.375 5.04167 3.375C5.96214 3.375 6.70833 4.12119 6.70833 5.04167C6.70833 5.96214 5.96214 6.70833 5.04167 6.70833C4.12119 6.70833 3.375 5.96214 3.375 5.04167Z"
            fill="#90909D"
        />
        <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M8.93796 0.25H9.06204C10.8754 0.249981 12.321 0.249965 13.4544 0.402353C14.6247 0.559698 15.5862 0.893122 16.3466 1.65345C17.1069 2.41377 17.4403 3.37528 17.5976 4.54559C17.75 5.67904 17.75 7.12466 17.75 8.93798V9.06202C17.75 10.8753 17.75 12.321 17.5976 13.4544C17.4403 14.6247 17.1069 15.5862 16.3466 16.3466C15.5862 17.1069 14.6247 17.4403 13.4544 17.5976C12.321 17.75 10.8753 17.75 9.06202 17.75H8.93798C7.12466 17.75 5.67904 17.75 4.54559 17.5976C3.37528 17.4403 2.41377 17.1069 1.65345 16.3466C0.893122 15.5862 0.559698 14.6247 0.402353 13.4544C0.249965 12.321 0.249981 10.8754 0.25 9.06204V8.93796C0.249981 7.12464 0.249965 5.67904 0.402353 4.54559C0.559698 3.37528 0.893122 2.41377 1.65345 1.65345C2.41377 0.893122 3.37528 0.559698 4.54559 0.402353C5.67904 0.249965 7.12464 0.249981 8.93796 0.25ZM4.76767 2.05416C3.78217 2.18665 3.231 2.43291 2.83196 2.83196C2.43291 3.231 2.18665 3.78217 2.05416 4.76767C1.91844 5.77715 1.91667 7.11047 1.91667 9C1.91667 10.8895 1.91844 12.2229 2.05416 13.2323C2.18665 14.2178 2.43291 14.769 2.83196 15.168C3.231 15.5671 3.78217 15.8133 4.76767 15.9458C5.77715 16.0816 7.11047 16.0833 9 16.0833C10.8895 16.0833 12.2229 16.0816 13.2323 15.9458C14.2178 15.8133 14.769 15.5671 15.168 15.168C15.5671 14.769 15.8133 14.2178 15.9458 13.2323C16.0816 12.2229 16.0833 10.8895 16.0833 9C16.0833 7.11047 16.0816 5.77715 15.9458 4.76767C15.8133 3.78217 15.5671 3.231 15.168 2.83196C14.769 2.43291 14.2178 2.18665 13.2323 2.05416C12.2229 1.91844 10.8895 1.91667 9 1.91667C7.11047 1.91667 5.77715 1.91844 4.76767 2.05416Z"
            fill="#90909D"
        />
        <path
            d="M17.3667 9.58568C15.7442 8.53928 14.1983 8.1008 12.7188 8.16259C11.2462 8.22409 9.92198 8.77774 8.73084 9.57412C6.71545 10.9216 4.94285 13.0716 3.35574 14.9967C3.1045 15.3015 2.85786 15.6006 2.61569 15.89L1.91666 16.5008L2.93582 17.1947C3.65598 17.4931 4.49827 17.6237 5.46623 17.6873C6.43044 17.7507 7.59187 17.7507 8.97293 17.7507H9.06259C10.8759 17.7507 12.3215 17.7507 13.4549 17.5983C14.6253 17.441 15.5868 17.1075 16.3471 16.3472C17.0133 15.681 17.3539 14.8636 17.5335 13.8785C17.7084 12.9189 17.7411 11.7355 17.7484 10.2902L17.7507 9.83331L17.3667 9.58568Z"
            fill="#90909D"
        />
    </svg>
);

const CloseIcon = () => (
    <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g id="x-close">
            <path
                id="Icon"
                d="M10.5 3.5L3.5 10.5M3.5 3.5L10.5 10.5"
                stroke="#90909D"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
        </g>
    </svg>
);

export interface IUploadState {
    fileName: string;
    fileSize: number;
    progress: number;
    timeRemaining?: number;
    isComplete: boolean;
}

interface UploadProgressProps {
    state: IUploadState;
    onCancel: () => void;
    icon?: React.ReactNode;
}

export function UploadProgress({ state, onCancel, icon = <DefaultFileIcon /> }: Readonly<UploadProgressProps>) {
    const { fileName, fileSize, progress, timeRemaining, isComplete } = state;

    const formatFileSize = (size: number) => {
        if (size < 1024 * 1024) {
            return `${(size / 1024).toFixed(1)} KB`;
        } else {
            return `${(size / (1024 * 1024)).toFixed(1)} MB`;
        }
    };

    return (
        <div className="flex items-center gap-2 w-full">
            <div className="w-5 h-5 flex-shrink-0 overflow-hidden">{icon}</div>
            <div className="flex-1 flex flex-col items-start justify-start gap-2 w-full text-left text-sm max-w-[95%]">
                <h3 className="self-stretch relative tracking-[0.02em] leading-[18px] font-medium text-[#151519] truncate overflow-hidden whitespace-nowrap">
                    {fileName}
                </h3>
                <div className="self-stretch flex flex-row items-center justify-between text-[#3a3a41]">
                    <span className="flex-1 relative tracking-[0.02em] leading-[18px]" data-testid="file-size">
                        {formatFileSize(fileSize)}
                    </span>
                    <div className="flex flex-row items-center justify-start gap-2">
                        <span className="relative tracking-[0.02em] leading-[18px]">
                            {!isComplete && `${progress}%・${timeRemaining} sec left`}
                            {isComplete && "Upload complete"}
                        </span>
                        <button
                            onClick={onCancel}
                            className="relative w-3.5 h-3.5 flex items-center justify-center"
                            aria-label="Cancel upload"
                        >
                            <CloseIcon />
                        </button>
                    </div>
                </div>
                <div className="self-stretch flex flex-row items-center justify-start">
                    <div className="flex-1 relative rounded-lg h-2">
                        <div className="absolute inset-0 rounded-[4px] bg-[#f9f9fa]" />
                        <div
                            className="absolute top-0 left-0 rounded-[4px] bg-[#5c068c] h-2 transition-all duration-300 ease-in-out"
                            style={{ width: `${progress}%` }}
                        />
                    </div>
                </div>
            </div>
        </div>
    );
}
