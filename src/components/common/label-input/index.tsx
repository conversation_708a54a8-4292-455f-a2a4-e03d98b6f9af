import { InfoIcon } from "@/components/icons/auth";
import { FormikProps } from "formik";
import { ReactNode, useState } from "react";
import { HidePasswordIcon, LockedIcon, ShowPasswordIcon } from "./password-icons";
import { InformationCircleIcon } from "@heroicons/react/24/outline";

interface Props<T> {
    formik?: FormikProps<T>;
    name: string;
    className?: string;
    hint?: string;
    useFormik?: boolean;
    showError?: boolean;
    error?: string;
    label?: string;
    required?: boolean;
    locked?: boolean;
    customLabel?: ReactNode;
    postAppend?: string;
    /**
     * Whether to mask the input for security (shows dots instead of actual values)
     * Recommended for sensitive inputs like security question answers
     */
    masked?: boolean;
}

const FormikContent = <T,>({
    formik,
    name,
    label,
    required,
    hint,
    locked,
    customLabel,
    inputId,
    passwordShown,
    postAppend,
    showError,
    masked,
    togglePasswordReveal,
    ...rest
}: Props<T> & {
    inputId: string;
    passwordShown: boolean;
    togglePasswordReveal: () => void;
} & React.HTMLProps<HTMLInputElement>) => (
    <>
        {customLabel ||
            (label && (
                <label
                    htmlFor={inputId}
                    className={`text-[#151519] text-xs font-semibold leading-4 tracking-[0.24px] ${
                        formik?.touched[name as keyof T] && formik?.errors[name as keyof T] ? "text-[#D92D20]" : ""
                    }`}
                >
                    {label}
                </label>
            ))}
        <div className="relative">
            <input
                {...rest}
                id={inputId}
                name={name}
                value={formik?.values[name as keyof T] as string}
                onChange={formik?.handleChange}
                onBlur={formik?.handleBlur}
                className={
                    formik?.touched[name as keyof T] && formik?.errors[name as keyof T]
                        ? "inputError"
                        : "border rounded-[8px] focus:border-[1.5px] focus:border-[#5C068C] focus:outline-none disabled:border-[#E3E5E8] disabled:bg-[#F7F7F8] hover:border-[1.5px] hover:border-[#DBDBE1] hover:bg-[#F9F9FA] disabled:hover:border-[#E3E5E8] disabled:hover:bg-[#F7F7F8]"
                }
                type={passwordShown ? "text" : masked ? "password" : rest.type}
            />
            {(rest.type === "password" || masked) && (
                <button
                    type="button"
                    data-testid="password-toggle"
                    className="absolute top-1/2 -translate-y-1/2 right-3 cursor-pointer"
                    onClick={togglePasswordReveal}
                >
                    {passwordShown ? (
                        <HidePasswordIcon data-testid="hide-password-icon" />
                    ) : (
                        <ShowPasswordIcon data-testid="show-password-icon" />
                    )}
                </button>
            )}
            {locked && (
                <div data-testid="locked-icon" className="absolute top-1/2 -translate-y-1/2 right-3">
                    <LockedIcon />
                </div>
            )}
        </div>
        {hint && (
            <div className="flex items-center text-[#3A3A41] gap-[0.25rem] text-[14px] font-medium leading-[18px] tracking-[0.28px]">
                <InformationCircleIcon className="w-5 text-[#90909D]" />
                <p className="text-[14px] font-medium leading-[18px] tracking-[0.28px]">{hint}</p>
            </div>
        )}
        {formik?.touched[name as keyof T] && formik?.errors[name as keyof T] && (
            <div className="flex items-center gap-[0.25rem] text-[#D92D20] text-[14px] font-medium leading-[18px] tracking-[0.28px]">
                <InfoIcon data-testid="info-icon" />
                <span className="text-[14px] font-medium leading-[18px] tracking-[0.28px] text-[#D92D20]">
                    {formik.errors[name as keyof T] as string}
                </span>
            </div>
        )}
        {postAppend && <span className="absolute top-1/2 right-3 text-sm text-[#90909D]">{postAppend}</span>}
    </>
);

const NonFormikContent = <T,>({
    name,
    label,
    required,
    hint,
    showError,
    error,
    customLabel,
    inputId,
    passwordShown,
    masked,
    togglePasswordReveal,
    ...rest
}: Props<T> & {
    inputId: string;
    passwordShown: boolean;
    togglePasswordReveal: () => void;
} & React.HTMLProps<HTMLInputElement>) => (
    <>
        {customLabel ||
            (label && (
                <label
                    htmlFor={inputId}
                    className={`text-[#151519] text-xs font-semibold leading-4 tracking-[0.24px] ${showError ? "text-[#D92D20]" : ""}`}
                >
                    {label}
                </label>
            ))}
        <div className="relative">
            <input
                className="border rounded-[8px] focus:border-[1.5px] focus:border-[#5C068C] focus:outline-none disabled:border-[#E3E5E8] disabled:bg-[#F7F7F8] hover:border-[1.5px] hover:border-[#DBDBE1] hover:bg-[#F9F9FA] disabled:hover:border-[#E3E5E8] disabled:hover:bg-[#F7F7F8]"
                {...rest}
                id={inputId}
                name={name}
                type={passwordShown ? "text" : masked ? "password" : rest.type}
            />
            {(rest.type === "password" || masked) && (
                <button
                    type="button"
                    data-testid="password-toggle"
                    className="absolute bottom-3 right-3 cursor-pointer"
                    onClick={togglePasswordReveal}
                >
                    {passwordShown ? (
                        <HidePasswordIcon data-testid="hide-password-icon" />
                    ) : (
                        <ShowPasswordIcon data-testid="show-password-icon" />
                    )}
                </button>
            )}
        </div>
        {hint && (
            <div className="flex items-center text-[#3A3A41] gap-[0.25rem] text-[14px] font-medium leading-[18px] tracking-[0.28px]">
                <InformationCircleIcon className="w-5 text-[#90909D]" />
                <p className="text-[14px] font-medium leading-[18px] tracking-[0.28px]">{hint}</p>
            </div>
        )}
        {showError && (
            <div className="flex items-center gap-[0.25rem] text-[#D92D20] text-[14px] font-medium leading-[18px] tracking-[0.28px]">
                <InfoIcon data-testid="info-icon" />
                <span className="text-[14px] font-medium leading-[18px] tracking-[0.28px] text-[#D92D20]">{error}</span>
            </div>
        )}
    </>
);

const LabelInput = <T,>({
    formik,
    name,
    className = "",
    useFormik = true,
    ...rest
}: Props<T> & React.HTMLProps<HTMLInputElement>) => {
    const [passwordShown, setPasswordShown] = useState(false);
    const togglePasswordReveal = () => setPasswordShown(!passwordShown);
    const inputId = `input-${name}`;

    return (
        <div className={`inputContainer ${className}`}>
            {useFormik && formik ? (
                <FormikContent
                    formik={formik}
                    name={name}
                    inputId={inputId}
                    passwordShown={passwordShown}
                    togglePasswordReveal={togglePasswordReveal}
                    {...rest}
                />
            ) : (
                <NonFormikContent
                    name={name}
                    inputId={inputId}
                    passwordShown={passwordShown}
                    togglePasswordReveal={togglePasswordReveal}
                    {...rest}
                />
            )}
        </div>
    );
};

export default LabelInput;
