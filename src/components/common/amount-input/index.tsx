import { formatNumber, formatNumberToNaira } from "@/functions/stringManipulations";
import { FormikProps } from "formik";
import React, { useState } from "react";
import LabelInput from "../label-input";

type FormValues = { [key: string]: unknown };

type Props<T extends FormValues> = {
    formik: FormikProps<T>;
    className?: string;
    label: string;
    name: keyof T;
    showCurrency?: boolean;
    disabled?: boolean;
};

const AmountInput = <T extends FormValues>({
    formik,
    className,
    label,
    name,
    showCurrency = true,
    disabled = false,
}: Props<T>) => {
    const [isFocused, setIsFocused] = useState(false);

    const { value } = formik.getFieldProps(name as string);

    const displayValue = () => {
        if (!value) return "";

        if (isFocused) {
            // Don't format when focused; allow raw string (including . or trailing decimals)
            return String(value);
        }

        if (isNaN(Number(value))) return "";
        return showCurrency ? formatNumberToNaira(Number(value), 2) : formatNumber(Number(value), 2);
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (disabled) return;

        let rawValue = e.target.value;

        // Allow digits, commas, and one dot (.)
        rawValue = rawValue.replace(/[^0-9.,]/g, "");

        // Remove commas
        let cleanValue = rawValue.replace(/,/g, "");

        // Allow only one dot
        const parts = cleanValue.split(".");
        if (parts.length > 2) return;

        // Limit decimal to 2 digits
        if (parts[1]?.length > 2) {
            cleanValue = parts[0] + "." + parts[1].slice(0, 2);
        }

        formik.setFieldValue(name as string, cleanValue);
    };

    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
        setIsFocused(false);
        formik.handleBlur(e);

        const val = formik.values[name];

        if (val !== "" && val !== null) {
            const numericVal = Number(val);

            if (!isNaN(numericVal)) {
                const fixed = numericVal.toFixed(2);

                // Clear if value is 0.00
                if (Number(fixed) === 0) {
                    formik.setFieldValue(name as string, "");
                } else {
                    formik.setFieldValue(name as string, fixed);
                }
            }
        }
    };

    return (
        <LabelInput
            name={name as string}
            label={label}
            value={displayValue()}
            onChange={handleChange}
            onBlur={handleBlur}
            onFocus={() => setIsFocused(true)}
            showError={formik.touched[name] && !!formik.errors[name]}
            error={formik.errors[name] as string}
            className={className}
            disabled={disabled}
            data-testid="amount"
        />
    );
};

export default AmountInput;
