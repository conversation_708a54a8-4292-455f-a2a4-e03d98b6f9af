import { Dispatch, HTMLInputType<PERSON>ttribute, SetStateAction } from "react";
import ReactOtpInput from "react-otp-input";
import styles from "./styles.module.css";

interface OTPInputProps {
    value: string;
    setValue: Dispatch<SetStateAction<string>>;
    type?: HTMLInputTypeAttribute;
    /**
     * Whether to mask the OTP input for security (shows dots instead of actual values)
     * Recommended for sensitive 2FA/MFA inputs
     */
    masked?: boolean;
}

const OTPInput = ({
    value,
    setValue,
    type,
    masked = true, // Default to masked for security
}: OTPInputProps) => (
    <ReactOtpInput
        value={value}
        onChange={setValue}
        numInputs={6}
        renderSeparator={(index) => {
            if (index === 2) {
                return <span className="font-bold text-4xl text-[#DBDBE1]">-</span>;
            }
        }}
        // Use password input type when masked is true for security
        inputType={masked ? "password" : "text"}
        containerStyle={styles.container}
        shouldAutoFocus
        data-testid="otp-input"
        renderInput={(props) => (
            <input
                {...props}
                type={masked ? "password" : type || "text"}
                className="!w-[56px] !h-[56px] rounded-lg flex items-center justify-center border-[#DBDBE1] border text-[#90909D] font-semibold text-4xl focus:outline-primary"
            />
        )}
    />
);

export default OTPInput;
