"use client";

import * as React from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { usePagination } from "@/hooks/use-pagination";

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/common/select";

interface PaginationProps {
    totalItems: number;
    onPageChange?: (page: number) => void;
    onItemsPerPageChange?: (count: number) => void;
    className?: string;
    itemsPerPageOptions?: number[];
    initialPage?: number;
    initialItemsPerPage?: number;
    currentItemsPerPage?: number;
    totalPages?: number;
    hasNext?: boolean;
    hasPrevious?: boolean;
    currentPage?: number;
}

export function Pagination({
    totalItems,
    onPageChange,
    onItemsPerPageChange,
    className,
    itemsPerPageOptions = [1, 3, 5, 10, 20, 50],
    initialPage = 1,
    initialItemsPerPage = 10,
    currentItemsPerPage,
    totalPages: apiTotalPages,
    hasNext: apiHasNext,
    hasPrevious: apiHasPrevious,
    currentPage,
}: PaginationProps) {
    // Ensure we always have a valid initialPage value (default to 1)
    const effectiveInitialPage = currentPage ?? initialPage ?? 1;

    const {
        page,
        setPage,
        itemsPerPage,
        setItemsPerPage,
        totalPages: calculatedTotalPages,
        canNextPage: calculatedCanNextPage,
        canPrevPage: calculatedCanPrevPage,
    } = usePagination({
        totalItems,
        initialPage: effectiveInitialPage,
        initialItemsPerPage,
    });

    // Use API values if provided, otherwise use calculated values
    const displayTotalPages = apiTotalPages ?? calculatedTotalPages;
    const canNextPage = apiHasNext ?? calculatedCanNextPage;
    const canPrevPage = apiHasPrevious ?? calculatedCanPrevPage;

    // Always ensure displayPage has a valid value (default to page from hook, which defaults to 1)
    const displayPage = currentPage || page;

    // Use the parent's itemsPerPage state if provided
    const displayItemsPerPage = currentItemsPerPage || itemsPerPage;

    // Generate array of available page numbers for dropdown (1 to totalPages)
    // Ensure we have at least 1 page even if displayTotalPages is 0
    const effectiveTotalPages = Math.max(1, displayTotalPages);
    const displayPageItems = React.useMemo(
        () => Array.from({ length: effectiveTotalPages }, (_, i) => i + 1),
        [effectiveTotalPages]
    );

    // Handle page change
    const handlePageChange = (newPage: string) => {
        const pageNumber = parseInt(newPage, 10);
        // Only update internal state if no external currentPage prop is provided
        if (!currentPage) {
            setPage(pageNumber);
        }
        onPageChange?.(pageNumber);
    };

    // Handle items per page change
    const handleItemsPerPageChange = (newCount: string) => {
        const count = parseInt(newCount, 10);
        // Only update internal state if no external currentItemsPerPage prop is provided
        if (!currentItemsPerPage) {
            setItemsPerPage(count);
        }
        onItemsPerPageChange?.(count);
    };

    // Handle next page click
    const handleNextPage = () => {
        if (canNextPage) {
            const nextPageNumber = displayPage + 1;
            // Only update internal state if no external currentPage prop is provided
            if (!currentPage) {
                setPage(nextPageNumber);
            }
            onPageChange?.(nextPageNumber);
        }
    };

    // Handle previous page click
    const handlePrevPage = () => {
        if (canPrevPage) {
            const prevPageNumber = displayPage - 1;
            // Only update internal state if no external currentPage prop is provided
            if (!currentPage) {
                setPage(prevPageNumber);
            }
            onPageChange?.(prevPageNumber);
        }
    };

    return (
        <div className={`flex items-center justify-between px-2 ${className}`}>
            <div className="flex items-center space-x-2">
                <span className="text-sm text-muted-foreground">Items per page:</span>
                <Select value={displayItemsPerPage.toString()} onValueChange={handleItemsPerPageChange}>
                    <SelectTrigger className="h-8 min-w-[55px] w-fit border-0">
                        <SelectValue>{displayItemsPerPage}</SelectValue>
                    </SelectTrigger>
                    <SelectContent side="top">
                        {itemsPerPageOptions.map((option) => (
                            <SelectItem key={option} value={option.toString()}>
                                {option}
                            </SelectItem>
                        ))}
                    </SelectContent>
                </Select>
            </div>

            <div className="flex items-center space-x-2">
                <Select
                    value={displayPage.toString()}
                    onValueChange={handlePageChange}
                    defaultValue={initialPage.toString()}
                >
                    <SelectTrigger className="h-8 w-[65px] border-0">
                        <SelectValue>{displayPage || initialPage}</SelectValue>
                    </SelectTrigger>
                    <SelectContent side="top">
                        {displayPageItems.length > 0 ? (
                            displayPageItems.map((pageNum) => (
                                <SelectItem key={pageNum} value={pageNum.toString()}>
                                    {pageNum}
                                </SelectItem>
                            ))
                        ) : (
                            <SelectItem value="1">1</SelectItem>
                        )}
                    </SelectContent>
                </Select>

                <span className="text-sm text-muted-foreground">
                    of {effectiveTotalPages} {effectiveTotalPages > 1 ? "pages" : "page"}
                </span>

                <div className="flex items-center space-x-2">
                    <button className="h-8 w-8 p-0" onClick={handlePrevPage} disabled={!canPrevPage}>
                        <span className="sr-only">Go to previous page</span>
                        <ChevronLeft className={`h-4 w-4 ${!canPrevPage ? "filter grayscale contrast-0" : ""}`} />
                    </button>
                    <button className="h-8 w-8 p-0" onClick={handleNextPage} disabled={!canNextPage}>
                        <span className="sr-only">Go to next page</span>
                        <ChevronRight className={`h-4 w-4 ${!canNextPage ? "filter grayscale contrast-0" : ""}`} />
                    </button>
                </div>
            </div>
        </div>
    );
}
