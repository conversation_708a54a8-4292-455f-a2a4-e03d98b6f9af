"use client";

import autoAnimate from "@formkit/auto-animate";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useCallback, useEffect, useRef, useState } from "react";
import ScrollContainer from "react-indiana-drag-scroll";

interface Tab {
    id: string;
    label: string | React.ReactNode;
}

interface Props {
    tabs: React.ReactNode[] | Tab[];
    panels?: React.ReactNode[];
    className?: string;
    panelClassName?: string;
    activeTab?: string;
    onChange?: (tab: string) => void;
    tabSpacing?: string;
}

const TabSwitch = ({ panels, tabs, className, panelClassName = "", activeTab, onChange, tabSpacing }: Props) => {
    const params = useSearchParams();
    const router = useRouter();
    const pathname = usePathname();
    const paramTab = params.get("tab");

    const getValidTabIndex = (param: string | null, tabCount: number): number => {
        const tabIndex = Number(param);
        if (isNaN(tabIndex) || tabIndex < 0 || tabIndex >= tabCount) {
            return 0;
        }
        return tabIndex;
    };

    const [selectedTab, setSelectedTab] = useState<number>(getValidTabIndex(paramTab, tabs.length));
    const parentRef = useRef(null);

    // Update selectedTab when URL parameters change
    useEffect(() => {
        const tabIndex = Number(paramTab);
        // Handle invalid, negative, or out-of-bounds tab indices
        if (isNaN(tabIndex) || tabIndex < 0 || tabIndex >= tabs.length) {
            setSelectedTab(0);
        } else {
            setSelectedTab(tabIndex);
        }
    }, [paramTab, tabs.length]);

    const getTabKey = (tab: React.ReactNode | Tab) => {
        if (typeof tab === "string") return tab;
        if (isTabObject(tab)) return tab.id;
        return String(tab);
    };

    // Check if tabs is an array of objects with id and label properties
    const isTabObject = (tab: React.ReactNode | Tab): tab is Tab =>
        typeof tab === "object" && tab !== null && "id" in tab && "label" in tab;

    useEffect(() => {
        if (parentRef.current) {
            autoAnimate(parentRef.current);
        }
    }, [parentRef]);

    const createQueryString = useCallback(
        (name: string, value: string | number) => {
            const newParams = new URLSearchParams(params.toString());
            newParams.set(name, value.toString());

            return newParams.toString();
        },
        [params]
    );

    useEffect(() => {
        // Only update the URL if we're not using the activeTab prop
        if (!activeTab && !onChange) {
            router.push(pathname + "?" + createQueryString("tab", selectedTab));
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [pathname, router, selectedTab, activeTab, onChange]);

    // If using the new interface with activeTab and onChange
    if (activeTab !== undefined && onChange) {
        return (
            <div className={className}>
                <ScrollContainer className="flex items-center w-full max-w-full no-scroll-bar overflow-x-auto text-[#90909D] text-sm font-medium gap-[8px] border-b border-b-[#E3E5E8]">
                    {tabs.map((tab) => {
                        const tabId = isTabObject(tab) ? tab.id : String(tab);
                        const tabLabel = isTabObject(tab) ? tab.label : tab;

                        return tabId === activeTab ? (
                            <button
                                key={tabId}
                                className="text-primary duration-300 flex h-[40px] p-[10px_10px_20px_10px] justify-center items-center gap-[10px] border-b-4 border-b-[#5C068C] transition-colors"
                            >
                                {tabLabel}
                            </button>
                        ) : (
                            <button
                                key={tabId}
                                className="duration-300 flex h-[40px] p-[10px_10px_20px_10px] justify-center items-center gap-[10px] transition-colors"
                                onClick={() => onChange(tabId)}
                            >
                                {tabLabel}
                            </button>
                        );
                    })}
                </ScrollContainer>

                {/* Add spacing between tabs and content if tabSpacing is provided */}
                {tabSpacing && <div className={tabSpacing} />}

                {activeTab && panels && (
                    <div className={panelClassName}>
                        {panels[tabs.findIndex((tab) => (isTabObject(tab) ? tab.id : String(tab)) === activeTab)]}
                    </div>
                )}
            </div>
        );
    }

    // Original implementation for backwards compatibility
    return (
        <div className={`${className ?? ""}`}>
            <ScrollContainer
                className={
                    "flex items-center w-full max-w-full no-scroll-bar overflow-x-auto text-[#90909D] text-sm font-medium gap-[8px] border-b border-b-[#E3E5E8]"
                }
            >
                {tabs.map((tab, index) => {
                    const tabLabel = isTabObject(tab) ? tab.label : tab;

                    return index === selectedTab ? (
                        <button
                            key={getTabKey(tab)}
                            className="text-primary duration-300 flex h-[40px] p-[10px_10px_20px_10px] justify-center items-center gap-[10px] border-b-4 border-b-[#5C068C] transition-colors"
                        >
                            {tabLabel}
                        </button>
                    ) : (
                        <button
                            key={getTabKey(tab)}
                            className="duration-300 flex h-[40px] p-[10px_10px_20px_10px] justify-center items-center gap-[10px] transition-colors"
                            onClick={() => {
                                setSelectedTab(index);
                                router.push(pathname + "?" + createQueryString("tab", index));
                            }}
                        >
                            {tabLabel}
                        </button>
                    );
                })}
            </ScrollContainer>

            {/* Add spacing between tabs and content if tabSpacing is provided */}
            {tabSpacing && <div className={tabSpacing} />}

            {/* Panels */}
            <div ref={parentRef} className={panelClassName}>
                {panels && panels.length > 0 && panels[selectedTab]}
            </div>
        </div>
    );
};

export default TabSwitch;
