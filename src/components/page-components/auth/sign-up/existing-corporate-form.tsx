"use client";

import CountryCode from "@/components/common/country-code";
import LabelInput from "@/components/common/label-input";
import PasswordInput from "@/components/common/password-input";
import { HelpIcon } from "@/components/icons/auth";
import { sendCatchFeedback } from "@/functions/feedback";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { useFormik } from "formik";
import { useRouter } from "next/navigation";
import { useMemo, useState } from "react";
import * as yup from "yup";
import { Button } from "@/components/common/buttonv3";
import { savePersonalInfo, setUserState } from "@/redux/slices/auth/signupSlice2";
import { PATH_AUTH } from "@/routes/path";
import TermsCheckbox from "@/components/common/terms-checkbox";
import { openSupportDialog } from "@/redux/features/supportDialog";
import { capitalizeUserName } from "@/functions/stringManipulations";

const ExistingCorporateForm = () => {
    const { personalInfo } = useAppSelector((state) => state.signupNew);
    const dispatch = useAppDispatch();
    const router = useRouter();
    const [termsAccepted, setTermsAccepted] = useState(false);
    const [termsError, setTermsError] = useState(false);

    const formik = useFormik({
        initialValues: useMemo(
            () => ({
                firstName: personalInfo?.firstName ?? "",
                lastName: personalInfo?.lastName ?? "",
                emailAddress: personalInfo?.emailAddress ?? "",
                mobileNumber: personalInfo?.phoneNumber ?? "",
                password: personalInfo?.password ?? "",
                confirmPassword: "",
            }),
            []
        ),
        onSubmit: () => {
            submitValues();
        },
        validationSchema: yup.object({
            firstName: yup.string().required("First name is required"),
            lastName: yup.string().required("Last name is required"),
            emailAddress: yup
                .string()
                .matches(/^[^\s@]+@[^\s@]+\.[^\s@]+$/, "Enter a valid email")
                .required("Email is required"),
            mobileNumber: yup
                .string()
                .matches(/^\d{10}$/, "Mobile number must be exactly 10 digits")
                .required("Mobile number is required")
                .length(10, "Mobile number must be exactly 10 digits"),
            password: yup
                .string()
                .required("Password is required")
                .min(14, "Password must be at least 14 characters")
                .matches(/[A-Z]/, "Password must contain at least one uppercase letter")
                .matches(/[0-9]/, "Password must contain at least one number")
                .matches(/[!@#$%^&*(),.?":{}|<>]/, "Password must contain at least one special character"),
            confirmPassword: yup
                .string()
                .required("Please confirm your password")
                .oneOf([yup.ref("password")], "Passwords must match"),
        }),
    });

    const formattedPhoneNumber = formik.values.mobileNumber ? `0${formik.values.mobileNumber}` : "";

    const submitValues = async () => {
        const errors = await formik.validateForm();

        // Check if terms are accepted
        if (!termsAccepted) {
            setTermsError(true);
            return;
        }

        if (Object.keys(errors).length === 0) {
            dispatch(
                savePersonalInfo({
                    firstName: capitalizeUserName(formik.values.firstName),
                    lastName: capitalizeUserName(formik.values.lastName),
                    emailAddress: formik.values.emailAddress,
                    phoneNumber: formattedPhoneNumber,
                    password: formik.values.password,
                })
            );
            dispatch(setUserState("existing"));

            router.push(PATH_AUTH.verifyPersonalEmail);
        } else {
            // Show all validation errors
            Object.keys(errors).forEach((field) => {
                formik.setFieldTouched(field, true, false);
            });
            // Show a general validation error message
            sendCatchFeedback("Please fill in all required fields correctly");
        }
    };

    const isButtonDisabled = Object.keys(formik.touched).length === 0 || Object.keys(formik.errors).length > 0;

    return (
        <>
            <p className="text-subText text-sm leading-[18px] font-normal">
                Provide some personal information to get you started
            </p>
            <section className="w-full max-w-[452px] mt-6">
                <form onSubmit={formik.handleSubmit} className="bg-white rounded-[20px] p-6" noValidate>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-5 mb-5">
                        <LabelInput
                            formik={formik}
                            name="firstName"
                            label="First name"
                            showError={formik.touched.firstName}
                        />
                        <LabelInput
                            formik={formik}
                            name="lastName"
                            label="Last name"
                            showError={formik.touched.lastName}
                        />
                    </div>
                    <LabelInput
                        formik={formik}
                        name="emailAddress"
                        label="Email address"
                        type="email"
                        className="mb-5"
                        showError={formik.touched.emailAddress}
                    />

                    <div className="flex flex-col gap-[10px]">
                        <label htmlFor="mobileNumber" className="text-[#151519] text-xs font-semibold text-left">
                            Mobile number
                        </label>
                        <div className="grid gri-cols-1 md:grid-cols-3 gap-[0.75rem] mb-5">
                            <CountryCode country="NG" variant="full" size="base" />
                            <LabelInput
                                formik={formik}
                                name="mobileNumber"
                                id="mobileNumber"
                                className="md:col-span-2"
                                label=""
                                showError={formik.touched.mobileNumber}
                            />
                        </div>
                    </div>

                    <PasswordInput
                        formik={formik}
                        passwordFieldName="password"
                        confirmPasswordFieldName="confirmPassword"
                        className="mb-5"
                        showValidation={true}
                    />

                    <div className="mt-6">
                        <TermsCheckbox
                            checked={termsAccepted}
                            onCheckedChange={(checked) => {
                                setTermsAccepted(checked);
                                setTermsError(false);
                            }}
                            error={termsError}
                            className="mb-4"
                        />
                        <Button type="submit" className="!w-full" disabled={isButtonDisabled}>
                            Get started
                        </Button>
                    </div>
                </form>
                <footer className="mt-[20px]">
                    <div className="text-subText text-[0.875rem] text-center flex items-center gap-[0.25rem] justify-center font-medium">
                        <HelpIcon /> Having trouble signing up?
                        <button
                            onClick={() => dispatch(openSupportDialog())}
                            className="text-primary hover:underline cursor-pointer"
                        >
                            Get Support
                        </button>
                    </div>
                </footer>
            </section>
        </>
    );
};

export default ExistingCorporateForm;
