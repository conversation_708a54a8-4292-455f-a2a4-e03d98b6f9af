"use client";

import { sendCatchFeedback, sendFeedback } from "@/functions/feedback";
import { registerUser } from "@/redux/actions/auth/signupActions";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { PATH_AUTH } from "@/routes/path";
import OTPContainer from "../otp-container";
import { maskEmail } from "@/functions/facilities";
import { clearRegisterUserState, setCurrentStep } from "@/redux/slices/auth/signupSlice2";
import { useOtp } from "@/hooks/useOtp";

const VerifyBusinessEmailForm = () => {
    const { directorInfo, personalAndBusinessInfo, userSignupOption } = useAppSelector((state) => state.signupNew);

    const emailAddress =
        userSignupOption === "existing"
            ? directorInfo?.corporateEmailAddress
            : userSignupOption === "new"
              ? personalAndBusinessInfo?.corporateEmailAddress
              : "";

    const {
        loading: registerUserLoading,
        error: registerUserError,
        success: registerUserSuccess,
    } = useAppSelector((state) => state.signupNew);

    const dispatch = useAppDispatch();
    const [otpValue, setOtpValue] = useState("");
    const router = useRouter();

    const sameEmail = personalAndBusinessInfo?.emailAddress === personalAndBusinessInfo?.corporateEmailAddress;

    const {
        validateOtp,
        resendOtp,
        sendOtp,
        validateOtpLoading,
        validateOtpSuccess,
        validateOtpError,
        resendOtpLoading,
        resendOtpSuccess,
        resendOtpError,
        clearOtpState,
    } = useOtp();

    const handleResendOTP = async () => {
        resendOtp(emailAddress as string);
    };

    const submitForm = async () => {
        validateOtp({
            receiver: emailAddress as string,
            otp: otpValue,
        });
    };

    useEffect(() => {
        sendOtp({
            receiver: emailAddress as string,
            receiverType: "EMAIL",
        });

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [emailAddress]);

    useEffect(() => {
        if (validateOtpSuccess) {
            dispatch(registerUser());
            clearOtpState("validate");
        }

        if (resendOtpSuccess) {
            sendFeedback("OTP has been sent successfully", "success", () => clearOtpState("resend"));
        }

        if (resendOtpError) {
            sendCatchFeedback(resendOtpError, () => clearOtpState("resend"));
        }

        if (validateOtpError) {
            sendCatchFeedback(validateOtpError, () => clearOtpState("validate"));
            setOtpValue("");
        }
    }, [validateOtpError, validateOtpSuccess, resendOtpError, resendOtpSuccess, dispatch, clearOtpState]);

    useEffect(() => {
        if (registerUserSuccess) {
            sendFeedback("Account created successfully", "success", () => dispatch(clearRegisterUserState()));
            dispatch(setCurrentStep(7));
            router.replace(PATH_AUTH.setUpMfa);
        }
        if (registerUserError) {
            sendCatchFeedback(registerUserError ?? "Unable to create account", () =>
                dispatch(clearRegisterUserState())
            );
            router.replace(PATH_AUTH.signup);
        }

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [registerUserSuccess, registerUserError]);

    return (
        <OTPContainer
            value={otpValue}
            setValue={setOtpValue}
            onSubmit={submitForm}
            onResend={handleResendOTP}
            loading={validateOtpLoading || registerUserLoading}
            resendLoading={resendOtpLoading}
            variant="autoVerify"
            title={sameEmail ? "Verify your email" : "Verify your company email"}
            type="email"
            subtitle={
                <p className="text-[#3A3A41]">
                    We&apos;ve sent a 6 digit verification code to{" "}
                    <b>{emailAddress ? maskEmail(emailAddress) : "--"}</b>
                </p>
            }
            buttonText="Verify Now"
            resendText="Resend Code"
            className="custom-container-class"
            buttonClassName="custom-button-class"
        />
    );
};

export default VerifyBusinessEmailForm;
