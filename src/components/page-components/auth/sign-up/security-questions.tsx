"use client";

import { useEffect, useState } from "react";
import { useFormik } from "formik";
import * as yup from "yup";
import { useRouter } from "next/navigation";

import LabelInput from "@/components/common/label-input";
import Dropdown from "@/components/common/dropdown";
import { Button } from "@/components/common/buttonv3";
import { sendCatchFeedback, sendFeedback } from "@/functions/feedback";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { PATH_AUTH } from "@/routes/path";
import { clearState } from "@/redux/slices/auth/signupSlice2";
import { userAxios } from "@/api/axios";
import { handleError } from "@/lib/utils";
import {
    setSecurityQuestions as handleSecurityQuestions,
    updatePreferredMFA,
} from "@/redux/actions/auth/signupActions";
import type { SecurityQuestionBody } from "@/redux/types/auth/signup";

// Define the security question type
interface SecurityQuestion {
    id: number;
    question: string;
}

const SecurityQuestionsForm = () => {
    const router = useRouter();
    const dispatch = useAppDispatch();
    const [securityQuestions, setSecurityQuestions] = useState<SecurityQuestion[]>([]);
    const [loadingQuestions, setLoadingQuestions] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);
    const [requiredQuestions, setRequiredQuestions] = useState<number>(3);
    const {
        personalInfo,
        userSignupOption,
        personalAndBusinessInfo,
        setSecurityQuestions: securityQuestionsState,
        teamMemberInfo,
        corporateId,
    } = useAppSelector((state) => state.signupNew);

    const { error: updatePreferredMFAError, success: updatePreferredMFASuccess } = useAppSelector(
        (state) => state.signupNew?.updatePreferredMFA || {}
    );

    const emailAddress =
        userSignupOption === "existing"
            ? personalInfo?.emailAddress
            : userSignupOption === "new"
              ? personalAndBusinessInfo?.emailAddress
              : userSignupOption === "team-invite"
                ? teamMemberInfo?.emailAddress
                : "";

    // Dynamically generate validation schema based on available questions
    const generateValidationSchema = (questionCount: number) => {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const schema: any = {};

        for (let i = 1; i <= questionCount; i++) {
            // Add question validation
            schema[`question${i}`] = yup.string().required("Security question is required");

            // Add answer validation
            schema[`answer${i}`] = yup.string().required("Answer is required");
        }

        return yup.object(schema);
    };

    // Generate initial values based on question count
    const generateInitialValues = (questionCount: number) => {
        const values: Record<string, string> = {};
        for (let i = 1; i <= questionCount; i++) {
            values[`question${i}`] = "";
            values[`answer${i}`] = "";
        }
        return values;
    };

    // Initialize form with Formik
    const formik = useFormik({
        initialValues: generateInitialValues(requiredQuestions),
        validationSchema: generateValidationSchema(requiredQuestions),
        validateOnChange: true,
        validateOnBlur: true,
        onSubmit: (values) => {
            submitForm(values);
        },
        enableReinitialize: true, // Important to handle dynamic schema changes
    });

    // Handle form submission
    const submitForm = async (values: Record<string, string>) => {
        if (!emailAddress) {
            sendCatchFeedback("Email address is required");
            return;
        }

        // Format the security questions and answers for submission
        const securityQuestionsData: SecurityQuestionBody = {
            corporateId: Number(corporateId),
            userEmail: emailAddress,
            userSecurityQuestionRequests: [],
        };

        // Always process exactly 3 questions (or fewer if not enough are available)
        const questionsToProcess = Math.min(3, securityQuestions.length);

        for (let i = 1; i <= questionsToProcess; i++) {
            const questionId = values[`question${i}`];
            const answer = values[`answer${i}`];

            if (questionId && answer) {
                const selectedQuestion = securityQuestions.find((q) => q.id.toString() === questionId);

                if (!selectedQuestion) {
                    throw new Error(`Question with ID ${questionId} not found`);
                }

                securityQuestionsData.userSecurityQuestionRequests.push({
                    question: selectedQuestion.question,
                    // Normalize answer to lowercase for case-insensitive validation
                    answer: answer.trim().toLowerCase(),
                });
            }
        }

        await dispatch(handleSecurityQuestions(securityQuestionsData));
    };

    // Get all currently selected question IDs except the current one
    const getSelectedQuestionIds = () => {
        const selectedIds = [];

        // Always check exactly 3 questions (or fewer if not enough are available)
        const questionsToCheck = Math.min(3, securityQuestions.length);

        for (let i = 1; i <= questionsToCheck; i++) {
            const questionValue = formik.values[`question${i}`];
            if (questionValue) {
                selectedIds.push(questionValue);
            }
        }

        return selectedIds;
    };

    // Filter questions for a specific dropdown
    const getFilteredQuestions = (currentFieldName: string) => {
        const currentValue = formik.values[currentFieldName];
        const allSelectedIds = getSelectedQuestionIds();

        // Return all questions if nothing is selected yet
        if (allSelectedIds.length === 0) {
            return securityQuestions.map((q) => ({ value: q.id.toString(), label: q.question }));
        }

        // Filter out questions that are already selected in other dropdowns
        return securityQuestions
            .filter((question) => {
                const questionId = question.id.toString();
                // Include this question if it's the current selection or not selected elsewhere
                return questionId === currentValue || !allSelectedIds.includes(questionId);
            })
            .map((q) => ({ value: q.id.toString(), label: q.question }));
    };

    // Determine if form is valid for submission
    const isFormValid = () => {
        // Always check exactly 3 questions (or fewer if not enough are available)
        const questionsToCheck = Math.min(3, securityQuestions.length);

        // Check if all required fields are filled
        for (let i = 1; i <= questionsToCheck; i++) {
            if (!formik.values[`question${i}`] || !formik.values[`answer${i}`]) {
                return false;
            }
        }

        // Check if there are any validation errors
        if (Object.keys(formik.errors).length > 0) {
            return false;
        }

        // Check if all selected questions are unique
        const selectedQuestions: string[] = [];
        for (let i = 1; i <= questionsToCheck; i++) {
            const questionValue = formik.values[`question${i}`];
            if (questionValue) {
                if (selectedQuestions.includes(questionValue)) {
                    return false; // Duplicate question found
                }
                selectedQuestions.push(questionValue);
            }
        }

        return true;
    };

    // Handle dropdown change to clear any duplicate selections
    const handleQuestionChange = (fieldName: string, value: string) => {
        // Update the current field
        formik.setFieldValue(fieldName, value);

        // Always check exactly 3 questions (or fewer if not enough are available)
        const questionsToCheck = Math.min(3, securityQuestions.length);

        // Check for duplicates in other fields and clear them
        for (let i = 1; i <= questionsToCheck; i++) {
            const otherFieldName = `question${i}`;

            // Skip the current field
            if (otherFieldName === fieldName) continue;

            // If another field has the same value, clear it
            if (formik.values[otherFieldName] === value) {
                formik.setFieldValue(otherFieldName, "");
            }
        }
    };

    // Fetch security questions from API
    useEffect(() => {
        setLoadingQuestions(true);
        const fetchSecurityQuestions = async () => {
            try {
                const res = await userAxios.get("/v1/security-questions");
                const questions = res.data;
                setSecurityQuestions(questions);

                // Always require exactly 3 questions regardless of how many are available
                const availableCount = questions.length;
                // Only use the minimum if less than 3 questions are available
                const requiredCount = Math.min(3, availableCount);
                setRequiredQuestions(requiredCount);

                // Reset form with new validation schema and initial values
                formik.resetForm({
                    values: generateInitialValues(requiredCount),
                    errors: {},
                });

                setError(null);
            } catch (error) {
                const errorMessage = handleError(error);
                sendCatchFeedback(errorMessage);
                setError(errorMessage);
            } finally {
                setLoadingQuestions(false);
            }
        };

        fetchSecurityQuestions();
    }, []);

    useEffect(() => {
        if (securityQuestionsState.success) {
            dispatch(updatePreferredMFA({ email: emailAddress as string, mfaMethod: "SECURITY_QUESTION" }));
        }

        if (securityQuestionsState.error) {
            sendCatchFeedback(securityQuestionsState.error, () => dispatch(clearState("setSecurityQuestions")));
        }

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [securityQuestionsState.success, securityQuestionsState.error, emailAddress]);

    useEffect(() => {
        if (updatePreferredMFASuccess) {
            sendFeedback("MFA setup successfully", "success", () => dispatch(clearState("updatePreferredMFA")));
            router.push(PATH_AUTH.transactionPin);
        }
        if (updatePreferredMFAError) {
            sendCatchFeedback("Unable to update preferred MFA", () => dispatch(clearState("updatePreferredMFA")));
            router.push(PATH_AUTH.transactionPin);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [updatePreferredMFASuccess, updatePreferredMFAError]);

    if (loadingQuestions) {
        return (
            <div className="w-full mt-6 max-w-[508px]">
                <div className="bg-white rounded-[20px] p-6 pb-0">
                    <div className="h-[200px] flex items-center justify-center">
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary" />
                    </div>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="w-full mt-6 max-w-[508px]">
                <div className="bg-white rounded-[20px] p-6 pb-0 text-center text-red-500">
                    <p>{error}</p>
                </div>
            </div>
        );
    }

    if (securityQuestions.length === 0) {
        return (
            <div className="w-full mt-6 max-w-[508px]">
                <div className="bg-white rounded-[20px] p-6 pb-0 text-center">
                    <p>No security questions available. Please contact support.</p>
                </div>
            </div>
        );
    }

    // If we have fewer than 3 questions, show a message
    if (securityQuestions.length < 3) {
        return (
            <div className="w-full mt-6 max-w-[508px]">
                <div className="bg-white rounded-[20px] p-6 pb-0 text-center">
                    <p>At least 3 security questions are required. Please contact support.</p>
                </div>
            </div>
        );
    }

    return (
        <section className="w-full mt-6 max-w-[508px]">
            <form onSubmit={formik.handleSubmit} className="bg-white rounded-[20px] p-6 pb-0" noValidate>
                <div className="">
                    {/* Dynamically render question-answer pairs based on required questions */}
                    {Array.from({ length: 3 }).map((_, index) => {
                        const questionNum = index + 1;
                        const questionField = `question${questionNum}`;
                        const answerField = `answer${questionNum}`;

                        return (
                            <div className="mb-6" key={questionField}>
                                <Dropdown
                                    options={getFilteredQuestions(questionField)}
                                    name={questionField}
                                    label={`Question ${questionNum}`}
                                    placeholder="Select a question"
                                    size="sm"
                                    className="mb-3"
                                    formik={formik}
                                    onChange={(value) => handleQuestionChange(questionField, value as string)}
                                />
                                <LabelInput
                                    formik={formik}
                                    name={answerField}
                                    label="Your answer"
                                    placeholder="Enter your answer"
                                    showError={formik.touched[answerField]}
                                    masked={true}
                                    hint="Answer is case-insensitive"
                                />
                            </div>
                        );
                    })}
                </div>

                <div className="mt-8 mb-5">
                    <Button
                        type="submit"
                        className="!w-full"
                        disabled={!isFormValid() || securityQuestionsState.loading}
                        loading={securityQuestionsState.loading}
                    >
                        Continue
                    </Button>
                </div>
            </form>

            <div className="mb-6 text-center">
                <Button variant={"text-neutral"} onClick={() => router.push(PATH_AUTH.setUpMfa)}>
                    Back to two-factor authentication options
                </Button>
            </div>
        </section>
    );
};

export default SecurityQuestionsForm;

export const SecurityTips = () => (
    <div className="bg-gray-50 p-4 rounded-lg h-fit md:max-w-[448px] md:mt-12">
        <h3 className="font-semibold mb-3">Security tips</h3>
        <ul className="space-y-2 list-inside list-disc">
            <li className="text-sm font-normal text-subText">Choose a question with an answer only you would know</li>
            <li className="text-sm font-normal text-subText">Avoid guessable information</li>
            <li className="text-sm font-normal text-subText">Keep your answer private and don't share with anyone</li>
        </ul>
    </div>
);
