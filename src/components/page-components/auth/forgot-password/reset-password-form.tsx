"use client";

//NEXT IMPORTS
import { useRouter, useSearchParams } from "next/navigation";

//OTHER IMPORTS
import { useFormik } from "formik";

// PROJECT IMPORTS
import { Button } from "@/components/common/buttonv3";
import PasswordInput from "@/components/common/password-input";
import LoadingIndicator from "@/components/common/loading-indicator";
import { sendFeedback } from "@/functions/feedback";
import { ResetPasswordValidationSchema } from "@/functions/ruleEngine";
import { resetPassword } from "@/redux/actions/auth/resetPasswordActions";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { PATH_AUTH } from "@/routes/path";
import { ResetPasswordType } from "@/types/auth";
import { useEffect } from "react";

const ResetPasswordForm = () => {
    const param = useSearchParams();
    const router = useRouter();
    const token = decodeURIComponent(param.get("token") ?? "");
    const email = decodeURIComponent(param.get("email") ?? "");
    const { loading, success } = useAppSelector((state) => state.resetPassword.resetPassword || {});
    const dispatch = useAppDispatch();

    const initialValues: ResetPasswordType = {
        password: "",
        confirmPassword: "",
    };

    const formik = useFormik({
        initialValues: initialValues,
        onSubmit: () => {
            submitValues();
        },
        validationSchema: ResetPasswordValidationSchema,
    });

    const submitValues = async () => {
        if (email && token) {
            await dispatch(
                resetPassword({
                    email: email,
                    newPassword: formik.values.password,
                    passwordResetToken: token,
                })
            );
        }
    };

    useEffect(() => {
        if (success) {
            sendFeedback("Reset reset successfully", "success");
            formik.resetForm();
            router.push(`${PATH_AUTH.resetPasswordNotification}`);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [success]);

    return (
        <form onSubmit={formik.handleSubmit} className="w-full max-w-[420px]">
            <PasswordInput
                formik={formik}
                passwordFieldName="password"
                confirmPasswordFieldName="confirmPassword"
                label="Password"
                confirmLabel="Confirm Password"
                className="mb-8"
            />

            <Button type="submit" disabled={loading} className="!w-full">
                {loading ? <LoadingIndicator size={20} /> : "Reset your password"}
            </Button>
        </form>
    );
};
export default ResetPasswordForm;
