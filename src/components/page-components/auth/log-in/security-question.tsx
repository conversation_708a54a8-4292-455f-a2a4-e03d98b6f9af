import { userAxios } from "@/api/axios";
import { But<PERSON> } from "@/components/common/buttonv3";
import LabelInput from "@/components/common/label-input";
import { sendCatchFeedback, sendFeedback } from "@/functions/feedback";
import useRouteAfterLogin from "@/hooks/useRouteAfterLogin";
import { cookies } from "@/lib/cookies";
import { storageManager } from "@/lib/custom";
import { handleError } from "@/lib/utils";
import { validateUserSecurityQuestion, verifyDevice } from "@/redux/actions/auth/signinActions";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { signinActions } from "@/redux/slices/auth/signinSlice";
import { PATH_AUTH } from "@/routes/path";
import { useFormik } from "formik";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import * as yup from "yup";

const SecurityQuestion = () => {
    const params = useSearchParams();
    const email = storageManager.getItem("preLoginData")?.username ?? "";
    const [question, setQuestion] = useState("");
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const {
        error: verifyDeviceError,
        success: verifyDeviceSuccess,
        loading: verifyDeviceLoading,
    } = useAppSelector((state) => state.signin?.verifyDevice || {});
    const {
        error: validateUserSecurityQuestionError,
        success: validateUserSecurityQuestionSuccess,
        loading: validateUserSecurityQuestionLoading,
    } = useAppSelector((state) => state.signin?.validateUserSecurityQuestion || {});
    const router = useRouter();
    const route = useRouteAfterLogin();

    const mfaMethod = params.get("mfa");

    const currentStep = useAppSelector((state) => state.signin.currentLoginStep);

    const dispatch = useAppDispatch();

    const formik = useFormik({
        initialValues: {
            question,
            answer: "",
        },
        enableReinitialize: true,
        onSubmit: () => {
            handleFormSubmit();
        },
        validationSchema: yup.object({
            question: yup.string().required("Question is required"),
            answer: yup.string().required("Answer is required"),
        }),
    });

    const handleFormSubmit = () => {
        dispatch(
            validateUserSecurityQuestion({
                userEmail: email,
                question: question, // Using the state question which now matches formik
                // Normalize answer to lowercase for case-insensitive validation
                answer: formik.values.answer.trim().toLowerCase(),
                loginFlag: true,
            })
        );
    };

    const getRandomIndex = (length: number): number => Math.floor(Math.random() * length);

    useEffect(() => {
        const fetchQuestions = async () => {
            const token = cookies.getToken();
            if (token) return;

            setLoading(true);
            try {
                const response = await userAxios(`/v1/user-security-questions?email=${email}`);

                // Generate the random index once
                const randomIndex = getRandomIndex(response.data.length);
                const selectedQuestion = response.data[randomIndex]?.question;

                // Use the same question for both state and formik
                setQuestion(selectedQuestion);
                formik.setFieldValue("question", selectedQuestion);

                setError(null);
            } catch (error) {
                console.error("Error fetching question:", error);
                setError(handleError(error));
            } finally {
                setLoading(false);
            }
        };

        fetchQuestions();

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [email]);

    useEffect(() => {
        if (error) {
            sendCatchFeedback(error);
        }
        if (verifyDeviceError) {
            sendCatchFeedback(verifyDeviceError);
            dispatch(signinActions.clearState("verifyDevice"));
            router.replace(PATH_AUTH.login);
        }
        if (validateUserSecurityQuestionError) {
            sendCatchFeedback(validateUserSecurityQuestionError, () =>
                dispatch(signinActions.clearState("validateUserSecurityQuestion"))
            );
        }

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [error, validateUserSecurityQuestionError, verifyDeviceError]);

    useEffect(() => {
        if (verifyDeviceSuccess) {
            sendFeedback("Login successful", "success", () => dispatch(signinActions.clearState("verifyDevice")));
            router.replace(route);
        }

        if (validateUserSecurityQuestionSuccess) {
            dispatch(verifyDevice(email));
            dispatch(signinActions.clearState("validateUserSecurityQuestion"));
        }

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [verifyDeviceSuccess, validateUserSecurityQuestionSuccess, currentStep, email]);

    if (mfaMethod !== "security-question") {
        return null;
    }

    if (loading) {
        return <div>Loading...</div>;
    }

    if (!question) {
        return <div>No question available.</div>;
    }

    return (
        <div className="space-y-4">
            <div className="text-center mb-[40px]">
                <h2 className="text-2xl font-bold text-black">Let's make sure it's you</h2>
                <p className="text-base font-normal text-subText mt-[12px]">
                    Provide the answer to your security question below
                </p>
            </div>

            <form className="grid gap-8" onSubmit={formik.handleSubmit}>
                <LabelInput
                    name="answer"
                    label={question}
                    placeholder="Enter your answer"
                    formik={formik}
                    masked={true}
                    hint="Answer is case-insensitive"
                />
                <Button
                    loading={validateUserSecurityQuestionLoading || verifyDeviceLoading}
                    disabled={validateUserSecurityQuestionLoading}
                    type="submit"
                    fullWidth
                    size="lg"
                >
                    Continue
                </Button>
            </form>
        </div>
    );
};

export default SecurityQuestion;
