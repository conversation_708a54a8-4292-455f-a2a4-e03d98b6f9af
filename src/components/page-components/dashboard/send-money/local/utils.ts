import {
    addDays,
    addMonths,
    addWeeks,
    addYears,
    differenceInDays,
    differenceInMonths,
    differenceInWeeks,
    differenceInYears,
} from "date-fns";
import { multiplePaymentFormSteps, MultiplePaymentFormStepType } from "../data";

export const getFrequencyValue = ({
    frequency = "DAILY",
    startDate,
    endDate,
    endCount,
}: {
    endDate: string;
    startDate: string;
    frequency: string;
    endCount?: string;
}) => {
    let returnValue = 1;

    if (endCount) {
        return Number(endCount);
    }
    switch (frequency) {
        case "DAILY":
            // Add 1 day to the end date to ensure it is inclusive
            returnValue = differenceInDays(new Date(endDate), new Date(startDate)) + 1;
            break;
        case "MONTHLY":
            returnValue = differenceInMonths(new Date(endDate), new Date(startDate));
            break;
        case "WEEKLY":
            returnValue = differenceInWeeks(new Date(endDate), new Date(startDate));
            break;
        case "YEARLY":
            returnValue = differenceInYears(new Date(endDate), new Date(startDate));
            break;

        default:
            break;
    }

    return returnValue;
};

export const getCorrespondingIndexForFormStepForMultiple: (formStep: MultiplePaymentFormStepType) => number = (
    formStep
) => {
    if (formStep === "Select recipients") {
        return 0;
    }
    if (formStep === "Payment information") {
        return 1;
    }
    if (formStep === "Review payment") {
        return 2;
    }

    return multiplePaymentFormSteps.length;
};

export const getRecurringFeedbackString = ({
    frequency = "DAILY",
    startDate,
    endDate,
}: {
    endDate: string;
    startDate: string;
    frequency: string;
}) => {
    let returnValue = "3 months";
    switch (frequency) {
        case "DAILY":
            // Add 1 day to the end date to ensure it is inclusive
            returnValue = `${differenceInDays(new Date(endDate), new Date(startDate)) + 1} daily`;
            break;
        case "MONTHLY":
            returnValue = `${differenceInMonths(new Date(endDate), new Date(startDate))} monthly`;
            break;
        case "WEEKLY":
            returnValue = `${differenceInWeeks(new Date(endDate), new Date(startDate))} weekly`;
            break;
        case "YEARLY":
            returnValue = `${differenceInYears(new Date(endDate), new Date(startDate))} yearly`;
            break;

        default:
            break;
    }

    return returnValue;
};

export const getRecurringEndDateFromOccurrences = ({
    occurrences,
    startDate,
    frequency,
}: {
    startDate: string;
    frequency: string;
    occurrences: number;
}) => {
    let returnValue = new Date(startDate);
    switch (frequency) {
        case "DAILY":
            returnValue = addDays(returnValue, occurrences);
            break;
        case "MONTHLY":
            returnValue = addMonths(returnValue, occurrences);
            break;
        case "WEEKLY":
            returnValue = addWeeks(returnValue, occurrences);
            break;
        case "YEARLY":
            returnValue = addYears(returnValue, occurrences);
            break;

        default:
            break;
    }

    return returnValue?.toISOString()?.split("T")[0];
};

export const getRecurringEndStringFromOccurrences = ({
    occurrences,
    frequency,
}: {
    frequency: string;
    occurrences: string;
}) => {
    let returnValue = "";
    switch (frequency) {
        case "DAILY":
            returnValue = `${occurrences} daily`;
            break;
        case "MONTHLY":
            returnValue = `${occurrences} monthly`;
            break;
        case "WEEKLY":
            returnValue = `${occurrences} weekly`;
            break;
        case "YEARLY":
            returnValue = `${occurrences} yearly`;
            break;

        default:
            break;
    }

    return returnValue;
};

const generateTaxedFees = (fee: number, vat: number) => {
    const taxedFees = vat * fee;
    const totalFees = taxedFees + fee;

    const totalRoundedFee = parseFloat((Math.ceil(totalFees * 100) / 100).toFixed(2));
    return totalRoundedFee;
};

export const calculateTransactionFee = ({ amount, destinationBank }: { amount: number; destinationBank: string }) => {
    const VAT = 0.075; //7.5%
    const FCMBSortCode = "000003";

    if (FCMBSortCode === destinationBank) return 0;
    else if (amount >= 0 && amount <= 5000) {
        return generateTaxedFees(10, VAT);
    } else if (amount > 5000 && amount <= 50000) {
        return generateTaxedFees(25, VAT);
    } else {
        return generateTaxedFees(50, VAT);
    }
};
