"use client";

import LoadingIndicator from "@/components/common/loading-indicator";
import SearchInput from "@/components/common/search-input";
import { LocalRecipientType } from "@/components/page-components/dashboard/recipients/type";
import { useDebounce } from "@/hooks/useDebounce";
import { getLocalRecipientAction } from "@/redux/actions/recipients/local-recipient";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { Dispatch, SetStateAction, useEffect, useMemo, useState } from "react";
import RecipientCard from "./recipient-card";

const RecipientsList = ({
    selectedRecipients,
    setSelectedRecipients,
}: {
    setSelectedRecipients: Dispatch<SetStateAction<LocalRecipientType[] | []>>;
    selectedRecipients: LocalRecipientType[] | [];
}) => {
    const { recipientData: allRecipients, loading } = useAppSelector(
        (state) => state.recipient.getLocalRecipient ?? {}
    );
    const dispatch = useAppDispatch();
    // Search state - initialize from URL
    const [searchQuery, setSearchQuery] = useState("");
    const debouncedQuery = useDebounce(searchQuery, 300); // 300ms debounce
    // Search handler
    const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchQuery(e.target.value);
    };

    const handleRecipientClick = (recipient: LocalRecipientType) => {
        if (selectedRecipients.some((item) => item.accountNumber === recipient.accountNumber)) {
            setSelectedRecipients(selectedRecipients.filter((item) => item.accountNumber !== recipient.accountNumber));
        } else {
            setSelectedRecipients((old) => [...old, recipient]);
        }
    };

    useEffect(() => {
        dispatch(
            getLocalRecipientAction({
                params: {
                    pageNo: 0,
                    pageSize: 10,
                },
            })
        );
    }, [dispatch]);

    const recipientsToUse = useMemo(() => {
        if (!debouncedQuery.trim()) return allRecipients;

        return allRecipients.filter((recipient: LocalRecipientType) => {
            const lowercaseQuery = debouncedQuery.toLowerCase();
            return (
                recipient.accountNumber?.includes(debouncedQuery) ||
                recipient.accountName?.toLowerCase().includes(lowercaseQuery) ||
                recipient.bank?.toLowerCase().includes(lowercaseQuery)
            );
        });
    }, [allRecipients, debouncedQuery]);

    return (
        <div className="w-full pt-8">
            <SearchInput
                placeholder="Account name, bank, account number"
                size="md"
                width="w-full"
                value={searchQuery}
                onChange={handleSearch}
            />

            {/* Recipients list */}
            <div className="flex flex-col mt-6 gap-6">
                {loading ? (
                    <LoadingIndicator />
                ) : (
                    recipientsToUse.map((recipient: LocalRecipientType) => (
                        <RecipientCard
                            key={recipient.accountNumber}
                            recipient={recipient}
                            onClick={() => handleRecipientClick(recipient)}
                            isSelected={selectedRecipients.some(
                                (item) => item.accountNumber === recipient.accountNumber
                            )}
                            totalRecipients={selectedRecipients.length}
                        />
                    ))
                )}
            </div>
        </div>
    );
};

export default RecipientsList;
