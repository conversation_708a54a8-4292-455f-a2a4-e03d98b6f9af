import { But<PERSON> } from "@/components/common/buttonv3";
import { LocalRecipientType } from "@/components/page-components/dashboard/recipients/type";
import { getNameInitials, maskNumber } from "@/functions/stringManipulations";
import clsx from "clsx";
import { AddRecipientIcon, DeleteRecipientIcon } from "../../../icons";

interface RecipientCardProps {
    recipient: LocalRecipientType;
    onClick: () => void;
    isSelected?: boolean;
    totalRecipients?: number;
}

const RecipientCard = ({ recipient, onClick, isSelected, totalRecipients = 0 }: RecipientCardProps) => (
    <div
        className={clsx("w-full rounded-xl flex items-center gap-2 justify-between text-left py-2 px-0")}
        aria-label={`View details for ${recipient.accountName}`}
    >
        <div className="flex items-center gap-4">
            <div className="w-10 h-10 flex items-center justify-center rounded-full text-subText font-medium bg-[#F9F9FA]">
                {getNameInitials(recipient.accountName)}
            </div>
            <div className="flex flex-col">
                <p className="text-sm font-medium">{recipient.accountName}</p>
                <p className="text-subText text-sm">
                    {recipient.bank} ・{maskNumber(recipient.accountNumber)}
                </p>
            </div>
        </div>
        <Button variant="text-primary" onClick={onClick} disabled={totalRecipients >= 15}>
            {isSelected ? <DeleteRecipientIcon /> : <AddRecipientIcon />}
        </Button>
    </div>
);

export default RecipientCard;
