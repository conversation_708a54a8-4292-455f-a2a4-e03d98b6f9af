import AmountInput from "@/components/common/amount-input";
import LabelInput from "@/components/common/label-input";
import AccountSelector from "@/components/page-components/dashboard/accounts/payments/components/account-selector";
import { FormikProps } from "formik";
import pluralize from "pluralize";
import { Dispatch, SetStateAction, useEffect, useMemo } from "react";
import { LocalRecipientType } from "../../../recipients/type";
import { BulkRecipientType, MultiplePaymentFormType } from "../../data";
import TransferTypeSelector from "../transfer-type-selector";

type Props<T extends MultiplePaymentFormType> = {
    formik: FormikProps<T>;
    openScheduleModal: () => void;
    selectedRecipients: LocalRecipientType[] | BulkRecipientType[] | [];
    type?: "multiple" | "bulk";
    openRecurringModal: () => void;
    setAccountBalance: Dispatch<SetStateAction<number | undefined>>;
    accountBalance?: number;
};

const FormDetails = <T extends MultiplePaymentFormType>({
    formik,
    openRecurringModal,
    openScheduleModal,
    selectedRecipients,
    type = "multiple",
    setAccountBalance,
    accountBalance,
}: Props<T>) => {
    const totalAmount = useMemo(
        () => selectedRecipients.reduce((a, b) => a + Number((b as BulkRecipientType).amount || 0), 0),
        [selectedRecipients]
    );

    useEffect(() => {
        // Set field when total amount changes
        if (type === "bulk" && totalAmount !== undefined) {
            formik.setFieldValue("amount", totalAmount);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [totalAmount, type]);

    useEffect(() => {
        if (type === "bulk" && accountBalance !== undefined) {
            formik.setFieldTouched("amount", true, true); // Mark it as touched so yup can validate
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [type, accountBalance]);

    return (
        <div className="w-full">
            <div className="flex-col flex items-center w-full">
                <div className="flex flex-col w-full items-center">
                    <h3 className="font-semibold text-black text-2xl text-center" data-testid="heading">
                        Payment information
                    </h3>
                </div>
                <p className="text-black font-semibold text-xs mb-2 mt-12 text-left self-start">Recipient(s)</p>
                <div className="p-3 w-full flex items-center justify-between border border-[#E3E5E8] rounded-[12px]">
                    <div className="flex items-center gap-4">
                        <div className="bg-[#F9F9FA] w-10 h-10 justify-center flex items-center rounded-full font-medium text-subText">
                            {selectedRecipients.length}
                        </div>
                        <div>
                            <p className="text-subText text-sm">Sending to</p>
                            <p className="text-black text-sm font-medium">
                                {selectedRecipients.length} {pluralize("recipient", selectedRecipients.length)}
                            </p>
                        </div>
                    </div>
                </div>
                <form className="w-full mt-5">
                    <AccountSelector
                        labelName="Send from"
                        selectedAccount={formik.values.transferSource}
                        onChange={(account) => formik.setFieldValue("transferSource", account)}
                        updateExternalBalance={(balance) => setAccountBalance(balance)}
                    />

                    <AmountInput<T>
                        formik={formik}
                        className="my-5"
                        label="Amount"
                        name="amount"
                        disabled={type === "bulk"}
                    />

                    {/* Transfer Type Selection */}
                    <TransferTypeSelector
                        formik={formik}
                        openRecurringModal={openRecurringModal}
                        openScheduleModal={openScheduleModal}
                    />

                    <LabelInput
                        formik={formik}
                        name="narration"
                        label="Narration"
                        className="my-5"
                        placeholder="Write something here..."
                    />
                </form>
            </div>
        </div>
    );
};

export default FormDetails;
