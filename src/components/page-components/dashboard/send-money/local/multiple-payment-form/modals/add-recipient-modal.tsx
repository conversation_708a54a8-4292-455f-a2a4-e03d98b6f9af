"use client";

import { But<PERSON> } from "@/components/common/buttonv3";
import { CheckboxLabel } from "@/components/common/checkbox-label";
import CloseX from "@/components/common/close-x";
import SideDrawer from "@/components/common/drawer";
import Dropdown from "@/components/common/dropdown";
import LabelInput from "@/components/common/label-input";
import LoadingIndicator from "@/components/common/loading-indicator";
import { sendCatchFeedback, sendFeedback } from "@/functions/feedback";
import {
    addLocalRecipient,
    fetchRecipientDetails,
    getLocalRecipientAction,
} from "@/redux/actions/recipients/local-recipient";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { recipientActions } from "@/redux/slices/recipientsSlice";
import { useFormik } from "formik";
import { useEffect, useMemo, useState } from "react";
import * as yup from "yup";
import { VerifiedIcon } from "../../../icons";

type Props = {
    open: boolean;
    onClose: () => void;
};

const AddRecipientModal = ({ open, onClose }: Props) => {
    const [internalNotifyState, setInternalNotifyState] = useState(false);
    const [internalRetrieval, setInternalRetrieval] = useState(false);
    const [verified, setVerified] = useState(false);
    const { banks, loading: loadingBanks } = useAppSelector((state) => state.recipient.getLocalBanks || []);
    const {
        details,
        loading: retrievingDetails,
        error: retrievalError,
    } = useAppSelector((state) => state.recipient.getRecipientDetails ?? {});

    const handleChange = (checked: boolean) => {
        setInternalNotifyState(checked);
    };
    const { loading: recipientLoading, success: recipientSuccess } = useAppSelector(
        (state) => state.recipient.addLocalRecipient || {}
    );
    const dispatch = useAppDispatch();

    const formik = useFormik({
        initialValues: useMemo(
            () => ({
                accountName: "",
                bank: "",
                currencyCode: "NGN",
                accountNumber: "",
            }),
            []
        ),
        onSubmit: () => {
            submitValues();
        },
        validationSchema: yup.object({
            currencyCode: yup.string().required("Required"),
            accountNumber: yup
                .string()
                .matches(/^\d+$/, "Account number must contain only numbers")
                .matches(/^\d{10}$/, "Account number must be exactly 10 digits")
                .required("Account number is required"),
            bank: yup.string().required("Required"),
            accountName: yup.string().required("Required"),
        }),
        enableReinitialize: true,
    });

    const submitValues = async () => {
        await dispatch(
            addLocalRecipient({
                accountName: formik.values.accountName,
                bank: banks.find((item) => item.bankCode === formik.values.bank)?.bankName ?? "",
                currencyCode: formik.values.currencyCode,
                accountNumber: formik.values.accountNumber,
            })
        );
    };

    useEffect(() => {
        if (recipientSuccess) {
            onClose();
            formik.resetForm();
            sendFeedback("Recipient added", "success");
            dispatch(recipientActions.clearLocalRecipientSuccess());

            // Get latest recipients
            dispatch(
                getLocalRecipientAction({
                    params: {
                        pageNo: 0,
                        pageSize: 10,
                    },
                })
            );

            dispatch(recipientActions.clearState("getRecipientDetails"));
            setVerified(false);
        }
    }, [onClose, formik, recipientSuccess, dispatch]);

    // Trigger verification when all required fields are filled
    useEffect(() => {
        const getAccountDetails = async () => {
            try {
                await dispatch(
                    fetchRecipientDetails({
                        channelCode: "1",
                        destinationInstitutionCode: formik.values.bank,
                        accountNumber: formik.values.accountNumber,
                    })
                );
            } catch (error) {
                sendCatchFeedback(error);
            } finally {
                setInternalRetrieval(true);
            }
        };
        if (formik.values.bank && formik.values.accountNumber && formik.values.accountNumber.length === 10) {
            getAccountDetails();
        }
    }, [formik.values.bank, formik.values.accountNumber, dispatch]);

    // Update account name when details are fetched
    useEffect(() => {
        // update only when the retrieval was done from here
        if (internalRetrieval && details && details.accountName) {
            formik.setFieldValue("accountName", details.accountName);
            setVerified(true);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [details, internalRetrieval]);

    useEffect(() => {
        if (retrievalError || (details && !details.accountName)) {
            formik.setFieldValue("accountName", "");
            setVerified(false);
            sendCatchFeedback(retrievalError ?? "Could not retrieve account details", () => {
                dispatch(recipientActions.clearState("getRecipientDetails"));
            });
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [retrievalError, details, dispatch]);

    return (
        <div data-testid="add-recipient-modal">
            <SideDrawer isOpen={open}>
                <div className="flex flex-col justify-between h-full">
                    <div className="border-b border-[#E3E5E8] flex justify-between items-center py-[19px] px-4">
                        <h2 className="text-[20px] leading-[26px] font-semibold text-black">Add a new recipient</h2>
                        <CloseX
                            onClick={() => {
                                onClose();
                                formik.resetForm();
                            }}
                            data-testid="modal-close"
                            color="#90909D"
                        />
                    </div>
                    <form onSubmit={formik.handleSubmit} className="w-full mt-5 flex flex-col h-full px-5">
                        <LabelInput formik={formik} name="accountNumber" label="Account number" className="mb-5" />
                        <Dropdown
                            options={banks?.map((bank) => ({ label: bank.bankName, value: bank.bankCode }))}
                            label="Bank"
                            name="bank"
                            size="sm"
                            value={{
                                label: banks?.find((bank) => bank.bankCode === formik.values.bank)?.bankName ?? "",
                                value: formik.values.bank,
                            }}
                            formik={formik}
                            className="mb-5"
                            isLoading={loadingBanks}
                        />
                        <div className="relative mb-5">
                            <LabelInput
                                formik={formik}
                                name="accountName"
                                label="Account name"
                                placeholder={
                                    retrievingDetails
                                        ? "Verifying account..."
                                        : "Complete all fields above to fetch recipient name"
                                }
                                disabled={true}
                                // readOnly={true}
                            />

                            {retrievingDetails && (
                                <div className="absolute right-4 top-[55%]">
                                    <LoadingIndicator size={16} />
                                </div>
                            )}
                            {!retrievingDetails && verified && (
                                <div className="absolute right-4 top-[55%]">
                                    <VerifiedIcon />
                                </div>
                            )}
                        </div>
                        <CheckboxLabel
                            checked={internalNotifyState}
                            onChange={handleChange}
                            label="Save recipient for future payments"
                        />
                    </form>
                    <div className="flex items-center justify-end w-full gap-3 mt-auto border-t border-t-[#E3E5E8] py-3 px-5">
                        <Button type="button" disabled={recipientLoading} onClick={onClose} variant="outline">
                            Cancel
                        </Button>
                        <Button type="button" loading={recipientLoading} onClick={formik.submitForm}>
                            Add recipient
                        </Button>
                    </div>
                </div>
            </SideDrawer>
        </div>
    );
};

export default AddRecipientModal;
