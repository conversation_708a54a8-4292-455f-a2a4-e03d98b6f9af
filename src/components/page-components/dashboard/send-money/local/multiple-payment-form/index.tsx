"use client";

import { <PERSON><PERSON> } from "@/components/common/buttonv3";
import { sendFeedback } from "@/functions/feedback";
import { formatNumberToNaira } from "@/functions/stringManipulations";
import { getLocalBanksAction } from "@/redux/actions/recipients/local-recipient";
import { sendMultipleTransfer } from "@/redux/actions/sendMoneyActions";
import { getTeamMemberDetails } from "@/redux/actions/transferMfaActions";
import { closeSendMoneyDialog } from "@/redux/features/sendMoneyDialog";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { recipientActions } from "@/redux/slices/recipientsSlice";
import { clearVerifyPin, openVerifyPinModal } from "@/redux/slices/securitySlice";
import { AccountType } from "@/types/standalone";
import autoAnimate from "@formkit/auto-animate";
import { useFormik } from "formik";
import pluralize from "pluralize";
import { Dispatch, SetStateAction, useEffect, useMemo, useRef, useState } from "react";
import * as yup from "yup";
import TransferMfaVerification from "../../../accounts/payments/components/transfer-mfa-verification";
import { LocalRecipientType } from "../../../recipients/type";
import {
    multiplePaymentFormSteps,
    MultiplePaymentFormStepType,
    MultiplePaymentFormType,
    PaymentTypeTitles,
} from "../../data";
import FormStep from "../../form-step";
import FormApproval from "../single-payment-form/form-approval";
import RecurringPaymentModal from "../single-payment-form/modals/recurring-payment-modal";
import SchedulePaymentModal from "../single-payment-form/modals/schedule-payment-modal";
import {
    getCorrespondingIndexForFormStepForMultiple,
    getFrequencyValue,
    getRecurringEndDateFromOccurrences,
} from "../utils";
import FormDetails from "./form-details";
import FormSendList from "./form-send-list";
import FormSummary from "./form-summary";
import AddRecipientModal from "./modals/add-recipient-modal";
import ExistingRecipientModal from "./modals/existing-recipient-modal";
import SuccessModal from "./modals/success-modal";

const MultiplePaymentForm = ({
    setSelectedPaymentType,
    paymentType,
}: {
    setSelectedPaymentType: Dispatch<SetStateAction<PaymentTypeTitles | undefined>>;
    paymentType: PaymentTypeTitles | undefined;
}) => {
    const [formStep, setFormStep] = useState<MultiplePaymentFormStepType>(multiplePaymentFormSteps[0]);
    const parentRef = useRef(null);
    const { loading, success } = useAppSelector((state) => state.sendMoney.sendMultipleTransfer || {});
    const dispatch = useAppDispatch();
    const [recurringPaymentModal, setRecurringPaymentModal] = useState(false);
    const [addRecipientModal, setAddRecipientModal] = useState(false);
    const [schedulePaymentModal, setSchedulePaymentModal] = useState(false);
    const [recipientModal, setRecipientModal] = useState(false);
    const [successModal, setSuccessModal] = useState(false);
    const [selectedRecipients, setSelectedRecipients] = useState<LocalRecipientType[] | []>([]);
    const { selectedAccount } = useAppSelector((state) => state.account);
    const { success: pinSuccess, pin } = useAppSelector((state) => state.security.verifyPin);
    const [pinInitiated, setPinInitiated] = useState(false);
    const { banks } = useAppSelector((state) => state.recipient.getLocalBanks ?? []);
    const { success: teamMemberRetrieved, loading: teamMemberLoading } = useAppSelector(
        (state) => state.transferMfaSlice.getTeamMemberDetails
    );
    const teamMember = useAppSelector((state) => state.transferMfaSlice.teamMember);
    const [MFAVerificationState, setMFAVerificationState] = useState({
        verifiedPin: false,
        verifiedMFA: false,
        pin: "",
    });
    const [accountBalance, setAccountBalance] = useState<undefined | number>(undefined);

    const formik = useFormik<MultiplePaymentFormType>({
        initialValues: {
            bank: "",
            accountName: "",
            amount: "",
            transferType: "INSTANT",
            transferSource: selectedAccount,
            accountNumber: "",
            narration: "",
            requiresApproval: true,

            // For scheduled transfer
            scheduledDate: "",

            // For re-occurring transfer
            reoccurringEndDate: "",
            reoccurringFrequency: "",
            reoccurringStartDate: "",
            reoccurringReminderSchedule: "",
            recurringTenureType: "",
            reoccurringEndOccurrences: "",
        },
        onSubmit: () => {
            dispatch(getTeamMemberDetails());
            setPinInitiated(true);
        },
        validationSchema: useMemo(
            () =>
                yup.object({
                    transferSource: yup
                        .mixed<AccountType>()
                        .required("Transfer source is required")
                        .test(
                            "is-valid-object",
                            "Transfer source must be a valid account",
                            (value) => value !== undefined && value !== null
                        ),
                    transferType: yup
                        .string()
                        .required("Required")
                        .oneOf(
                            ["INSTANT", "RECURRING", "SCHEDULED"],
                            "Accepted values are: INSTANT, RECURRING, SCHEDULED"
                        ),
                    amount: yup
                        .number()
                        .typeError("Amount must be a number")
                        .required("Amount is required")
                        .test(
                            "max-balance",
                            `The amount exceeds your available balance of ${formatNumberToNaira(accountBalance || 0, 2)}`,
                            function (value) {
                                if (accountBalance === undefined) return true; // account balance is still fetching
                                if (value === undefined || value === null) return true; // skip test
                                if (accountBalance === 0) return false; // no money in account
                                return value <= accountBalance; // value must not be greater than balance
                            }
                        ),
                    narration: yup.string().required("Required"),
                }),

            [accountBalance]
        ),
        enableReinitialize: true,
    });

    useEffect(() => {
        if (accountBalance !== undefined && formik.values.amount) {
            formik.setFieldTouched("amount", true, true); // Mark it as touched so yup can validate
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [accountBalance]);

    useEffect(() => {
        if (teamMemberRetrieved && teamMember && pinInitiated) {
            // MFA is setup
            if (teamMember.mfaStatus) {
                // Always require PIN verification
                dispatch(openVerifyPinModal());
            } else {
                return sendFeedback("You need to set up MFA to proceed", "error");
            }
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [teamMemberRetrieved, teamMember, pinInitiated]);

    const submitValues = async (token: string) => {
        await dispatch(
            sendMultipleTransfer(
                selectedRecipients.map((recipient) => {
                    const {
                        reoccurringEndDate,
                        transferType,
                        scheduledDate,
                        reoccurringStartDate,
                        reoccurringFrequency,
                        amount,
                        accountName,
                        narration,
                        accountNumber,
                        transferSource,
                        reoccurringEndOccurrences,
                    } = formik.values;

                    let dateOfFirstPayment = "";

                    if (transferType === "SCHEDULED") {
                        dateOfFirstPayment = new Date(scheduledDate)?.toISOString()?.split("T")[0];
                    } else if (reoccurringStartDate) {
                        dateOfFirstPayment = new Date(reoccurringStartDate)?.toISOString()?.split("T")[0];
                    }

                    let dateOfLastPayment = "";
                    if (scheduledDate || reoccurringEndDate || reoccurringEndOccurrences) {
                        // Recurring payment with end occurrences
                        if (reoccurringEndOccurrences) {
                            dateOfLastPayment = getRecurringEndDateFromOccurrences({
                                occurrences: Number(reoccurringEndOccurrences),
                                frequency: reoccurringFrequency,
                                startDate: reoccurringStartDate,
                            });
                        } else {
                            dateOfLastPayment = new Date(scheduledDate || reoccurringEndDate)
                                ?.toISOString()
                                ?.split("T")[0];
                        }
                    }

                    let reoccurringValue = 1;
                    if (transferType !== "SCHEDULED" && reoccurringStartDate) {
                        if (reoccurringEndOccurrences) {
                            reoccurringValue = Number(reoccurringEndOccurrences);
                        } else if (reoccurringEndDate) {
                            reoccurringValue = getFrequencyValue({
                                endDate: reoccurringEndDate,
                                startDate: reoccurringStartDate,
                                frequency: reoccurringFrequency,
                            });
                        } else {
                            reoccurringValue = 1;
                        }
                    }

                    return {
                        sourceAccount: transferSource?.accountNumber ?? "",
                        sourceAccountName: transferSource?.accountName ?? "",
                        destinationAccount: recipient.accountNumber,
                        destinationAccountName: recipient.accountName,
                        bankCode: banks?.find((bank) => bank.bankName === recipient.bank)?.bankCode ?? "",
                        bankName: recipient.bank,
                        sourceAccountCurrency: transferSource?.currencyCode ?? "NGN",
                        destinationAccountCurrency: "NGN",
                        amount: Number(amount),
                        requestType: transferType,
                        needsApproval: true,
                        narration,
                        frequencyType: reoccurringFrequency || "DAILY", // Default to daily
                        firstPaymentDate: dateOfFirstPayment,
                        lastPaymentDate: dateOfLastPayment,
                        frequencyValue: reoccurringValue,
                        transactionPin: pin,
                        token,
                    };
                })
            )
        );
    };

    const goBackToPreviousStep = () => {
        if (multiplePaymentFormSteps.indexOf(formStep) === 0) {
            setSelectedPaymentType(undefined);
        } else {
            setFormStep(multiplePaymentFormSteps[multiplePaymentFormSteps.indexOf(formStep) - 1]);
        }
    };

    const proceedToNextStep = () => {
        if (multiplePaymentFormSteps.indexOf(formStep) < multiplePaymentFormSteps.length) {
            setFormStep(multiplePaymentFormSteps[multiplePaymentFormSteps.indexOf(formStep) + 1]);
        } else {
            // submit form
            formik.submitForm();
        }
    };

    // Modal controls
    const openScheduleModalFunc = () => {
        setSchedulePaymentModal(true);
    };

    const closeScheduleModalFunc = () => {
        setSchedulePaymentModal(false);
    };
    const openRecipientModalFunc = () => {
        setRecipientModal(true);
    };

    const closeRecipientModalFunc = () => {
        setRecipientModal(false);
    };
    const openRecurringModalFunc = () => {
        setRecurringPaymentModal(true);
    };

    const closeRecurringModalFunc = () => {
        setRecurringPaymentModal(false);
    };

    const openAddRecipientModalFunc = () => {
        setAddRecipientModal(true);
    };

    const closeAddRecipientModalFunc = () => {
        setAddRecipientModal(false);
    };

    useEffect(() => {
        if (parentRef.current) {
            autoAnimate(parentRef.current);
        }
    }, [parentRef]);

    useEffect(() => {
        if (pinInitiated && pinSuccess && pin) {
            setMFAVerificationState({
                verifiedPin: true,
                verifiedMFA: false,
                pin: pin,
            });

            if (teamMember?.mfaStatus) {
                return;
            } else {
                return sendFeedback("You need to set up MFA to proceed", "error");
            }
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [pinSuccess, pin, pinInitiated]);

    useEffect(() => {
        if (success) {
            // Open success modal
            setSuccessModal(true);
            setPinInitiated(false);

            setMFAVerificationState({
                verifiedPin: false,
                verifiedMFA: true,
                pin: "",
            });

            // Clear pin
            dispatch(clearVerifyPin());

            sendFeedback("Transfer successful", "success");
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [success]);

    useEffect(() => {
        dispatch(getLocalBanksAction());

        // Clean up recipient details state when component unmounts
        return () => {
            dispatch(recipientActions.clearState("getRecipientDetails"));
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const handleMfaVerificationDrawer = () => {
        setMFAVerificationState((prev) => ({
            ...prev,
            verifiedPin: false,
            pin: "",
        }));
        setPinInitiated(false);
    };

    return (
        <>
            <div className="flex flex-col lg:flex-row gap-8 pl-primary">
                <FormStep
                    correspondingCheck={() => getCorrespondingIndexForFormStepForMultiple(formStep)}
                    formSteps={multiplePaymentFormSteps}
                />
                <div className="lg:w-[50%] py-8 pr-primary lg:pl-primary">
                    <div ref={parentRef} className="w-full">
                        {formStep === "Select recipients" && (
                            <FormSendList
                                setSelectedRecipients={setSelectedRecipients}
                                selectedRecipients={selectedRecipients}
                                openRecipientModal={openRecipientModalFunc}
                                openAddRecipientModal={openAddRecipientModalFunc}
                            />
                        )}
                        {formStep === "Payment information" && (
                            <FormDetails
                                formik={formik}
                                openScheduleModal={openScheduleModalFunc}
                                selectedRecipients={selectedRecipients}
                                openRecurringModal={openRecurringModalFunc}
                                setAccountBalance={setAccountBalance}
                            />
                        )}
                        {formStep === "Review payment" && (
                            <FormSummary
                                formik={formik}
                                paymentType={paymentType}
                                goToPrevStep={goBackToPreviousStep}
                                selectedRecipients={selectedRecipients}
                            />
                        )}
                    </div>
                    <div className="flex w-full mt-8 gap-3 items-center justify-end pr-primary lg:pr-0">
                        <Button variant="outline" onClick={goBackToPreviousStep} type="button">
                            {formStep === "Select recipients" ? "Go back" : "Previous"}
                        </Button>

                        {formStep === "Review payment" ? (
                            <Button onClick={formik.submitForm} loading={loading || teamMemberLoading} type="button">
                                Send Payment
                            </Button>
                        ) : (
                            <Button
                                onClick={proceedToNextStep}
                                type="button"
                                disabled={
                                    formStep === "Select recipients"
                                        ? selectedRecipients.length < 1
                                        : !(formik.isValid && formik.dirty)
                                }
                            >
                                {formStep === "Select recipients"
                                    ? `Continue with ${selectedRecipients.length} ${pluralize("recipient", selectedRecipients.length)}`
                                    : "Continue"}
                            </Button>
                        )}
                    </div>
                </div>
                {formStep === "Review payment" && formik.values.requiresApproval && (
                    <FormApproval amount={Number((formik.values.amount as number) * selectedRecipients.length) || 0} />
                )}
            </div>
            <RecurringPaymentModal open={recurringPaymentModal} onClose={closeRecurringModalFunc} formik={formik} />
            <AddRecipientModal open={addRecipientModal} onClose={closeAddRecipientModalFunc} />
            <SchedulePaymentModal open={schedulePaymentModal} onClose={closeScheduleModalFunc} formik={formik} />
            <ExistingRecipientModal
                open={recipientModal}
                onClose={closeRecipientModalFunc}
                setSelectedRecipients={setSelectedRecipients}
                selectedRecipients={selectedRecipients}
            />
            <SuccessModal
                open={successModal}
                onClose={() => {
                    setSelectedPaymentType(undefined);
                    formik.resetForm();
                    dispatch(closeSendMoneyDialog());
                    setSuccessModal(false);
                }}
                formik={formik}
                selectedRecipients={selectedRecipients}
            />

            {pinInitiated &&
                MFAVerificationState.verifiedPin &&
                teamMember?.mfaStatus &&
                !MFAVerificationState.verifiedMFA && (
                    <TransferMfaVerification
                        userMfaType={teamMember?.preferredMfaMethod}
                        onClose={handleMfaVerificationDrawer}
                        isOpen
                        onVerified={submitValues}
                        email={teamMember?.email}
                        phoneNumber={teamMember?.phoneNumber}
                    />
                )}
        </>
    );
};

export default MultiplePaymentForm;
