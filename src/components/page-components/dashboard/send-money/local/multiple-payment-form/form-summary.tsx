import { Button } from "@/components/common/buttonv3";
import { formatNumberToNaira } from "@/functions/stringManipulations";
import { useAppSelector } from "@/redux/hooks";
import { FormikProps } from "formik";
import { LocalRecipientType } from "../../../recipients/type";
import { BulkRecipientType, MultiplePaymentFormType, PaymentTypeTitles, RecipientType } from "../../data";
import { calculateTransactionFee, getFrequencyValue } from "../utils";

type Props<T extends MultiplePaymentFormType> = {
    formik: FormikProps<T>;
    paymentType: PaymentTypeTitles | undefined;
    selectedRecipients: LocalRecipientType[] | BulkRecipientType[] | [];
    goToPrevStep: () => void;
};

const FormSummary = <T extends MultiplePaymentFormType>({
    formik,
    goToPrevStep,
    paymentType,
    selectedRecipients,
}: Props<T>) => {
    const { banks } = useAppSelector((state) => state.recipient.getLocalBanks ?? []);

    return (
        <div className="w-full">
            <div className="flex-col flex items-center">
                <div className="flex flex-col items-center">
                    <h3 className="font-semibold text-black text-2xl text-center" data-testid="heading">
                        One last check before sending!
                    </h3>
                </div>
                <div className="w-full flex items-center flex-col gap-2 my-5 text-center">
                    <div className="bg-[#F9F9FA] w-16 h-16 justify-center flex items-center rounded-full font-medium text-subText text-lg">
                        {selectedRecipients.length}
                    </div>
                    <p className="font-semibold text-xl">
                        {formatNumberToNaira(
                            Number(formik.values.amount) ||
                                selectedRecipients.reduce(
                                    (a, b) => a + Number((b as BulkRecipientType).amount || 0),
                                    0
                                ) ||
                                0,
                            2
                        )}
                    </p>
                    <p className="text-subText">{selectedRecipients.length} recipients</p>
                </div>
                <div className="w-full">
                    {/* Payment info */}
                    <div className="mt-8 w-full flex items-center justify-between">
                        <p className="font-semibold">Payment information</p>
                        <Button variant="text-primary" type="button" onClick={goToPrevStep}>
                            Edit
                        </Button>
                    </div>

                    <div className="mt-5 border border-[#E3E5E8] rounded-xl p-5">
                        <div className="flex w-full items-center justify-between gap-1 text-sm">
                            <span className="text-subText">Payment Type</span>
                            <span className="font-medium text-right">{paymentType}</span>
                        </div>
                        <div className="flex w-full items-center justify-between gap-1 text-sm mt-8">
                            <span className="text-subText">
                                {formik.values.transferType !== "RECURRING" ? "Amount" : "Recurring amount"}
                            </span>
                            <span className="font-medium text-right">
                                {formatNumberToNaira(
                                    Number(formik.values.amount) ||
                                        selectedRecipients.reduce(
                                            (a, b) => a + Number((b as BulkRecipientType).amount || 0),
                                            0
                                        ) ||
                                        0,
                                    2
                                )}
                            </span>
                        </div>
                        {paymentType === "Multiple recipients" && formik.values.transferType !== "RECURRING" && (
                            <div className="flex w-full items-center justify-between gap-1 text-sm mt-8">
                                <span className="text-subText">Total Amount</span>
                                <span className="font-medium text-right">
                                    {formatNumberToNaira(
                                        Number(formik.values.amount as number) * selectedRecipients.length
                                    )}
                                </span>
                            </div>
                        )}
                        {formik.values.transferType === "RECURRING" && (
                            <div className="flex w-full items-center justify-between gap-1 text-sm mt-8">
                                <span className="text-subText">Total amount</span>
                                <span className="font-medium">
                                    {formatNumberToNaira(
                                        Number(
                                            paymentType === "Multiple recipients"
                                                ? Number(formik.values.amount as number) * selectedRecipients.length
                                                : selectedRecipients.reduce(
                                                      (a, b) => a + Number((b as BulkRecipientType).amount || 0),
                                                      0
                                                  )
                                        ) *
                                            getFrequencyValue({
                                                frequency: formik.values.reoccurringFrequency,
                                                startDate: formik.values.reoccurringStartDate,
                                                endDate: formik.values.reoccurringEndDate,
                                                endCount: formik.values.reoccurringEndOccurrences,
                                            }),
                                        2
                                    )}
                                </span>
                            </div>
                        )}
                        <div className="flex w-full items-center justify-between gap-1 text-sm mt-8">
                            <span className="text-subText">Fees</span>
                            <span className="font-medium text-right">
                                {paymentType === "Multiple recipients"
                                    ? formatNumberToNaira(
                                          selectedRecipients.reduce(
                                              (a, b) =>
                                                  a +
                                                  calculateTransactionFee({
                                                      amount: formik.values.amount as number,
                                                      destinationBank:
                                                          banks?.find(
                                                              (bank) =>
                                                                  bank.bankName === (b as unknown as RecipientType).bank
                                                          )?.bankCode ?? (b as unknown as RecipientType).bank,
                                                  }),
                                              0
                                          ),
                                          2
                                      )
                                    : formatNumberToNaira(
                                          selectedRecipients.reduce(
                                              (a, b) =>
                                                  a +
                                                  calculateTransactionFee({
                                                      amount: (b as BulkRecipientType).amount,
                                                      destinationBank: (b as BulkRecipientType).bankCode,
                                                  }),
                                              0
                                          ),
                                          2
                                      )}
                                {formik.values.transferType === "RECURRING" && " per payment cycle"}
                            </span>
                        </div>
                        <div className="flex w-full items-center justify-between gap-1 text-sm mt-8">
                            <span className="text-subText">Send from</span>
                            <span className="font-medium text-right">
                                {formik.values.transferSource?.accountName} ・
                                {formik.values.transferSource?.accountNumber}
                            </span>
                        </div>
                        <div className="flex w-full items-center justify-between gap-1 text-sm mt-8">
                            <span className="text-subText">Payment Delivery</span>
                            <span className="font-medium text-right capitalize">
                                {formik.values.transferType?.toLowerCase()}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default FormSummary;
