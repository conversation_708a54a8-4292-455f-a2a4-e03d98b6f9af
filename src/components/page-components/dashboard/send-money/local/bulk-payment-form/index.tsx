"use client";

import { <PERSON><PERSON> } from "@/components/common/buttonv3";
import { sendFeedback } from "@/functions/feedback";
import { formatNumberToNaira } from "@/functions/stringManipulations";
import {
    sendBulkTransfer,
    updateBulkPaymentTemplate,
    updateBulkPaymentTemplateDetails,
} from "@/redux/actions/sendMoneyActions";
import { getTeamMemberDetails } from "@/redux/actions/transferMfaActions";
import { closeSendMoneyDialog } from "@/redux/features/sendMoneyDialog";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { clearVerifyPin, openVerifyPinModal } from "@/redux/slices/securitySlice";
import { GetBulkTemplateType } from "@/redux/types/send-money";
import { AccountType } from "@/types/standalone";
import autoAnimate from "@formkit/auto-animate";
import { useFormik } from "formik";
import { Dispatch, SetStateAction, useEffect, useRef, useState } from "react";
import * as yup from "yup";
import TransferMfaVerification from "../../../accounts/payments/components/transfer-mfa-verification";
import {
    bulkPaymentFormSteps,
    BulkPaymentFormStepType,
    BulkRecipientType,
    MultiplePaymentFormType,
    PaymentTypeTitles,
} from "../../data";
import FormStep from "../../form-step";
import FormDetails from "../multiple-payment-form/form-details";
import FormSummary from "../multiple-payment-form/form-summary";
import FormApproval from "../single-payment-form/form-approval";
import RecurringPaymentModal from "../single-payment-form/modals/recurring-payment-modal";
import SchedulePaymentModal from "../single-payment-form/modals/schedule-payment-modal";
import { getFrequencyValue, getRecurringEndDateFromOccurrences } from "../utils";
import FileUploadConfirmation from "./file-upload-confirmation";
import ProcessedResultsModal from "./modals/processed-results";
import SaveTemplateModal from "./modals/save-template-modal";
import SuccessModal from "./modals/success-modal";
import SelectBulkTemplate from "./select-bulk-template";

const BulkPaymentForm = ({
    setSelectedPaymentType,
    paymentType,
}: {
    setSelectedPaymentType: Dispatch<SetStateAction<PaymentTypeTitles | undefined>>;
    paymentType: PaymentTypeTitles | undefined;
}) => {
    const [formStep, setFormStep] = useState<BulkPaymentFormStepType>(bulkPaymentFormSteps[0]);
    const parentRef = useRef(null);
    const { loading, success } = useAppSelector((state) => state.sendMoney.sendBulkTransfer ?? {});
    const [recurringPaymentModal, setRecurringPaymentModal] = useState(false);
    const [successModal, setSuccessModal] = useState(false);
    const [saveTemplateModal, setSaveTemplateModal] = useState(false);
    const [schedulePaymentModal, setSchedulePaymentModal] = useState(false);
    const [selectedRecipients, setSelectedRecipients] = useState<BulkRecipientType[] | []>([]);
    const [uploadedFile, setUploadedFile] = useState<File | undefined>(undefined);
    const [isUploading, setIsUploading] = useState(false);
    const [isUploadComplete, setIsUploadComplete] = useState(!!uploadedFile);
    const [processedModal, setProcessedModal] = useState(false);
    const [selectedTemplate, setSelectedTemplate] = useState<GetBulkTemplateType | undefined>();
    const { selectedAccount } = useAppSelector((state) => state.account);
    const { success: pinSuccess, pin } = useAppSelector((state) => state.security.verifyPin);
    const [userAccountBalance, setUserAccountBalance] = useState<undefined | number>(undefined);
    const [pinInitiated, setPinInitiated] = useState(false);
    const [shouldUpdateTemplate, setShouldUpdateTemplate] = useState(false);
    const { success: teamMemberSuccess, loading: gettingTeamMember } = useAppSelector(
        (state) => state.transferMfaSlice.getTeamMemberDetails
    );
    const teamMember = useAppSelector((state) => state.transferMfaSlice.teamMember);
    const [mfaState, setMfaState] = useState({
        pinIsVerified: false,
        mfaIsVerified: false,
        pin: "",
    });
    const dispatch = useAppDispatch();

    const formik = useFormik<MultiplePaymentFormType>({
        initialValues: {
            transferSource: selectedAccount,
            accountNumber: "",
            bank: "",
            accountName: "",
            amount: "",
            transferType: "INSTANT",
            narration: "",
            requiresApproval: true,

            // For scheduled transfer
            scheduledDate: "",

            // For re-occurring transfer
            reoccurringFrequency: "",
            reoccurringStartDate: "",
            reoccurringReminderSchedule: "",
            recurringTenureType: "",
            reoccurringEndDate: "",
            reoccurringEndOccurrences: "",
        },
        onSubmit: () => {
            dispatch(getTeamMemberDetails());

            setPinInitiated(true);
        },
        validationSchema: yup.object({
            transferSource: yup
                .mixed<AccountType>()
                .required("Transfer source is required")
                .test(
                    "is-valid-object",
                    "Transfer source must be a valid account",
                    (value) => value !== undefined && value !== null
                ),
            transferType: yup
                .string()
                .required("Required")
                .oneOf(["INSTANT", "RECURRING", "SCHEDULED"], "Accepted values are: INSTANT, RECURRING, SCHEDULED"),
            narration: yup.string().required("Required"),
            amount: yup
                .number()
                .typeError("Amount must be a number")
                .required("Amount is required")
                .test(
                    "max-account-balance",
                    `The amount exceeds your available balance of ${formatNumberToNaira(userAccountBalance || 0, 2)}`,
                    function (value) {
                        if (userAccountBalance === undefined) return true; // account balance isn't fetched
                        if (value === undefined || value === null) return true; // skip the test
                        if (userAccountBalance === 0) return false; // no money in the account
                        return value <= userAccountBalance; // value must not be greater than the balance
                    }
                ),
        }),
        enableReinitialize: true,
    });

    const goToPreviousStep = () => {
        if (bulkPaymentFormSteps.indexOf(formStep) === 0) {
            setSelectedPaymentType(undefined);
        } else {
            setFormStep(bulkPaymentFormSteps[bulkPaymentFormSteps.indexOf(formStep) - 1]);
        }
    };

    const goToNextStep = () => {
        if (bulkPaymentFormSteps.indexOf(formStep) < bulkPaymentFormSteps.length) {
            setFormStep(bulkPaymentFormSteps[bulkPaymentFormSteps.indexOf(formStep) + 1]);
        } else {
            // submit form
            formik.submitForm();
        }
    };

    // Modal controls
    const openScheduleModalFunction = () => {
        setSchedulePaymentModal(true);
    };

    const closeScheduleModalFunction = () => {
        setSchedulePaymentModal(false);
    };

    const openRecurringModalFunction = () => {
        setRecurringPaymentModal(true);
    };

    const closeRecurringModalFunction = () => {
        setRecurringPaymentModal(false);
    };

    const openSaveTemplateModalFunction = () => {
        setSaveTemplateModal(true);
    };

    const closeSaveTemplateModalFunction = () => {
        setSaveTemplateModal(false);
    };

    const retrieveCorrespondingIndexForFormStep = () => {
        if (formStep === "Prepare file") {
            return 0;
        }
        if (formStep === "Payment information") {
            return 1;
        }
        if (formStep === "Review payment") {
            return 2;
        }

        return bulkPaymentFormSteps.length;
    };

    useEffect(() => {
        if (pinInitiated && teamMemberSuccess && teamMember) {
            // MFA is setup
            if (teamMember.mfaStatus) {
                // Always require PIN verification
                dispatch(openVerifyPinModal());
            } else {
                return sendFeedback("You need to set up MFA to proceed", "error");
            }
        }
    }, [teamMemberSuccess, teamMember, dispatch, pinInitiated]);

    const submitValues = async (token: string) => {
        await dispatch(
            sendBulkTransfer(
                selectedRecipients.map((recipient) => {
                    const {
                        transferType,
                        narration,
                        reoccurringStartDate,
                        transferSource,
                        reoccurringEndDate,
                        scheduledDate,
                        reoccurringFrequency,
                        reoccurringEndOccurrences,
                    } = formik.values;

                    const { destinationAccount, destinationAccountName, bankCode, bankName, amount } = recipient;

                    let firstPayment = "";

                    if (transferType === "SCHEDULED") {
                        firstPayment = new Date(scheduledDate)?.toISOString()?.split("T")[0];
                    } else if (reoccurringStartDate) {
                        firstPayment = new Date(reoccurringStartDate)?.toISOString()?.split("T")[0];
                    }

                    let lastPayment = "";
                    if (scheduledDate || reoccurringEndDate || reoccurringEndOccurrences) {
                        // Recurring payment with end occurrences
                        if (reoccurringEndOccurrences) {
                            lastPayment = getRecurringEndDateFromOccurrences({
                                occurrences: Number(reoccurringEndOccurrences),
                                frequency: reoccurringFrequency,
                                startDate: reoccurringStartDate,
                            });
                        } else {
                            lastPayment = new Date(scheduledDate || reoccurringEndDate)?.toISOString()?.split("T")[0];
                        }
                    }

                    let continuity = 1;
                    if (transferType !== "SCHEDULED" && reoccurringStartDate) {
                        if (reoccurringEndOccurrences) {
                            continuity = Number(reoccurringEndOccurrences);
                        } else if (reoccurringEndDate) {
                            continuity = getFrequencyValue({
                                endDate: reoccurringEndDate,
                                startDate: reoccurringStartDate,
                                frequency: reoccurringFrequency,
                            });
                        } else {
                            continuity = 1;
                        }
                    }

                    return {
                        sourceAccount: transferSource?.accountNumber.toString() ?? "",
                        sourceAccountName: transferSource?.accountName ?? "",
                        destinationAccount: destinationAccount.toString(),
                        destinationAccountName,
                        bankCode,
                        bankName,
                        sourceAccountCurrency: "NGN",
                        destinationAccountCurrency: "NGN",
                        amount,
                        requestType: transferType,
                        needsApproval: true,
                        narration: narration,
                        frequencyType: reoccurringFrequency || "DAILY", // Default to daily
                        firstPaymentDate: firstPayment,
                        lastPaymentDate: lastPayment,
                        frequencyValue: continuity,
                        transactionPin: pin,
                        token,
                    };
                })
            )
        );
    };

    useEffect(() => {
        if (parentRef.current) {
            autoAnimate(parentRef.current);
        }
    }, [parentRef]);

    useEffect(() => {
        if (pinInitiated && pinSuccess && pin) {
            setMfaState({
                pinIsVerified: true,
                mfaIsVerified: false,
                pin: pin,
            });

            if (teamMember?.mfaStatus) {
                return;
            } else {
                return sendFeedback("You need to set up MFA to proceed", "error");
            }
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [pinSuccess, pin, pinInitiated, teamMember?.mfaStatus]);

    useEffect(() => {
        if (success) {
            setPinInitiated(false);

            setMfaState({
                pinIsVerified: false,
                mfaIsVerified: true,
                pin: "",
            });

            // Clear pin
            dispatch(clearVerifyPin());

            // Open success modal
            setSuccessModal(true);
            if (selectedTemplate && !shouldUpdateTemplate) {
                // Update the template last used date
                dispatch(updateBulkPaymentTemplate(selectedTemplate));
            } else if (selectedTemplate && shouldUpdateTemplate) {
                // Upload the template file contents
                dispatch(updateBulkPaymentTemplateDetails(selectedTemplate));
            } else {
                // Open template save modal
                openSaveTemplateModalFunction();
            }

            sendFeedback("Successfully initiated bulk transfer", "success");
        }
    }, [dispatch, selectedTemplate, shouldUpdateTemplate, success]);

    const handleMfaDrawer = () => {
        setMfaState((prev) => ({
            ...prev,
            pinIsVerified: false,
            pin: "",
        }));
        setPinInitiated(false);
    };

    return (
        <>
            <div className="flex flex-col lg:flex-row gap-8 pl-primary">
                <FormStep correspondingCheck={retrieveCorrespondingIndexForFormStep} formSteps={bulkPaymentFormSteps} />
                <div className="lg:w-[50%] py-8 pr-primary lg:pl-primary">
                    <div ref={parentRef} className="w-full">
                        {formStep === "Prepare file" && !uploadedFile && (
                            <SelectBulkTemplate
                                setIsUploadComplete={setIsUploadComplete}
                                setIsUploading={setIsUploading}
                                isUploading={isUploading}
                                setUploadedFile={setUploadedFile}
                                uploadedFile={uploadedFile}
                                setSelectedRecipients={setSelectedRecipients}
                                setProcessedModal={setProcessedModal}
                                setSelectedTemplate={setSelectedTemplate}
                            />
                        )}
                        {formStep === "Prepare file" && uploadedFile && (
                            <FileUploadConfirmation
                                selectedRecipients={selectedRecipients}
                                setUploadedFile={setUploadedFile}
                                setSelectedRecipients={setSelectedRecipients}
                                setProcessedModal={setProcessedModal}
                            />
                        )}
                        {formStep === "Payment information" && (
                            <FormDetails
                                formik={formik}
                                openScheduleModal={openScheduleModalFunction}
                                selectedRecipients={selectedRecipients}
                                openRecurringModal={openRecurringModalFunction}
                                setAccountBalance={setUserAccountBalance}
                                accountBalance={userAccountBalance}
                                type="bulk"
                            />
                        )}
                        {formStep === "Review payment" && (
                            <FormSummary
                                formik={formik}
                                paymentType={paymentType}
                                goToPrevStep={goToPreviousStep}
                                selectedRecipients={selectedRecipients}
                            />
                        )}
                    </div>
                    {formStep !== "Prepare file" && (
                        <div className="flex w-full mt-8 gap-3 items-center justify-end">
                            <Button variant="outline" onClick={goToPreviousStep}>
                                Previous
                            </Button>
                            {formStep !== "Review payment" ? (
                                <Button
                                    onClick={goToNextStep}
                                    disabled={
                                        selectedRecipients.length === 0 ||
                                        !(formik.isValid && formik.dirty) ||
                                        userAccountBalance === undefined
                                    }
                                >
                                    Continue
                                </Button>
                            ) : (
                                <Button onClick={formik.submitForm} loading={loading || gettingTeamMember}>
                                    Send Payment
                                </Button>
                            )}
                        </div>
                    )}
                </div>
                {/* Require Approval */}
                {formStep === "Review payment" && formik.values.requiresApproval && (
                    <FormApproval
                        amount={
                            selectedRecipients.reduce((a, b) => a + Number((b as BulkRecipientType).amount || 0), 0) ||
                            0
                        }
                    />
                )}
            </div>
            <RecurringPaymentModal
                open={recurringPaymentModal}
                onClose={closeRecurringModalFunction}
                formik={formik}
                isBulk
                bulkAmount={formatNumberToNaira(
                    selectedRecipients.reduce((a, b) => a + Number((b as BulkRecipientType).amount || 0), 0) || 0,
                    2
                )}
            />
            <SchedulePaymentModal open={schedulePaymentModal} onClose={closeScheduleModalFunction} formik={formik} />
            <SaveTemplateModal
                open={saveTemplateModal}
                onClose={closeSaveTemplateModalFunction}
                selectedRecipients={selectedRecipients}
                setSelectedTemplate={setSelectedTemplate}
            />
            <ProcessedResultsModal
                isOpen={processedModal}
                onClose={() => setProcessedModal(false)}
                setSelectedRecipients={setSelectedRecipients}
                selectedRecipients={selectedRecipients}
                goToNextStep={goToNextStep}
                selectedTemplate={selectedTemplate}
                setShouldUpdateTemplate={setShouldUpdateTemplate}
                shouldUpdateTemplate={shouldUpdateTemplate}
            />
            <SuccessModal
                open={successModal}
                onClose={() => {
                    formik.resetForm();
                    setSuccessModal(false);
                    dispatch(closeSendMoneyDialog());
                    setSelectedPaymentType(undefined);
                }}
                selectedRecipients={selectedRecipients}
                selectedTemplate={selectedTemplate}
                setSelectedTemplate={setSelectedTemplate}
            />

            {pinInitiated && mfaState.pinIsVerified && teamMember?.mfaStatus && !mfaState.mfaIsVerified && (
                <TransferMfaVerification
                    userMfaType={teamMember?.preferredMfaMethod}
                    onClose={handleMfaDrawer}
                    isOpen
                    onVerified={submitValues}
                    email={teamMember?.email}
                    phoneNumber={teamMember?.phoneNumber}
                />
            )}
        </>
    );
};

export default BulkPaymentForm;
