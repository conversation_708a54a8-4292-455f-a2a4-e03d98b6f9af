import { Button } from "@/components/common/buttonv3";
import LoadingIndicator from "@/components/common/loading-indicator";
import { formatNumberToNaira, getNameInitials, maskNumber } from "@/functions/stringManipulations";
import { useTransactionFees } from "@/hooks/useTransactionFees";
import { useAppSelector } from "@/redux/hooks";
import { FormikProps } from "formik";
import { PaymentTypeTitles, SinglePaymentFormType } from "../../data";
import TransferTypeIndicator from "../transfer-type-indicator";
import { getFrequencyValue } from "../utils";

type Props<T extends SinglePaymentFormType> = {
    formik: FormikProps<T>;
    paymentType: PaymentTypeTitles | undefined;
    goToPrevStep: () => void;
};

const FormSummary = <T extends SinglePaymentFormType>({ formik, goToPrevStep, paymentType }: Props<T>) => {
    const { fees, loading } = useTransactionFees({
        amount: Number(formik.values.amount),
        bankCode: formik.values.bank,
    });
    const { banks } = useAppSelector((state) => state.recipient.getLocalBanks ?? []);

    return (
        <div className="w-full">
            <div className="flex-col flex items-center">
                <div className="flex flex-col items-center">
                    <h3 className="font-semibold text-black text-2xl text-center" data-testid="heading">
                        One last check before sending!
                    </h3>
                </div>
                <div className="mt-12 w-full">
                    {/* Recipient Summary */}
                    <p className="text-black font-semibold mb-5">Recipient</p>
                    {formik.values.accountNumber && (
                        <div className="w-full rounded-xl flex items-center gap-2 justify-between text-left p-4 border border-[#E3E5E8]">
                            <div className="flex items-center gap-4">
                                <div className="w-10 h-10 flex items-center justify-center rounded-full text-subText font-medium bg-[#F9F9FA]">
                                    {getNameInitials(formik.values.accountName)}
                                </div>
                                <div className="flex flex-col">
                                    <p className="text-sm font-medium">{formik.values.accountName}</p>
                                    <p className="text-subText text-sm">
                                        {banks?.find((bank) => bank.bankCode === formik.values.bank)?.bankName ??
                                            formik.values.bank}{" "}
                                        ・{maskNumber(formik.values.accountNumber)}
                                    </p>
                                </div>
                            </div>
                            <Button
                                variant="primary-ghost"
                                style={{
                                    borderRadius: "20px",
                                }}
                                type="button"
                                onClick={goToPrevStep}
                            >
                                Change
                            </Button>
                        </div>
                    )}

                    {/* Payment info */}
                    <div className="mt-8 w-full flex items-center justify-between">
                        <p className="font-semibold">Payment information</p>
                        <Button variant="text-primary" type="button" onClick={goToPrevStep}>
                            Edit
                        </Button>
                    </div>

                    <div className="mt-5 border border-[#E3E5E8] rounded-xl p-5">
                        <div className="flex w-full items-center justify-between gap-1 text-sm">
                            <span className="text-subText">Payment Type</span>
                            <span className="font-medium">{paymentType}</span>
                        </div>
                        <div className="flex w-full items-center justify-between gap-1 text-sm mt-8">
                            <span className="text-subText">Payment Delivery</span>
                            <span className="font-medium capitalize">{formik.values.transferType?.toLowerCase()}</span>
                        </div>
                        <div className="flex w-full items-center justify-between gap-1 text-sm mt-8">
                            <span className="text-subText">
                                {formik.values.transferType !== "RECURRING" ? "Amount" : "Recurring amount"}
                            </span>
                            <span className="font-medium">
                                {formatNumberToNaira(Number(formik.values.amount || ""), 2)}
                            </span>
                        </div>
                        {formik.values.transferType === "RECURRING" && (
                            <div className="flex w-full items-center justify-between gap-1 text-sm mt-8">
                                <span className="text-subText">Total amount</span>
                                <span className="font-medium">
                                    {formatNumberToNaira(
                                        Number(formik.values.amount || "") *
                                            getFrequencyValue({
                                                frequency: formik.values.reoccurringFrequency,
                                                endCount: formik.values.reoccurringEndOccurrences,
                                                endDate: formik.values.reoccurringEndDate,
                                                startDate: formik.values.reoccurringStartDate,
                                            }),
                                        2
                                    )}
                                </span>
                            </div>
                        )}

                        <div className="flex w-full items-center justify-between gap-1 text-sm mt-8">
                            <span className="text-subText">Fees</span>
                            <span className="font-medium">
                                {loading ? <LoadingIndicator size={10} /> : formatNumberToNaira(fees, 2)}
                                {!loading && formik.values.transferType === "RECURRING" && " per payment cycle"}
                            </span>
                        </div>
                        <div className="flex w-full items-center justify-between gap-1 text-sm mt-8">
                            <span className="text-subText">Send from</span>
                            <span className="font-medium">
                                {formik.values.transferSource?.accountName} ・
                                {formik.values.transferSource?.accountNumber}
                            </span>
                        </div>
                    </div>

                    <TransferTypeIndicator formik={formik} />
                </div>
            </div>
        </div>
    );
};

export default FormSummary;
