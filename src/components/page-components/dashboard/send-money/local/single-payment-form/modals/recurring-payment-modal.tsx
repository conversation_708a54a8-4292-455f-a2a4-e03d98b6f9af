"use client";

import AmountInput from "@/components/common/amount-input";
import { But<PERSON> } from "@/components/common/buttonv3";
import CustomModal from "@/components/common/custom-modal";
import Dropdown from "@/components/common/dropdown";
import { Label } from "@/components/common/label";
import LabelInput from "@/components/common/label-input";
import { RadioGroup, RadioGroupItem } from "@/components/common/radio-group";
import { FormikProps } from "formik";
import {
    MultiplePaymentFormType,
    recurringFrequencyOptions,
    recurringReminderScheduleOptions,
    SinglePaymentFormType,
} from "../../../data";

type Props<T extends SinglePaymentFormType | MultiplePaymentFormType> = {
    formik: FormikProps<T>;
    open: boolean;
    onClose: () => void;
    isBulk?: boolean;
    bulkAmount?: string;
};

const RecurringPaymentModal = <T extends SinglePaymentFormType | MultiplePaymentFormType>({
    open,
    onClose,
    formik,
    isBulk,
    bulkAmount,
}: Props<T>) => {
    const closeModalAndClearState = () => {
        formik.setFieldValue("reoccurringFrequency", "");
        formik.setFieldValue("reoccurringStartDate", "");
        formik.setFieldValue("reoccurringEndDate", "");
        formik.setFieldValue("recurringTenureType", "");
        formik.setFieldValue("reoccurringReminderSchedule", "");
        formik.setFieldValue("reoccurringEndOccurrences", "");
        formik.setFieldValue("transferType", "INSTANT");
        onClose();
    };
    return (
        <CustomModal
            isOpen={open}
            onRequestClose={closeModalAndClearState}
            width="482px"
            title="Set up recurring mandate"
        >
            <p className="text-center text-subText  text-sm mb-[56px]">
                Create an automatic payment schedule to{" "}
                <span className="text-black font-semibold">{formik.values.accountName}</span>
            </p>

            <div className="w-full">
                {!isBulk ? (
                    <AmountInput<T> formik={formik} className="mb-5" label="Recurring amount" name="amount" />
                ) : (
                    <LabelInput
                        useFormik={false}
                        value={bulkAmount}
                        className="mb-5"
                        label="Recurring amount"
                        name="amount"
                        disabled
                        readOnly
                    />
                )}
                <Dropdown
                    options={recurringFrequencyOptions.map((item) => ({ label: item.label, value: item.value }))}
                    name="reoccurringFrequency"
                    label="Frequency"
                    size="sm"
                    className="mb-5"
                    formik={formik}
                    value={{
                        label: recurringFrequencyOptions.find(
                            (item) => item.value === formik.values.reoccurringFrequency
                        )?.label,
                        value: formik.values.reoccurringFrequency,
                    }}
                />
                <LabelInput
                    formik={formik}
                    name="reoccurringStartDate"
                    label="Start date"
                    className="mb-5"
                    type="date"
                    min={new Date().toISOString().split("T")[0]}
                />
                <Dropdown
                    options={recurringReminderScheduleOptions.map((item) => ({ label: item, value: item }))}
                    name="reoccurringReminderSchedule"
                    label="Set early reminder"
                    size="sm"
                    className="mb-8"
                    formik={formik}
                    value={{
                        label: formik.values.reoccurringReminderSchedule,
                        value: formik.values.reoccurringReminderSchedule,
                    }}
                />
                <p className="text-black font-semibold text-sm mb-[45px]">When should this payment end?</p>

                <RadioGroup
                    key={formik.values.recurringTenureType}
                    value={formik.values.recurringTenureType}
                    className="flex flex-col gap-[45px]"
                    onValueChange={(value) => {
                        formik.setFieldValue("recurringTenureType", value);
                    }}
                >
                    {["date", "occurrence"].map((item) => (
                        <div key={item} className="w-full flex items-center justify-between gap-2">
                            <div className="flex items-center space-x-2 w-fit">
                                <RadioGroupItem value={item} id={item} />
                                <Label htmlFor={item}>
                                    {item === "date" && "Specify date"}
                                    {item === "occurrence" && "End after"}
                                </Label>
                            </div>
                            {item === "date" && (
                                <LabelInput
                                    formik={formik}
                                    name="reoccurringEndDate"
                                    type="date"
                                    min={new Date().toISOString().split("T")[0]}
                                    disabled={formik.values.recurringTenureType !== "date"}
                                />
                            )}
                            {item === "occurrence" && (
                                <div className="flex items-center gap-4">
                                    <LabelInput
                                        formik={formik}
                                        name="reoccurringEndOccurrences"
                                        type="number"
                                        className="!w-[100px] !h-11"
                                        disabled={formik.values.recurringTenureType !== "occurrence"}
                                    />
                                    <div className="w-full text-black font-medium p-3 h-11 py-0 flex items-center  border border-[#DBDBE1] rounded-lg">
                                        Occurrences
                                    </div>
                                </div>
                            )}
                        </div>
                    ))}
                </RadioGroup>

                <div className="flex w-full mt-10 gap-3 items-center justify-end ">
                    <Button variant="outline" onClick={closeModalAndClearState} type="button">
                        Cancel
                    </Button>
                    <Button
                        onClick={onClose}
                        type="button"
                        disabled={
                            !formik.values.reoccurringFrequency ||
                            !formik.values.reoccurringStartDate ||
                            !formik.values.recurringTenureType ||
                            (formik.values.recurringTenureType === "date" && !formik.values.reoccurringEndDate) ||
                            (formik.values.recurringTenureType === "occurrence" &&
                                !formik.values.reoccurringEndOccurrences)
                        }
                    >
                        Confirm
                    </Button>
                </div>
            </div>
        </CustomModal>
    );
};

export default RecurringPaymentModal;
