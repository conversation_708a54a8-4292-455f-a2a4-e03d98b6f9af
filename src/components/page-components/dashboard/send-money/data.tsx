import { AccountType } from "@/types/standalone";
import { ReactNode } from "react";
import { BulkPaymentIcon, MultipleRecipientIcon, SinglePaymentIcon } from "./icons";

export type PaymentTypeTitles = "Single payment" | "Multiple recipients" | "Bulk payment";

export type PaymentType = {
    icon: ReactNode;
    title: PaymentTypeTitles;
    description: string;
    onClick: () => void;
};

export const getPaymentTypes: ({
    clickHandler,
}: {
    clickHandler: (title: PaymentTypeTitles) => void;
}) => PaymentType[] = ({ clickHandler }) => [
    {
        title: "Single payment",
        description: "Send an instant, scheduled or recurring payment to a single person or business",
        icon: <SinglePaymentIcon />,
        onClick: () => clickHandler("Single payment"),
    },
    {
        title: "Multiple recipients",
        description: "Pay up to 15 people at once without uploading a CSV template.",
        icon: <MultipleRecipientIcon />,
        onClick: () => clickHandler("Multiple recipients"),
    },
    {
        title: "Bulk payment",
        description: "Download our CSV template and pay up to 1,000 people or businesses all at once.",
        icon: <BulkPaymentIcon />,
        onClick: () => clickHandler("Bulk payment"),
    },
];

// Single Payment
export type SinglePaymentFormStepType = "Payment information" | "Review payment";

export const singlePaymentFormSteps: SinglePaymentFormStepType[] = ["Payment information", "Review payment"];

export const recurringFrequencyOptions = [
    { label: "Daily", value: "DAILY" },
    { label: "Weekly", value: "WEEKLY" },
    { label: "Monthly", value: "MONTHLY" },
    { label: "Yearly", value: "YEARLY" },
];

export const recurringReminderScheduleOptions = ["1 day before", "2 days before", "1 week before"];

// Multiple payment
export type MultiplePaymentFormStepType = "Select recipients" | "Payment information" | "Review payment";

export const multiplePaymentFormSteps: MultiplePaymentFormStepType[] = [
    "Select recipients",
    "Payment information",
    "Review payment",
];

// Bulk payment
export type BulkPaymentFormStepType = "Prepare file" | "Payment information" | "Review payment";

export const bulkPaymentFormSteps: BulkPaymentFormStepType[] = [
    "Prepare file",
    "Payment information",
    "Review payment",
];

export type ExtractedBulkFileData = {
    ToAccount: string;
    ToAccountName: string;
    ToBankCode: number;
    ToBankName: string;
    Amount: number;
    Narration: string;
    data_id: string;
};

export type BulkRecipientType = {
    destinationAccount: string;
    destinationAccountName: string;
    bankCode: string;
    bankName: string;
    narration?: string;
    amount: number;
    data_id: string;
    error?: string;
};

export const recipientFilters = ["Local", "Foreign", "Bills"] as const;

export interface RecipientType {
    accountName: string;
    dateAdded: string;
    accountNumber: string;
    maskedAccountNumber: string;
    bank: string;
    accountType: string;
    recipientAddress?: string;
    bankAddress?: string;
    upcomingPayment?: {
        amount: string;
        date: string;
    };
}

export type FilterType = (typeof recipientFilters)[number];

// Constants to reduce duplication
const ACCOUNT_TYPES = {
    CORPORATE: "Corporate",
    BUSINESS: "Business",
    PERSONAL: "Personal",
} as const;

const BANKS = {
    BOA: {
        name: "Bank of America",
        address: "715, Ash Dr. San Jose, South Dakota 83475",
    },
    GT: {
        name: "GT Bank",
    },
    FIRST: {
        name: "First Bank",
    },
    ACCESS: {
        name: "Access Bank",
    },
    ZENITH: {
        name: "Zenith Bank",
    },
    HSBC: {
        name: "HSBC Bank",
        address: "8 Canada Square, London E14 5HQ, United Kingdom",
    },
} as const;

// Helper function to mask account number
const maskAccountNumber = (accountNumber: string): string => `****${accountNumber.slice(-4)}`;

// Helper function to create upcoming payment
const createUpcomingPayment = (amount: string, date: string) => ({
    amount,
    date,
});

export const sampleRecipients: RecipientType[] = [
    {
        accountName: "Telfar Global",
        bank: BANKS.BOA.name,
        accountNumber: "**********",
        maskedAccountNumber: maskAccountNumber("**********"),
        accountType: ACCOUNT_TYPES.CORPORATE,
        dateAdded: "23 Mar, 2024",
        recipientAddress: "972, Westheimer Rd. Santa Ana, Illinois, 85486, United States",
        bankAddress: BANKS.BOA.address,
        upcomingPayment: createUpcomingPayment("₦295,700.00", "21 April, 2025"),
    },
    {
        accountName: "Dangote Farms",
        bank: BANKS.GT.name,
        accountNumber: "**********",
        maskedAccountNumber: maskAccountNumber("**********"),
        accountType: ACCOUNT_TYPES.CORPORATE,
        dateAdded: "23 Mar, 2024",
        upcomingPayment: createUpcomingPayment("₦295,700.00", "21 April, 2025"),
    },
    {
        accountName: "Adenuga Holdings",
        bank: BANKS.FIRST.name,
        accountNumber: "**********",
        maskedAccountNumber: maskAccountNumber("**********"),
        accountType: ACCOUNT_TYPES.CORPORATE,
        dateAdded: "15 Feb, 2024",
    },
    {
        accountName: "Tech Solutions Ltd",
        bank: BANKS.ACCESS.name,
        accountNumber: "**********",
        maskedAccountNumber: maskAccountNumber("**********"),
        accountType: ACCOUNT_TYPES.BUSINESS,
        dateAdded: "10 Jan, 2024",
        recipientAddress: "Mainzer Landstraße 11-17, 60329 Frankfurt, Germany",
        bankAddress: "Taunusanlage 12, 60325 Frankfurt am Main, Germany",
        upcomingPayment: createUpcomingPayment("₦150,000.00", "30 March, 2024"),
    },
    {
        accountName: "Sarah Johnson",
        bank: BANKS.ZENITH.name,
        accountNumber: "**********",
        maskedAccountNumber: maskAccountNumber("**********"),
        accountType: ACCOUNT_TYPES.PERSONAL,
        dateAdded: "5 Mar, 2024",
        upcomingPayment: createUpcomingPayment("₦50,000.00", "15 April, 2024"),
    },
    {
        accountName: "Global Traders Inc",
        bank: BANKS.HSBC.name,
        accountNumber: "**********",
        maskedAccountNumber: maskAccountNumber("**********"),
        accountType: ACCOUNT_TYPES.CORPORATE,
        dateAdded: "20 Feb, 2024",
        recipientAddress: "456 Oxford Street, London, W1C 1AP, United Kingdom",
        bankAddress: BANKS.HSBC.address,
    },
    {
        accountName: "Green Energy Solutions",
        bank: "Fidelity Bank",
        accountNumber: "**********",
        maskedAccountNumber: maskAccountNumber("**********"),
        accountType: ACCOUNT_TYPES.BUSINESS,
        dateAdded: "12 Jan, 2024",
        upcomingPayment: createUpcomingPayment("₦750,000.00", "1 April, 2024"),
    },
    {
        accountName: "Michael Okonkwo",
        bank: "Stanbic IBTC",
        accountNumber: "**********",
        maskedAccountNumber: maskAccountNumber("**********"),
        accountType: ACCOUNT_TYPES.PERSONAL,
        dateAdded: "8 Mar, 2024",
    },
    {
        accountName: "Sunshine Schools",
        bank: "Union Bank",
        accountNumber: "**********",
        maskedAccountNumber: maskAccountNumber("**********"),
        accountType: ACCOUNT_TYPES.BUSINESS,
        dateAdded: "25 Feb, 2024",
        upcomingPayment: createUpcomingPayment("₦425,000.00", "10 April, 2024"),
    },
    {
        accountName: "Lagos Logistics",
        bank: "Polaris Bank",
        accountNumber: "**********",
        maskedAccountNumber: maskAccountNumber("**********"),
        accountType: ACCOUNT_TYPES.CORPORATE,
        dateAdded: "18 Jan, 2024",
    },
    {
        accountName: "Healthcare Plus",
        bank: "Wema Bank",
        accountNumber: "**********",
        maskedAccountNumber: maskAccountNumber("**********"),
        accountType: ACCOUNT_TYPES.BUSINESS,
        dateAdded: "3 Mar, 2024",
        upcomingPayment: createUpcomingPayment("₦180,000.00", "25 March, 2024"),
    },
];

export const sampleBillRecipients: RecipientType[] = [
    {
        accountName: "Data",
        bank: "MTN Data Plan",
        accountNumber: "***********",
        maskedAccountNumber: "*****75412",
        accountType: "Mobile",
        dateAdded: "2024-02-09",
        recipientAddress: "Lagos, Nigeria",
        upcomingPayment: {
            amount: "5000",
            date: "2024-03-01",
        },
    },
    {
        accountName: "Julie’s Airtime",
        bank: "Airtel Direct Top Up",
        accountNumber: "***********",
        maskedAccountNumber: "*****75412",
        accountType: "Mobile",
        dateAdded: "2024-01-15",
        recipientAddress: "Abuja, Nigeria",
    },
    {
        accountName: "Electricity",
        bank: "EKEDC (Prepaid)",
        accountNumber: "***********",
        maskedAccountNumber: "*****06793",
        accountType: "Utility",
        dateAdded: "2023-12-20",
        recipientAddress: "Ikeja, Lagos, Nigeria",
        bankAddress: "EKEDC HQ, Lagos, Nigeria",
        upcomingPayment: {
            amount: "12000",
            date: "2024-02-28",
        },
    },
    {
        accountName: "Cable TV",
        bank: "DSTV (Compact Plus)",
        accountNumber: "***********",
        maskedAccountNumber: "*****06793",
        accountType: "Subscription",
        dateAdded: "2023-11-10",
        recipientAddress: "Port Harcourt, Nigeria",
        upcomingPayment: {
            amount: "10800",
            date: "2024-03-05",
        },
    },
];

export type SinglePaymentFormType = {
    transferSource: AccountType | undefined;
    accountNumber: string;
    bank: string;
    accountName: string;
    amount: string | number;
    narration: string;
    transferType: "INSTANT" | "RECURRING" | "SCHEDULED";
    requiresApproval: boolean;

    // For recurring
    reoccurringFrequency: string;
    reoccurringStartDate: string;
    reoccurringReminderSchedule: string;
    recurringTenureType: string;
    reoccurringEndDate: string;
    reoccurringEndOccurrences: string;

    // For scheduling
    scheduledDate: string;
};

export type MultiplePaymentFormType = {
    transferSource: AccountType | undefined;
    amount: string | number;
    narration: string;
    transferType: "INSTANT" | "RECURRING" | "SCHEDULED";
    accountNumber: string;
    bank: string;
    accountName: string;
    requiresApproval: boolean;

    // For recurring
    reoccurringFrequency: string;
    reoccurringStartDate: string;
    reoccurringReminderSchedule: string;
    recurringTenureType: string;
    reoccurringEndDate: string;
    reoccurringEndOccurrences: string;

    // For scheduling
    scheduledDate: string;
};

export type ApprovalModuleType = "TRANSFER" | "BILL_PAYMENT";

export type ApprovalBodyType = { amount: number; type: ApprovalModuleType };
