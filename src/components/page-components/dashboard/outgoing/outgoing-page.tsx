"use client";

import React, { useState, useRef, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import TabSwitch from "@/components/common/tab-switch";
import { TransferTab } from "./types";
import { TRANSFER_TABS } from "./utils/constants";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { openSendMoneyDialog } from "@/redux/features/sendMoneyDialog";
import { useOutgoingFilters } from "./hooks/useOutgoingFilters";
import {
    fetchTransfers,
    fetchScheduledTransfers,
    fetchRecurringTransfers,
    OutgoingRequestParams,
} from "@/redux/actions/transferActions";
import { FilterSection } from "./filters/filter-section";
import { filterOutgoing } from "./utils/filterUtils";
import OutgoingContent from "./list/outgoing-content";

export default function OutgoingPage() {
    // State and hooks
    const [activeTab, setActiveTab] = useState<TransferTab>("instant");
    const amountDropdownRef = useRef<HTMLDivElement>(null);
    const searchParams = useSearchParams();

    // Redux hooks
    const dispatch = useAppDispatch();
    const { transfers } = useAppSelector((state) => state.transfer);
    const { scheduledTransfers } = useAppSelector((state) => state.transfer);
    const { recurringTransfers } = useAppSelector((state) => state.transfer);

    // Filter hooks - now passing activeTab
    const {
        currentFilters,
        tempFilters,
        searchInput,
        dateFilterLabel,
        isFilterOpen,
        isAmountDropdownOpen,
        amountFilterOption,
        tempAmountValues,
        setTempFilters,
        setIsFilterOpen,
        setIsAmountDropdownOpen,
        setAmountFilterOption,
        handleAmountValueChange,
        applyAmountFilter,
        applyCustomDateFilter,
        handleDateFilterSelect,
        onApplyFilter,
        onClearAll,
        onSearch,
    } = useOutgoingFilters(activeTab);

    // Handlers
    const handleSendMoney = () => {
        dispatch(openSendMoneyDialog());
    };

    const handleTabChange = (tab: string) => {
        setActiveTab(tab as TransferTab);
        // Reset to page 1 when switching tabs
        const params = new URLSearchParams(searchParams.toString());
        params.set("page", "1");
        window.history.replaceState({}, "", `${window.location.pathname}?${params.toString()}`);
    };

    // Get pagination parameters from URL
    const currentPage = Number(searchParams.get("page")) || 1;
    const pageSize = Number(searchParams.get("size")) || 20;

    // Fetch transfers data when component mounts, when tab changes, or when pagination changes
    useEffect(() => {
        const params: OutgoingRequestParams = {
            pageNo: currentPage - 1, // API is zero-indexed
            pageSize: pageSize,
            isUserInitiated: false,
        };

        if (activeTab === "instant") {
            dispatch(
                fetchTransfers({
                    ...params,
                    paymentType: "INSTANT",
                })
            );
        } else if (activeTab === "scheduled") {
            dispatch(
                fetchScheduledTransfers({
                    ...params,
                    paymentType: "SCHEDULED",
                })
            );
        } else if (activeTab === "recurring") {
            dispatch(
                fetchRecurringTransfers({
                    ...params,
                    paymentType: "RECURRING",
                })
            );
        }
    }, [dispatch, activeTab, currentPage, pageSize]);

    // Get the appropriate transfers data based on the active tab
    const getTransfersData = () => {
        switch (activeTab) {
            case "instant":
                return transfers || [];
            case "scheduled":
                return scheduledTransfers || [];
            case "recurring":
                return recurringTransfers || [];
            default:
                return [];
        }
    };

    // Apply client-side filtering since backend APIs don't support filtering
    const filteredTransfers =
        filterOutgoing(getTransfersData(), {
            ...(currentFilters || {}),
            search: currentFilters?.search || "",
        }) || [];

    // Check if any filters are currently applied
    const areFiltersApplied = Boolean(
        currentFilters &&
            ((currentFilters.search && currentFilters.search.trim() !== "") ||
                (currentFilters.startDate && currentFilters.startDate !== "") ||
                (currentFilters.endDate && currentFilters.endDate !== "") ||
                (currentFilters.minAmount && currentFilters.minAmount !== "") ||
                (currentFilters.maxAmount && currentFilters.maxAmount !== "") ||
                (currentFilters.amount && currentFilters.amount !== "") ||
                (currentFilters.dateFilterType && currentFilters.dateFilterType !== ""))
    );

    // Determine the current UI state based on loading status, errors, and data
    const renderContent = () => (
        <OutgoingContent
            activeTab={activeTab}
            filteredTransfers={filteredTransfers}
            areFiltersApplied={areFiltersApplied}
        />
    );
    return (
        <div className="flex flex-col px-8">
            {/* Page header */}
            <div className="flex items-center mb-9">
                <h1 className="text-black [text-edge:cap] [leading-trim:both] text-xl font-semibold leading-[26px] tracking-[0.3px]">
                    Outgoing payments
                </h1>
            </div>

            {/* Main content with consistent spacing */}
            <div className="flex flex-col">
                {/* Tab switch */}
                <TabSwitch
                    tabs={TRANSFER_TABS}
                    activeTab={activeTab}
                    onChange={handleTabChange}
                    tabSpacing="h-[24px]"
                />

                {/* Filter section */}
                <div className="mb-6">
                    <FilterSection
                        searchQuery={searchInput} // Use immediate search input for responsive UI
                        currentFilters={currentFilters}
                        tempFilters={tempFilters}
                        dateFilterLabel={dateFilterLabel}
                        isFilterOpen={isFilterOpen}
                        isAmountDropdownOpen={isAmountDropdownOpen}
                        amountFilterOption={amountFilterOption}
                        tempAmountValues={tempAmountValues}
                        amountDropdownRef={amountDropdownRef}
                        onSearch={onSearch}
                        onApplyFilter={onApplyFilter}
                        onClearAll={onClearAll}
                        handleSendMoney={handleSendMoney}
                        handleDateFilterSelect={handleDateFilterSelect}
                        setTempFilters={setTempFilters}
                        setIsFilterOpen={setIsFilterOpen}
                        setIsAmountDropdownOpen={setIsAmountDropdownOpen}
                        setAmountFilterOption={setAmountFilterOption}
                        handleAmountValueChange={handleAmountValueChange}
                        applyAmountFilter={applyAmountFilter}
                        applyCustomDateFilter={applyCustomDateFilter}
                    />
                </div>

                {/* Content */}
                {renderContent()}
            </div>
        </div>
    );
}
