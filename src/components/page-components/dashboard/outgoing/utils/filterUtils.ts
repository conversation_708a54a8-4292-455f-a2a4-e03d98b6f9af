/**
 * Purpose: Provides client-side filtering utilities for outgoing payment transactions.
 *
 * Functionality: This file implements comprehensive client-side filtering capabilities for outgoing payments,
 * including search query filtering, amount range and exact amount filtering, and date range filtering.
 * All filtering functions are designed to be null-safe and handle undefined/malformed data gracefully to
 * prevent runtime errors. The main filterOutgoing function orchestrates all filter criteria and returns
 * a filtered array of transfers. Since backend APIs don't support filtering, all filtering operations
 * are performed client-side to provide immediate, responsive user interactions.
 *
 * Dependencies: Relies on ITransfer interface from "../types" for type safety and advancedSearchMatch
 * function from "./searchUtils" for sophisticated search capabilities across multiple transfer fields.
 *
 * Usage: Called by the outgoing page component to filter transfer data based on user-selected criteria.
 * The filterOutgoing function accepts a transfers array and filter object, returning filtered results.
 * All filtering operations include comprehensive null/undefined checks to ensure stability with
 * unexpected data structures and prevent application crashes during filtering operations.
 */

import { ITransfer } from "../types";
import { advancedSearchMatch } from "./searchUtils";

/**
 * Client-side filtering utility functions.
 * All filtering is performed client-side since backend APIs don't support filtering.
 */

/**
 * Check if a transfer matches the search query
 * Enhanced to search across date, counterparty, narration, status, and amount fields (case-insensitive)
 * Uses the advanced search algorithm from searchUtils for better matching
 * @param transfer Transfer object to check
 * @param query Search query string
 * @returns Boolean indicating if the transfer matches the query
 */
export const matchesSearch = (transfer: ITransfer, query: string): boolean => advancedSearchMatch(transfer, query);

/**
 * Check if a transfer's amount falls within the specified range
 * @param transfer Transfer object to check
 * @param minAmount Minimum amount
 * @param maxAmount Maximum amount
 * @returns Boolean indicating if the transfer amount matches the criteria
 */
export const matchesAmount = (
    transfer: ITransfer,
    minAmount: string | undefined,
    maxAmount: string | undefined
): boolean => {
    if (!transfer || typeof transfer.amount !== "number") return true;

    const amount = transfer.amount;
    if (minAmount && amount <= parseFloat(minAmount)) return false;
    if (maxAmount && amount >= parseFloat(maxAmount)) return false;
    return true;
};

/**
 * Check if a transfer falls within the specified date range
 * @param transfer Transfer object to check
 * @param startDate Start date of the range
 * @param endDate End date of the range
 * @returns Boolean indicating if the transfer date falls within the range
 */
export const matchesDateRange = (
    transfer: ITransfer,
    startDate: string | undefined,
    endDate: string | undefined
): boolean => {
    if (!startDate && !endDate) return true;

    // Return true if transfer.date is null or undefined
    if (!transfer.date) return true;

    const transferDate = new Date(transfer.date);

    if (startDate) {
        const start = new Date(startDate);
        // Set to start of day to include all times on the start date
        start.setHours(0, 0, 0, 0);
        if (transferDate < start) return false;
    }

    if (endDate) {
        const end = new Date(endDate);
        // Set to end of day to include all times on the end date
        end.setHours(23, 59, 59, 999);
        if (transferDate > end) return false;
    }

    return true;
};

/**
 * Check if a transfer's amount matches the exact amount
 * @param transfer Transfer object to check
 * @param exactAmount Exact amount to match
 * @returns Boolean indicating if the transfer amount matches exactly
 */
export const matchesExactAmount = (transfer: ITransfer, exactAmount: string | undefined): boolean => {
    if (!transfer || !exactAmount || typeof transfer.amount !== "number") return true;
    return transfer.amount === parseFloat(exactAmount);
};

/**
 * Main filtering function that applies all filter criteria using client-side filtering.
 * Since backend APIs don't support filtering, all filtering is performed client-side
 * to provide immediate, responsive filtering without additional API calls.
 *
 * @param transfers List of outgoing payments to filter
 * @param filters Filter criteria including search, amount, and date filters
 * @returns Filtered list of outgoing payments matching the specified criteria
 */
export const filterOutgoing = (
    transfers: ITransfer[],
    filters: {
        search?: string;
        minAmount?: string;
        maxAmount?: string;
        amount?: string;
        startDate?: string;
        endDate?: string;
    }
): ITransfer[] => {
    // Handle null/undefined transfers array to prevent runtime errors
    if (!transfers || !Array.isArray(transfers)) {
        return [];
    }

    const { search = "", minAmount, maxAmount, amount, startDate, endDate } = filters;

    return transfers.filter((transfer) => {
        // If exact amount is specified, use that instead of min/max range
        if (amount) {
            return (
                matchesSearch(transfer, search) &&
                matchesExactAmount(transfer, amount) &&
                matchesDateRange(transfer, startDate, endDate)
            );
        }

        // Otherwise use min/max range
        return (
            matchesSearch(transfer, search) &&
            matchesAmount(transfer, minAmount, maxAmount) &&
            matchesDateRange(transfer, startDate, endDate)
        );
    });
};

// For backward compatibility, keep the old function name as an alias
export const filterTransfers = filterOutgoing;
