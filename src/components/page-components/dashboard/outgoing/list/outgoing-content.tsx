"use client";

import React, { useCallback, useEffect, useMemo, useState } from "react";
import { OutgoingContentState, TransferTab, ITransfer } from "../types";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { fetchTransfers, fetchScheduledTransfers, fetchRecurringTransfers } from "@/redux/actions/transferActions";
import OutgoingList from "./outgoing-list";
import LoadingStateComponent from "./loading-state";
import ErrorState from "./error-state";
import FilteredEmptyState from "./filtered-empty-state";

interface OutgoingContentProps {
    activeTab: TransferTab;
    filteredTransfers?: ITransfer[];
    areFiltersApplied?: boolean;
}

/**
 * OutgoingContent acts as a container component that manages the state and data fetching
 * for the OutgoingList component. It handles the loading, error, and empty states, and
 * passes the appropriate data to the OutgoingList component based on the active tab.
 *
 * @param activeTab The currently active tab (instant, scheduled, recurring)
 */
export default function OutgoingContent({
    activeTab,
    filteredTransfers,
    areFiltersApplied = false,
}: Readonly<OutgoingContentProps>) {
    const dispatch = useAppDispatch();

    // UI state management
    const [contentState, setContentState] = useState<OutgoingContentState>(OutgoingContentState.LOADING);

    // Redux state
    const {
        transfers,
        transfersLoading,
        transfersError,
        scheduledTransfers,
        scheduledTransfersLoading,
        scheduledTransfersError,
        recurringTransfers,
        recurringTransfersLoading,
        recurringTransfersError,
    } = useAppSelector((state) => state.transfer);

    // Determine which transfers to display based on the active tab
    // Use filteredTransfers if provided (from search/filtering), otherwise use Redux data
    const currentTransfers = useMemo(() => {
        if (filteredTransfers !== undefined) {
            return filteredTransfers;
        }

        switch (activeTab) {
            case "instant":
                return transfers;
            case "scheduled":
                return scheduledTransfers;
            case "recurring":
                return recurringTransfers;
            default:
                return [];
        }
    }, [activeTab, transfers, scheduledTransfers, recurringTransfers, filteredTransfers]);

    // Determine loading state based on the active tab
    const isLoading = useMemo(() => {
        switch (activeTab) {
            case "instant":
                return transfersLoading;
            case "scheduled":
                return scheduledTransfersLoading;
            case "recurring":
                return recurringTransfersLoading;
            default:
                return false;
        }
    }, [activeTab, transfersLoading, scheduledTransfersLoading, recurringTransfersLoading]);

    // Determine error state based on the active tab
    const errorMessage = useMemo(() => {
        switch (activeTab) {
            case "instant":
                return transfersError ?? "";
            case "scheduled":
                return scheduledTransfersError ?? "";
            case "recurring":
                return recurringTransfersError ?? "";
            default:
                return "";
        }
    }, [activeTab, transfersError, scheduledTransfersError, recurringTransfersError]);

    // Update content state based on loading, error, and data conditions
    useEffect(() => {
        if (isLoading) {
            setContentState(OutgoingContentState.LOADING);
        } else if (errorMessage) {
            setContentState(OutgoingContentState.ERROR);
        } else if (currentTransfers.length === 0) {
            // Check if this is empty due to filtering (filters are applied and results are empty) or truly empty
            if (filteredTransfers !== undefined && areFiltersApplied) {
                setContentState(OutgoingContentState.FILTERED_EMPTY);
            } else {
                setContentState(OutgoingContentState.EMPTY);
            }
        } else {
            setContentState(OutgoingContentState.DATA);
        }
    }, [isLoading, errorMessage, currentTransfers, filteredTransfers, areFiltersApplied]);

    /**
     * Handle retry when there's an error
     */
    const handleRetry = useCallback(() => {
        // Re-fetch data for the current tab using proper pagination
        const urlParams = new URLSearchParams(window.location.search);
        const currentPage = Number(urlParams.get("page")) || 1;
        const pageSize = Number(urlParams.get("size")) || 20;

        const params = {
            pageNo: currentPage - 1, // API is zero-indexed
            pageSize: pageSize,
            isUserInitiated: true,
        };

        switch (activeTab) {
            case "instant":
                dispatch(fetchTransfers({ ...params, paymentType: "INSTANT" }));
                break;
            case "scheduled":
                dispatch(fetchScheduledTransfers({ ...params, paymentType: "SCHEDULED" }));
                break;
            case "recurring":
                dispatch(fetchRecurringTransfers({ ...params, paymentType: "RECURRING" }));
                break;
        }
    }, [activeTab, dispatch]);

    // Render appropriate component based on content state
    switch (contentState) {
        case OutgoingContentState.LOADING:
            return (
                <div className="flex justify-center w-full">
                    <LoadingStateComponent />
                </div>
            );
        case OutgoingContentState.ERROR:
            return (
                <div className="flex justify-center w-full">
                    <ErrorState onRetry={handleRetry} />
                </div>
            );
        case OutgoingContentState.EMPTY:
            return <OutgoingList transfers={[]} activeTab={activeTab} isLoading={false} />;
        case OutgoingContentState.FILTERED_EMPTY:
            return (
                <div className="flex justify-center w-full">
                    <FilteredEmptyState />
                </div>
            );
        case OutgoingContentState.DATA:
            return <OutgoingList transfers={currentTransfers} activeTab={activeTab} isLoading={isLoading} />;
        default:
            return null;
    }
}
