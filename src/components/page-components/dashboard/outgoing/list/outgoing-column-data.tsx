import { ColumnDef } from "@tanstack/react-table";
import { ITransfer, TableMetaWithActions, TransferTab } from "../types";
import Checkbox from "@/components/common/checkbox";
import { formatDateWithTime } from "@/functions/date";
import { formatNumberToNaira } from "@/functions/stringManipulations";
import Badge from "@/components/common/badge";
import { ArrowDownIcon } from "@/components/icons/outgoing";
import TableMoreAction from "@/components/common/table/table-more-action";
import { menuItemType } from "@/components/common/table/types";
import { isBulkTransfer } from "../utils/statusUtils";
import { getStatusMapping, isSuccessfulStatus } from "@/utils/status-mapping";

// Extended table metadata interface that includes download receipt functionality
export interface TableMetaWithDownload extends TableMetaWithActions {
    handleDownloadReceipt: (transfer: ITransfer) => void;
    downloadReceiptLoading: boolean;
}

/**
 * Generate column definitions for the outgoing transfers table
 * @param activeTab The currently active tab
 * @returns Array of column definitions for TanStack Table
 */
export const getOutgoingColumns = (activeTab: TransferTab): ColumnDef<ITransfer>[] => {
    // Define base columns that are common across all tabs
    const baseColumns: ColumnDef<ITransfer>[] = [
        // Selection column
        {
            id: "select",
            header: ({ table }) => (
                <Checkbox
                    checked={table.getIsAllPageRowsSelected()}
                    onCheckedChange={(checked) => table.toggleAllPageRowsSelected(checked)}
                    aria-label="Select all"
                    size="sm"
                    indeterminate={table.getIsSomePageRowsSelected()}
                />
            ),
            cell: ({ row }) => (
                <Checkbox
                    checked={row.getIsSelected()}
                    onCheckedChange={(checked) => row.toggleSelected(checked)}
                    aria-label="Select row"
                    size="sm"
                />
            ),
            enableSorting: false,
            enableHiding: false,
        },
        // Date column
        {
            accessorKey: "date",
            header: ({ column }) => (
                <button
                    className="flex items-center gap-1"
                    onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
                >
                    <span>{activeTab === "recurring" ? "Date created" : "Date"}</span>
                    <ArrowDownIcon />
                </button>
            ),
            cell: ({ row }) => {
                const transfer = row.original;
                return (
                    <span className="font-semibold text-black">
                        {transfer.date ? formatDateWithTime(transfer.date) : "Not available"}
                    </span>
                );
            },
        },
        // Counterparty column
        {
            accessorKey: "counterparty",
            header: "Counterparty",
            cell: ({ row }) => {
                const transfer = row.original;
                const isTransferBulk = isBulkTransfer(transfer);

                return (
                    <div className="flex flex-col">
                        <span className="font-medium text-gray-900">
                            {isTransferBulk ? "Bulk Transfer" : transfer.counterparty}
                        </span>
                        <span className="text-sm text-gray-500">
                            {isTransferBulk
                                ? `${transfer.totalTransfers} recipients`
                                : `${transfer.bank ?? ""}, ${transfer.accountNumber}`}
                        </span>
                    </div>
                );
            },
        },
        // Narration column
        {
            accessorKey: "narration",
            header: "Narration",
            cell: ({ row }) => row.original.narration,
        },
        // Status column
        {
            accessorKey: "status",
            header: "Status",
            cell: ({ row }) => {
                const statusMapping = getStatusMapping(row.original.status);
                return <Badge color={statusMapping.color} text={statusMapping.text} size="sm" />;
            },
        },
        // Amount column
        {
            accessorKey: "amount",
            header: ({ column }) => (
                <button
                    className="flex items-center gap-1"
                    onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
                >
                    <span>Amount</span>
                    <ArrowDownIcon />
                </button>
            ),
            cell: ({ row }) => formatNumberToNaira(row.original.amount),
        },
        // Actions column
        {
            id: "actions",
            cell: ({ row, table }) => {
                const transfer = row.original;
                // Access functions from table metadata
                const { setIsDetailsOpen, setSelectedTransfer } = table.options.meta as TableMetaWithActions;

                const menuItems: menuItemType<ITransfer>[] = [
                    {
                        label: "View",
                        onClick: (transfer: ITransfer) => {
                            if (setSelectedTransfer && setIsDetailsOpen) {
                                setSelectedTransfer(transfer);
                                setIsDetailsOpen(true);
                            }
                        },
                    },
                ];

                // Add download receipt for all transfers - disabled for non-successful ones
                menuItems.push({
                    label: "Download receipt",
                    onClick: (transfer: ITransfer) => {
                        // The handleDownloadReceipt function will be provided by the component
                        const handleDownloadReceipt = (table.options.meta as TableMetaWithDownload)
                            .handleDownloadReceipt;
                        if (handleDownloadReceipt) {
                            handleDownloadReceipt(transfer);
                        }
                    },
                    disabled:
                        !isSuccessfulStatus(transfer.status) ||
                        (table.options.meta as TableMetaWithDownload).downloadReceiptLoading,
                });

                return <TableMoreAction data={transfer} menuItems={menuItems} />;
            },
        },
    ];

    // Add frequency column for recurring tab
    if (activeTab === "recurring") {
        // Insert frequency column after counterparty column (index 2)
        baseColumns.splice(3, 0, {
            accessorKey: "frequency",
            header: "Frequency",
            cell: ({ row }) => row.original.frequency ?? "N/A",
        });
    }

    return baseColumns;
};
