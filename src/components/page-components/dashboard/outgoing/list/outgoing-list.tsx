"use client";

import React, { useState, useMemo, useCallback } from "react";
import { useSearch<PERSON><PERSON><PERSON>, useRouter, usePathname } from "next/navigation";
import { ITransfer, TransferTab } from "../types";
import { Pagination } from "@/components/common/pagination";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { downloadReceipt } from "@/redux/actions/transferActions";
import dynamic from "next/dynamic";
import { sendCatchFeedback } from "@/functions/feedback";
import { DataTable } from "@/components/common/table/DataTable";
import {
    ColumnFiltersState,
    SortingState,
    getCoreRowModel,
    getFilteredRowModel,
    getSortedRowModel,
    useReactTable,
} from "@tanstack/react-table";
import { getOutgoingColumns, TableMetaWithDownload } from "./outgoing-column-data";

// Dynamically import the details component for better performance
const OutgoingDetails = dynamic(() => import("../details/outgoing-details"), {
    ssr: false,
});

/**
 * Props interface for the OutgoingList component
 */
interface OutgoingListProps {
    /** List of transfers to display */
    transfers: ITransfer[];
    /** Currently active tab (instant, scheduled, recurring) */
    activeTab?: TransferTab;
    /** Whether the list is in a loading state */
    isLoading?: boolean;
}

/**
 * OutgoingList component displays a table of transfers with sorting
 * and actions like viewing details, downloading receipts, etc.
 *
 * Features:
 * - Sortable columns (date and amount)
 * - Bulk selection
 * - Transfer details modal
 * - Download receipt functionality
 * - Status badges
 */
export default function OutgoingList({
    transfers,
    activeTab = "instant",
    isLoading = false,
}: Readonly<OutgoingListProps>) {
    const dispatch = useAppDispatch();
    const router = useRouter();
    const pathname = usePathname();
    const searchParams = useSearchParams();

    // Local component state
    const [sorting, setSorting] = useState<SortingState>([]);
    const [rowSelection, setRowSelection] = useState({});
    const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
    const [selectedTransfer, setSelectedTransfer] = useState<ITransfer | null>(null);
    const [isDetailsOpen, setIsDetailsOpen] = useState(false);

    // Pagination state from URL
    const currentPage = Number(searchParams.get("page")) || 1;
    const itemsPerPage = Number(searchParams.get("size")) || 20;

    // Redux state - get pagination data based on active tab
    const transferState = useAppSelector((state) => state.transfer);
    const { downloadReceiptLoading } = transferState;

    // Get pagination metadata based on active tab
    const getPaginationData = () => {
        switch (activeTab) {
            case "instant":
                return {
                    totalElements: transferState.transfersTotalElements || 0,
                    totalPages: transferState.transfersTotalPages || 0,
                    hasNext: transferState.transfersHasNext || false,
                    hasPrevious: transferState.transfersHasPrevious || false,
                };
            case "scheduled":
                return {
                    totalElements: transferState.scheduledTransfersTotalElements || 0,
                    totalPages: transferState.scheduledTransfersTotalPages || 0,
                    hasNext: transferState.scheduledTransfersHasNext || false,
                    hasPrevious: transferState.scheduledTransfersHasPrevious || false,
                };
            case "recurring":
                return {
                    totalElements: transferState.recurringTransfersTotalElements || 0,
                    totalPages: transferState.recurringTransfersTotalPages || 0,
                    hasNext: transferState.recurringTransfersHasNext || false,
                    hasPrevious: transferState.recurringTransfersHasPrevious || false,
                };
            default:
                return {
                    totalElements: 0,
                    totalPages: 0,
                    hasNext: false,
                    hasPrevious: false,
                };
        }
    };

    const paginationData = getPaginationData();

    // Server-side pagination - use transfers as-is (no client-side slicing)
    const paginatedTransfers = transfers;

    /**
     * Updates URL parameters for pagination
     */
    const updateUrlParams = useCallback(
        (newPage: number, newSize: number) => {
            const params = new URLSearchParams(window.location.search);
            params.set("page", newPage.toString());
            params.set("size", newSize.toString());

            const newUrl = `${pathname}?${params.toString()}`;
            router.push(newUrl, { scroll: false });
        },
        [pathname, router]
    );

    /**
     * Handles page change
     */
    const handlePageChange = useCallback(
        (page: number) => {
            // Get current itemsPerPage from URL at the time of the call
            const currentItemsPerPage = Number(new URLSearchParams(window.location.search).get("size")) || 10;
            updateUrlParams(page, currentItemsPerPage);
        },
        [updateUrlParams]
    );

    /**
     * Handles items per page change
     */
    const handleItemsPerPageChange = useCallback(
        (newItemsPerPage: number) => {
            // Reset to page 1 when changing items per page
            updateUrlParams(1, newItemsPerPage);
        },
        [updateUrlParams]
    );

    /**
     * Handles closing the transfer details modal
     */
    const handleCloseDetails = useCallback(() => {
        setIsDetailsOpen(false);
    }, []);

    /**
     * Handles row click to open transfer details
     */
    const handleRowClick = useCallback((transfer: ITransfer) => {
        setSelectedTransfer(transfer);
        setIsDetailsOpen(true);
    }, []);

    /**
     * Handles downloading a transfer receipt
     */
    const handleDownloadReceipt = useCallback(
        (transfer: ITransfer) => {
            const transactionId = transfer.transferScheduledId;
            if (!transactionId) {
                sendCatchFeedback(new Error("No valid transaction ID found for this transfer"));
                return;
            }
            dispatch(
                downloadReceipt({
                    transactionId,
                    isUserInitiated: true,
                })
            );
        },
        [dispatch]
    );

    // Create memoized columns based on the active tab
    const columns = useMemo(() => getOutgoingColumns(activeTab), [activeTab]);

    // Set up the table instance with memoization
    const table = useReactTable({
        data: paginatedTransfers,
        columns,
        getCoreRowModel: getCoreRowModel(),
        onSortingChange: setSorting,
        getSortedRowModel: getSortedRowModel(),
        onRowSelectionChange: setRowSelection,
        onColumnFiltersChange: setColumnFilters,
        getFilteredRowModel: getFilteredRowModel(),
        state: {
            sorting,
            rowSelection,
            columnFilters,
        },
        meta: {
            setIsDetailsOpen: (value: boolean) => setIsDetailsOpen(value),
            setSelectedTransfer: (transfer: ITransfer) => setSelectedTransfer(transfer),
            handleDownloadReceipt,
            downloadReceiptLoading,
        } as TableMetaWithDownload,
    });

    // Extract empty state messages from EmptyStateComponent based on the active tab
    const getEmptyStateTitle = () => {
        switch (activeTab) {
            case "instant":
                return "No payments yet";
            case "scheduled":
                return "No scheduled payments";
            case "recurring":
                return "No active recurring payments";
            default:
                return "No payments found";
        }
    };

    const getEmptyStateDescription = () => {
        switch (activeTab) {
            case "instant":
                return "Ready for your first payment? Click 'send money' to make your first payment.";
            case "scheduled":
                return "Payments scheduled for future dates will show up here.";
            case "recurring":
                return "Your regular automated payments will show up here.";
            default:
                return "Start by creating a new transfer.";
        }
    };

    // Always render DataTable instead of conditionally showing EmptyStateComponent
    // This ensures table headers are displayed even when there's no data, consistent with all-transactions.tsx
    return (
        <div className="space-y-4 pb-12 flex flex-col items-center w-full">
            <div className="w-full">
                {/* The overflow-x-auto enables horizontal scrolling when table width exceeds viewport */}
                {/* The min-w-[1500px] ensures table maintains column widths on smaller screens */}
                <div className="w-full overflow-x-auto">
                    <div className="min-w-[1500px]">
                        <DataTable
                            table={table}
                            columns={columns}
                            loading={isLoading}
                            emptyTabletitle={getEmptyStateTitle()}
                            emptyTabledescription={getEmptyStateDescription()}
                            onRowClick={handleRowClick}
                        />
                    </div>
                </div>
            </div>

            {isDetailsOpen && selectedTransfer && (
                <OutgoingDetails
                    isOpen={isDetailsOpen}
                    handleCloseDetails={handleCloseDetails}
                    transfer={selectedTransfer}
                    activeTab={activeTab}
                />
            )}

            {transfers.length > 0 && (
                <div className="fixed bottom-0 left-[256px] right-0 pb-6">
                    <Pagination
                        totalItems={paginationData.totalElements}
                        onPageChange={handlePageChange}
                        onItemsPerPageChange={handleItemsPerPageChange}
                        itemsPerPageOptions={[10, 20, 50, 100]}
                        className="px-8"
                        totalPages={paginationData.totalPages}
                        hasNext={paginationData.hasNext}
                        hasPrevious={paginationData.hasPrevious}
                        currentPage={currentPage}
                        currentItemsPerPage={itemsPerPage}
                    />
                </div>
            )}
        </div>
    );
}
