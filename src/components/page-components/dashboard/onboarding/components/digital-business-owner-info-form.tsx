import { But<PERSON> } from "@/components/common/buttonv3";
import DatePicker from "@/components/common/date-picker";
import SideDrawer from "@/components/common/drawer";
import Dropdown from "@/components/common/dropdown";
import FileAttachment from "@/components/common/file-attachment";
import LabelInput from "@/components/common/label-input";
import ProgressBar from "@/components/common/progress-bar/progress-bar";
import { InfoIcon } from "@/components/icons/auth";
import { sendCatchFeedback, sendFeedback } from "@/functions/feedback";
import { findOptionByValue } from "@/functions/stringManipulations";
import { getForeignCountries } from "@/redux/actions/digitalOnboardingAction";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { addOwner, IBusinessOwner, updateOwner } from "@/redux/slices/onboarding/businessOwnersSlice";
import { XMarkIcon } from "@heroicons/react/24/outline";
import { useFormik } from "formik";
import React, { useEffect, useMemo, useState } from "react";
import { v4 as uuidv4 } from "uuid";
import * as yup from "yup";
import { OptionProp } from "../../outgoing/types";
import { idTypes, nigeriaStatesAndCities, proofOfAddress, roles } from "../libs/mock-data";

const handleDocumentFileSelection = (
    files: FileList | null | undefined,
    documentIndex: number,
    documentType: string,
    formik: ReturnType<typeof useFormik>
) => {
    if (files?.[0]) {
        const updatedDocs = [...formik.values.documents];
        updatedDocs[documentIndex] = {
            documentType,
            file: files[0],
        };
        formik.setFieldValue("documents", updatedDocs);
    }
};

type FormikDocumentError = {
    file: string;
};

type DocumentUploadProps = {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    formik: any;
    index: number;
    type: "IDENTITY" | "PROOF_OF_ADDRESS";
    headerText: string;
    error?: FormikDocumentError;
};

const DocumentUpload: React.FC<DocumentUploadProps> = ({ formik, index, type, headerText, error }) => {
    const file = formik.values.documents[index]?.file;

    return (
        <div>
            <FileAttachment
                headerText={headerText}
                maxSize={10}
                acceptedTypes={["pdf", "png", "jpg"]}
                className="w-full h-auto"
                width="100%"
                initialFile={file instanceof File ? file : null}
                storedFile={
                    file && !(file instanceof File) && file.name
                        ? {
                              name: file.name,
                              size: file.size ?? 0,
                          }
                        : null
                }
                onFilesSelected={(files) => handleDocumentFileSelection(files, index, type, formik)}
                onFileRemoved={() => {
                    const updatedDocs = [...formik.values.documents];
                    updatedDocs[index] = { documentType: type, file: undefined };
                    formik.setFieldValue("documents", updatedDocs);
                }}
            />

            {error && (
                <div className="flex items-center mt-2 gap-[0.25rem] text-[14px] font-medium leading-[18px] tracking-[0.28px] text-[#D92D20]">
                    <InfoIcon data-testid="info-icon" />
                    <span className="text-[14px] font-medium text-[#D92D20] leading-[18px] tracking-[0.28px]">
                        {error.file}
                    </span>
                </div>
            )}
        </div>
    );
};

const DigitalBusinessOwnerInfoForm: React.FC<{
    open: boolean;
    onClose: () => void;
    headerText: string;
    initialData: IBusinessOwner | undefined;
}> = ({ open, onClose, headerText, initialData }) => {
    const [formProgress, setFormProgress] = useState<number>(0);
    const [countries, setCountries] = useState<OptionProp[]>([]);
    const [loading, setLoading] = useState<boolean>(false);
    const [dates, setDates] = useState<{ dateOfBirth: Date | undefined }>({
        dateOfBirth: initialData?.dateOfBirth ? new Date(initialData.dateOfBirth) : undefined,
    });
    const dispatch = useAppDispatch();
    const owners = useAppSelector((state) => state.businessOwners.owners);

    useEffect(() => {
        const fetchCountries = async () => {
            const resultAction = await dispatch(getForeignCountries());
            if (getForeignCountries.fulfilled.match(resultAction)) {
                const rawCountries = resultAction.payload as { name: string }[];

                let transformed = rawCountries.map((item) => ({
                    label: item.name,
                    value: item.name,
                }));

                const hasNigeria = transformed.some((item) => item.value.toLowerCase() === "nigeria");

                if (!hasNigeria) {
                    transformed.unshift({ label: "Nigeria", value: "Nigeria" });
                } else {
                    transformed = [
                        transformed.find((item) => item.value.toLowerCase() === "nigeria")!,
                        ...transformed.filter((item) => item.value.toLowerCase() !== "nigeria"),
                    ];
                }

                setCountries(transformed);
            } else {
                sendCatchFeedback("Failed to fetch countries");
            }
        };
        fetchCountries();
    }, [dispatch]);
    const formik = useFormik<IBusinessOwner>({
        initialValues: useMemo(
            () => ({
                id: initialData?.id ?? uuidv4(),
                bvn: initialData?.bvn ?? "",
                fullLegalName: initialData?.fullLegalName ?? "",
                dateOfBirth: initialData?.dateOfBirth ?? "",
                nationality: initialData?.nationality ?? "",
                role: initialData?.role ?? "",
                ownershipPercentage: initialData?.ownershipPercentage ?? "",
                identityDocumentType: initialData?.identityDocumentType ?? "",
                residentialAddress: initialData?.residentialAddress ?? "",
                city: initialData?.city ?? "",
                state: initialData?.state ?? "",
                proofOfAddressType: initialData?.proofOfAddressType ?? "",
                documents: initialData?.documents ?? [
                    { documentType: "IDENTITY", file: undefined },
                    { documentType: "PROOF_OF_ADDRESS", file: undefined },
                ],
            }),
            [initialData]
        ),
        onSubmit: (values) => {
            submitValues(values);
        },
        validationSchema: yup.object({
            bvn: yup
                .string()
                .required("BVN is required")
                .matches(/^\d{11}$/, "BVN must be exactly 11 digits"),
            fullLegalName: yup.string().required("Full legal name Required"),
            dateOfBirth: yup.string().required("Date of birth is Required"),
            nationality: yup.string().required("Nationality is Required"),
            role: yup.string().required("Role is Required"),
            ownershipPercentage: yup.string().required("Ownership percentage is Required"),
            identityDocumentType: yup.string().required("ID type is Required"),
            residentialAddress: yup.string().required("Address is Required"),
            city: yup.string().required("City is Required"),
            state: yup.string().required("State is Required"),
            proofOfAddressType: yup.string().required("Proof of address is Required"),
            documents: yup
                .array()
                .of(
                    yup.object().shape({
                        documentType: yup.string().required("Document type is required"),
                        file: yup.mixed().test("file-required-based-on-type", "File is required", function (value) {
                            const { documentType } = this.parent;

                            if ((documentType === "IDENTITY" || documentType === "PROOF_OF_ADDRESS") && !value) {
                                return this.createError({
                                    message:
                                        documentType === "IDENTITY"
                                            ? "ID document is required"
                                            : "Proof of address document is required",
                                    path: `${this.path}`,
                                });
                            }

                            return true;
                        }),
                    })
                )
                .required("Documents are required")
                .test(
                    "must-have-identity-and-proof",
                    "Both identity and proof of address documents are required",
                    function (docs) {
                        if (!docs || !Array.isArray(docs)) return false;

                        const hasIdentity = docs.some((doc) => doc?.documentType === "IDENTITY" && !!doc?.file);
                        const hasProof = docs.some((doc) => doc?.documentType === "PROOF_OF_ADDRESS" && !!doc?.file);

                        return hasIdentity && hasProof;
                    }
                ),
        }),
        enableReinitialize: true,
    });

    useEffect(() => {
        const requiredFields = [
            "bvn",
            "fullLegalName",
            "dateOfBirth",
            "nationality",
            "role",
            "ownershipPercentage",
            "identityDocumentType",
            "residentialAddress",
            "city",
            "state",
            "proofOfAddressType",
        ] as const;
        const hasIdentityDoc = formik.values.documents[0]?.file !== undefined;
        const hasProofOfAddressDoc = formik.values.documents[1]?.file !== undefined;
        const totalRequiredItems = requiredFields.length + 2;
        const completedFields = requiredFields.filter((field) => {
            const value = formik.values[field];
            return value !== undefined && value !== "";
        }).length;
        const completedItems = completedFields + (hasIdentityDoc ? 1 : 0) + (hasProofOfAddressDoc ? 1 : 0);
        setFormProgress(Math.round((completedItems / totalRequiredItems) * 100));
    }, [formik.values]);

    const findIndexById = (id: string) => owners.findIndex((owner) => owner.id === id);

    const submitValues = async (values: IBusinessOwner) => {
        setLoading(true);
        const preparedDocuments = values.documents.map((doc) => ({
            documentType: doc.documentType,
            file: doc.file,
        }));

        const ownerData: IBusinessOwner = {
            ...values,
            documents: preparedDocuments,
            dateOfBirth: values.dateOfBirth,
        };

        const existingIndex = findIndexById(values.id);
        if (existingIndex !== -1) {
            dispatch(updateOwner({ index: existingIndex, data: ownerData }));
        } else {
            dispatch(addOwner(ownerData));
        }
        setLoading(false);
        formik.resetForm();
        sendFeedback("Owner info saved", "success");
        onClose();
    };

    const allStates = useMemo(
        () =>
            Object.keys(nigeriaStatesAndCities).map((item) => ({
                label: item,
                value: item,
            })),
        []
    );

    const allCities = useMemo(
        () =>
            formik.values.state && nigeriaStatesAndCities[formik.values.state as keyof typeof nigeriaStatesAndCities]
                ? (nigeriaStatesAndCities[formik.values.state as keyof typeof nigeriaStatesAndCities] || []).map(
                      (item) => ({
                          label: item,
                          value: item,
                      })
                  )
                : [],
        [formik.values.state]
    );

    useEffect(() => {
        if (open && initialData) {
            setTimeout(() => {
                formik.setValues(initialData);
                setDates({
                    dateOfBirth: initialData.dateOfBirth ? new Date(initialData.dateOfBirth) : undefined,
                });
            }, 0);
        } else if (open) {
            formik.resetForm();
            setDates({ dateOfBirth: undefined });
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [open, initialData]);

    return (
        <SideDrawer isOpen={open}>
            <div>
                <div className="flex justify-between items-center border-b py-5 px-4">
                    <h2 data-testid="form-title" className="text-lg font-semibold tracking-tight text-[#151518]">
                        {headerText}
                    </h2>
                    <button
                        name="close"
                        data-testid="close-icon"
                        onClick={onClose}
                        className="text-gray-500 hover:text-gray-700"
                    >
                        <XMarkIcon className="w-5 h-5" />
                    </button>
                </div>
                <ProgressBar value={formProgress} />
                <section className="py-8 px-6">
                    <form onSubmit={formik.handleSubmit} data-testid="add-business-owner-form" className="space-y-8">
                        <fieldset>
                            <legend className="text-sm font-semibold tracking-tight text-[#151518] pb-6">
                                Personal Information
                            </legend>
                            <div className="space-y-5">
                                <LabelInput
                                    formik={formik}
                                    name="bvn"
                                    label="Bank Verification Number (BVN)"
                                    type="text"
                                    showError={formik.touched.bvn}
                                />
                                <LabelInput
                                    formik={formik}
                                    name="fullLegalName"
                                    label="Full Legal Name"
                                    type="text"
                                    showError={formik.touched.fullLegalName}
                                />
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-5 mb-5">
                                    <DatePicker
                                        label="Date of birth"
                                        value={dates.dateOfBirth}
                                        onChange={(value) => {
                                            setDates({ dateOfBirth: value });
                                            formik.setFieldValue("dateOfBirth", value ? value.toISOString() : "");
                                        }}
                                        error={
                                            formik.touched.dateOfBirth && formik.errors.dateOfBirth
                                                ? formik.errors.dateOfBirth
                                                : undefined
                                        }
                                        placeholder="Select birthdate"
                                        disableFuture
                                    />
                                    <Dropdown
                                        value={findOptionByValue(formik.values.nationality, countries)}
                                        options={countries}
                                        placeholder="Select a nationality"
                                        name="nationality"
                                        label="Nationality"
                                        size="sm"
                                        formik={formik}
                                    />
                                </div>
                                <Dropdown
                                    value={findOptionByValue(formik.values.role, roles)}
                                    options={roles}
                                    placeholder="Select a role"
                                    name="role"
                                    label="Role"
                                    size="sm"
                                    formik={formik}
                                />
                                <LabelInput
                                    name="ownershipPercentage"
                                    label="Ownership Percentage"
                                    placeholder="e.g. 25"
                                    type="text"
                                    showError={formik.touched.ownershipPercentage}
                                    formik={formik}
                                    postAppend="%"
                                />
                            </div>
                        </fieldset>

                        <fieldset>
                            <legend className="text-sm font-semibold tracking-tight text-[#151518] pb-6">
                                Identity Verification
                            </legend>
                            <div className="space-y-5">
                                <Dropdown
                                    value={findOptionByValue(formik.values.identityDocumentType, idTypes)}
                                    options={idTypes}
                                    placeholder="Select one"
                                    name="identityDocumentType"
                                    label="Choose an ID type to upload"
                                    size="sm"
                                    formik={formik}
                                />
                                <DocumentUpload
                                    formik={formik}
                                    index={0}
                                    type="IDENTITY"
                                    headerText=" "
                                    error={
                                        (typeof formik.errors.documents === "string"
                                            ? undefined
                                            : Array.isArray(formik.errors.documents)
                                              ? formik.errors.documents?.[0]
                                              : undefined) as FormikDocumentError
                                    }
                                />
                            </div>
                        </fieldset>

                        <fieldset>
                            <legend className="text-sm font-semibold tracking-tight text-[#151518] pb-6">
                                Residential address verification
                            </legend>
                            <div className="space-y-5">
                                <LabelInput
                                    formik={formik}
                                    name="residentialAddress"
                                    label="Address"
                                    type="text"
                                    showError={formik.touched.residentialAddress}
                                />
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-5 mb-5">
                                    <Dropdown
                                        options={allStates}
                                        value={findOptionByValue(formik.values.state, allStates)}
                                        name="state"
                                        label="State"
                                        size="sm"
                                        formik={formik}
                                        showError={formik.touched.state}
                                    />
                                    <Dropdown
                                        options={allCities}
                                        value={findOptionByValue(formik.values.city, allCities)}
                                        name="city"
                                        label="City"
                                        size="sm"
                                        formik={formik}
                                        showError={formik.touched.city}
                                    />
                                </div>
                                <Dropdown
                                    value={findOptionByValue(formik.values.proofOfAddressType, proofOfAddress)}
                                    options={proofOfAddress}
                                    placeholder="Select one"
                                    name="proofOfAddressType"
                                    label="Proof of address"
                                    size="sm"
                                    formik={formik}
                                />
                                <DocumentUpload
                                    formik={formik}
                                    index={1}
                                    type="PROOF_OF_ADDRESS"
                                    headerText="Upload Proof of Address"
                                    error={
                                        (typeof formik.errors.documents === "string"
                                            ? { file: formik.errors.documents }
                                            : Array.isArray(formik.errors.documents)
                                              ? formik.errors.documents?.[1]
                                              : undefined) as FormikDocumentError
                                    }
                                />
                            </div>
                        </fieldset>

                        <div className="flex justify-end mt-8">
                            <Button variant="outline" className="mr-2" onClick={onClose} type="button">
                                Cancel
                            </Button>
                            <Button
                                loading={loading}
                                type="submit"
                                variant="primary"
                                size="medium"
                                disabled={!(formik.isValid && formik.dirty)}
                            >
                                Save
                            </Button>
                        </div>
                    </form>
                </section>
            </div>
        </SideDrawer>
    );
};

export default DigitalBusinessOwnerInfoForm;
