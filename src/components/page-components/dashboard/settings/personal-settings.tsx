/**
 * PersonalSettings component
 *
 * Displays and allows editing of the user's personal information.
 * Fetches user data from the API on mount and displays it in an organized format.
 * Provides functionality to edit individual fields through the EditableField component.
 * Now includes 2FA support for secure field updates.
 *
 * Features:
 * - Loading state with skeleton UI
 * - Error handling with retry functionality
 * - Fallback text for missing data
 * - Edit mode for address information
 * - Date picker for date of birth field
 * - 2FA verification for sensitive field updates
 */
import { useEffect, useState, useCallback } from "react";
import { EditableField } from "./ui/editable-field";
import { Button } from "@/components/common/buttonv3";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { fetchPersonalInfo, updatePersonalInfo, updatePersonalInfoWithToken } from "@/redux/actions/settingsActions";
import { sendFeedback } from "@/functions/feedback";
import { AddressForm, AddressDisplay } from "./address";
import { PersonalSettingsSkeleton } from "./ui";
import { extractErrorMessage } from "./utils";
import { capitalizeUserName } from "@/functions/stringManipulations";

export const PersonalSettings = () => {
    const dispatch = useAppDispatch();
    const {
        data: personalInfo,
        loading,
        error,
        updateLoading,
    } = useAppSelector((state) => state.settings.personalInfo);
    const { countries } = useAppSelector((state) => state.countries);
    const [isEditingAddress, setIsEditingAddress] = useState(false);
    const [number, setNumber] = useState("");
    const [address, setAddress] = useState("");
    const [city, setCity] = useState("");
    const [state, setState] = useState("");
    const [country, setCountry] = useState("");
    const [isCountriesLoading, setIsCountriesLoading] = useState(false);
    const [hasError, setHasError] = useState(false);
    const [errorMessage, setErrorMessage] = useState("");

    // Fetch personal info when component mounts
    useEffect(() => {
        dispatch(fetchPersonalInfo())
            .unwrap()
            .catch((err) => {
                setHasError(true);
                setErrorMessage(extractErrorMessage(err, "Unable to get personal information"));
            });
    }, [dispatch]);

    // Update local error state when Redux error state changes
    useEffect(() => {
        if (error) {
            setHasError(true);
            setErrorMessage(extractErrorMessage(error, "Unable to get personal information"));
        }
    }, [error]);

    // Check if countries are loading
    useEffect(() => {
        setIsCountriesLoading(!countries || countries.length === 0);
    }, [countries]);

    // Initialize address form fields when data is loaded
    useEffect(() => {
        if (personalInfo?.address) {
            if (typeof personalInfo.address === "object" && personalInfo.address !== null) {
                const addressObj = personalInfo.address as Record<string, string>;
                setNumber(addressObj.number || "");
                setAddress(addressObj.street || "");
                setCity(addressObj.city || "");
                setState(addressObj.state || "");
                setCountry(addressObj.country || "");
            } else if (typeof personalInfo.address === "string") {
                // Handle legacy string address
                setAddress(personalInfo.address);
                setNumber("");
                setCity("");
                setState("");
                setCountry("");
            }
        }
    }, [personalInfo]);

    /**
     * Handles saving updated field values without MFA
     * Follows API Integration Guidelines for empty strings and proper feedback
     */
    const handleSave = useCallback(
        (field: string) => (value: string) => {
            // Capitalize preferred name if that's the field being updated
            const processedValue = field === "preferredName" ? capitalizeUserName(value) : value;

            // Following API Integration Guideline: use empty string instead of null/undefined
            const payload = {
                [field]: processedValue || "",
                address: {}, // Ensure this is an empty object with no properties
            };

            // Map field names to user-friendly input names for feedback
            const fieldNameMap: Record<string, string> = {
                preferredName: "preferred name",
                phoneNumber: "phone number",
                dateOfBirth: "date of birth",
                role: "role",
            };

            const displayName = fieldNameMap[field] || field.replace(/-/g, " ");

            dispatch(updatePersonalInfo(payload))
                .unwrap() // Ensure sequential execution to prevent race conditions
                .then(() => {
                    // Fetch latest personal information to keep UI in sync with API
                    dispatch(fetchPersonalInfo());

                    // Following API Integration Guideline: use sendFeedback for successful operations
                    sendFeedback(`Your ${displayName} has been updated successfully`, "success", undefined, "Success");
                })
                .catch(() => {
                    // Error already handled by thunk with sendCatchFeedback
                });
        },
        [dispatch]
    );

    /**
     * Handles saving updated field values with MFA token
     * Follows API Integration Guidelines for empty strings and proper feedback
     */
    const handleSaveWithToken = useCallback(
        (field: string) => (value: string, token: string) => {
            // Capitalize preferred name if that's the field being updated
            const processedValue = field === "preferredName" ? capitalizeUserName(value) : value;

            // Following API Integration Guideline: use empty string instead of null/undefined
            const payload = {
                [field]: processedValue || "",
                address: {}, // Ensure this is an empty object with no properties
                token: token,
            };

            // Map field names to user-friendly input names for feedback
            const fieldNameMap: Record<string, string> = {
                preferredName: "preferred name",
                phoneNumber: "phone number",
                dateOfBirth: "date of birth",
                role: "role",
            };

            const displayName = fieldNameMap[field] || field.replace(/-/g, " ");

            dispatch(updatePersonalInfoWithToken(payload))
                .unwrap() // Ensure sequential execution to prevent race conditions
                .then(() => {
                    // Fetch latest personal information to keep UI in sync with API
                    dispatch(fetchPersonalInfo());

                    // Following API Integration Guideline: use sendFeedback for successful operations
                    sendFeedback(`Your ${displayName} has been updated successfully`, "success", undefined, "Success");
                })
                .catch(() => {
                    // Error already handled by thunk with sendCatchFeedback
                });
        },
        [dispatch]
    );

    /**
     * Handles saving the address information
     * Constructs the address object according to the API's expected format
     * Calls fetchPersonalInfo after successful updates to keep UI in sync
     * Follows API Integration Guidelines for proper data structure and feedback
     */
    const handleSaveAddress = useCallback(
        (formAddress?: { number: string; street: string; city: string; state: string; country: string }) => {
            // If form address is provided, use it directly
            // Otherwise, use the state values (this helps handle the case when AddressForm is not used)
            const addressObject = formAddress || {
                number: number || "",
                street: address || "",
                city: city || "",
                state: state || "",
                country: country || "",
            };

            // Send the address object to match the API's expected format
            dispatch(updatePersonalInfo({ address: addressObject }))
                .unwrap() // Ensure sequential execution to prevent race conditions
                .then(() => {
                    // Fetch latest personal information to keep UI in sync with API
                    dispatch(fetchPersonalInfo());
                    setIsEditingAddress(false);

                    // Following API Integration Guideline: use sendFeedback for successful operations
                    sendFeedback("Your address has been updated successfully", "success", undefined, "Success");
                })
                .catch(() => {
                    // Error already handled by thunk with sendCatchFeedback
                });
        },
        [number, address, city, state, country, dispatch]
    );

    /**
     * Handles saving the address information with MFA token
     * Constructs the address object according to the API's expected format
     * Calls fetchPersonalInfo after successful updates to keep UI in sync
     * Follows API Integration Guidelines for proper data structure and feedback
     */
    const handleSaveAddressWithToken = useCallback(
        (
            token: string,
            formAddress?: { number: string; street: string; city: string; state: string; country: string }
        ) => {
            // If form address is provided, use it directly
            // Otherwise, use the state values (this helps handle the case when AddressForm is not used)
            const addressObject = formAddress || {
                number: number || "",
                street: address || "",
                city: city || "",
                state: state || "",
                country: country || "",
            };

            // Send the address object with token to match the API's expected format
            dispatch(updatePersonalInfoWithToken({ address: addressObject, token }))
                .unwrap() // Ensure sequential execution to prevent race conditions
                .then(() => {
                    // Fetch latest personal information to keep UI in sync with API
                    dispatch(fetchPersonalInfo());
                    setIsEditingAddress(false);

                    // Following API Integration Guideline: use sendFeedback for successful operations
                    sendFeedback("Your address has been updated successfully", "success", undefined, "Success");
                })
                .catch(() => {
                    // Error already handled by thunk with sendCatchFeedback
                });
        },
        [number, address, city, state, country, dispatch]
    );

    /**
     * Toggles the address editing mode
     */
    const toggleAddressEdit = useCallback(() => {
        setIsEditingAddress((prev) => !prev);
    }, []);

    /**
     * Retries fetching personal information and resets the error state
     */
    const handleRetry = useCallback(() => {
        setHasError(false);
        setErrorMessage("");
        dispatch(fetchPersonalInfo())
            .unwrap()
            .catch((err) => {
                setHasError(true);
                setErrorMessage(extractErrorMessage(err, "Unable to get personal information"));
            });
    }, [dispatch]);

    // Loading state with skeleton UI
    if (loading) {
        return <PersonalSettingsSkeleton />;
    }

    // Error state with retry button - uses the local hasError state which isn't cleared by middleware
    if (hasError) {
        return (
            <section className="bg-white rounded-[20px] border border-[#E3E5E8] p-6" aria-live="assertive">
                <div className="space-y-6">
                    <h2 className="text-xl font-semibold text-black">Personal information</h2>
                    <div className="text-red-500 py-4">{errorMessage || "Unable to get personal information"}</div>
                    <Button onClick={handleRetry} aria-label="Retry loading personal information">
                        Retry
                    </Button>
                </div>
            </section>
        );
    }

    // Only render content if we have data and no error
    if (!personalInfo) {
        return <PersonalSettingsSkeleton />;
    }

    // Format date of birth if available
    const formattedDateOfBirth = personalInfo?.dateOfBirth ?? "";

    return (
        <section className="bg-white rounded-[20px] border border-[#E3E5E8] p-6">
            <div className="space-y-6">
                <header>
                    <h2 className="text-xl font-semibold text-black">Personal information</h2>
                </header>

                <div className="space-y-6">
                    <div>
                        <h3 className="text-base font-medium text-black">Full legal name</h3>
                        <div className="flex justify-between items-center mt-1">
                            <p className="text-base text-subText">
                                {personalInfo?.fullName ?? "No full name available"}
                            </p>
                        </div>
                    </div>

                    <div>
                        <h3 className="text-base font-medium text-black">Email address</h3>
                        <div className="flex justify-between items-center mt-1">
                            <p className="text-base text-subText">{personalInfo?.email ?? "No email available"}</p>
                        </div>
                    </div>

                    <div>
                        <EditableField
                            name="preferred-name"
                            label="Preferred name"
                            value={personalInfo?.preferredName ?? ""}
                            emptyValueText="No preferred name available"
                            onSave={handleSave("preferredName")}
                            onSaveWithToken={handleSaveWithToken("preferredName")}
                            requireMfa={true}
                            required
                        />
                    </div>

                    <div>
                        <h3 className="text-base font-medium text-black">Phone number</h3>
                        <div className="flex justify-between items-center mt-1">
                            <p className="text-base text-subText">
                                {personalInfo?.phoneNumber ?? "No phone number available"}
                            </p>
                        </div>
                    </div>

                    <div>
                        <EditableField
                            name="date-of-birth"
                            label="Date of birth"
                            value={formattedDateOfBirth}
                            emptyValueText="No date of birth available"
                            onSave={handleSave("dateOfBirth")}
                            onSaveWithToken={handleSaveWithToken("dateOfBirth")}
                            requireMfa={true}
                            isDatePicker={true}
                            required
                        />
                    </div>

                    <div>
                        <EditableField
                            name="role"
                            value={personalInfo?.role ?? ""}
                            label="Role"
                            emptyValueText="No role available"
                            options={[{ label: "Customer success manager", value: "customer-service" }]}
                            isDropdown={true}
                            onSave={handleSave("role")}
                            onSaveWithToken={handleSaveWithToken("role")}
                            requireMfa={true}
                            required
                            disabled={true}
                        />
                    </div>

                    <section className="pt-6 border-t border-gray-200">
                        <h2 className="text-xl font-semibold text-black">Address information</h2>
                        {isEditingAddress ? (
                            <AddressForm
                                number={number}
                                address={address}
                                city={city}
                                state={state}
                                country={country}
                                countries={countries}
                                isCountriesLoading={isCountriesLoading}
                                toggleAddressEdit={toggleAddressEdit}
                                handleSaveAddress={handleSaveAddress}
                                handleSaveAddressWithToken={handleSaveAddressWithToken}
                                updateLoading={updateLoading}
                            />
                        ) : (
                            <AddressDisplay address={personalInfo?.address} toggleAddressEdit={toggleAddressEdit} />
                        )}
                    </section>
                </div>
            </div>
        </section>
    );
};
