"use client";

import { useEffect, useState, useCallback } from "react";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { fetchPersonalInfo } from "@/redux/actions/settingsActions";

import LoginTransactionsSection from "@/components/page-components/dashboard/settings/ui/security/LoginTransactionsSection";
import ChangePasswordModal from "@/components/page-components/dashboard/settings/ui/security/ChangePasswordModal";
import ChangePinModal from "@/components/page-components/dashboard/settings/ui/security/ChangePinModal";
import ManageDevicesModal from "@/components/page-components/dashboard/settings/ui/security/ManageDevicesModal";
import TwoFaSection from "@/components/page-components/dashboard/settings/ui/security/TwoFaSection";
import SettingsMfaVerification from "./components/settings-mfa-verification";
import { getTeamMemberDetails } from "@/redux/actions/transferMfaActions";
import { resetAllStates } from "@/redux/slices/settingsMfaSlice";

export const SecuritySettings = () => {
    const [isChangePinModalOpen, setIsChangePinModalOpen] = useState(false);
    const [isChangePasswordModalOpen, setIsChangePasswordModalOpen] = useState(false);
    const [isManageDevicesModalOpen, setIsManageDevicesModalOpen] = useState(false);

    const dispatch = useAppDispatch();

    // MFA verification for PIN change
    const [initiatePinChangeMfaFlow, setInitiatePinChangeMfaFlow] = useState(false);
    // MFA verification for Password change
    const [initiatePasswordChangeMfaFlow, setInitiatePasswordChangeMfaFlow] = useState(false);
    const { success: teamMemberSuccess } = useAppSelector((state) => state.transferMfaSlice.getTeamMemberDetails);
    const teamMember = useAppSelector((state) => state.transferMfaSlice.teamMember);

    const [mfaVerificationState, setMfaVerificationState] = useState({
        mfaVerified: false,
        showMfaModal: false,
        actionType: null as "pin" | "password" | null, // Track which action triggered MFA
        token: null as string | null, // Store MFA verification token
    });

    // Handle Change PIN button click - initiate MFA verification first
    const handleChangePinClick = () => {
        dispatch(getTeamMemberDetails());
        setInitiatePinChangeMfaFlow(true);
        setMfaVerificationState((prev) => ({ ...prev, actionType: "pin" }));
    };

    // Handle Change Password button click - initiate MFA verification first
    const handleChangePasswordClick = () => {
        dispatch(getTeamMemberDetails());
        setInitiatePasswordChangeMfaFlow(true);
        setMfaVerificationState((prev) => ({ ...prev, actionType: "password" }));
    };

    // Handle MFA verification completion
    const handleMfaVerified = useCallback(
        (token: string) => {
            const actionType = mfaVerificationState.actionType;

            setMfaVerificationState({
                mfaVerified: true,
                showMfaModal: false,
                actionType: null,
                token: token,
            });
            setInitiatePinChangeMfaFlow(false);
            setInitiatePasswordChangeMfaFlow(false);

            // Open the appropriate modal based on action type
            if (actionType === "pin") {
                setIsChangePinModalOpen(true);
            } else if (actionType === "password") {
                setIsChangePasswordModalOpen(true);
            }
        },
        [mfaVerificationState.actionType]
    );

    // Handle team member details response
    useEffect(() => {
        if (teamMemberSuccess && teamMember && (initiatePinChangeMfaFlow || initiatePasswordChangeMfaFlow)) {
            // Check if user has MFA enabled
            if (teamMember?.mfaStatus) {
                // Show MFA verification modal
                setMfaVerificationState((prev) => ({
                    ...prev,
                    mfaVerified: false,
                    showMfaModal: true,
                }));
            } else {
                // No MFA required, directly show the appropriate modal
                handleMfaVerified(""); // Pass empty token when no MFA is required
            }
        }
    }, [teamMemberSuccess, teamMember, initiatePinChangeMfaFlow, initiatePasswordChangeMfaFlow, handleMfaVerified]);

    // Handle MFA verification modal close
    const handleMfaVerificationClose = () => {
        setMfaVerificationState({
            mfaVerified: false,
            showMfaModal: false,
            actionType: null,
            token: null,
        });
        setInitiatePinChangeMfaFlow(false);
        setInitiatePasswordChangeMfaFlow(false);
    };

    // Handle PIN operation errors - close modals and reset state
    const handlePinError = useCallback(() => {
        // // Close all PIN-related modals

        // Reset MFA flow flags
        setInitiatePinChangeMfaFlow(false);

        // Reset all MFA states in Redux
        dispatch(resetAllStates());

        const timer = setTimeout(() => {
            // Reset the submission flag
            setIsChangePinModalOpen(false);

            // Reset MFA verification state
            setMfaVerificationState({
                mfaVerified: false,
                showMfaModal: false,
                actionType: null,
                token: null,
            });
        }, 1000); // 1 second delay

        return () => clearTimeout(timer);
    }, [dispatch]);

    // Handle password operation errors - close modals and reset state
    const handlePasswordError = useCallback(() => {
        // Reset MFA flow flags
        setInitiatePasswordChangeMfaFlow(false);

        // Reset all MFA states in Redux
        dispatch(resetAllStates());

        const timer = setTimeout(() => {
            // Reset the submission flag
            setIsChangePasswordModalOpen(false);

            // Reset MFA verification state
            setMfaVerificationState({
                mfaVerified: false,
                showMfaModal: false,
                actionType: null,
                token: null,
            });
        }, 1000); // 1 second delay

        return () => clearTimeout(timer);
    }, [dispatch]);

    // Handle Change Password Modal close - reset MFA state
    const handleChangePasswordModalClose = useCallback(() => {
        setIsChangePasswordModalOpen(false);

        // Reset MFA verification state to require fresh verification for next action
        setMfaVerificationState({
            mfaVerified: false,
            showMfaModal: false,
            actionType: null,
            token: null,
        });

        // Reset MFA flow flags
        setInitiatePasswordChangeMfaFlow(false);

        // Reset all MFA states in Redux
        dispatch(resetAllStates());
    }, [dispatch]);

    // Handle Change PIN Modal close - reset MFA state
    const handleChangePinModalClose = useCallback(() => {
        setIsChangePinModalOpen(false);

        // Reset MFA verification state to require fresh verification for next action
        setMfaVerificationState({
            mfaVerified: false,
            showMfaModal: false,
            actionType: null,
            token: null,
        });

        // Reset MFA flow flags
        setInitiatePinChangeMfaFlow(false);

        // Reset all MFA states in Redux
        dispatch(resetAllStates());
    }, [dispatch]);

    // Get security and settings states to monitor success
    const { changePin } = useAppSelector((state) => state.security);
    const { changePassword } = useAppSelector((state) => state.settings);

    // Clear MFA state after successful PIN change
    useEffect(() => {
        if (changePin.success) {
            dispatch(resetAllStates());
            // Reset MFA verification state
            setMfaVerificationState({
                mfaVerified: false,
                showMfaModal: false,
                actionType: null,
                token: null,
            });
        }
    }, [changePin.success, dispatch]);

    useEffect(() => {
        if (changePin.error) {
            dispatch(resetAllStates());
            // Reset MFA verification state

            const timer = setTimeout(() => {
                // Reset the submission flag
                setIsChangePinModalOpen(false);

                // Reset MFA verification state
                setMfaVerificationState({
                    mfaVerified: false,
                    showMfaModal: false,
                    actionType: null,
                    token: null,
                });
            }, 1000); // 1 second delay

            return () => clearTimeout(timer);
        }
    }, [changePin.error, dispatch]);

    // Clear MFA state after successful password change
    useEffect(() => {
        if (changePassword.success) {
            dispatch(resetAllStates());
            // Reset MFA verification state
            setMfaVerificationState({
                mfaVerified: false,
                showMfaModal: false,
                actionType: null,
                token: null,
            });
        }
    }, [changePassword.success, dispatch]);

    // Clear MFA state after password change error
    useEffect(() => {
        if (changePassword.error) {
            // Reset MFA verification state to prevent stale success feedback
            dispatch(resetAllStates());

            const timer = setTimeout(() => {
                // Reset the submission flag
                setIsChangePasswordModalOpen(false);

                // Reset MFA verification state
                setMfaVerificationState({
                    mfaVerified: false,
                    showMfaModal: false,
                    actionType: null,
                    token: null,
                });
            }, 1000); // 1 second delay

            return () => clearTimeout(timer);
        }
    }, [changePassword.error, dispatch]);

    // Fetch personal info when component mounts
    useEffect(() => {
        dispatch(fetchPersonalInfo());
    }, [dispatch]);

    return (
        <div data-testid="security-tab" className="bg-white rounded-lg border border-gray-100 p-6 shadow-sm">
            <ChangePasswordModal
                isOpen={isChangePasswordModalOpen}
                onRequestClose={handleChangePasswordModalClose}
                token={mfaVerificationState.token}
                onError={handlePasswordError}
            />
            <ChangePinModal
                isOpen={isChangePinModalOpen}
                onRequestClose={handleChangePinModalClose}
                token={mfaVerificationState.token}
                onError={handlePinError}
            />
            <ManageDevicesModal
                isOpen={isManageDevicesModalOpen}
                onRequestClose={() => setIsManageDevicesModalOpen(false)}
            />
            <div className="space-y-8">
                <h1 className="text-xl font-semibold text-black pb-5 border-b-[#E3E5E8] border-b">
                    Security & Authentication
                </h1>

                <LoginTransactionsSection
                    onChangePassword={handleChangePasswordClick}
                    onChangePin={handleChangePinClick}
                />

                <TwoFaSection />
            </div>

            {/* MFA Verification Modal for PIN and Password Changes */}
            {mfaVerificationState.showMfaModal && teamMember && (
                <SettingsMfaVerification
                    userMfaType={teamMember?.preferredMfaMethod}
                    onClose={handleMfaVerificationClose}
                    isOpen={true}
                    onVerified={handleMfaVerified}
                    email={teamMember?.email}
                    phoneNumber={teamMember?.phoneNumber}
                />
            )}
        </div>
    );
};
