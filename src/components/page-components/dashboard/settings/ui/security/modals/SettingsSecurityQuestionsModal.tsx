"use client";

import React, { useState, useEffect, useCallback } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import CustomModal from "@/components/common/custom-modal";
import { Button } from "@/components/common/buttonv3";
import LabelInput from "@/components/common/label-input";
import Dropdown from "@/components/common/dropdown";
import { sendCatchFeedback, sendFeedback } from "@/functions/feedback";
import { useAppSelector } from "@/redux/hooks";
import { userAxios } from "@/api/axios";

// Define the security question type
interface SecurityQuestion {
    id: number;
    question: string;
}

interface SettingsSecurityQuestionsModalProps {
    isOpen: boolean;
    onClose: () => void;
    onBack: () => void;
    onSetupComplete: () => void;
}

const SettingsSecurityQuestionsModal: React.FC<SettingsSecurityQuestionsModalProps> = ({
    isOpen,
    onClose,
    onBack,
    onSetupComplete,
}) => {
    const user = useAppSelector((state) => state.user.user);
    const userEmail = user?.email;

    const [securityQuestions, setSecurityQuestions] = useState<SecurityQuestion[]>([]);
    const [requiredQuestions, setRequiredQuestions] = useState(3);
    const [loadingQuestions, setLoadingQuestions] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [questionsLoaded, setQuestionsLoaded] = useState(false);

    // Dynamically generate validation schema based on available questions
    const generateValidationSchema = (questionCount: number) => {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const schema: any = {};

        for (let i = 1; i <= questionCount; i++) {
            // Add question validation
            schema[`question${i}`] = Yup.string().required("Security question is required");

            // Add answer validation
            schema[`answer${i}`] = Yup.string().required("Answer is required");
        }

        return Yup.object(schema);
    };

    // Generate initial values based on question count
    const generateInitialValues = (questionCount: number) => {
        const values: Record<string, string> = {};
        for (let i = 1; i <= questionCount; i++) {
            values[`question${i}`] = "";
            values[`answer${i}`] = "";
        }
        return values;
    };

    // Initialize form with Formik
    const formik = useFormik({
        initialValues: generateInitialValues(requiredQuestions),
        validationSchema: generateValidationSchema(requiredQuestions),
        validateOnChange: true,
        validateOnBlur: true,
        enableReinitialize: true, // Important to handle dynamic schema changes
        onSubmit: async (values) => {
            await handleSubmitSecurityQuestions(values);
        },
    });

    // Handle form submission for settings
    const handleSubmitSecurityQuestions = async (values: Record<string, string>) => {
        if (!userEmail) {
            sendCatchFeedback("Email address is required");
            return;
        }

        setIsSubmitting(true);
        try {
            // Format the security questions and answers for submission
            const securityQuestionsData = {
                userEmail: userEmail,
                corporateId: user?.corporateId || 0,
                userSecurityQuestionRequests: [] as Array<{ question: string; answer: string }>,
            };

            // Always process exactly 3 questions (or fewer if not enough are available)
            const questionsToProcess = Math.min(3, securityQuestions.length);

            for (let i = 1; i <= questionsToProcess; i++) {
                const questionId = values[`question${i}`];
                const answer = values[`answer${i}`];

                if (questionId && answer) {
                    const selectedQuestion = securityQuestions.find((q) => q.id.toString() === questionId);

                    if (!selectedQuestion) {
                        throw new Error(`Question with ID ${questionId} not found`);
                    }

                    securityQuestionsData.userSecurityQuestionRequests.push({
                        question: selectedQuestion.question,
                        // Normalize answer to lowercase for case-insensitive validation
                        answer: answer.trim().toLowerCase(),
                    });
                }
            }

            // Submit security questions
            await userAxios.post("/v1/user-security-questions", securityQuestionsData);

            // Update preferred MFA method
            await userAxios.patch(`/v1/team-members?emailAddress=${userEmail}&mfaMethod=SECURITY_QUESTION`);

            sendFeedback("Security questions setup completed successfully", "success");
            onSetupComplete();
        } catch (error) {
            sendCatchFeedback(error);
        } finally {
            setIsSubmitting(false);
        }
    };

    // Get all currently selected question IDs except the current one
    const getSelectedQuestionIds = () => {
        const selectedIds = [];

        // Always check exactly 3 questions (or fewer if not enough are available)
        const questionsToCheck = Math.min(3, securityQuestions.length);

        for (let i = 1; i <= questionsToCheck; i++) {
            const questionValue = formik.values[`question${i}`];
            if (questionValue) {
                selectedIds.push(questionValue);
            }
        }

        return selectedIds;
    };

    // Filter questions for a specific dropdown
    const getFilteredQuestions = (currentFieldName: string) => {
        const currentValue = formik.values[currentFieldName];
        const allSelectedIds = getSelectedQuestionIds();

        // Return all questions if nothing is selected yet
        if (allSelectedIds.length === 0) {
            return securityQuestions.map((q) => ({ value: q.id.toString(), label: q.question }));
        }

        // Filter out questions that are already selected in other dropdowns
        return securityQuestions
            .filter((question) => {
                const questionId = question.id.toString();
                // Include this question if it's the current selection or not selected elsewhere
                return questionId === currentValue || !allSelectedIds.includes(questionId);
            })
            .map((q) => ({ value: q.id.toString(), label: q.question }));
    };

    // Handle dropdown change to clear any duplicate selections
    const handleQuestionChange = (fieldName: string, value: string) => {
        // Update the current field
        formik.setFieldValue(fieldName, value);

        // Always check exactly 3 questions (or fewer if not enough are available)
        const questionsToCheck = Math.min(3, securityQuestions.length);

        // Check for duplicates in other fields and clear them
        for (let i = 1; i <= questionsToCheck; i++) {
            const otherFieldName = `question${i}`;

            // Skip the current field
            if (otherFieldName === fieldName) continue;

            // If another field has the same value, clear it
            if (formik.values[otherFieldName] === value) {
                formik.setFieldValue(otherFieldName, "");
            }
        }
    };

    // Fetch available security questions
    const fetchSecurityQuestions = useCallback(async () => {
        if (questionsLoaded) return; // Prevent multiple fetches

        setLoadingQuestions(true);
        try {
            const response = await userAxios.get("/v1/security-questions");
            const questions = response.data;
            setSecurityQuestions(questions);

            // Always require exactly 3 questions regardless of how many are available
            const availableCount = questions.length;
            const requiredCount = Math.min(3, availableCount);
            setRequiredQuestions(requiredCount);
            setQuestionsLoaded(true);

            // Note: Form reset will be handled in a separate useEffect when requiredQuestions changes
        } catch (error) {
            sendCatchFeedback(error);
        } finally {
            setLoadingQuestions(false);
        }
    }, [questionsLoaded]); // Only depend on questionsLoaded flag

    // Load questions when modal opens
    useEffect(() => {
        if (isOpen && !questionsLoaded) {
            fetchSecurityQuestions();
        }
    }, [isOpen, questionsLoaded, fetchSecurityQuestions]);

    // Reset form when requiredQuestions changes (after fetching questions)
    useEffect(() => {
        if (questionsLoaded && requiredQuestions > 0) {
            // Use setTimeout to avoid the infinite loop by deferring the form reset
            const timer = setTimeout(() => {
                formik.resetForm({
                    values: generateInitialValues(requiredQuestions),
                    errors: {},
                });
            }, 0);

            return () => clearTimeout(timer);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [questionsLoaded, requiredQuestions]); // Intentionally excluding formik

    // Reset everything when modal closes
    useEffect(() => {
        if (!isOpen) {
            // Use setTimeout to avoid the infinite loop
            const timer = setTimeout(() => {
                formik.resetForm();
            }, 0);

            setSecurityQuestions([]);
            setRequiredQuestions(3);
            setQuestionsLoaded(false);

            return () => clearTimeout(timer);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isOpen]); // Intentionally excluding formik

    // Check if form is valid - matching auth pattern
    const isFormValid = () => {
        for (let i = 1; i <= requiredQuestions; i++) {
            if (!formik.values[`question${i}`] || !formik.values[`answer${i}`]) {
                return false;
            }
        }
        return formik.isValid;
    };

    // Loading state - matching auth pattern
    if (loadingQuestions) {
        return (
            <CustomModal isOpen={isOpen} onRequestClose={onClose} title="Set up security questions" width="600px">
                <div className="space-y-6">
                    <button
                        onClick={onBack}
                        className="flex items-center gap-2 text-sm text-primary hover:text-primary/80 transition-colors"
                    >
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                            <path d="M10 12l-4-4 4-4" stroke="currentColor" strokeWidth="2" fill="none" />
                        </svg>
                        Back
                    </button>
                    <div className="flex items-center justify-center py-12">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
                    </div>
                </div>
            </CustomModal>
        );
    }

    // Error state
    if (securityQuestions.length === 0 && questionsLoaded) {
        return (
            <CustomModal isOpen={isOpen} onRequestClose={onClose} title="Set up security questions" width="600px">
                <div className="space-y-6">
                    <button
                        onClick={onBack}
                        className="flex items-center gap-2 text-sm text-primary hover:text-primary/80 transition-colors"
                    >
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                            <path d="M10 12l-4-4 4-4" stroke="currentColor" strokeWidth="2" fill="none" />
                        </svg>
                        Back
                    </button>
                    <div className="text-center py-8">
                        <p className="text-subText">No security questions available. Please contact support.</p>
                    </div>
                </div>
            </CustomModal>
        );
    }

    // Main form - matching auth pattern
    return (
        <CustomModal isOpen={isOpen} onRequestClose={onClose} title="Set up security questions" width="600px">
            <div className="space-y-6">
                {/* Back button */}
                <button
                    onClick={onBack}
                    className="flex items-center gap-2 text-sm text-primary hover:text-primary/80 transition-colors"
                >
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                        <path d="M10 12l-4-4 4-4" stroke="currentColor" strokeWidth="2" fill="none" />
                    </svg>
                    Back
                </button>

                {/* Instructions */}
                <div className="text-sm text-subText">
                    <p>Choose 3 security questions and provide answers that only you would know.</p>
                </div>

                {/* Form */}
                <div className="space-y-6">
                    <form onSubmit={formik.handleSubmit} className="space-y-6">
                        {/* Render 3 question-answer pairs */}
                        {Array.from({ length: 3 }).map((_, index) => {
                            const questionNum = index + 1;
                            const questionField = `question${questionNum}`;
                            const answerField = `answer${questionNum}`;

                            return (
                                <div key={questionField} className="space-y-4">
                                    <Dropdown
                                        options={getFilteredQuestions(questionField)}
                                        name={questionField}
                                        label={`Security Question ${questionNum}`}
                                        placeholder="Select a question"
                                        size="sm"
                                        formik={formik}
                                        onChange={(value) => handleQuestionChange(questionField, value as string)}
                                    />
                                    <LabelInput
                                        formik={formik}
                                        name={answerField}
                                        label="Your Answer"
                                        placeholder="Enter your answer"
                                        showError={formik.touched[answerField]}
                                        masked={true}
                                        hint="Answer is case-insensitive"
                                    />
                                </div>
                            );
                        })}

                        <div className="flex gap-3 pt-4">
                            <Button type="button" variant="outline" className="flex-1" onClick={onBack}>
                                Cancel
                            </Button>
                            <Button
                                type="submit"
                                className="flex-1"
                                disabled={!isFormValid() || isSubmitting}
                                loading={isSubmitting}
                            >
                                Set up
                            </Button>
                        </div>
                    </form>
                </div>
            </div>
        </CustomModal>
    );
};

export default SettingsSecurityQuestionsModal;
