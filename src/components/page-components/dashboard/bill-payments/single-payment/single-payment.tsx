"use client";

import React, { useEffect, useRef, useState } from "react";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { updateBillDetails } from "@/redux/slices/singleBillPayment";
import { fetchPaymentItems } from "@/redux/actions/billPaymentThunks";
import { clearPaymentItems } from "@/redux/slices/billPaymentSlice";
import NormalBillDetails, { NormalBillDetailsValues } from "./normal-bill-details";
import UtilityBillDetails, { UtilityBillDetails as UtilityBillDetailsType } from "./utility-bill-details";
import { useCategoryBillDetails } from "../hooks/use-category-bill-details";
import { useCentralizedBillers } from "../hooks/use-centralized-billers";
import { cleanCategoryName } from "../utils";
import { useSearchParams } from "next/navigation";
import type { Biller } from "../types";

/**
 * Interface for the service provider change callback
 * This enables real-time tracking of form state changes for payment items fetching
 */
interface ServiceProviderChangeData {
    billerName: string;
    billerId: string | number;
    biller: Biller;
}

/**
 * SingleBillPayment component
 *
 * This component implements the dual-approach pattern for bill payments:
 * 1. Specialized handling for "electricity" and "cable-tv" categories using UtilityBillDetails
 * 2. Standard handling for all other categories using NormalBillDetails
 *
 * This approach ensures any new category automatically works with the standard flow
 * while maintaining specialized UI for utility and cable TV categories.
 *
 * RULE COMPLIANCE:
 * - Uses centralized biller management to eliminate inconsistent caching behavior
 * - Implements memoization techniques to prevent unnecessary re-renders
 * - Optimized for performance (CIB serves up to a million users)
 * - Uses null checks with optional chaining (?.) and nullish coalescing (??)
 * - Includes real-time form state tracking for immediate payment items fetching
 */
const SingleBillPayment = () => {
    const params = useSearchParams();
    const dispatch = useAppDispatch();

    // Track the last billerId we fetched payment items for to prevent redundant calls
    // This follows the container/presentation pattern from Frontend Guidelines
    // where containers manage data and components consume it
    const lastFetchedBillerIdRef = useRef<string | null>(null);

    /**
     * State bridge for tracking current form state in real-time
     * This enables the container to react to form field changes immediately
     * rather than waiting for Redux state updates, fixing the core issue
     * where selectedBillerId only responded to Redux state changes
     */
    const [currentServiceProvider, setCurrentServiceProvider] = useState<string>("");
    const [currentBillerId, setCurrentBillerId] = useState<string>("");

    const { categoryId, categoryName, billDetails, billType, navigateToPaymentInfo } = useCategoryBillDetails();
    const categoryIdNum = Number(categoryId);
    const billerName = params.get("biller") || "";
    const referenceNo = params.get("referenceNo") || "";

    // Use centralized biller management hook instead of direct Redux access
    // This eliminates the inconsistent caching behavior observed in bill details components
    const {
        billers,
        loading: billersLoading,
        fetchBillersForCategory,
        hasBillersForCategory,
    } = useCentralizedBillers();

    // Get payment items state from Redux (following Redux Implementation Standards)
    const {
        data: paymentItems,
        loading: paymentItemsLoading,
        error: paymentItemsError,
    } = useAppSelector((state) => state.billPayments.paymentItems);

    /**
     * Handles service provider changes from child components
     * Immediately triggers payment items fetching for the new service provider
     *
     * RULE COMPLIANCE: Uses optional chaining for null safety, React.useCallback for performance
     */
    const handleServiceProviderChange = React.useCallback(
        (data: ServiceProviderChangeData) => {
            // Validate billerId before proceeding - no fallback data
            const billerIdStr = data.billerId?.toString().trim();
            if (!billerIdStr) {
                // Do not proceed if billerId is empty, null, or undefined
                // Clear state to reflect invalid selection
                setCurrentServiceProvider("");
                setCurrentBillerId("");
                dispatch(clearPaymentItems());
                return;
            }

            // Update current service provider and biller ID state for reactive updates
            setCurrentServiceProvider(data.billerName);
            setCurrentBillerId(billerIdStr);

            // Clear existing payment items when service provider changes
            // Following Redux normalization principles
            dispatch(clearPaymentItems());

            // Only fetch payment items if we have a valid billerId and it's different from the last fetched
            if (billerIdStr !== lastFetchedBillerIdRef.current) {
                // Update tracking ref to prevent race conditions
                lastFetchedBillerIdRef.current = billerIdStr;

                // Dispatch payment items fetch using existing Redux thunk
                dispatch(fetchPaymentItems({ billerId: billerIdStr }));
            }
        },
        [dispatch]
    );

    /**
     * Centralized biller fetching logic using the centralized hook
     *
     * This effect handles fetching billers for the current category using the
     * centralized biller management hook, which provides consistent caching
     * behavior across all components and eliminates race conditions.
     *
     * RULE COMPLIANCE:
     * - Uses centralized hook to prevent inconsistent API call patterns
     * - Implements null checks using optional chaining
     * - Optimized for performance with smart caching
     */
    useEffect(() => {
        // Only proceed if we have a valid categoryId
        if (!categoryId || !categoryIdNum) {
            return;
        }

        // Check if we need to fetch billers for this category using centralized logic
        if (!hasBillersForCategory(categoryIdNum) && !billersLoading) {
            // Use centralized fetch function which handles all caching logic internally
            fetchBillersForCategory(categoryIdNum);
        }
    }, [categoryId, categoryIdNum, hasBillersForCategory, billersLoading, fetchBillersForCategory]);

    // Initialize form state from Redux state on component mount
    useEffect(() => {
        if (billDetails?.serviceProvider && !currentServiceProvider) {
            setCurrentServiceProvider(billDetails.serviceProvider);

            // Find the corresponding biller ID
            if (billers) {
                const selectedBiller = billers.find(
                    (biller) =>
                        (biller.billername &&
                            biller.billername.toLowerCase() === billDetails.serviceProvider?.toLowerCase()) ||
                        (biller.billerName &&
                            biller.billerName.toLowerCase() === billDetails.serviceProvider?.toLowerCase())
                );
                if (selectedBiller) {
                    const billerId = (selectedBiller.billerid ?? selectedBiller.billerId)?.toString();
                    setCurrentBillerId(billerId ?? "");
                }
            }
        }
    }, [billDetails?.serviceProvider, billers, currentServiceProvider]);

    // Determine the selected biller ID based on current form state and Redux state
    // This now reacts to both form changes and Redux state, fixing the core issue
    // RULE COMPLIANCE: Uses memoization to prevent unnecessary re-renders
    const selectedBillerId = React.useMemo(() => {
        // Prioritize current form state over Redux state for real-time reactivity
        if (currentBillerId) {
            return currentBillerId;
        }

        // Fallback to Redux state (for backward compatibility and initial load)
        if (!billDetails?.serviceProvider || !billers) {
            return undefined;
        }

        const selectedBiller = billers.find(
            (biller) =>
                (biller.billername && biller.billername.toLowerCase() === billDetails.serviceProvider?.toLowerCase()) ||
                (biller.billerName && biller.billerName.toLowerCase() === billDetails.serviceProvider?.toLowerCase())
        );

        const billerId = selectedBiller ? (selectedBiller.billerid ?? selectedBiller.billerId)?.toString() : undefined;

        return billerId;
    }, [currentBillerId, billers, billDetails?.serviceProvider]);

    /**
     * Centralized payment items fetching with smart caching
     *
     * This optimization is crucial for the CIB application serving millions of users
     * Only fetches when necessary to minimize network requests and improve performance
     * Following the container/presentation pattern from Frontend Guidelines
     * where containers manage data and components consume it
     *
     * RULE COMPLIANCE:
     * - Uses optional chaining for null safety
     * - Implements smart caching to optimize performance
     */
    useEffect(() => {
        // Only proceed if we have a valid selectedBillerId
        // Using optional chaining for null safety as per Frontend Guidelines
        if (!selectedBillerId) {
            return;
        }

        // Check if we need to fetch payment items for this biller
        // Smart caching logic prevents redundant API calls
        const shouldFetchPaymentItems =
            // We don't have payment items loaded yet
            !paymentItems ||
            paymentItems.length === 0 ||
            // OR the billerId has changed from what we last fetched
            lastFetchedBillerIdRef.current !== selectedBillerId;

        // Only fetch if conditions are met and we're not already loading
        // This prevents race conditions and duplicate API calls
        if (shouldFetchPaymentItems && !paymentItemsLoading) {
            // Update the tracking ref before dispatching to prevent race conditions
            lastFetchedBillerIdRef.current = selectedBillerId;

            // Dispatch the API call to fetch payment items for this biller
            // Using existing Redux thunk following API Integration Standards
            dispatch(fetchPaymentItems({ billerId: selectedBillerId }));
        }
    }, [selectedBillerId, paymentItems, paymentItemsLoading, paymentItemsError, dispatch]);

    /**
     * Handles submission for standard bill categories (airtime and all others)
     * Updates the Redux store with the standard fields and navigates to payment info
     *
     * @param values - The form values from NormalBillDetails
     */
    const handleNormalSubmit = (values: NormalBillDetailsValues) => {
        dispatch(
            updateBillDetails({
                networkProvider: values.networkProvider,
                phoneNumber: values.phoneNumber,
                amount: values.amount,
                customerDetails: values.customerDetails,
            })
        );
        navigateToPaymentInfo();
    };

    /**
     * Handles submission for specialized utility bill categories
     * Updates the Redux store with category-specific fields and navigates to payment info
     *
     * @param values - The form values from UtilityBillDetails
     */
    const handleUtilitySubmit = (values: UtilityBillDetailsType) => {
        // Determine which field to use based on the bill type
        const submissionData =
            values.type === "electricity"
                ? {
                      identificationNumber: values.customerId,
                      serviceProvider: values.serviceProvider,
                      amount: values.amount,
                      customerDetails: values.customerDetails,
                      package: values.package,
                      serviceID: values.paymentCode,
                  }
                : {
                      smartCardNumber: values.customerId,
                      serviceProvider: values.serviceProvider,
                      package: values.package,
                      amount: values.amount,
                      customerDetails: values.customerDetails,
                      serviceID: values.paymentCode,
                  };

        dispatch(updateBillDetails(submissionData));
        navigateToPaymentInfo();
    };

    /**
     * Creates a user-friendly title for the bill payment page
     * based on the category name
     *
     * @returns A formatted title string for the bill payment page
     */
    const getCustomTitle = () => {
        if (!categoryName) return "Bill payment";

        // Clean the category name and add "bill payment" suffix
        const cleanedName = cleanCategoryName(categoryName);
        return `${cleanedName} bill payment`;
    };

    /**
     * Renders the appropriate component based on bill type
     * Implements the dual-approach pattern:
     * - UtilityBillDetails for "electricity" and "cable-tv" types
     * - NormalBillDetails for all other bill types
     *
     * This ensures any new category automatically works with the standard component
     *
     * @returns The appropriate component for the current bill type
     */
    const renderComponent = () => {
        if (!categoryId) return null;

        // The categories that require specialized utility components
        if (billType === "electricity") {
            return (
                <UtilityBillDetails
                    onSubmit={handleUtilitySubmit}
                    defaultValues={{
                        serviceProvider: billDetails?.serviceProvider ?? billerName ?? "",
                        identificationNumber: billDetails?.identificationNumber ?? referenceNo ?? "",
                        customerId: billDetails?.identificationNumber ?? referenceNo ?? "",
                        amount: String(billDetails?.amount ?? ""),
                        customerDetails: billDetails?.customerDetails ?? "",
                        package: billDetails?.package ?? "",
                        paymentCode: billDetails?.serviceID ?? "",
                    }}
                    categoryId={categoryIdNum}
                    billType="electricity"
                    onServiceProviderChange={handleServiceProviderChange}
                />
            );
        } else if (billType === "cable-tv") {
            return (
                <UtilityBillDetails
                    onSubmit={handleUtilitySubmit}
                    defaultValues={{
                        serviceProvider: billDetails?.serviceProvider || billerName || "",
                        smartCardNumber: billDetails?.smartCardNumber || referenceNo || "",
                        customerId: billDetails?.smartCardNumber || referenceNo || "",
                        package: billDetails?.package || "",
                        amount: String(billDetails?.amount || ""),
                        customerDetails: billDetails?.customerDetails || "",
                        paymentCode: billDetails?.serviceID || "",
                    }}
                    categoryId={categoryIdNum}
                    billType="cable-tv"
                    onServiceProviderChange={handleServiceProviderChange}
                />
            );
        } else {
            // For airtime and all other bill types, use the normal component
            // This ensures any new category automatically works with the standard component
            return (
                <NormalBillDetails
                    onSubmit={handleNormalSubmit}
                    initialValues={{
                        networkProvider: billDetails?.networkProvider || billerName || "",
                        phoneNumber: billDetails?.phoneNumber || referenceNo || "",
                        amount: String(billDetails?.amount || ""),
                    }}
                    categoryId={categoryIdNum}
                    title={getCustomTitle()}
                    categoryName={categoryName} // Pass category name for validation decisions
                    onServiceProviderChange={handleServiceProviderChange}
                />
            );
        }
    };

    return <div>{renderComponent()}</div>;
};

export default SingleBillPayment;
