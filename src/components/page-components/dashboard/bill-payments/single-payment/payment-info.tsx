/**
 * @file Payment Information Component
 *
 * @purpose This file provides the form for selecting payment account and entering payment details.
 *
 * @functionality The PaymentInfo component serves as the second step in the single bill payment flow.
 * It allows users to select an account to pay from (displaying available balance) and enter
 * an optional narration (description) for the payment. The component handles form state persistence
 * through Redux, implements optimized state updates with debouncing to prevent redundant renders,
 * and enforces navigation constraints (disabling the continue button until a valid account is selected).
 * It includes proper form cleanup and validation, exit confirmation handling, and responsive UI feedback.
 *
 * @dependencies
 * - Redux for state management and form persistence
 * - Common UI components (But<PERSON>, AccountSelector, LabelTextArea)
 * - Stepper for navigation progress indication
 * - Custom hooks for exit confirmation handling
 *
 * @usage This component is used as the second step in the bill payment flow, after bill details
 * have been collected. It receives bill details from the previous step, collects account and
 * narration information, and passes the combined data to the review step.
 */

"use client";

import { useState, FormEvent, useEffect, useRef, useCallback } from "react";
import { useFormik, FormikProps } from "formik";
import * as yup from "yup";
import { Button } from "@/components/common/buttonv3";
import Stepper from "@/components/common/stepper";
import { singlePaymentSteps } from "../utils/data";
import { useExitHandlers } from "../hooks/useExitHandlers";
import FullScreenDrawer from "@/components/common/full-screen-drawer";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { updatePaymentInfo, resetSingleBillPayment } from "@/redux/slices/singleBillPayment";
import { resetAccounts, setSelectedAccount } from "@/redux/features/accounts";
import { InfoIcon } from "@/components/icons/auth";

import AccountSelector from "../common/account-selector";
import LabelTextArea from "@/components/common/text-area";
import { formatAccountName, formatAccountNumber } from "../common/account-utils";

interface BaseBillDetails {
    amount: number;
    serviceProvider: string;
    package: string;
}

interface CableTVBillDetails extends BaseBillDetails {
    type: "cable-tv";
    smartCardNumber: string;
}

interface ElectricityBillDetails extends BaseBillDetails {
    type: "electricity";
    identificationNumber: string;
}

interface AirtimeBillDetails extends BaseBillDetails {
    type: "airtime";
    phoneNumber: string;
}

export type BillDetails = CableTVBillDetails | ElectricityBillDetails | AirtimeBillDetails;

interface PaymentInfo {
    narration: string;
    accountNumber: string;
}

// Validation schema for the payment form
const paymentValidationSchema = yup.object({
    narration: yup.string().trim().required("Narration is required"),
    accountNumber: yup.string().required("Account is required"),
});

interface PaymentInfoProps {
    onBack: () => void;
    onContinue: (values: PaymentInfo) => void;
    billDetails: BillDetails;
    defaultValues: PaymentInfo;
    onFieldChange?: (field: keyof PaymentInfo, value: string) => void;
    categoryName?: string; // Optional category name for dynamic headers
}

const PaymentForm: React.FC<{
    onSubmit: (e: FormEvent) => void;
    narration: string;
    setNarration: (value: string) => void;
    accountNumber: string;
    setAccountNumber: (value: string) => void;
    onBack: () => void;
    isSubmitting: boolean;
    disableContinue: boolean;
    onAccountChange: (accountNumber: string) => void;
    formik: FormikProps<PaymentInfo>;
}> = ({
    onSubmit,
    narration,
    setNarration,
    accountNumber,
    onBack,
    isSubmitting,
    disableContinue,
    onAccountChange,
    formik,
}) => (
    <form onSubmit={onSubmit} className="w-full space-y-6" aria-label="Payment information form">
        <div className="space-y-2">
            <AccountSelector selectedAccount={accountNumber} onAccountChange={onAccountChange} />
        </div>

        <div className="space-y-2">
            <LabelTextArea
                name="narration"
                label="Narration *"
                placeholder="Write something here..."
                value={narration}
                onChange={(e) => setNarration(e.target.value)}
                className="pt-5 pb-5 pl-3 pr-3"
                minHeight="63px"
                maxHeight="200px"
            />
            {formik.touched.narration && formik.errors.narration && (
                <div className="flex items-center gap-[0.25rem] text-[#D92D20] text-[14px] font-medium leading-[18px] tracking-[0.28px]">
                    <InfoIcon data-testid="info-icon" />
                    <span className="text-[14px] font-medium leading-[18px] tracking-[0.28px] text-[#D92D20]">
                        {formik.errors.narration}
                    </span>
                </div>
            )}
        </div>

        <div className="pt-4 flex justify-end gap-3">
            <Button
                type="button"
                variant="outline"
                size="medium"
                className="text-sm font-semibold"
                onClick={onBack}
                disabled={isSubmitting}
            >
                Previous
            </Button>
            <Button
                type="submit"
                variant="primary"
                size="medium"
                className="text-sm font-semibold"
                disabled={isSubmitting || disableContinue}
            >
                {isSubmitting ? "Processing..." : "Next"}
            </Button>
        </div>
    </form>
);

const PaymentInfo: React.FC<PaymentInfoProps> = ({
    onBack,
    onContinue,
    defaultValues,
    onFieldChange = () => {},
    categoryName,
}) => {
    const dispatch = useAppDispatch();
    const persistedPaymentInfo = useAppSelector((state) => state.singleBillPayment.paymentInfo);

    // Use the correct accounts state where data is actually stored
    const { accounts, loadingStatus } = useAppSelector((state) => state.account);
    const accountsLoading = loadingStatus === "loading";

    const { isOpen, showExitConfirmation, handleClose, handleConfirmExit, handleCancelExit } = useExitHandlers({
        onExit: () => {
            dispatch(resetSingleBillPayment());
            dispatch(resetAccounts());
        },
    });

    // Initialize state from persisted values or defaults only once
    const [narration, setNarration] = useState(() => persistedPaymentInfo.narration || defaultValues.narration);

    const [accountNumber, setAccountNumber] = useState(() => persistedPaymentInfo.accountNumber || "");

    const [isSubmitting, setIsSubmitting] = useState(false);

    // Initialize Formik for validation only
    const formik = useFormik({
        initialValues: {
            narration,
            accountNumber,
        },
        validationSchema: paymentValidationSchema,
        validateOnChange: true,
        validateOnBlur: true,
        onSubmit: async () => {
            // Validate form before submission
            const errors = await formik.validateForm();
            if (Object.keys(errors).length > 0) {
                // Touch all fields to show errors
                Object.keys(formik.values).forEach((field) => {
                    formik.setFieldTouched(field, true);
                });
                return;
            }

            // Prevent multiple submissions
            if (isSubmitting) return;

            setIsSubmitting(true);

            // Dispatch to Redux first
            dispatch(
                updatePaymentInfo({
                    narration,
                    accountNumber,
                })
            );

            // Then immediately continue to next page
            onContinue({
                narration,
                accountNumber,
            });
        },
    });

    // Sync Formik values with local state
    useEffect(() => {
        formik.setFieldValue("narration", narration);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [narration]); // Removed formik from dependency array to prevent infinite loops

    useEffect(() => {
        formik.setFieldValue("accountNumber", accountNumber);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [accountNumber]); // Removed formik from dependency array to prevent infinite loops

    // Auto-select first account when accounts are loaded and no account is selected
    useEffect(() => {
        if (accounts.length > 0 && !accountNumber) {
            const defaultAccount = accounts[0];
            setAccountNumber(defaultAccount.accountNumber);
            dispatch(updatePaymentInfo({ accountNumber: defaultAccount.accountNumber }));

            // Also update accounts.selectedAccount with formatted display string
            const formattedDisplay = `${formatAccountName(defaultAccount)} ${formatAccountNumber(defaultAccount.accountNumber)}`;
            dispatch(setSelectedAccount(formattedDisplay));
        }
    }, [accounts, accountNumber, dispatch]);

    // Handle account change from AccountSelector
    const handleAccountChange = useCallback(
        (selectedAccountNumber: string) => {
            if (selectedAccountNumber && selectedAccountNumber !== accountNumber) {
                setAccountNumber(selectedAccountNumber);
                // Immediately update Redux store
                dispatch(updatePaymentInfo({ accountNumber: selectedAccountNumber }));
                // Also call onFieldChange if provided
                if (onFieldChange) {
                    onFieldChange("accountNumber", selectedAccountNumber);
                }
            }
        },
        [accountNumber, dispatch, onFieldChange]
    );

    // Use useCallback to memoize the handleSubmit function with Formik validation
    const handleSubmit = useCallback(
        async (e: FormEvent) => {
            e.preventDefault();

            // Validate form before submission using Formik
            const errors = await formik.validateForm();
            if (Object.keys(errors).length > 0) {
                // Touch all fields to show errors
                Object.keys(formik.values).forEach((field) => {
                    formik.setFieldTouched(field, true);
                });
                return;
            }

            // Prevent multiple submissions
            if (isSubmitting) return;

            setIsSubmitting(true);

            // Dispatch to Redux first
            dispatch(
                updatePaymentInfo({
                    narration,
                    accountNumber,
                })
            );

            // Then immediately continue to next page
            onContinue({
                narration,
                accountNumber,
            });
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [narration, accountNumber, dispatch, onContinue, isSubmitting]
    );

    // Replace single isInitialMount with individual refs to avoid first-render updates
    const isNarrationInitialMount = useRef(true);
    const isAccountInitialMount = useRef(true);

    // Track the last values to prevent redundant updates
    const lastNarrationRef = useRef(narration);
    const lastAccountNumberRef = useRef(accountNumber);

    // Track timeout IDs to ensure proper cleanup
    const narrationTimeoutRef = useRef<NodeJS.Timeout | null>(null);
    const accountTimeoutRef = useRef<NodeJS.Timeout | null>(null);

    // Update Redux state when narration changes, but only when user explicitly changes them
    useEffect(() => {
        if (isNarrationInitialMount.current) {
            isNarrationInitialMount.current = false;
            return;
        }

        // Skip if the value hasn't actually changed
        if (narration === lastNarrationRef.current) {
            return;
        }

        lastNarrationRef.current = narration;

        // Clear any existing timeouts to prevent multiple updates
        if (narrationTimeoutRef.current) {
            clearTimeout(narrationTimeoutRef.current);
        }

        // Use a timeout to avoid immediate state updates that could cause loops
        narrationTimeoutRef.current = setTimeout(() => {
            dispatch(updatePaymentInfo({ narration }));
            narrationTimeoutRef.current = null;
        }, 300); // Use a debounce of 300ms

        return () => {
            if (narrationTimeoutRef.current) {
                clearTimeout(narrationTimeoutRef.current);
            }
        };
    }, [narration, dispatch]);

    useEffect(() => {
        if (isAccountInitialMount.current) {
            isAccountInitialMount.current = false;
            return;
        }

        // Skip if the value hasn't actually changed
        if (accountNumber === lastAccountNumberRef.current) {
            return;
        }

        lastAccountNumberRef.current = accountNumber;

        // Clear any existing timeouts to prevent multiple updates
        if (accountTimeoutRef.current) {
            clearTimeout(accountTimeoutRef.current);
        }

        // Use a timeout to avoid immediate state updates that could cause loops
        accountTimeoutRef.current = setTimeout(() => {
            dispatch(updatePaymentInfo({ accountNumber }));
            if (onFieldChange) {
                onFieldChange("accountNumber", accountNumber);
            }
            accountTimeoutRef.current = null;
        }, 300); // Use a debounce of 300ms

        return () => {
            if (accountTimeoutRef.current) {
                clearTimeout(accountTimeoutRef.current);
            }
        };
    }, [accountNumber, dispatch, onFieldChange]);

    // Determine which steps to use based on bill type
    const getSteps = () => singlePaymentSteps;

    // Get the page title based on bill type and category name
    const getPageTitle = () => {
        // If a category name is provided, use it for the title
        if (categoryName) {
            return `${categoryName} bill payment`;
        }

        return "Bill payment";
    };

    return (
        <FullScreenDrawer
            isOpen={isOpen}
            onClose={handleClose}
            title={getPageTitle()}
            showExitConfirmation={showExitConfirmation}
            onConfirmExit={handleConfirmExit}
            onCancelExit={handleCancelExit}
            showSupport={true}
            disablePadding={true}
        >
            <div className="flex-1 flex min-h-0 relative flex-col lg:flex-row">
                <nav
                    className="lg:absolute left-0 top-0 bottom-0 w-[240px] pt-8 px-14 pb-14 bg-white"
                    aria-label="Payment progress"
                >
                    <Stepper steps={getSteps()} currentStep={2} />
                </nav>

                <main className="flex-1 flex justify-center overflow-y-auto px-primary lg:px-0">
                    <div className="w-full max-w-screen-sm lg:max-w-[470px] flex flex-col items-center pt-8">
                        <h2
                            id="bill-payment-title"
                            className="text-[#151519] text-2xl font-semibold leading-[30px] tracking-tight mb-12"
                        >
                            Payment information
                        </h2>

                        <PaymentForm
                            onSubmit={handleSubmit}
                            narration={narration}
                            setNarration={setNarration}
                            accountNumber={accountNumber}
                            setAccountNumber={setAccountNumber}
                            onBack={onBack}
                            isSubmitting={isSubmitting}
                            disableContinue={accountsLoading || accounts.length === 0 || !accountNumber}
                            onAccountChange={handleAccountChange}
                            formik={formik}
                        />
                    </div>
                </main>
            </div>
        </FullScreenDrawer>
    );
};

export default PaymentInfo;
