"use client";

import { Bill<PERSON>ate<PERSON><PERSON>, RecentB<PERSON><PERSON>, BillDialog } from "./main-page";
import SearchInput from "@/components/common/search-input";
import React, { useEffect, useState, useMemo } from "react";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { fetchBillCategories } from "@/redux/actions/billPaymentThunks";
import { useSearchParams, useRouter } from "next/navigation";
import { getCategoryIcon } from "./utils/utils";
import { RecentBillsProvider } from "./context/RecentBillsContext";

// Simple debounce hook for search input
const useDebounce = (value: string, delay: number) => {
    const [debouncedValue, setDebouncedValue] = useState(value);

    useEffect(() => {
        const handler = setTimeout(() => {
            setDebouncedValue(value);
        }, delay);

        return () => {
            clearTimeout(handler);
        };
    }, [value, delay]);

    return debouncedValue;
};

/**
 * BillPayments is the main component for the bill payments module
 * Displays bill categories and recent bills side by side
 */
const BillPayments = () => {
    const dispatch = useAppDispatch();
    const router = useRouter();
    const searchParams = useSearchParams();

    // Initialize search from URL or empty string
    const [searchTerm, setSearchTerm] = useState<string>(searchParams.get("search") ?? "");

    // Debounce search to prevent excessive re-renders
    const debouncedSearch = useDebounce(searchTerm, 500);

    const {
        content: allCategories = [],
        loading = false,
        error,
    } = useAppSelector((state) => state.billPayments.categories) || {};

    const [isBillDialogOpen, setIsBillDialogOpen] = useState(false);
    const [selectedCategory, setSelectedCategory] = useState<{ name: string; id: number; description?: string } | null>(
        null
    );

    // Effect to update URL when search changes
    useEffect(() => {
        const params = new URLSearchParams(searchParams.toString());
        if (debouncedSearch) {
            params.set("search", debouncedSearch);
        } else {
            params.delete("search");
        }
        router.replace(`?${params.toString()}`, { scroll: false });
    }, [debouncedSearch, router, searchParams]);

    // Effect to fetch all categories once (without search parameter since API doesn't support it)
    useEffect(() => {
        // Only fetch categories if they're not already loaded (empty array)
        // This implements session-based caching to prevent unnecessary API calls
        // Categories are cached in sessionStorage and fetched only once per session
        if (!allCategories.length && !loading && !error) {
            dispatch(
                fetchBillCategories({
                    page: 1,
                    size: 50, // Fetch more to ensure we have enough to filter
                })
            );
        }
    }, [dispatch, allCategories.length, loading, error]);

    // Client-side filtering of categories
    const filteredCategories = useMemo(() => {
        if (!debouncedSearch.trim()) return allCategories;

        return allCategories.filter((category) => {
            const categoryName = category.categoryName ?? "";
            const description = category.description ?? "";

            return (
                categoryName.toLowerCase().includes(debouncedSearch.toLowerCase()) ||
                description.toLowerCase().includes(debouncedSearch.toLowerCase())
            );
        });
    }, [allCategories, debouncedSearch]);

    // Process categories with icons
    const processedCategories = useMemo(
        () =>
            filteredCategories.map((category) => ({
                ...category,
                icon: getCategoryIcon(category.categoryName),
            })),
        [filteredCategories]
    );

    const handleCategoryClick = (category: { name: string; id: number; description?: string }) => {
        setSelectedCategory(category);
        setIsBillDialogOpen(true);
    };

    // Handle search input change
    const handleSearch = (value: string) => {
        setSearchTerm(value);
    };

    return (
        <div className="flex flex-col gap-6">
            {/* Use flex-col for small screens and flex-row for large screens */}
            <div className="flex flex-col bp:flex-row bp:justify-between relative pb-8">
                {/* Main content area - takes full width on small screens */}
                <div className="space-y-6 flex-1 w-full bp:max-w-[633px]">
                    <h1 className="text-2xl font-bold text-[#151518] leading-[30px] tracking-tight">Bill payments</h1>
                    <div className="w-full">
                        <SearchInput
                            placeholder="Search billers"
                            size="md"
                            width="w-full"
                            value={searchTerm}
                            onValueChange={handleSearch}
                        />
                    </div>
                    <BillCategories
                        onCategoryClick={handleCategoryClick}
                        categories={processedCategories}
                        isLoading={loading}
                        error={error}
                        searchTerm={debouncedSearch}
                    />
                </div>

                {/* Recent bills section - wrapped with RecentBillsProvider */}
                <div className="w-full bp:w-[337px] bp:pl-6 relative mt-8 bp:mt-0">
                    <div
                        className="hidden bp:block absolute left-0 inset-y-0 w-[1px] bg-[#e3e5e7]"
                        style={{ top: "-2rem", bottom: "-8rem" }}
                    />
                    <RecentBillsProvider>
                        <RecentBills />
                    </RecentBillsProvider>
                </div>
            </div>
            <BillDialog
                isOpen={isBillDialogOpen}
                onClose={() => setIsBillDialogOpen(false)}
                category={selectedCategory?.name ?? ""}
                categoryId={selectedCategory?.id ?? -1}
                categoryDescription={selectedCategory?.description ?? ""}
            />
        </div>
    );
};

export default BillPayments;
