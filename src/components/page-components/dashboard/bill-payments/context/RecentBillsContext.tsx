import React, { createContext, useContext, useState, useCallback, useEffect, ReactNode, useMemo } from "react";
import { billAxios } from "@/api/axios";
import { RecentBillsResponse, RecentBillApiItem } from "@/redux/types/billPayments";

// Define a bill interface that matches what the components expect
export interface RecentBill {
    id: string;
    iconType: string;
    provider: string;
    type: string;
    categoryName: string;
    reference: string;
    amount: string;
    date: string;
    status: string;
    serviceId?: string;
    serviceName?: string;
    account?: string;
    narration?: string;
    isBulkPayment?: boolean;
    customIcon?: React.FC<{ className?: string; color?: string; strokeWidth?: number }>;
    // Additional fields for repeat payment functionality
    paymentCode?: string;
    customerId?: string | null;
    customerMobile?: string | null;
    // Additional fields for bulk payment aggregation
    uniqueBillerCount?: number; // Number of unique billers in bulk payment
    recipientCount?: number; // Total number of recipients in bulk payment
    billers?: string[]; // List of unique billers
    individualPayments?: RecentBillApiItem[]; // Original payment data for bulk
}

interface RecentBillsContextType {
    bills: RecentBill[];
    loading: boolean;
    error: string | null;
    refetchRecentBills: () => Promise<void>;
}

// Create context with undefined default value
const RecentBillsContext = createContext<RecentBillsContextType | undefined>(undefined);

/**
 * Custom hook to access the recent bills context
 * Must be used within a RecentBillsProvider
 */
export const useRecentBills = (): RecentBillsContextType => {
    const context = useContext(RecentBillsContext);
    if (!context) {
        throw new Error("useRecentBills must be used within a RecentBillsProvider");
    }
    return context;
};

interface RecentBillsProviderProps {
    children: ReactNode;
}

/**
 * Provider component for recent bills data
 * Fetches and manages recent bills data from the API
 * Provides data and refetch function to child components
 * Enforces a limit of 15 items for optimal performance
 */
export const RecentBillsProvider: React.FC<RecentBillsProviderProps> = ({ children }) => {
    const [bills, setBills] = useState<RecentBill[]>([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    /**
     * Formats a status string by capitalizing the first letter
     * and removing underscores
     */
    const formatStatus = useCallback((status: string): string => {
        if (!status) return "";

        // Split by underscore and capitalize first letter of first word
        const parts = status.split("_");
        const firstWord = parts[0].charAt(0).toUpperCase() + parts[0].slice(1).toLowerCase();
        const restWords = parts.slice(1).map((word) => word.toLowerCase());

        return [firstWord, ...restWords].join(" ");
    }, []);

    /**
     * Format amount from string to currency format
     */
    const formatAmount = useCallback((amount: string): string => {
        if (!amount) return "₦0.00";

        // Convert to number and format with Naira symbol
        const numberAmount = parseFloat(amount);
        return `₦${numberAmount.toLocaleString(undefined, {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
        })}`;
    }, []);

    /**
     * Determines if a key represents a bulk payment
     * Keys containing only numbers are bulk payments
     * Keys containing letters (alphanumeric) are single payments
     */
    const isBulkPaymentKey = useCallback((key: string): boolean => /^\d+$/.test(key), []);

    /**
     * Creates a RecentBill object from API item data
     */
    const createRecentBill = useCallback(
        (
            item: RecentBillApiItem,
            options: {
                id: string;
                iconType: string;
                isBulkPayment: boolean;
                provider?: string;
                amount?: string;
                uniqueBillerCount?: number;
                recipientCount?: number;
                billers?: string[];
                individualPayments?: RecentBillApiItem[];
            }
        ): RecentBill => ({
            id: options.id,
            iconType: options.iconType,
            provider: options.provider ?? item.biller ?? "-",
            type: item.billType ?? "-",
            categoryName: item.billType ?? "-",
            reference: item.requestReference,
            amount: options.amount ?? formatAmount(item.amount),
            date: item.createdAt ?? "",
            status: formatStatus(item.status),
            account: item.accountNumber,
            narration: item.narration ?? "-",
            isBulkPayment: options.isBulkPayment,
            // Add fields needed for repeat payment
            paymentCode: item.paymentCode,
            customerId: item.customerId,
            customerMobile: item.customerMobile,
            // Add bulk payment aggregation fields (only for bulk payments)
            uniqueBillerCount: options.uniqueBillerCount,
            recipientCount: options.recipientCount,
            billers: options.billers,
            individualPayments: options.individualPayments,
        }),
        [formatAmount, formatStatus]
    );

    /**
     * Transforms API response to match UI format
     * Aggregates bulk payments into single entries and handles single payments
     */
    const transformApiResponse = useCallback(
        (content: Record<string, RecentBillApiItem[]> | RecentBillApiItem[]): RecentBill[] => {
            const allItems: RecentBill[] = [];

            // Handle new grouped object structure
            if (content && typeof content === "object" && !Array.isArray(content)) {
                // Process each group in the content object
                Object.entries(content).forEach(([key, items]) => {
                    if (Array.isArray(items) && items.length > 0) {
                        const isBulk = isBulkPaymentKey(key);

                        if (isBulk) {
                            // For bulk payments, aggregate into a single entry
                            const uniqueBillers = new Set(
                                items.map((item) => item.biller).filter((biller): biller is string => Boolean(biller))
                            );
                            const totalAmount = items.reduce((sum, item) => sum + parseFloat(item.amount || "0"), 0);
                            const firstItem = items[0]; // Use first payment for metadata

                            // Determine provider display based on unique billers count
                            const provider = uniqueBillers.size > 1 ? "Multiple billers" : (firstItem.biller ?? "-");

                            allItems.push(
                                createRecentBill(firstItem, {
                                    id: key, // Use the group key as ID
                                    iconType: "bulk-payment",
                                    isBulkPayment: true,
                                    provider,
                                    amount: formatAmount(totalAmount.toString()),
                                    uniqueBillerCount: uniqueBillers.size,
                                    recipientCount: items.length,
                                    billers: Array.from(uniqueBillers),
                                    individualPayments: items,
                                })
                            );
                        } else {
                            // For single payments, process each item individually
                            items.forEach((item) => {
                                allItems.push(
                                    createRecentBill(item, {
                                        id: String(item.id),
                                        iconType: "receipt",
                                        isBulkPayment: false,
                                    })
                                );
                            });
                        }
                    }
                });
            }
            // Handle legacy array structure (backward compatibility removed per requirements)
            else if (Array.isArray(content)) {
                // Process as individual items
                content.forEach((item) => {
                    allItems.push(
                        createRecentBill(item, {
                            id: String(item.id),
                            iconType: "receipt", // Assume single payments in legacy array
                            isBulkPayment: false,
                        })
                    );
                });
            }

            // Sort bills by date with the latest bills at the top
            allItems.sort((a: RecentBill, b: RecentBill) => {
                // If either date is empty, treat it as oldest
                if (!a.date) return 1;
                if (!b.date) return -1;

                // Compare dates - newer dates should come first
                return new Date(b.date).getTime() - new Date(a.date).getTime();
            });

            // Enforce 15-item limit for recent bills display
            // This ensures consistent performance regardless of API response size
            return allItems.slice(0, 15);
        },
        [createRecentBill, formatAmount, isBulkPaymentKey]
    );

    /**
     * Processes the API response and returns transformed bills
     * Handles various response formats and data structures
     */
    const processApiResponse = useCallback(
        (response: { data?: { content?: unknown } }): RecentBill[] => {
            if (!response?.data?.content) {
                return [];
            }

            const content = response.data.content;

            // Handle grouped object structure
            if (typeof content === "object" && !Array.isArray(content)) {
                const hasData = Object.values(content).some((items) => Array.isArray(items) && items.length > 0);
                return hasData ? transformApiResponse(content as Record<string, RecentBillApiItem[]>) : [];
            }

            return [];
        },
        [transformApiResponse]
    );

    /**
     * Creates user-friendly error message from caught error
     */
    const createErrorMessage = useCallback((error: unknown): string => {
        const isTimeoutError =
            error instanceof Error && (error.message.includes("timeout") || error.message.includes("exceeded"));

        return isTimeoutError ? "Unable to load recent bills at this time" : "Failed to fetch recent bills";
    }, []);

    /**
     * Fetches recent bills from the API
     * Sets loading and error states appropriately
     * Transforms API response to match UI format
     * Enforces a 15-item limit regardless of API response size
     */
    const fetchRecentBills = useCallback(async () => {
        setLoading(true);
        setError(null);

        try {
            const response = await billAxios.get<RecentBillsResponse>("/api/v1/vas/payments", {
                params: { pageNo: 1, pageSize: 15 },
            });

            const transformedBills = processApiResponse(response);
            setBills(transformedBills);
        } catch (error) {
            const errorMessage = createErrorMessage(error);
            setError(errorMessage);

            // Following Core Principle #4: Don't show error toasts for initial data loading
            // No sendCatchFeedback here as this is initial data loading
        } finally {
            setLoading(false);
        }
    }, [processApiResponse, createErrorMessage]);

    // Fetch bills on initial mount
    useEffect(() => {
        fetchRecentBills();
    }, [fetchRecentBills]);

    // Provide context value
    const value = useMemo(
        () => ({
            bills,
            loading,
            error,
            refetchRecentBills: fetchRecentBills,
        }),
        [bills, loading, error, fetchRecentBills]
    );

    return <RecentBillsContext.Provider value={value}>{children}</RecentBillsContext.Provider>;
};
