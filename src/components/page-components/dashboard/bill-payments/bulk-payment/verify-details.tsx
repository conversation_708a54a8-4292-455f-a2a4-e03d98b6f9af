/**
 * Verify Bulk Details Component
 *
 * Purpose: Manages the verification and editing of bulk airtime payment entries before submission,
 * with robust error handling for network provider API failures.
 *
 * Functionality: This component provides a comprehensive interface for reviewing, validating,
 * filtering, sorting, and managing bulk airtime entries. It validates entries against API data,
 * displays entry status (valid/invalid), and allows users to add, edit, or remove entries. The
 * component ensures that validation can only proceed when network provider data is successfully
 * loaded, preventing invalid payments due to missing validation data. It includes error handling
 * with retry capability for API failures, advanced filtering by network provider and entry status,
 * sorting by amount, and summary statistics for all entries. Uses centralized biller management
 * to ensure consistent API call patterns and prevent redundant network requests.
 *
 * Dependencies:
 * - React hooks for state and effects
 * - Redux for global state management
 * - useCentralizedBillers hook for consistent biller data management
 * - Common UI components (Button, Stepper, Table, etc.)
 * - Dialog components for adding, editing, and deleting entries
 * - Network provider validation from the API
 * - Status determination functions from "./table/utils"
 *
 * Usage: Used as the second step in the bulk payment flow after file upload. Allows users to review
 * and manage entries from the uploaded CSV file, add additional entries manually, and ensure all
 * entries are valid before proceeding to payment information. The component enforces validation rules
 * for phone numbers, network providers, and amounts, and prevents proceeding when network provider
 * data is unavailable.
 */

"use client";

import React, { useEffect, useState, useMemo } from "react";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { useCentralizedBillers } from "../hooks/use-centralized-billers";
import { setEntries, updateFilterState, resetBulkAirtime } from "@/redux/features/bulkAirtime";
import { BulkAirtimeEntry, FilterType, bulkAirtimeSteps } from "../utils";
import { useExitHandlers } from "../hooks/useExitHandlers";
import FullScreenDrawer from "@/components/common/full-screen-drawer";
import Stepper from "@/components/common/stepper";
import { Button } from "@/components/common/buttonv3";
import { PlusIcon } from "@/components/icons/bill-payment-icons";
import DeleteConfirmation from "@/components/common/delete-confirmation";
import { SummaryCard } from "@/components/common/summary-card";
import { FilterButtons } from "./table/filter-buttons";
import { AirtimeTable } from "./table/airtime-table";
import AirtimeForm from "./airtime-dialog";
import { getEntryStatus } from "./table/utils";

interface VerifyBulkDetailsProps {
    onBack: () => void;
    onContinue: () => void;
    categoryName?: string;
}

export default function VerifyBulkDetails({
    onBack,
    onContinue,
    categoryName = "Airtime",
}: Readonly<VerifyBulkDetailsProps>) {
    const dispatch = useAppDispatch();

    // Get entries and filter state from Redux
    const {
        entries: reduxEntries,
        validEntriesCount,
        totalValidAmount,
        filterState,
    } = useAppSelector((state) => state.bulkAirtime);

    // Use centralized biller management hook instead of direct Redux access
    // This eliminates inconsistent caching behavior and provides consistent API call patterns
    const {
        billers,
        loading: billersLoading,
        error: billersError,
        fetchBillersForCategory,
        hasBillersForCategory,
    } = useCentralizedBillers();

    // Exit handling for the full screen drawer
    const { isOpen, showExitConfirmation, handleClose, handleConfirmExit, handleCancelExit } = useExitHandlers({
        onExit: () => dispatch(resetBulkAirtime()),
    });

    // Local state for managing dialogs
    const [showAddAirtime, setShowAddAirtime] = useState(false);
    const [editEntry, setEditEntry] = useState<BulkAirtimeEntry | null>(null);
    const [deleteEntry, setDeleteEntry] = useState<BulkAirtimeEntry | null>(null);

    /**
     * Fetch network providers when component mounts using centralized hook
     *
     * RULE COMPLIANCE:
     * - Uses centralized hook to prevent inconsistent API call patterns
     * - Implements null checks and performance optimization
     * - Follows the centralized biller management strategy
     */
    useEffect(() => {
        const AIRTIME_CATEGORY_ID = 4;

        // Check if we need to fetch billers for airtime category using centralized logic
        if (!hasBillersForCategory(AIRTIME_CATEGORY_ID) && !billersLoading) {
            // Use centralized fetch function which handles all caching logic internally
            fetchBillersForCategory(AIRTIME_CATEGORY_ID);
        }
    }, [hasBillersForCategory, billersLoading, fetchBillersForCategory]);

    // Update entries status when billers load or change
    /**
     * Validates and updates entries when billers data is available
     *
     * This effect is responsible for:
     * 1. Revalidating all entries when network provider data from the API becomes available
     * 2. Applying additional validation rules to entries that may have come from a CSV file:
     *    - Marks entries as "Invalid" if the phone number format is incorrect
     *    - Marks entries as "Invalid" if the network provider is not found in the API
     *    - Marks entries as "Invalid" if the amount is less than or equal to 0
     * 3. Updating the Redux store only if there are actual changes to avoid unnecessary renders
     *
     * This ensures that data coming from CSV files is properly validated against the latest
     * API data before being displayed to the user in the table.
     *
     * RULE COMPLIANCE:
     * - Uses optional chaining for null safety
     * - Implements performance optimization to avoid unnecessary Redux updates
     */
    useEffect(() => {
        if (billers && reduxEntries.length > 0) {
            // Process entries to apply additional validation based on API data
            const updatedEntries = reduxEntries.map((entry) => {
                // getEntryStatus handles validation against network providers from API
                // as well as checking phone numbers and amounts
                const updatedStatus = getEntryStatus(entry.phoneNumber, entry.network, billers, entry.amount);

                // Only update the status if it changed to avoid unnecessary Redux updates
                if (updatedStatus !== entry.status) {
                    return {
                        ...entry,
                        status: updatedStatus,
                    };
                }
                return entry;
            });

            // Only dispatch if there are actual changes
            const hasChanges = updatedEntries.some((entry, index) => entry.status !== reduxEntries[index].status);
            if (hasChanges) {
                dispatch(setEntries(updatedEntries));
            }
        }
    }, [billers, reduxEntries, dispatch]);

    // Memoized calculations for filtered entries and unique networks
    const { filteredEntries, uniqueNetworks } = useMemo(() => {
        // Filter entries based on status and network filters
        const filtered = reduxEntries.filter((entry) => {
            const statusMatch = filterState.filter === "All" || entry.status === filterState.filter;
            const networkMatch = filterState.networkFilter === "Network" || entry.network === filterState.networkFilter;
            return statusMatch && networkMatch;
        });

        // Sort entries by amount if sort order is specified
        if (filterState.sortOrder === "asc") {
            filtered.sort((a, b) => a.amount - b.amount);
        } else if (filterState.sortOrder === "desc") {
            filtered.sort((a, b) => b.amount - a.amount);
        }

        // Get unique network providers for the filter dropdown
        const networks = new Set(reduxEntries.map((entry) => entry.network));

        return {
            filteredEntries: filtered,
            uniqueNetworks: ["Network", ...Array.from(networks)],
        };
    }, [reduxEntries, filterState]);

    // Event handlers for filtering and sorting
    const handleFilterChange = (newFilter: FilterType) => {
        dispatch(updateFilterState({ filter: newFilter }));
    };

    const handleNetworkFilterChange = (network: string) => {
        dispatch(updateFilterState({ networkFilter: network }));
    };

    const toggleSortOrder = () => {
        let newSortOrder: "asc" | "desc" | null = null;
        if (filterState.sortOrder === null) {
            newSortOrder = "asc";
        } else if (filterState.sortOrder === "asc") {
            newSortOrder = "desc";
        }
        dispatch(updateFilterState({ sortOrder: newSortOrder }));
    };

    // Event handlers for entry management
    const handleEdit = (entry: BulkAirtimeEntry) => setEditEntry(entry);
    const handleDelete = (entry: BulkAirtimeEntry) => setDeleteEntry(entry);

    // Handler for updating an existing entry
    const handleUpdate = (values: { networkProvider: string; phoneNumber: string; amount: number }) => {
        if (editEntry) {
            const updatedEntries = reduxEntries.map((entry) =>
                entry.id === editEntry.id
                    ? {
                          ...entry,
                          network: values.networkProvider,
                          phoneNumber: values.phoneNumber,
                          amount: values.amount,
                          status: getEntryStatus(values.phoneNumber, values.networkProvider, billers, values.amount),
                      }
                    : entry
            );
            dispatch(setEntries(updatedEntries));
        }
        setEditEntry(null);
    };

    // Handler for adding a new entry
    const handleAddSingleAirtime = (values: { networkProvider: string; phoneNumber: string; amount: number }) => {
        const newEntry: BulkAirtimeEntry = {
            id: String(Date.now()),
            phoneNumber: values.phoneNumber,
            network: values.networkProvider,
            status: getEntryStatus(values.phoneNumber, values.networkProvider, billers, values.amount),
            amount: Number(values.amount),
        };
        dispatch(setEntries([...reduxEntries, newEntry]));
        setShowAddAirtime(false);
    };

    // Handler for deleting an entry
    const confirmDelete = () => {
        if (deleteEntry) {
            const updatedEntries = reduxEntries.filter((e) => e.id !== deleteEntry.id);
            dispatch(setEntries(updatedEntries));
            setDeleteEntry(null);
        }
    };

    // Summary items for the statistics card
    const summaryItems = [
        {
            label: "Invalid phone numbers",
            value: reduxEntries.length - validEntriesCount,
            valueColorClass: "text-[#D92D20]",
        },
        { label: "Valid phone numbers", value: validEntriesCount, valueColorClass: "text-[#039855]" },
        { label: "Total valid airtime amount", value: `₦${totalValidAmount.toLocaleString()}` },
    ];

    // Get the page title based on category
    const getPageTitle = () => `${categoryName} bulk bill payment`;

    return (
        <FullScreenDrawer
            isOpen={isOpen}
            onClose={handleClose}
            title={getPageTitle()}
            showExitConfirmation={showExitConfirmation}
            onConfirmExit={handleConfirmExit}
            onCancelExit={handleCancelExit}
            showSupport={true}
            disablePadding={true}
        >
            {/* Main content layout */}
            <div className="flex min-h-0 relative flex-1 flex-col lg:flex-row">
                {/* Left sidebar with stepper */}
                <nav className="lg:absolute left-0 top-0 bottom-0 w-[240px] pt-8 px-14 pb-14 bg-white">
                    <Stepper steps={bulkAirtimeSteps} currentStep={2} />
                </nav>

                {/* Main content area */}
                <div className="flex-1 px-14 py-8 flex flex-col items-center overflow-y-auto lg:px-0">
                    <div className="w-full max-w-[655px] flex flex-col min-h-full">
                        <h2 className="text-center text-[#151518] text-2xl font-semibold font-['SF Pro Display'] leading-[30px] tracking-tight mb-10">
                            Verify bulk {categoryName.toLowerCase()} details
                        </h2>

                        {/* Summary statistics */}
                        <div className="mb-10 w-[644px]">
                            <SummaryCard items={summaryItems} itemGap="24px" />
                        </div>

                        {/* Filter and add entry controls */}
                        <div className="flex justify-between items-start mb-4">
                            <FilterButtons currentFilter={filterState.filter} onFilterChange={handleFilterChange} />
                            <Button
                                variant="primary-ghost"
                                size="sm"
                                leftIcon={<PlusIcon className="w-4 h-4" />}
                                className="text-sm font-semibold"
                                onClick={() => setShowAddAirtime(true)}
                            >
                                Add single airtime
                            </Button>
                        </div>

                        {/* Entries table section */}
                        <section className="mb-8 w-[644px]" aria-label="Airtime entries">
                            <div className="max-h-[400px] overflow-y-auto">
                                {(() => {
                                    // Show loading state while fetching network providers
                                    if (billersLoading) {
                                        return (
                                            <div className="w-full flex justify-center" aria-live="polite">
                                                <output className="px-6 py-4 text-center text-sm text-[#393941]">
                                                    Loading network providers...
                                                </output>
                                            </div>
                                        );
                                    }

                                    // If there's an error fetching network providers, show error message and retry button
                                    // Following API Integration Guidelines for error handling and UI Guidelines for feedback
                                    if (billersError) {
                                        return (
                                            <div
                                                className="w-full flex flex-col items-center gap-4 py-4"
                                                role="alert"
                                                aria-live="assertive"
                                            >
                                                <output className="px-6 py-4 text-center text-sm text-[#D92D20]">
                                                    Unable to load network providers. This is required to validate your
                                                    payment entries.
                                                </output>
                                                <Button
                                                    variant="primary-ghost"
                                                    size="sm"
                                                    onClick={() => fetchBillersForCategory(4)}
                                                    className="text-sm font-semibold"
                                                    aria-label="Retry loading network providers"
                                                >
                                                    Retry
                                                </Button>
                                            </div>
                                        );
                                    }

                                    // Show empty state message if no entries to display
                                    if (filteredEntries.length === 0) {
                                        return (
                                            <div className="w-full flex justify-center">
                                                <output className="px-6 py-4 text-center text-sm text-[#393941]">
                                                    No entries found
                                                </output>
                                            </div>
                                        );
                                    }

                                    // Only render table if we have billers data (no error, not loading)
                                    // Following UI Development Guidelines to ensure components only render with valid data
                                    return (
                                        <AirtimeTable
                                            entries={filteredEntries}
                                            networkFilter={filterState.networkFilter}
                                            setNetworkFilter={handleNetworkFilterChange}
                                            uniqueNetworks={uniqueNetworks}
                                            sortOrder={filterState.sortOrder}
                                            toggleSortOrder={toggleSortOrder}
                                            onEdit={handleEdit}
                                            onDelete={handleDelete}
                                        />
                                    );
                                })()}
                            </div>
                        </section>

                        {/* Navigation buttons */}
                        <div className="flex justify-end gap-3">
                            <Button
                                type="button"
                                onClick={onBack}
                                variant="outline"
                                size="medium"
                                className="text-sm font-semibold"
                            >
                                Back
                            </Button>
                            <Button
                                type="button"
                                onClick={onContinue}
                                variant="primary"
                                size="medium"
                                className="text-sm font-semibold"
                                disabled={validEntriesCount <= 0}
                            >
                                Next
                            </Button>
                        </div>
                    </div>
                </div>
            </div>

            {/* Dialogs */}
            <AirtimeForm
                isOpen={showAddAirtime}
                onClose={() => setShowAddAirtime(false)}
                title={"Add single airtime"}
                onSubmit={handleAddSingleAirtime}
                mode="add"
            />

            {editEntry && (
                <AirtimeForm
                    isOpen={!!editEntry}
                    onClose={() => setEditEntry(null)}
                    title={"Edit airtime payment"}
                    onSubmit={handleUpdate}
                    initialValues={{
                        networkProvider: editEntry.network,
                        phoneNumber: editEntry.phoneNumber,
                        amount: editEntry.amount,
                    }}
                    mode="edit"
                    data-testid="edit-airtime-form"
                />
            )}

            {deleteEntry && (
                <DeleteConfirmation
                    isOpen={true}
                    onClose={() => setDeleteEntry(null)}
                    onConfirm={confirmDelete}
                    title={`Are you sure you want to remove ${deleteEntry.phoneNumber} (${deleteEntry.network})?`}
                    confirmButtonText="Yes, remove"
                    cancelButtonText="No, cancel"
                />
            )}
        </FullScreenDrawer>
    );
}
