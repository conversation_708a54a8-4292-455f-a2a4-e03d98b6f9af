/**
 * * Transaction Details Dialog Component for Bill Payments Module *
 * * Purpose: Displays comprehensive transaction details for a selected bill payment in an interactive side drawer. *
 * * Functionality: This component presents detailed information about a bill payment transaction including * status, amount, provider details, payment information, and metadata. It implements a responsive side drawer * layout with distinct sections for different information categories. The component provides interactive * functionality including the ability to repeat a payment (for single payments only) with transaction PIN * verification, report issues, and close the dialog. It handles various transaction states with appropriate * visual feedback through badges and formatting utilities. *
 * * Dependencies: * - React hooks for state and effects * - Redux for state management and actions * - SideDrawer component for layout * - Badge component for status visualization * - TransactionId component for reference display * - Button component for interactions * - RecentBillsContext for data management * - Date formatting utilities *
 * * Usage: This component is opened when a user clicks on a recent bill entry in the recent bills list. * It receives transaction data and handles all display logic, state management, and interaction * handling for viewing and acting on transaction details. */
"use client";
import * as React from "react";
import { useEffect, useState } from "react";
import { ListIcon, ReceiptIcon } from "@/components/icons/bill-payment-categories";
import { RepeatIcon } from "@/components/icons/bill-payment-icons";
import { X } from "lucide-react";
import { Button } from "@/components/common/buttonv3";
import { TransactionId } from "@/components/common/transaction-id";
import SideDrawer from "@/components/common/drawer";
import Badge, { BadgeColor } from "@/components/common/badge";
import { formatDateWithTime } from "@/functions/date";
import { RecentBill, useRecentBills } from "../context/RecentBillsContext";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { initiateBillPayment } from "@/redux/actions/billPaymentThunks";
import { openVerifyPinModal } from "@/redux/slices/securitySlice";
import { getSessionDetails } from "@/functions/userSession";
/**
 * Formats the account string to match the application's standard format
 * If the account is just a number, formats it as "****1234"
 * Returns "-" for empty/undefined values
 *
 * @param accountString - The account string to format
 * @returns The formatted account string
 */
const formatAccountDisplay = (accountString: string | undefined): string => {
    // Handle empty/undefined value with a dash as specified
    if (!accountString) return "-";

    // Format as "****1234"
    const lastFourDigits = accountString.slice(-4);
    return `****${lastFourDigits}`;
};

/**
 * Maps transaction status to appropriate badge color
 *
 * @param status - The transaction status
 * @returns The corresponding badge color
 */
const getStatusBadgeColor = (status: string | undefined): BadgeColor => {
    // Handle undefined status
    if (!status) return "neutral";

    const normalizedStatus = status.toLowerCase().replace(/_/g, " ");

    switch (normalizedStatus) {
        case "successful":
            return "success";
        case "failed":
            return "error";
        case "in progress":
            return "brand";
        case "pending":
            return "warning";
        case "awaiting approval":
            return "warning";
        case "rejected approval":
            return "error";
        default:
            return "neutral";
    }
};

function TransactionHeader({ transaction }: Readonly<{ transaction: RecentBill }>) {
    // Determine which icon to use:
    // - ListIcon for bulk payments
    // - ReceiptIcon for single payments (ensuring single payments always use the receipt icon)
    const IconComponent = transaction.isBulkPayment ? ListIcon : ReceiptIcon;

    // Get the status for display and determine badge color
    // Default to "-" if status is not provided
    const displayStatus = transaction.status || "-";
    const badgeColor = getStatusBadgeColor(displayStatus);

    // Determine the title based on bulk payment with multiple billers
    const getTitle = () => {
        if (transaction.isBulkPayment && transaction.uniqueBillerCount && transaction.uniqueBillerCount > 1) {
            return `${transaction.uniqueBillerCount} billers`;
        }
        return transaction.provider || "-";
    };

    return (
        <header className="border border-[#e3e5e7] rounded-xl p-4">
            <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-12 h-12 rounded-full bg-[#F9F0FE] flex items-center justify-center">
                    <IconComponent className="w-6 h-6" color="#3A3A41" strokeWidth={1.5} />
                </div>

                <div className="flex flex-col items-start flex-grow overflow-hidden">
                    <div
                        className="text-[#151518] text-base font-medium leading-normal tracking-tight truncate w-full"
                        data-testid="transaction-title"
                    >
                        {getTitle()}
                    </div>
                    <div className="text-[#393941] text-sm font-normal leading-[18px] tracking-tight">
                        {transaction.categoryName || "-"}
                    </div>
                    <div className="text-[#393941] text-sm font-normal leading-[18px] tracking-tight">
                        {transaction.date ? formatDateWithTime(transaction.date) : "-"}
                    </div>
                </div>

                <div className="flex-shrink-0 flex flex-col justify-start items-end gap-3">
                    <p
                        className="text-right text-[#151518] text-lg font-semibold font-['SF Pro Display'] leading-normal tracking-tight"
                        data-testid="transaction-amount"
                    >
                        {transaction.amount || "-"}
                    </p>
                    <Badge text={displayStatus} color={badgeColor} data-testid="transaction-status" />
                </div>
            </div>
        </header>
    );
}

interface TransactionDetailsDialogProps {
    isOpen: boolean;
    onClose: () => void;
    onReport?: () => void;
    transaction?: RecentBill;
}

/**
 * TransactionDetailsDialog component displays detailed information about a bill payment transaction
 * Shows comprehensive details including status, date, reference, amount, and other metadata
 */
export function TransactionDetailsDialog({
    isOpen,
    onClose,
    transaction,
    onReport,
}: Readonly<TransactionDetailsDialogProps>) {
    const dispatch = useAppDispatch();
    const corporateId = useAppSelector((state) => state.corporate.corporateId);
    const [isProcessing, setIsProcessing] = useState(false);
    const [hasProcessedPin, setHasProcessedPin] = useState(false);
    const { refetchRecentBills } = useRecentBills();
    const { success: pinSuccess, pin } = useAppSelector((state) => state.security.verifyPin);

    useEffect(() => {
        if (isOpen) {
            setHasProcessedPin(false);
        }
    }, [isOpen]);

    useEffect(() => {
        const handleEscape = (e: KeyboardEvent) => {
            if (e.key === "Escape" && isOpen) {
                onClose();
            }
        };

        document.addEventListener("keydown", handleEscape);
        return () => document.removeEventListener("keydown", handleEscape);
    }, [isOpen, onClose]);

    // Effect to process payment after PIN verification
    useEffect(() => {
        const processPayment = async () => {
            if (!transaction || transaction.isBulkPayment || !pinSuccess || !pin || hasProcessedPin) return;

            setHasProcessedPin(true);
            setIsProcessing(true);

            // Convert amount string to number (remove currency symbol and commas)
            const amountValue = parseFloat(transaction.amount.replace(/[^\d.-]/g, ""));

            // Get user's email from session, fall back to a default value if not available
            const userSession = getSessionDetails();
            const customerEmail = userSession?.email ?? " ";

            // Construct payment params with verified PIN
            const paymentParams = {
                customerId: transaction.customerId ?? "",
                amount: amountValue,
                paymentCode: transaction.paymentCode ?? "",
                customerMobile: transaction.customerMobile ?? "",
                accountNumber: transaction.account ?? "",
                customerEmail: customerEmail,
                requestReference: "", // Default empty string as per API docs
                requiresApproval: false,
                biller: transaction.provider ?? "", // Get provider as biller
                billType: transaction.categoryName ?? "", // Use category name for bill type
                narration: transaction.narration ?? "", // Use transaction narration
                transactionPin: pin, // Use the verified PIN
            };

            // Dispatch payment initiation and refresh bills list
            await dispatch(initiateBillPayment(paymentParams));

            if (refetchRecentBills) {
                await refetchRecentBills();
            }

            setIsProcessing(false);
        };

        processPayment();
    }, [pinSuccess, pin, transaction, corporateId, dispatch, refetchRecentBills, hasProcessedPin]);

    /**
     * Handles the repeat payment action by opening the PIN verification modal
     */
    const handleRepeatPayment = () => {
        if (!transaction || transaction.isBulkPayment) return;
        setHasProcessedPin(false);
        dispatch(openVerifyPinModal());
    };

    if (!transaction || !isOpen) return null;

    return (
        <SideDrawer isOpen={isOpen} className="max-w-[516px]">
            <div aria-labelledby="transaction-details" className="h-full flex flex-col">
                {/* Header Section */}
                <div className="flex-shrink-0 px-6 relative">
                    <Button
                        variant="text-neutral"
                        size="sm"
                        onClick={onClose}
                        className="absolute right-6 top-4 p-2 hover:bg-gray-100 rounded-full"
                        aria-label="Close transaction details"
                        data-testid="close-dialog-button"
                    >
                        <X className="h-5 w-5 text-[#90909D]" />
                    </Button>
                    <div className="pt-16 pb-0">
                        <TransactionHeader transaction={transaction} />
                    </div>
                </div>

                {/* Details Section */}
                <div className="flex-grow overflow-y-auto">
                    <div className="px-6 pb-10 mt-10">
                        <h2 className="text-[#151518] text-sm font-medium leading-[18px] tracking-tight mb-5 text-left">
                            Transaction details
                        </h2>
                        <div className="p-5 bg-white rounded-xl border border-[#e3e5e7] space-y-8">
                            {/* Service Details */}
                            <div className="space-y-6">
                                <div className="flex justify-between">
                                    <div className="text-[#393941] text-sm font-normal">Bill type</div>
                                    <div
                                        className="text-[#151518] text-sm font-medium max-w-[60%] text-right break-words"
                                        data-testid="details-bill-type"
                                    >
                                        {transaction.categoryName || "-"}
                                    </div>
                                </div>
                                <div className="flex justify-between">
                                    <div className="text-[#393941] text-sm font-normal">Biller</div>
                                    <div
                                        className="text-[#151518] text-sm font-medium max-w-[60%] text-right break-words"
                                        data-testid="details-biller"
                                    >
                                        {transaction.provider || "-"}
                                    </div>
                                </div>
                            </div>

                            <div className="w-full h-[1px] bg-[#e3e5e7]" />

                            {/* Payment Details */}
                            <div className="space-y-6">
                                <div className="flex justify-between">
                                    <div className="text-[#393941] text-sm font-normal">Amount</div>
                                    <div className="text-[#151518] text-sm font-medium max-w-[60%] text-right break-words">
                                        {transaction.amount || "-"}
                                    </div>
                                </div>
                                <div className="flex justify-between">
                                    <div className="text-[#393941] text-sm font-normal">Fees</div>
                                    <div className="text-[#151518] text-sm font-medium">₦0.00</div>
                                </div>
                                <div className="flex justify-between">
                                    <div className="text-[#393941] text-sm font-normal">Paid from</div>
                                    <div className="text-[#151518] text-sm font-medium max-w-[60%] text-right break-words">
                                        {formatAccountDisplay(transaction.account)}
                                    </div>
                                </div>
                            </div>

                            <div className="w-full h-[1px] bg-[#e3e5e7]" />

                            {/* Transaction Metadata */}
                            <div className="space-y-6">
                                <div className="flex justify-between">
                                    <div className="text-[#393941] text-sm font-normal">Date</div>
                                    <div className="text-[#151518] text-sm font-medium">
                                        {transaction.date ? formatDateWithTime(transaction.date) : "-"}
                                    </div>
                                </div>
                                <div className="flex justify-between">
                                    <div className="text-[#393941] text-sm font-normal">Reference</div>
                                    <TransactionId id={transaction.reference} textSize={14} />
                                </div>
                                <div className="flex justify-between">
                                    <div className="text-[#393941] text-sm font-normal">Narration</div>
                                    <div className="text-[#151518] text-sm font-medium max-w-[60%] text-right break-words">
                                        {transaction.narration ? transaction.narration.trim() : "-"}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Footer Section */}
                <div className="flex-shrink-0 p-6 border-t border-[#e3e5e7]">
                    <div className="flex justify-between items-center">
                        <Button variant="text-neutral" onClick={onReport}>
                            Report
                        </Button>
                        <div className="flex items-center gap-3">
                            <Button
                                variant="outline"
                                size="medium"
                                leftIcon={<RepeatIcon className="w-5 h-5 text-[#90909D]" />}
                                disabled={transaction.isBulkPayment || isProcessing}
                                title={
                                    transaction.isBulkPayment
                                        ? "Bulk payments cannot be repeated"
                                        : "Repeat this payment"
                                }
                                onClick={handleRepeatPayment}
                            >
                                <div className="text-center text-[#151519] text-sm font-semibold leading-[18px] tracking-tight">
                                    {isProcessing ? "Processing..." : "Repeat"}
                                </div>
                            </Button>
                        </div>
                    </div>
                </div>
            </div>
        </SideDrawer>
    );
}
