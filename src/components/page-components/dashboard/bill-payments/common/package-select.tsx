/**
 * @file package-select.tsx
 *
 * @purpose Provides a specialized dropdown for selecting bill payment packages with price awareness.
 *
 * @functionality This component renders a dropdown for selecting packages (plans/items) available
 * for a particular biller. It manages the complete package selection lifecycle, including loading
 * packages from the API based on the selected biller ID, finding matching packages by name or value,
 * handling package selection events, and updating form values based on package details. The component
 * intelligently handles fixed-price packages by automatically updating the amount field and marking
 * it as read-only. It preserves state between navigation by synchronizing with Redux and provides
 * appropriate loading states during API calls.
 *
 * @dependencies
 * - React: For component lifecycle and hooks
 * - Redux: For state persistence and management
 * - Formik: For form state integration
 * - usePaymentItems: Custom hook for API integration
 * - FormField: For consistent form field layout
 * - Dropdown: For UI implementation
 *
 * @usage Import this component in bill payment forms where package selection is required.
 * Pass a Formik instance, a callback for package selection events, and the selected biller ID.
 * The component will handle fetching available packages, selection logic, and fixed-price behavior.
 */

import React, { useEffect } from "react";
import { PackageSelectProps, BillPaymentFormValues } from "../types";
import FormField from "./form-field";
import Dropdown from "@/components/common/dropdown";
import { useAppSelector, useAppDispatch } from "@/redux/hooks";
import { usePaymentItems } from "../hooks/use-payment-items";
import { updateSelectedPackage } from "@/redux/slices/singleBillPayment";

/**
 * Component for selecting a package in the bill payment form
 *
 * @param props - The component props
 * @returns A package selection dropdown with form field wrapper
 */
export default function PackageSelect<T extends BillPaymentFormValues>({
    formik,
    onPackageSelect,
    selectedBillerId,
    showPackageAmount = true,
}: Readonly<PackageSelectProps<T>>) {
    const dispatch = useAppDispatch();
    const selectedPackage = useAppSelector((state) => state.singleBillPayment.selectedPackage);

    // Check if service provider is valid
    const isServiceProviderValid = formik.values.serviceProvider && !formik.errors.serviceProvider;

    // Use the modified usePaymentItems hook with skipFetch=true
    // This follows the optimization strategy where parent container handles data fetching
    // and child components consume cached data for better performance
    const { packageOptions, loading, findPaymentItemByName } = usePaymentItems(
        selectedBillerId,
        showPackageAmount,
        true // skipFetch=true - use cached data from parent container
    );

    /**
     * Finds the matching package option by label or value
     * This ensures the dropdown can find the correct option when returning to this page
     *
     * @param value - The value to find a match for
     * @returns The matching option or undefined if no match is found
     */
    const findMatchingPackage = (value: string) => {
        if (!value) return undefined;
        if (loading) return undefined;

        // First try to find by exact label match
        const byExactLabel = packageOptions.find(
            (option) => option.label && option.label.toLowerCase() === value.toLowerCase()
        );
        if (byExactLabel) return byExactLabel;

        // Then try to find by value
        const byValue = packageOptions.find(
            (option) => option.value && option.value.toLowerCase() === value.toLowerCase()
        );
        if (byValue) return byValue;

        // Finally try to find by partial label match
        const byPartialLabel = packageOptions.find(
            (option) => option.label && value.toLowerCase().includes(option.label.toLowerCase())
        );
        if (byPartialLabel) return byPartialLabel;

        return undefined;
    };

    // Initialize form values from Redux store when component mounts
    useEffect(() => {
        if (selectedPackage && !formik.values.package) {
            const matchingPackage = findMatchingPackage(selectedPackage.paymentCode);
            if (matchingPackage) {
                formik.setFieldValue("package", matchingPackage.label, false);
                // Always populate amount field from API response
                formik.setFieldValue("amount", selectedPackage.amount, false);
            }
        }
    }, [selectedPackage, packageOptions, formik]);

    /**
     * Handles selection changes in the package dropdown
     *
     * @param value - The selected value (string, object, or null/undefined)
     */
    const handlePackageChange = (value: string | undefined | null) => {
        if (value === undefined || value === null) {
            formik.setFieldValue("package", "", false);
            onPackageSelect(null);
            dispatch(updateSelectedPackage(null));
            return;
        }

        const trimmedValue = String(value).trim();
        formik.setFieldValue("package", trimmedValue, false);

        if (trimmedValue) {
            const paymentItem = findPaymentItemByName(trimmedValue);

            if (paymentItem) {
                onPackageSelect(paymentItem);
                dispatch(updateSelectedPackage(paymentItem));

                // Always populate amount field from API response
                formik.setFieldValue("amount", paymentItem.amount, false);
            } else {
                onPackageSelect(null);
                dispatch(updateSelectedPackage(null));
            }
        } else {
            onPackageSelect(null);
            dispatch(updateSelectedPackage(null));
        }

        setTimeout(() => {
            formik.setFieldTouched("package", true, true);
        }, 0);
    };

    // Show loading option while data is being fetched
    const displayOptions = loading ? [{ value: "loading", label: "Loading...", isDisabled: true }] : packageOptions;

    return (
        <FormField label="Package" name="package" touched={formik.touched.package as boolean | undefined}>
            <Dropdown<T, { value: string; label: string }>
                options={displayOptions}
                value={findMatchingPackage(formik.values.package)}
                onChange={(newValue) => {
                    if (loading) return; // Prevent selection while loading
                    if (typeof newValue === "string") {
                        handlePackageChange(newValue);
                    } else if (newValue && typeof newValue === "object") {
                        handlePackageChange(newValue.label);
                    } else {
                        handlePackageChange(null);
                    }
                }}
                placeholder="Search packages..."
                isLoading={loading}
                name="package"
                showError={!!(formik.touched.package && formik.errors.package)}
                error={formik.errors.package as string | undefined}
                useFormik={false}
                isDisabled={!isServiceProviderValid}
            />
        </FormField>
    );
}
