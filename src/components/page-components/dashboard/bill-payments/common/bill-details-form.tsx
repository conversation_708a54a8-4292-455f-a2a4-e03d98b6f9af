/**
 * @file bill-details-form.tsx
 *
 * @purpose Provides a standardized form template for capturing bill payment details across different bill categories.
 *
 * @functionality This component implements a reusable form layout for bill payment details that
 * adapts based on the bill category. It orchestrates the relationship between service provider
 * selection, available packages, and payment amounts through a composable interface. The component
 * handles biller selection, package fetching, and amount field state management, including disabling
 * the amount field for fixed-price packages. It wraps all functionality in an error boundary to
 * ensure graceful failure handling and provides slots for category-specific form elements through
 * the children prop.
 *
 * @dependencies
 * - Redux: For accessing biller data and selected package state
 * - React: For component composition and memo optimization
 * - ServiceProviderSelect/PackageSelect/AmountInput: Sub-components for form elements
 * - ErrorBoundary: For graceful error handling
 *
 * @usage Import this component in bill payment flows and provide a Formik instance, category ID,
 * and package selection handler. Use the children prop to insert category-specific form elements
 * between the standard fields. The component automatically handles service provider selection,
 * package loading, and amount field behavior including disabling for fixed-price packages.
 */

import React from "react";
import { useAppSelector } from "@/redux/hooks";
import AmountInput from "@/components/common/amount-input";
import ServiceProviderSelect from "./service-provider-select";
import PackageSelect from "./package-select";
import ErrorBoundary from "./error-boundary";
import { BillDetailsFormProps, BillPaymentFormValues } from "../types";

/**
 * BillDetailsForm component that implements a form for bill payment details
 * This component has been restructured to improve maintainability and testability:
 * - Error handling via an ErrorBoundary component
 * - Sub-components extracted for better separation of concerns
 * - Custom hooks created for API data fetching logic
 *
 * @template T - Type that extends BillPaymentFormValues (service provider, package, amount)
 * @param props - Component props including formik instance, category ID, and package selection handler
 * @returns A complete bill details form with service provider, package, and amount fields
 */
export default function BillDetailsForm<T extends BillPaymentFormValues>({
    formik,
    categoryId,
    onPackageSelect,
    children,
    onServiceProviderChange,
}: Readonly<BillDetailsFormProps<T>>) {
    const { data: billers } = useAppSelector((state) => state.billPayments.biller);

    // Find selected biller ID based on service provider name
    const selectedBillerId = React.useMemo(() => {
        if (!formik.values.serviceProvider || !billers) return undefined;

        const selectedBiller = billers.find(
            (biller) =>
                (biller.billername &&
                    biller.billername.toLowerCase() === formik.values.serviceProvider.toLowerCase()) ||
                (biller.billerName && biller.billerName.toLowerCase() === formik.values.serviceProvider.toLowerCase())
        );

        return selectedBiller ? (selectedBiller.billerid ?? selectedBiller.billerId)?.toString() : undefined;
    }, [billers, formik.values.serviceProvider]);

    // Get the selected package from Redux to determine if amount field should be disabled
    const selectedPackage = useAppSelector((state) => state.singleBillPayment.selectedPackage);

    return (
        <ErrorBoundary message="An error occurred while loading the bill details form. Please try again or contact support.">
            <div className="w-full space-y-6">
                <ServiceProviderSelect
                    formik={formik}
                    categoryId={categoryId}
                    onServiceProviderChange={onServiceProviderChange}
                />
                <PackageSelect formik={formik} onPackageSelect={onPackageSelect} selectedBillerId={selectedBillerId} />

                {children}

                <AmountInput
                    formik={formik}
                    label="Amount"
                    name="amount"
                    className={selectedPackage?.amountFixed ? "opacity-50 cursor-not-allowed" : ""}
                    disabled={selectedPackage?.amountFixed}
                />
            </div>
        </ErrorBoundary>
    );
}
