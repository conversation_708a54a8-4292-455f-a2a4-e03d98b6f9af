"use client";

import { <PERSON><PERSON> } from "@/components/common/buttonv3";
import Dropdown from "@/components/common/dropdown";
import { useState, useEffect } from "react";
import SideDrawer from "@/components/common/drawer";
import Checkbox from "@/components/common/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/common/radio-group";
import { Label } from "@/components/common/label";
import { ExportIcon } from "@/components/icons/transaction";
import { IAccount, ITransaction, ITransactionResponse } from "@/redux/types/transactions";
import CloseX from "@/components/common/close-x";
import { format, subMonths, startOfMonth, endOfMonth } from "date-fns";
import { acctsAxios } from "@/api/axios";
import { sendCatchFeedback } from "@/functions/feedback";
import { Loader2 } from "lucide-react";
import jsPDF from "jspdf";
import "jspdf-autotable";
import { mkConfig, generateCsv, download } from "export-to-csv";
import * as XLSX from "xlsx";

const columnValues = ["Amount", "Status", "Counterparty", "Reference", "Bank"];

interface ExportTransactionsProps {
    accounts: IAccount[];
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    data: any[];
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    columns: any[];
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    selectedRows?: any[];
}

type DateRangeType = "this-month" | "last-3-months" | "last-6-months" | "custom";

export function ExportTransactions({ accounts, selectedRows = [] }: ExportTransactionsProps) {
    const [isOpen, setIsOpen] = useState<boolean>(false);
    const [exportType, setExportType] = useState<"pdf" | "csv" | "excel">("csv");
    const [selectedAccount, setSelectedAccount] = useState<string>("");
    const [dateRangeType, setDateRangeType] = useState<DateRangeType>("this-month");
    const [startDate, setStartDate] = useState<Date>(startOfMonth(new Date()));
    const [endDate, setEndDate] = useState<Date>(endOfMonth(new Date()));
    // We'll store the fetched data temporarily here
    // const [, setExportData] = useState<ITransaction[]>([]);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [selectedColumns, setSelectedColumns] = useState<string[]>(columnValues);

    // Update date range based on selection
    useEffect(() => {
        const now = new Date();

        switch (dateRangeType) {
            case "this-month":
                setStartDate(startOfMonth(now));
                setEndDate(endOfMonth(now));
                break;
            case "last-3-months":
                setStartDate(startOfMonth(subMonths(now, 3)));
                setEndDate(endOfMonth(now));
                break;
            case "last-6-months":
                setStartDate(startOfMonth(subMonths(now, 6)));
                setEndDate(endOfMonth(now));
                break;
            // For custom, don't update the dates automatically
            case "custom":
                break;
        }
    }, [dateRangeType]);

    // Clear account selection when switching to table row selection mode
    useEffect(() => {
        if (selectedRows.length > 0) {
            setSelectedAccount("");
        }
    }, [selectedRows.length]);

    const handleCloseFilter = () => {
        setIsOpen(false);
        // Clear data when closing
        setSelectedAccount("");
        setDateRangeType("this-month");
        setStartDate(startOfMonth(new Date()));
        setEndDate(endOfMonth(new Date()));
        setSelectedColumns(columnValues);
        setExportType("csv");
    };

    const formatAccountText = (account: IAccount) => {
        const maskedNumber = `****${account.accountNumber.slice(-4)}`;
        return `${account.accountName} ${maskedNumber}`;
    };

    // Function to fetch transactions for export
    const fetchTransactionsForExport = async (): Promise<ITransaction[]> => {
        // Account selection is already validated in handleExport
        setIsLoading(true);
        try {
            const formattedStartDate = format(startDate, "yyyy-MM-dd");
            const formattedEndDate = format(endDate, "yyyy-MM-dd");

            const response = await acctsAxios.get<ITransactionResponse>(
                `/api/v1/accounts/${selectedAccount}/transactions`,
                {
                    params: {
                        pageNo: 0,
                        pageSize: 1000, // Get a large number of transactions for export
                        startDate: formattedStartDate,
                        endDate: formattedEndDate,
                    },
                }
            );

            // Check if response has content
            if (!response.data || !response.data.content) {
                sendCatchFeedback("No transaction data available for the selected period.");
                return [];
            }

            const transactions = response.data.content;
            return transactions;
        } catch (error) {
            sendCatchFeedback(error);
            return [];
        } finally {
            setIsLoading(false);
        }
    };

    // Custom export functions with improved formatting
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const exportToPDF = (dataToExport: any[]) => {
        try {
            // Extract only the data fields we want to export
            const simpleColumns = [
                { header: "Date", accessorKey: "createdDate" },
                { header: "Description", accessorKey: "narration" },
                { header: "Amount", accessorKey: "amount" },
                { header: "Type", accessorKey: "transactionType" },
                { header: "Status", accessorKey: "status" },
                { header: "Reference", accessorKey: "reference" },
            ];

            // Filter based on user selection
            const filteredColumns = simpleColumns.filter(
                (col) => selectedColumns.includes(col.header) || !columnValues.includes(col.header)
            );

            // Create PDF document in landscape orientation for better table display
            const doc = new jsPDF({
                orientation: "landscape",
                unit: "mm",
                format: "a4",
            });

            // Define colors
            const primaryColor = [0, 123, 255]; // Blue
            const secondaryColor = [33, 37, 41]; // Dark gray
            const lightGray = [240, 240, 240];
            const darkGray = [100, 100, 100];

            // Add company logo/header
            doc.setFillColor(primaryColor[0], primaryColor[1], primaryColor[2]);
            doc.rect(0, 0, doc.internal.pageSize.width, 15, "F");

            // Add title
            doc.setFont("helvetica", "bold");
            doc.setFontSize(18);
            doc.setTextColor(255, 255, 255);
            doc.text("TRANSACTION REPORT", 14, 10);

            // Add date and time of generation
            doc.setFont("helvetica", "normal");
            doc.setFontSize(8);
            doc.text(`Generated on: ${format(new Date(), "dd MMM yyyy HH:mm")}`, doc.internal.pageSize.width - 14, 10, {
                align: "right",
            });

            // Add report information section
            doc.setFillColor(lightGray[0], lightGray[1], lightGray[2]);
            doc.rect(0, 15, doc.internal.pageSize.width, 20, "F");

            doc.setTextColor(secondaryColor[0], secondaryColor[1], secondaryColor[2]);
            doc.setFontSize(10);

            // Add date range
            doc.setFont("helvetica", "bold");
            doc.text("Date Range:", 14, 22);
            doc.setFont("helvetica", "normal");
            doc.text(`${format(startDate, "dd MMM yyyy")} - ${format(endDate, "dd MMM yyyy")}`, 40, 22);

            // Add account info if available
            // Use selected account or first account, with a safer fallback
            const accountToUse = selectedAccount || (accounts && accounts.length > 0 ? accounts[0].accountNumber : "");
            const account = accounts.find((acc) => acc.accountNumber === accountToUse);

            doc.setFont("helvetica", "bold");
            doc.text("Account:", 14, 28);
            doc.setFont("helvetica", "normal");
            if (account) {
                doc.text(`${account.accountName} (${account.accountNumber})`, 40, 28);
            } else {
                doc.text(`${accountToUse}`, 40, 28);
            }

            // Add export summary
            doc.setFont("helvetica", "bold");
            doc.text("Total Records:", doc.internal.pageSize.width - 80, 22);
            doc.setFont("helvetica", "normal");
            doc.text(`${dataToExport.length}`, doc.internal.pageSize.width - 50, 22);

            doc.setFont("helvetica", "bold");
            doc.text("Export Type:", doc.internal.pageSize.width - 80, 28);
            doc.setFont("helvetica", "normal");
            doc.text("PDF Report", doc.internal.pageSize.width - 50, 28);

            // Simple column headers
            const tableColumn = filteredColumns.map((col) => col.header);

            // Format table rows with simple data
            const tableRows = dataToExport.map((row) =>
                filteredColumns.map((col) => {
                    const key = col.accessorKey;
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    const value = key && row ? (row as any)[key] : null;

                    // Format values appropriately
                    if (key === "amount" && typeof value === "number") {
                        return `₦${value.toLocaleString("en-NG", { minimumFractionDigits: 2 })}`;
                    } else if (key === "createdDate" && value) {
                        try {
                            return format(new Date(value), "dd MMM yyyy");
                        } catch {
                            return String(value);
                        }
                    } else if (key === "transactionType") {
                        return value === "Credit" ? "Credit" : "Debit";
                    } else if (key === "status") {
                        return value ? String(value).toUpperCase() : "";
                    } else if (typeof value === "string" && value.length > 60) {
                        // Truncate very long strings
                        return value.substring(0, 57) + "...";
                    } else if (value === null || value === undefined) {
                        return "";
                    } else {
                        return String(value);
                    }
                })
            );

            // Add table with simple styling
            // @ts-ignore
            doc.autoTable({
                head: [tableColumn],
                body: tableRows,
                startY: 40,
                styles: {
                    fontSize: 9,
                    cellPadding: 2,
                    lineColor: [220, 220, 220],
                    lineWidth: 0.1,
                    font: "helvetica",
                },
                headStyles: {
                    fillColor: [44, 62, 80],
                    textColor: [255, 255, 255],
                    fontStyle: "bold",
                },
                alternateRowStyles: {
                    fillColor: [240, 240, 240],
                },
                // Add margin to the table
                margin: { top: 40, right: 14, bottom: 20, left: 14 },
                // Handle row splitting across pages
                rowPageBreak: "auto",
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                didDrawPage: (data: any) => {
                    // Add footer on each page
                    doc.setFontSize(8);
                    doc.setTextColor(darkGray[0], darkGray[1], darkGray[2]);
                    doc.text(
                        `Page ${data.pageNumber} of ${doc.internal.getNumberOfPages()}`,
                        data.settings.margin.left,
                        doc.internal.pageSize.height - 10
                    );

                    // Add company info
                    doc.setFont("helvetica", "normal");
                    doc.text(
                        "FCMB",
                        doc.internal.pageSize.width - data.settings.margin.right,
                        doc.internal.pageSize.height - 10,
                        { align: "right" }
                    );

                    // Add header to all pages except the first
                    if (data.pageNumber > 1) {
                        // Add a mini header to continuation pages
                        doc.setFillColor(primaryColor[0], primaryColor[1], primaryColor[2]);
                        doc.rect(0, 0, doc.internal.pageSize.width, 10, "F");

                        doc.setFont("helvetica", "bold");
                        doc.setFontSize(10);
                        doc.setTextColor(255, 255, 255);
                        doc.text("TRANSACTION REPORT (Continued)", 14, 7);
                    }
                },
            });

            doc.save(`Transaction_Report_${format(new Date(), "yyyyMMdd_HHmm")}.pdf`);
        } catch (error) {
            console.error("Error generating PDF:", error);
            sendCatchFeedback("Error generating PDF. Please try a different format or with fewer rows.");
        }
    };

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const exportToCSV = (dataToExport: any[]) => {
        try {
            // Extract only the data fields we want to export
            const simpleColumns = [
                { header: "Date", accessorKey: "createdDate" },
                { header: "Description", accessorKey: "narration" },
                { header: "Amount", accessorKey: "amount" },
                { header: "Type", accessorKey: "transactionType" },
                { header: "Status", accessorKey: "status" },
                { header: "Reference", accessorKey: "reference" },
            ];

            // Filter based on user selection
            const filteredColumns = simpleColumns.filter(
                (col) => selectedColumns.includes(col.header) || !columnValues.includes(col.header)
            );

            // Simple column headers
            const headers = filteredColumns.map((col) => col.header);

            // Configure CSV options
            const options = mkConfig({
                fieldSeparator: ",",
                decimalSeparator: ".",
                useTextFile: false,
                useBom: true,
                useKeysAsHeaders: false,
                columnHeaders: headers,
                filename: `Transaction_Report_${format(new Date(), "yyyyMMdd_HHmm")}`,
                showColumnHeaders: true,
            });

            // Create a simple title section
            const titleRows = [
                { Date: "TRANSACTION REPORT" },
                { Date: `Date Range: ${format(startDate, "dd MMM yyyy")} - ${format(endDate, "dd MMM yyyy")}` },
                { Date: `Generated on: ${format(new Date(), "dd MMM yyyy HH:mm")}` },
                { Date: `Total Records: ${dataToExport.length}` },
                { Date: "" }, // Empty row
            ];

            // Format data rows in a simple way
            const dataRows = dataToExport.map((row) => {
                const newRow: Record<string, string> = {};

                filteredColumns.forEach((col) => {
                    const key = col.accessorKey;
                    const header = col.header;
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    const value = key && row ? (row as any)[key] : "";

                    // Format values appropriately
                    if (key === "amount" && typeof value === "number") {
                        newRow[header] = `₦${value.toLocaleString("en-NG", { minimumFractionDigits: 2 })}`;
                    } else if (key === "createdDate" && value) {
                        newRow[header] = format(new Date(value), "dd MMM yyyy");
                    } else if (key === "transactionType") {
                        newRow[header] = value === "Credit" ? "Credit" : "Debit";
                    } else if (key === "status") {
                        newRow[header] = value ? String(value).toUpperCase() : "";
                    } else if (value === null || value === undefined) {
                        newRow[header] = "";
                    } else {
                        newRow[header] = String(value);
                    }
                });

                return newRow;
            });

            // Combine title and data
            const allData = [...titleRows, ...dataRows];

            // Generate and download CSV
            const csv = generateCsv(options)(allData);
            download(options)(csv);
        } catch (error) {
            console.error("Error generating CSV:", error);
            sendCatchFeedback("Error generating CSV. Please try again with a different format.");
        }
    };

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const exportToExcel = (dataToExport: any[]) => {
        try {
            // Extract only the data fields we want to export
            const simpleColumns = [
                { header: "Date", accessorKey: "createdDate" },
                { header: "Description", accessorKey: "narration" },
                { header: "Amount", accessorKey: "amount" },
                { header: "Type", accessorKey: "transactionType" },
                { header: "Status", accessorKey: "status" },
                { header: "Reference", accessorKey: "reference" },
            ];

            // Filter based on user selection
            const filteredColumns = simpleColumns.filter(
                (col) => selectedColumns.includes(col.header) || !columnValues.includes(col.header)
            );

            // Create a workbook
            const wb = XLSX.utils.book_new();

            // Add metadata to the workbook
            wb.Props = {
                Title: "Transaction Report",
                Subject: "Financial Transactions",
                Author: "FCMB",
                CreatedDate: new Date(),
            };

            // Format data for Excel with simple structure
            const formattedData = dataToExport.map((row) => {
                const newRow: Record<string, string | number | Date | boolean | null> = {};

                filteredColumns.forEach((col) => {
                    const key = col.accessorKey;
                    const header = col.header;
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    const value = key && row ? (row as any)[key] : null;

                    // Format specific fields
                    if (key === "amount" && typeof value === "number") {
                        // Keep as number for Excel to format properly
                        newRow[header] = value;
                    } else if (key === "createdDate" && value) {
                        // Convert to Excel date format
                        try {
                            const dateValue = new Date(value);
                            newRow[header] = dateValue;
                        } catch {
                            newRow[header] = String(value);
                        }
                    } else if (key === "transactionType") {
                        newRow[header] = value === "Credit" ? "Credit" : "Debit";
                    } else if (key === "status") {
                        newRow[header] = value ? String(value).toUpperCase() : "";
                    } else if (value === null || value === undefined) {
                        newRow[header] = "";
                    } else {
                        newRow[header] = String(value);
                    }
                });

                return newRow;
            });

            // Create worksheet from data
            const ws = XLSX.utils.json_to_sheet(formattedData);

            // Add title and metadata rows
            const titleData = [
                ["TRANSACTION REPORT"],
                [`Date Range: ${format(startDate, "dd MMM yyyy")} - ${format(endDate, "dd MMM yyyy")}`],
                [`Generated on: ${format(new Date(), "dd MMM yyyy HH:mm")}`],
                [`Total Records: ${dataToExport.length}`],
                [""], // Empty row before data
            ];

            // Prepend title data to worksheet
            XLSX.utils.sheet_add_aoa(ws, titleData, { origin: "A1" });

            // Define column widths
            const colWidths = filteredColumns.map((col) => {
                const header = col.header;

                // Set width based on column type
                if (col.accessorKey === "amount") {
                    return { wch: 15 }; // Width for amount columns
                } else if (col.accessorKey === "createdDate") {
                    return { wch: 15 }; // Width for date columns
                } else if (col.accessorKey === "narration") {
                    return { wch: 40 }; // Width for description
                } else if (col.accessorKey === "reference") {
                    return { wch: 25 }; // Width for reference
                } else if (header && header.length > 15) {
                    return { wch: header.length + 5 }; // For long headers
                } else {
                    return { wch: 15 }; // Default width
                }
            });

            // Apply column widths
            ws["!cols"] = colWidths;

            // Define merged cells for the title section
            const mergeCount = Math.min(filteredColumns.length - 1, 5);
            ws["!merges"] = [
                // Merge cells for the title
                { s: { r: 0, c: 0 }, e: { r: 0, c: mergeCount } },
                // Merge cells for date range
                { s: { r: 1, c: 0 }, e: { r: 1, c: mergeCount } },
                // Merge cells for generation date
                { s: { r: 2, c: 0 }, e: { r: 2, c: mergeCount } },
                // Merge cells for record count
                { s: { r: 3, c: 0 }, e: { r: 3, c: mergeCount } },
            ];

            // Add the worksheet to the workbook
            XLSX.utils.book_append_sheet(wb, ws, "Transactions");

            // Write the workbook to file with a better filename
            XLSX.writeFile(wb, `Transaction_Report_${format(new Date(), "yyyyMMdd_HHmm")}.xlsx`);
        } catch (error) {
            console.error("Error generating Excel:", error);
            sendCatchFeedback("Error generating Excel file. Please try again with a different format.");
        }
    };

    const handleExport = async () => {
        // Early validation: Check if we have any data source
        if (!selectedRows || selectedRows.length === 0) {
            // No rows selected, check if account is selected for API-based export
            if (!selectedAccount) {
                sendCatchFeedback("Please select an account to export transactions");
                return;
            }
        }

        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        let dataToExport: any[] = [];

        // Check if account is selected for API-based exports
        if (selectedRows && selectedRows.length > 0) {
            // Use selected rows from table
            dataToExport = selectedRows;
        } else {
            // For API-based export, account selection is required (already validated above)
            const fetchedData = await fetchTransactionsForExport();
            if (!fetchedData || fetchedData.length === 0) {
                sendCatchFeedback("No data available for export");
                return;
            }
            dataToExport = fetchedData;
        }

        // Check if we have data to export
        if (dataToExport.length === 0) {
            sendCatchFeedback("No data available for export");
            return;
        }

        try {
            // Export based on selected type
            switch (exportType) {
                case "pdf":
                    exportToPDF(dataToExport);
                    break;
                case "csv":
                    exportToCSV(dataToExport);
                    break;
                case "excel":
                    exportToExcel(dataToExport);
                    break;
                default:
                    console.error("Export type is not supported or invalid.");
            }

            // Close the export dialog
            handleCloseFilter();
        } catch (error) {
            // Handle any errors during export
            console.error("Error during export:", error);
            sendCatchFeedback("An error occurred during export. Please try again.");
        }
    };

    return (
        <div>
            <div>
                <Button variant="outline" size="sm" rightIcon={<ExportIcon />} onClick={() => setIsOpen(true)}>
                    Export
                </Button>
            </div>

            <SideDrawer isOpen={isOpen}>
                <div className="flex flex-col justify-between h-full">
                    <div>
                        <div className="border-b border-[#E3E5E8] py-[19px] px-4">
                            <div className="flex justify-between items-center">
                                <h2 className="text-[20px] leading-[26px] font-semibold text-black">
                                    Export transactions
                                </h2>
                                <CloseX onClick={handleCloseFilter} data-testid="modal-close" color="#90909D" />
                            </div>
                            <p className="text-sm leading-[18px] font-normal text-subText mt-[12px]">
                                Download and analyze your transaction history.
                            </p>
                        </div>
                        <div className="grid gap-[40px] py-8 px-6 text-[14px] leading-[18px]">
                            {/* Date Range Selection - Always show for API-based exports */}
                            {selectedRows.length === 0 && (
                                <div>
                                    <h4 className="font-semibold mb-[20px]">Date range</h4>
                                    <RadioGroup
                                        defaultValue="this-month"
                                        className="flex flex-col gap-4"
                                        onValueChange={(value) => setDateRangeType(value as DateRangeType)}
                                    >
                                        <div className="flex items-center space-x-2">
                                            <RadioGroupItem value="this-month" id="r1" />
                                            <Label htmlFor="r1">This month</Label>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            <RadioGroupItem value="last-3-months" id="r2" />
                                            <Label htmlFor="r2">Last 3 months</Label>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            <RadioGroupItem value="last-6-months" id="r3" />
                                            <Label htmlFor="r3">Last 6 months</Label>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            <RadioGroupItem value="custom" id="r4" />
                                            <Label htmlFor="r4">Custom (choose a date range)</Label>
                                        </div>
                                    </RadioGroup>

                                    {/* Custom Date Range Picker */}
                                    {dateRangeType === "custom" && (
                                        <div className="mt-4 grid grid-cols-2 gap-4">
                                            <div>
                                                <Label className="mb-2 block">Start Date</Label>
                                                <input
                                                    type="date"
                                                    value={format(startDate, "yyyy-MM-dd")}
                                                    onChange={(e) => {
                                                        const date = e.target.value
                                                            ? new Date(e.target.value)
                                                            : new Date();
                                                        setStartDate(date);
                                                    }}
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                                                />
                                            </div>
                                            <div>
                                                <Label className="mb-2 block">End Date</Label>
                                                <input
                                                    type="date"
                                                    value={format(endDate, "yyyy-MM-dd")}
                                                    onChange={(e) => {
                                                        const date = e.target.value
                                                            ? new Date(e.target.value)
                                                            : new Date();
                                                        setEndDate(date);
                                                    }}
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                                                />
                                            </div>
                                        </div>
                                    )}
                                </div>
                            )}

                            {/* Account Selection - Always show for API-based exports */}
                            {selectedRows.length === 0 && (
                                <div>
                                    <Dropdown
                                        label="Choose an account"
                                        name="account-type"
                                        placeholder={"Select an account"}
                                        options={accounts.map((item) => ({
                                            label: formatAccountText(item),
                                            value: item.accountNumber,
                                        }))}
                                        size="sm"
                                        onChange={(value) =>
                                            setSelectedAccount(
                                                typeof value === "string" ? (value as string) : (value?.value as string)
                                            )
                                        }
                                    />
                                </div>
                            )}

                            {/* Export Format Selection */}
                            <div>
                                <h4 className="font-semibold">Choose your export format</h4>
                                <div className="font-medium mt-[20px] flex flex-col gap-4">
                                    <Checkbox
                                        label="CSV (For spreadsheet software)"
                                        id="csv"
                                        checked={exportType === "csv"}
                                        onCheckedChange={() => {
                                            setExportType("csv");
                                        }}
                                        size={"sm"}
                                    />
                                    <Checkbox
                                        label="PDF (For printing and sharing)"
                                        id="pdf"
                                        checked={exportType === "pdf"}
                                        onCheckedChange={() => {
                                            setExportType("pdf");
                                        }}
                                        size={"sm"}
                                    />
                                    <Checkbox
                                        label="Excel (Microsoft Excel)"
                                        id="excel"
                                        checked={exportType === "excel"}
                                        onCheckedChange={() => {
                                            setExportType("excel");
                                        }}
                                        size={"sm"}
                                    />
                                </div>
                            </div>

                            {/* Column Selection */}
                            <div>
                                <h4 className="font-semibold">Columns</h4>
                                <div className="font-medium mt-[20px] flex flex-col gap-4">
                                    {columnValues.map((item) => (
                                        <Checkbox
                                            id={item}
                                            key={item}
                                            label={item}
                                            size={"sm"}
                                            checked={selectedColumns.includes(item)}
                                            onCheckedChange={(checked) => {
                                                if (checked) {
                                                    setSelectedColumns([...selectedColumns, item]);
                                                } else {
                                                    setSelectedColumns(selectedColumns.filter((col) => col !== item));
                                                }
                                            }}
                                        />
                                    ))}
                                </div>
                            </div>

                            {/* Selected Rows Info */}
                            {selectedRows && selectedRows.length > 0 && (
                                <div className="p-3 bg-blue-50 rounded-md text-sm text-blue-700">
                                    {selectedRows.length} row(s) selected for export. Only selected rows will be
                                    exported.
                                </div>
                            )}

                            {/* Account Selection Info */}
                            {selectedRows.length === 0 && (
                                <div className="p-3 bg-yellow-50 rounded-md text-sm text-yellow-700">
                                    Please select an account to export transactions.
                                </div>
                            )}
                        </div>
                    </div>

                    <div className="flex justify-end items-center gap-3 border-t border-[#E3E5E8] py-4 px-6">
                        <Button type="button" variant="outline" onClick={handleCloseFilter} disabled={isLoading}>
                            Cancel
                        </Button>
                        <Button
                            type="button"
                            onClick={handleExport}
                            disabled={isLoading || (!selectedRows?.length && !selectedAccount)}
                        >
                            {isLoading ? (
                                <>
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    Loading...
                                </>
                            ) : (
                                "Export"
                            )}
                        </Button>
                    </div>
                </div>
            </SideDrawer>
        </div>
    );
}
