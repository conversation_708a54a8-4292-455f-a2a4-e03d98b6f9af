import Checkbox from "@/components/common/checkbox";
import TableMoreAction from "@/components/common/table/table-more-action";
import { ArrowDownIcon } from "@/components/icons/table";
import { formatDate } from "@/functions/date";
import { ITransaction } from "@/redux/types/transactions";
import { ColumnDef, TableMeta } from "@tanstack/react-table";

interface TableMetaWithSetIsOpen extends TableMeta<ITransaction> {
    setIsOpen: (value: boolean) => void;
    setIsReportOpen: (value: boolean) => void;
    setTransactionId: (value: string) => void;
    handleReportDownload: (value: string) => void;
}

export const columns: ColumnDef<ITransaction>[] = [
    {
        id: "select",
        header: ({ table }) => (
            <Checkbox
                checked={table.getIsAllPageRowsSelected()}
                onCheckedChange={(checked) => table.toggleAllPageRowsSelected(checked)}
                aria-label="Select all"
                size="sm"
                indeterminate={table.getIsSomePageRowsSelected()}
            />
        ),
        cell: ({ row }) => (
            <Checkbox
                checked={row.getIsSelected()}
                onCheckedChange={(checked) => row.toggleSelected(checked)}
                aria-label="Select row"
                size="sm"
            />
        ),
        enableSorting: false,
        enableHiding: false,
    },
    {
        accessorKey: "createdDate",
        header: ({ column }) => (
            <button
                className="flex items-center gap-1"
                onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            >
                <span>Date</span>
                <ArrowDownIcon />
            </button>
        ),
        cell: ({ row }) => {
            const payment = row.original;

            return <>{formatDate(payment.createdDate)}</>;
        },
    },

    {
        accessorKey: "narration",
        header: "Narration",
    },

    {
        accessorKey: "amount",
        header: ({ column }) => (
            <button
                className="flex items-center justify-end gap-1 text-right w-full"
                onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            >
                <ArrowDownIcon />
                <span className="text-right">Amount</span>
            </button>
        ),
        cell: ({ row }) => {
            const payment = row.original;
            const amount = parseFloat(row.getValue("amount"));
            const formatted = new Intl.NumberFormat("en-NG", {
                style: "currency",
                currency: "NGN",
            }).format(amount);

            return (
                <div
                    className={`text-right font-semibold ${payment.transactionType === "Credit" ? "text-[var(--success)]" : ""}`}
                >
                    {payment.transactionType === "Credit" ? "+" : "-"} {formatted}
                </div>
            );
        },
    },
    {
        id: "actions",
        cell: ({ row, table }) => {
            const payment = row.original;
            const setIsOpen = (table.options.meta as TableMetaWithSetIsOpen).setIsOpen;
            const setTransactionId = (table.options.meta as TableMetaWithSetIsOpen).setTransactionId;
            const handleReportDownload = (table.options.meta as TableMetaWithSetIsOpen).handleReportDownload;

            const updatedMenuItems = [
                {
                    label: "View",
                    type: ["Credit", "Debit"],
                    onClick: (data: ITransaction) => {
                        if (setIsOpen) {
                            setTransactionId(data.transactionId);
                            setIsOpen(true);
                        }
                    },
                },
                // {
                //     label: "Report",
                //     type: ["Credit", "Debit"],
                //     onClick: (data: ITransaction) => {
                //         if (setIsReportOpen) {
                //             setIsReportOpen(true);
                //         }
                //     },
                // },
                {
                    label: "Download receipt",
                    type: ["Credit", "Debit"],
                    onClick: (data: ITransaction) => {
                        handleReportDownload(data.transactionId);
                    },
                },
            ];

            return <TableMoreAction data={{ ...payment, id: payment.transactionId }} menuItems={updatedMenuItems} />;
        },
    },
];
