"use client";

import { useState, useEffect } from "react";
import { FilterIcon } from "lucide-react";
import LabelInput from "@/components/common/label-input";
import Dropdown from "@/components/common/dropdown";
import DatePicker from "@/components/common/date-picker";
import SideDrawer from "@/components/common/drawer";
import SearchInput from "@/components/common/search-input";
import { Button } from "@/components/common/buttonv3";
import CloseX from "@/components/common/close-x";

const dateFilterValues = ["Last 30 days", "Last 3 months", "Last 6 months", "Custom date"];
const amountFilterValue = ["Is exactly", "Is between", "Is less than", "Is greater than"];

interface ITransactionFilter {
    search?: string;
    startDate?: string;
    endDate?: string;
    minAmount?: string;
    maxAmount?: string;
    amount?: string;
}

interface TransactionFilterProps {
    readonly currentFilters: ITransactionFilter;
    readonly tempFilters: ITransactionFilter;
    readonly setTempFilters: (filters: ITransactionFilter) => void;
    readonly onApplyFilter: () => void;
    readonly onClearAll: () => void;
    readonly onSearch: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

export function TransactionFilter({
    currentFilters,
    tempFilters,
    setTempFilters,
    onApplyFilter,
    onClearAll,
    onSearch,
}: TransactionFilterProps) {
    const [isOpen, setIsOpen] = useState<boolean>(false);
    const [dateOption, setDateOption] = useState<{ label: string; value: string }>({
        label: "Custom date",
        value: "Custom date",
    });
    const [amountOptions, setAmountOptions] = useState<{ label: string; value: string }>({
        label: "Is between",
        value: "Is between",
    });

    useEffect(() => {
        if (tempFilters.startDate === "" && tempFilters.endDate === "") {
            setDateOption({ label: "Custom date", value: "Custom date" });
        }
    }, [tempFilters.startDate, tempFilters.endDate]);

    useEffect(() => {
        if (dateOption.value !== "Custom date") {
            const { startDate, endDate } = calculateDateRange(dateOption.value);
            setTempFilters({
                ...tempFilters,
                startDate: startDate.toISOString(),
                endDate: endDate.toISOString(),
            });
        }

        // eslint-disable-next-line
    }, [dateOption.value]);

    const handleCloseFilter = () => {
        setIsOpen(false);
    };

    function calculateDateRange(option: string): { startDate: Date; endDate: Date } {
        const endDate = new Date();
        const startDate = new Date();

        switch (option) {
            case "Last 30 days":
                startDate.setDate(endDate.getDate() - 30);
                break;
            case "Last 3 months":
                startDate.setMonth(endDate.getMonth() - 3);
                break;
            case "Last 6 months":
                startDate.setMonth(endDate.getMonth() - 6);
                break;
            default:
                break;
        }

        return { startDate, endDate };
    }

    const onChangeDateRange = (startDate: Date | null, endDate: Date | null) => {
        setTempFilters({
            ...tempFilters,
            startDate: startDate?.toISOString() || "",
            endDate: endDate?.toISOString() || "",
        });
    };

    const onChangeAmountRange = (minAmount: string, maxAmount: string) => {
        setTempFilters({
            ...tempFilters,
            minAmount,
            maxAmount,
        });
    };

    const renderAmountInput = () => {
        switch (amountOptions.value) {
            case "Is between":
                return (
                    <>
                        <LabelInput
                            name="min-amount"
                            label="Min. amount"
                            data-testid="min-amount"
                            value={tempFilters.minAmount}
                            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                onChangeAmountRange(e.target.value, tempFilters.maxAmount || "")
                            }
                            className="w-full"
                        />
                        <LabelInput
                            name="max-amount"
                            label="Max. amount"
                            data-testid="max-amount"
                            value={tempFilters.maxAmount}
                            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                onChangeAmountRange(tempFilters.minAmount || "", e.target.value)
                            }
                            className="w-full"
                        />
                    </>
                );
            case "Is exactly":
                return (
                    <LabelInput
                        name="amount"
                        label="Enter amount"
                        value={tempFilters.maxAmount}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                            setTempFilters({ ...tempFilters, amount: e.target.value });
                            onChangeAmountRange(e.target.value, e.target.value);
                        }}
                        className="w-full"
                    />
                );
            case "Is less than":
                return (
                    <LabelInput
                        name="max-amount"
                        data-testid="max-amount"
                        label="Enter amount"
                        value={tempFilters.maxAmount}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => onChangeAmountRange("", e.target.value)}
                        className="w-full"
                    />
                );
            case "Is greater than":
                return (
                    <LabelInput
                        name="min-amount"
                        data-testid="min-amount"
                        label="Enter amount"
                        value={tempFilters.minAmount}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => onChangeAmountRange(e.target.value, "")}
                        className="w-full"
                    />
                );
            default:
                return null;
        }
    };

    return (
        <div data-testid="transaction-filter">
            <div className="flex items-center gap-3">
                <SearchInput
                    placeholder="Search"
                    value={currentFilters.search}
                    onChange={onSearch}
                    data-testid="search-input"
                    size="sm"
                />
                <button
                    onClick={() => setIsOpen(true)}
                    className="flex items-center gap-[4px] rounded-full py-[9px] px-3 border border-[#DBDBE1] bg-[#F9F9FA] text-sm leading-[18px] font-medium text-subText h-8 w-auto"
                >
                    <FilterIcon color="#90909D" size={14} />
                    <span data-testid="add-filter">Add filter</span>
                </button>
            </div>

            <SideDrawer isOpen={isOpen}>
                <div className="flex flex-col justify-between h-full">
                    <div>
                        <div className="border-b border-[#E3E5E8] flex justify-between items-center py-[19px] px-4">
                            <h2 className="text-[20px] leading-[26px] font-semibold text-black">Filter</h2>
                            <CloseX onClick={handleCloseFilter} data-testid="modal-close" color="#90909D" />
                        </div>
                        <div className="grid gap-[20px] py-8 px-6">
                            <div>
                                <Dropdown
                                    label="Date"
                                    name="date"
                                    value={dateOption}
                                    options={dateFilterValues.map((item) => ({
                                        label: item,
                                        value: item,
                                    }))}
                                    useFormik={false}
                                    onChange={(value) => {
                                        setDateOption(value as { label: string; value: string });
                                    }}
                                />
                            </div>

                            <div className="flex flex-col gap-[20px] md:flex-row">
                                <DatePicker
                                    label="Start date"
                                    value={tempFilters.startDate ? new Date(tempFilters.startDate) : undefined}
                                    onChange={(value) =>
                                        onChangeDateRange(
                                            value || null,
                                            tempFilters.endDate ? new Date(tempFilters.endDate) : null
                                        )
                                    }
                                    disable={dateOption.value !== "Custom date"}
                                />
                                <DatePicker
                                    label="End date"
                                    position="bottom right"
                                    value={tempFilters.endDate ? new Date(tempFilters.endDate) : undefined}
                                    onChange={(value) =>
                                        onChangeDateRange(
                                            tempFilters.startDate ? new Date(tempFilters.startDate) : null,
                                            value || null
                                        )
                                    }
                                    disable={dateOption.value !== "Custom date"}
                                />
                            </div>

                            <div>
                                <Dropdown
                                    label="Amount"
                                    name="amount"
                                    value={amountOptions}
                                    options={amountFilterValue.map((item) => ({
                                        label: item,
                                        value: item,
                                    }))}
                                    useFormik={false}
                                    onChange={(value) => {
                                        setAmountOptions(value as { label: string; value: string });
                                        setTempFilters({
                                            ...tempFilters,
                                            minAmount: "",
                                            maxAmount: "",
                                            amount: "",
                                        });
                                    }}
                                />
                            </div>

                            <div className="flex flex-col gap-[20px] md:flex-row">{renderAmountInput()}</div>
                        </div>
                    </div>

                    <div className="flex justify-end items-center gap-3 border-t border-[#E3E5E8] py-4 px-6">
                        <Button
                            type="button"
                            variant="outline"
                            onClick={() => {
                                onClearAll();
                            }}
                        >
                            Clear
                        </Button>
                        <Button
                            type="button"
                            onClick={() => {
                                onApplyFilter();
                                handleCloseFilter();
                            }}
                        >
                            Apply filters
                        </Button>
                    </div>
                </div>
            </SideDrawer>
        </div>
    );
}
