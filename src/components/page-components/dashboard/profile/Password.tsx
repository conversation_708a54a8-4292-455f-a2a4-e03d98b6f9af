"use client";
import <PERSON>ton from "@/components/common/button";
import LabelInput from "@/components/common/label-input";
import PasswordInput from "@/components/common/password-input";
import { useAppDispatch } from "@/redux/hooks";
import { useFormik } from "formik";
import React from "react";
import * as yup from "yup";

const Password = () => {
    const [loading, setLoading] = React.useState(false);
    const dispatch = useAppDispatch();

    const formik = useFormik({
        initialValues: {
            oldPassword: "",
            newPassword: "",
            confirmPassword: "",
        },
        onSubmit: () => {
            submitValues();
        },
        validationSchema: yup.object({
            oldPassword: yup.string().required("Old password is required"),
            newPassword: yup
                .string()
                .required("New password is required")
                .min(14, "Password must be at least 14 characters")
                .matches(/[A-Z]/, "Password must contain at least one uppercase letter")
                .matches(/[0-9]/, "Password must contain at least one number")
                .matches(/[!@#$%^&*(),.?":{}|<>]/, "Password must contain at least one special character"),
            confirmPassword: yup
                .string()
                .required("Enter new password again")
                .oneOf([yup.ref("newPassword"), ""], "New Passwords don't match"),
        }),
    });

    const submitValues = async () => {
        // try {
        //   setLoading(true);
        //   const response = await appAxios.patch('/auth/update-password', {
        //     oldPassword: formik.values.oldPassword,
        //     newPassword: formik.values.newPassword,
        //   });
        //   sendFeedback(response.data?.message, 'success');
        //   const userObject = response.data?.data;
        //   dispatch(updateUser({ user: userObject }));
        // } catch (error: any) {
        //   sendCatchFeedback(error);
        // } finally {
        //   setLoading(false);
        // }
    };

    return (
        <section className="w-full bg-white border border-[#DFDFDF] py-6 px-[28px] rounded-md ">
            <h3 className="font-bold mb-5">Update Password</h3>
            <form onSubmit={formik.handleSubmit} className="w-full">
                <LabelInput
                    formik={formik}
                    name="oldPassword"
                    label="Old Password"
                    type="password"
                    className="mb-3 [&>label]:!mb-0"
                />
                <PasswordInput
                    formik={formik}
                    passwordFieldName="newPassword"
                    confirmPasswordFieldName="confirmPassword"
                    label="New Password"
                    confirmLabel="Confirm Password"
                    className="mb-6"
                />

                <Button type="submit" className="h-10" loading={loading}>
                    Update Password
                </Button>
            </form>
        </section>
    );
};

export default Password;
