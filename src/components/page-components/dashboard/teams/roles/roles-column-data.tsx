import type { ColumnDef, TableMeta } from "@tanstack/react-table";
import Checkbox from "@/components/common/checkbox";
import TableMoreAction from "@/components/common/table/table-more-action";
import type { menuItemType } from "@/components/common/table/types";
import { EditIcon, EyeIcon } from "@/components/icons/team";
import { RoleData } from "@/redux/types/roles";
import { useAppSelector } from "@/redux/hooks";
import { useMemo } from "react";
import { capitalizeUserName } from "@/functions/stringManipulations";

interface TeamMembers {
    teamMembers: string;
}

export interface FullRolesColumnData extends RoleData, TeamMembers {}

export interface IRolesTableMeta extends TableMeta<FullRolesColumnData> {
    setRoleToView: (value: RoleData | null) => void;
    setIsViewPermissionsSideDrawerOpen: (value: boolean) => void;
    setIsEditMode: (value: boolean) => void;
    setIsCreateCustomRoleModalOpen: (value: boolean) => void;
}

// Reusable text cell component
const TextCell = ({ value }: { value: string }) => (
    <p className="text-[#3A3A41] text-[0.875rem] font-normal tracking-[0.0175rem] leading-[1.125rem] capitalize">
        {value}
    </p>
);

// Separate function to handle date formatting logic
const formatDateCell = (dateValue: string): JSX.Element => {
    // If not a string or empty, return as is
    if (!dateValue || typeof dateValue !== "string") {
        return <TextCell value={dateValue} />;
    }

    // Try standard date parsing
    const date = new Date(dateValue);
    if (!isNaN(date.getTime())) {
        return <TextCell value={date.toDateString()} />;
    }

    // Try parsing as timestamp
    const timestampDate = new Date(parseInt(dateValue));
    if (!isNaN(timestampDate.getTime())) {
        return <TextCell value={timestampDate.toDateString()} />;
    }

    // Return original value as fallback
    return <TextCell value={dateValue} />;
};

// Component to display creator name
const CreatedByCell = ({ creatorId }: { creatorId: string }) => {
    // Get team members data from Redux store
    const { data: teamMembersData } = useAppSelector((state) => state.teamMembers.getTeamMembers);

    // Find the team member who created this role
    const creator = useMemo(() => {
        if (!teamMembersData || !Array.isArray(teamMembersData) || !creatorId) {
            return null;
        }

        // Convert creatorId to number for comparison
        const creatorIdNum = parseInt(creatorId, 10);
        if (isNaN(creatorIdNum)) {
            return null;
        }

        return teamMembersData.find((member) => member.id === creatorIdNum);
    }, [teamMembersData, creatorId]);

    // If creator is found, display their name
    if (creator) {
        return <TextCell value={`${capitalizeUserName(creator.firstName)} ${capitalizeUserName(creator.lastName)}`} />;
    }

    // If creator is not found, display the ID
    return <TextCell value={creatorId || "Unknown"} />;
};

// Function to create a text column
const createTextColumn = (accessorKey: keyof FullRolesColumnData, header: string): ColumnDef<FullRolesColumnData> => ({
    accessorKey,
    header,
    cell: ({ row }) => {
        // For date columns, apply formatting
        if (accessorKey === "dateCreated") {
            return formatDateCell(row.getValue(accessorKey));
        }

        // For team members column, show count of members with this role
        if (accessorKey === "teamMembers") {
            // Use a component that will access the Redux store
            return <TeamMembersCountCell roleId={row.original.id} />;
        }

        // For createdBy column, show the name of the creator
        if (accessorKey === "createdBy") {
            return <CreatedByCell creatorId={row.getValue(accessorKey)} />;
        }

        // For all other columns, just return the value
        return <TextCell value={row.getValue(accessorKey)} />;
    },
});

// Component to display team members count
const TeamMembersCountCell = ({ roleId }: { roleId: number }) => {
    // Get team members data from Redux store
    const { data: teamMembersData } = useAppSelector((state) => state.teamMembers.getTeamMembers);

    // Count team members with this role
    const memberCount = useMemo(() => {
        if (!teamMembersData || !Array.isArray(teamMembersData)) {
            return 0;
        }

        // Remember to adjust for the +1 difference in roleId
        return teamMembersData.filter((member) => member.roleId === roleId).length;
    }, [teamMembersData, roleId]);

    return (
        <p className="text-[#3A3A41] text-[0.875rem] font-normal tracking-[0.0175rem] leading-[1.125rem]">
            {memberCount}
        </p>
    );
};

// Define the text columns configuration
const textColumns: Array<{ key: keyof FullRolesColumnData; header: string }> = [
    { key: "name", header: "Role" },
    { key: "dateCreated", header: "Date Created" },
    { key: "createdBy", header: "Created By" },
    { key: "type", header: "Type" },
    { key: "teamMembers", header: "Team Members" },
];

// Selection column
const selectColumn: ColumnDef<FullRolesColumnData> = {
    id: "select",
    header: ({ table }) => (
        <Checkbox
            checked={table.getIsAllPageRowsSelected()}
            onCheckedChange={(checked) => table.toggleAllPageRowsSelected(checked)}
            aria-label="Select all"
            data-testid="checkbox-select-all"
            size="sm"
            indeterminate={table.getIsSomePageRowsSelected()}
        />
    ),
    cell: ({ row }) => (
        <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={(checked) => row.toggleSelected(checked)}
            aria-label="Select row"
            data-testid="checkbox-select-row"
            size="sm"
            role="checkbox-row"
        />
    ),
    enableSorting: false,
    enableHiding: false,
};

// Actions column
const actionsColumn: ColumnDef<FullRolesColumnData> = {
    id: "actions",
    cell: ({ row, table }) => {
        const roleData = row.original;
        const meta = table.options.meta as IRolesTableMeta;

        const menuItems: menuItemType<FullRolesColumnData>[] = [
            {
                label: "View role permissions",
                icon: <EyeIcon baseColor="#151519" />,
                onClick: (data: FullRolesColumnData) => {
                    if (data) {
                        meta.setRoleToView(data);
                        meta.setIsViewPermissionsSideDrawerOpen(true);
                    }
                },
            },
            {
                label: "Edit role",
                icon: <EditIcon baseColor="#151519" />,
                onClick: (data: FullRolesColumnData) => {
                    if (data) {
                        meta.setRoleToView(data);
                        meta.setIsEditMode(true);
                        meta.setIsCreateCustomRoleModalOpen(true);
                    }
                },
            },
        ];

        return (
            <div>
                <TableMoreAction
                    data-testid="more-actions"
                    data={{ ...roleData, id: roleData.id }}
                    menuItems={menuItems}
                />
            </div>
        );
    },
};

// Generate the columns array
export const columns: ColumnDef<FullRolesColumnData>[] = [
    selectColumn,
    ...textColumns.map((col) => createTextColumn(col.key, col.header)),
    actionsColumn,
];
