"use client";

import { useState, useEffect, useMemo, useCallback, useRef } from "react";
import { getCoreRowModel, useReactTable } from "@tanstack/react-table";
import { DataTable } from "@/components/common/table/DataTable";
import { columns, IPendingTableMeta } from "./pending-column-data";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { getPendingInvites } from "@/redux/actions/teamMembersActions";
import { RoleData } from "@/redux/types/roles";
import { PendingInvitesData, TeamMembersData } from "@/redux/types/teamMembers";
import { sendCatchFeedback } from "@/functions/feedback";
import { capitalizeUserName } from "@/functions/stringManipulations";

// We'll use the PendingInvitesData type from Redux
// but we need to adapt it to match the expected format for the table
interface IPendingTeamMember {
    id?: string;
    name: string;
    role: string;
    inviter: {
        name: string;
        dateInvited: string;
    };
}

// Memoized date objects for today and yesterday to avoid recreating them on every render
const getTodayAndYesterday = (() => {
    let cachedToday: Date | null = null;
    let cachedYesterday: Date | null = null;
    let lastUpdateDay = -1;

    return () => {
        const now = new Date();
        const currentDay = now.getDate();

        // Only recalculate if the day has changed
        if (lastUpdateDay !== currentDay) {
            const today = new Date(now);
            today.setHours(0, 0, 0, 0);

            const yesterday = new Date(today);
            yesterday.setDate(yesterday.getDate() - 1);

            cachedToday = today;
            cachedYesterday = yesterday;
            lastUpdateDay = currentDay;
        }

        return { today: cachedToday!, yesterday: cachedYesterday! };
    };
})();

// Function to format date string to a more readable format (date only)
const formatDate = (dateString: string): string => {
    if (!dateString) return "N/A";

    // Parse the ISO date string
    const date = new Date(dateString);

    // Check if the date is valid
    if (isNaN(date.getTime())) return "Invalid date";

    // Get cached today and yesterday dates
    const { today, yesterday } = getTodayAndYesterday();

    // Reset time part for accurate date comparison
    const dateToCompare = new Date(date);
    dateToCompare.setHours(0, 0, 0, 0);

    // Return "Today" or "Yesterday" for recent dates
    if (dateToCompare.getTime() === today.getTime()) {
        return "Today";
    } else if (dateToCompare.getTime() === yesterday.getTime()) {
        return "Yesterday";
    }

    // Format the date as "DD MMM YYYY" for older dates
    const day = date.getDate();
    const month = date.toLocaleString("default", { month: "short" });
    const year = date.getFullYear();

    return `${day} ${month} ${year}`;
};

// Function to convert PendingInvitesData to IPendingTeamMember with error handling
const adaptPendingInvites = (
    pendingInvites: PendingInvitesData[],
    teamMembers: TeamMembersData[] | null
): IPendingTeamMember[] => {
    if (!pendingInvites || !Array.isArray(pendingInvites)) return [];

    return pendingInvites.map((invite) => {
        try {
            // Find the team member who invited this person
            let inviterName = invite?.invitedBy || "Unknown";

            // If we have team members data and invitedBy is a numeric ID
            if (teamMembers && Array.isArray(teamMembers) && invite?.invitedBy) {
                // Try to parse invitedBy as a number
                const inviterId = parseInt(invite.invitedBy, 10);

                if (!isNaN(inviterId)) {
                    // Find the team member with this ID
                    const inviter = teamMembers.find((member) => member.id === inviterId);

                    // If found, use their full name
                    if (inviter) {
                        inviterName =
                            `${capitalizeUserName(inviter.firstName)} ${capitalizeUserName(inviter.lastName)}`.trim();
                    }
                }
            }

            return {
                id: invite?.id?.toString() || "",
                name:
                    `${capitalizeUserName(invite?.firstName || "")} ${capitalizeUserName(invite?.lastName || "")}`.trim() ||
                    "Unknown",
                role: invite?.roleId?.toString() || "0", // This will be replaced with actual role name in the future
                inviter: {
                    name: inviterName, // Use the resolved name
                    dateInvited: formatDate(invite?.timeInvited || ""), // Format the date for better readability
                },
            };
        } catch (error) {
            console.error("Error adapting invite:", error, invite);
            // Return a fallback object if there's an error
            return {
                id: "",
                name: "Error",
                role: "0",
                inviter: {
                    name: "Unknown",
                    dateInvited: "Error",
                },
            };
        }
    });
};

interface TabPendingInvitationProps {
    rolesData?: RoleData[] | null;
    "data-testid"?: string;
    refreshRolesData?: () => void;
    refreshPendingInvites?: () => void;
    searchTerm?: string;
}

const TabPendingInvitation: React.FC<TabPendingInvitationProps> = ({
    rolesData: propRolesData,
    refreshPendingInvites: propRefreshPendingInvites,
    searchTerm = "",
}) => {
    const dispatch = useAppDispatch();
    const [rowSelection, setRowSelection] = useState({});
    // Dynamic empty table messages based on whether we're searching or not
    const emptyTabledescription = searchTerm
        ? `No pending invitations found matching "${searchTerm}". Try a different search term.`
        : "You have no pending invitations yet. Invite your team members to start using Corporate Internet banking.";
    const emptyTabletitle = searchTerm ? "No search results" : "No pending invitations yet";

    // Get pending invites data from Redux store
    const { data: pendingInvitesData, error, loading } = useAppSelector((state) => state.teamMembers.getPendingInvites);

    // Get team members data from Redux store
    const { data: teamMembersData } = useAppSelector((state) => state.teamMembers.getTeamMembers);

    // Use roles data from props if available, otherwise get from Redux store
    const { data: reduxRolesData } = useAppSelector((state) => state.roles.getAllRoles);
    // Use props data if available, otherwise use Redux data
    const rolesData = propRolesData || reduxRolesData;

    // Track if data has been fetched
    const dataFetchedRef = useRef(false);

    // Function to explicitly refresh data when needed
    const refreshPendingInvites = useCallback(() => {
        // Use the prop function if available, otherwise dispatch directly
        if (propRefreshPendingInvites) {
            propRefreshPendingInvites();
        } else {
            dispatch(getPendingInvites());
        }
    }, [dispatch, propRefreshPendingInvites]);

    // No need to fetch roles data here as it's already fetched in the parent component

    // Handle success and error states - simplified version
    useEffect(() => {
        // Only show error feedback
        if (error) {
            sendCatchFeedback(error);
        }
    }, [error]);

    // Handle component unmount
    useEffect(
        () => () => {
            // Reset the data fetched flag when component unmounts
            dataFetchedRef.current = false;
        },
        []
    );

    // Create a role mapping for efficient lookups
    const roleMap = useMemo(() => {
        const map: Record<number, string> = {};

        if (rolesData && Array.isArray(rolesData)) {
            rolesData.forEach((role) => {
                map[role.id] = role.name;
            });
        }

        return map;
    }, [rolesData]);

    // Convert pending invites data to the format expected by the table
    const adaptedData = useMemo(() => {
        // Add extra null/undefined checks to prevent errors during data refresh
        if (!pendingInvitesData) return [];
        if (!Array.isArray(pendingInvitesData)) return [];

        try {
            // First adapt the data, passing team members data for resolving inviter names
            const adapted = adaptPendingInvites(pendingInvitesData, teamMembersData);

            // If no search term, return all adapted data
            if (!searchTerm) {
                return adapted;
            }

            // Convert search term to lowercase for case-insensitive comparison
            const lowerSearchTerm = searchTerm.toLowerCase();

            // Filter by name (we only search by name for pending invitations)
            return adapted.filter((invite) => {
                const name = invite?.name.toLowerCase();
                return name.includes(lowerSearchTerm);
            });
        } catch (error) {
            console.error("Error adapting pending invites data:", error);
            return [];
        }
    }, [pendingInvitesData, searchTerm]);

    // Create the table directly - don't call hooks inside other hooks
    const table = useReactTable({
        data: adaptedData,
        columns,
        getCoreRowModel: getCoreRowModel(),
        onRowSelectionChange: setRowSelection,
        state: {
            rowSelection,
        },
        meta: {
            roleMap,
            refreshPendingInvites,
        } as unknown as IPendingTableMeta,
    });

    // Memoize the entire component output to prevent unnecessary re-renders
    const memoizedContent = useMemo(() => {
        // Add a final safety check to ensure table is valid
        if (!table || typeof table !== "object") {
            // Return a loading state if table is not ready
            return (
                <main data-testid="pending-content" className="">
                    <div className="flex justify-center items-center h-40">
                        <p>Loading...</p>
                    </div>
                </main>
            );
        }

        return (
            <main data-testid="pending-content" className="">
                <DataTable
                    columns={columns}
                    table={table}
                    loading={loading}
                    emptyTabledescription={emptyTabledescription}
                    emptyTabletitle={emptyTabletitle}
                />
            </main>
        );
    }, [table, loading, emptyTabledescription, emptyTabletitle]);

    return memoizedContent;
};

export default TabPendingInvitation;
