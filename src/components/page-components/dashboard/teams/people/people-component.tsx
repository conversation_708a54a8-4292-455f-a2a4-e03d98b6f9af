"use client";
import React, { useCallback, useState, useEffect, useMemo } from "react";
import { useTranslation } from "react-i18next";
import TabSwitch from "@/components/common/tab-switch";
import { PeopleFilter } from "./people-filter";
import TabMembers from "./tab-members";
import { Button } from "@/components/common/buttonv3";
import { UserIcon } from "@/components/icons/team";
import TabPendingInvitation from "./tab-pending-invitation";
import InviteTeamMemberModal from "./modals/invite-team-member-modal";
import { useSearchParams, useRouter, usePathname } from "next/navigation";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { getPendingInvites, getTeamMembers } from "@/redux/actions/teamMembersActions";
import { getAllRoles, getAllPermissions } from "@/redux/actions/rolesActions";
import { convertCamelCaseToWords } from "@/functions/stringManipulations";
import { sendCatchFeedback } from "@/functions/feedback";
import { X } from "lucide-react";
// Using hardcoded permission string for HOC
import { PATH_PROTECTED } from "@/routes/path";

interface IPeopleFilter {
    size?: number | null;
    page?: number | null;
    search?: string;
    startDate?: string;
    endDate?: string;
    source?: string;
    minAmount?: string;
    maxAmount?: string;
    amount?: string;
}

function formatDate(dateStr: string): string {
    const decodedDateStr = decodeURIComponent(dateStr);

    const date = new Date(decodedDateStr);

    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    const day = date.getDate().toString().padStart(2, "0");

    return `${year}-${month}-${day}`;
}

const PeopleComponent = () => {
    const { t } = useTranslation();
    const [activeTab, setActiveTab] = useState("members");

    // Track if data has been fetched
    const [dataFetched, setDataFetched] = useState({
        roles: false,
        pendingInvites: false,
        teamMembers: false,
        permissions: false,
    });
    const dispatch = useAppDispatch();

    // Get pending invites data from Redux store using a memoized selector
    const pendingInvitesState = useAppSelector((state) => state.teamMembers.getPendingInvites);
    const { data: pendingInvitesData, loading: pendingInvitesLoading } = pendingInvitesState;

    // Get roles data from Redux store
    const rolesState = useAppSelector((state) => state.roles.getAllRoles);
    const { data: rolesData, loading: rolesLoading } = rolesState;

    // Memoize the pending count to prevent unnecessary re-renders
    const pendingCount = useMemo(() => {
        // Add extra null/undefined checks to prevent errors during data refresh
        if (pendingInvitesData && Array.isArray(pendingInvitesData)) {
            return pendingInvitesData.length.toString();
        }
        return pendingInvitesLoading ? "..." : "0";
    }, [pendingInvitesData, pendingInvitesLoading]);

    // Get team members data from Redux store
    const teamMembersState = useAppSelector((state) => state.teamMembers.getTeamMembers);

    // Get permissions data from Redux store
    const permissionsState = useAppSelector((state) => state.roles.getAllPermissions);
    const { data: permissionsData, loading: permissionsLoading } = permissionsState;

    // Centralized function to refresh data
    const refreshData = useCallback(
        (dataType: "roles" | "pendingInvites" | "teamMembers" | "permissions" | "all") => {
            if (dataType === "roles" || dataType === "all") {
                dispatch(getAllRoles());
                setDataFetched((prev) => ({ ...prev, roles: true }));
            }
            if (dataType === "pendingInvites" || dataType === "all") {
                dispatch(getPendingInvites());
                setDataFetched((prev) => ({ ...prev, pendingInvites: true }));
            }
            if (dataType === "teamMembers" || dataType === "all") {
                dispatch(getTeamMembers());
                setDataFetched((prev) => ({ ...prev, teamMembers: true }));
            }
            if (dataType === "permissions" || dataType === "all") {
                dispatch(getAllPermissions());
                setDataFetched((prev) => ({ ...prev, permissions: true }));
            }
        },
        [dispatch]
    );

    // Fetch data when switching to the members tab
    useEffect(() => {
        if (activeTab === "members") {
            // Fetch team members data when the tab is first selected
            if (!dataFetched.teamMembers) {
                refreshData("teamMembers");
            }

            // Fetch permissions data when the tab is first selected
            if (!dataFetched.permissions) {
                refreshData("permissions");
            }
        }
    }, [activeTab, dataFetched.teamMembers, dataFetched.permissions, refreshData]);

    // Fetch data when component mounts
    useEffect(() => {
        // Only fetch data if it hasn't been fetched yet
        if (!dataFetched.roles && !rolesData && !rolesLoading) {
            dispatch(getAllRoles());
            setDataFetched((prev) => ({ ...prev, roles: true }));
        }

        if (!dataFetched.pendingInvites && !pendingInvitesData && !pendingInvitesLoading) {
            dispatch(getPendingInvites());
            setDataFetched((prev) => ({ ...prev, pendingInvites: true }));
        }

        // Fetch permissions data if needed
        if (!dataFetched.permissions && !permissionsData && !permissionsLoading) {
            dispatch(getAllPermissions());
            setDataFetched((prev) => ({ ...prev, permissions: true }));
        }

        // Fetch team members data if needed
        // We don't fetch this immediately to improve initial load time
        // It will be fetched when the user switches to the members tab

        // Handle errors in data fetching
        if (rolesState.error) {
            sendCatchFeedback(rolesState.error);
        }

        if (pendingInvitesState.error) {
            sendCatchFeedback(pendingInvitesState.error);
        }

        if (teamMembersState.error) {
            sendCatchFeedback(teamMembersState.error);
        }

        if (permissionsState.error) {
            sendCatchFeedback(permissionsState.error);
        }
    }, [
        dispatch,
        dataFetched,
        rolesData,
        rolesLoading,
        pendingInvitesData,
        pendingInvitesLoading,
        permissionsData,
        permissionsLoading,
        rolesState.error,
        pendingInvitesState.error,
        teamMembersState.error,
        permissionsState.error,
    ]);
    const [isInviteTeamMemberModalOpen, setIsInviteTeamMemberModalOpen] = useState(false);

    // filter code section
    const router = useRouter();
    const pathname = usePathname();
    const params = useSearchParams();

    const [currentFilters, setCurrentFilters] = useState<IPeopleFilter>({
        size: Number(params.get("size")) || 10,
        page: Number(params.get("page")) || 1,
        search: params.get("search") ?? "",
        startDate: params.get("startDate") ?? "",
        endDate: params.get("endDate") ?? "",
        source: params.get("source") ?? "",
        minAmount: params.get("minAmount") ?? "",
        maxAmount: params.get("maxAmount") ?? "",
        amount: params.get("amount") ?? "",
    });

    // We no longer need tempFilters since we're only using search

    const createQueryString = useCallback((searchTerm: string) => {
        // Only include the search term in the URL parameters
        const newParams = new URLSearchParams();
        if (searchTerm) {
            newParams.set("search", searchTerm);
        }
        return newParams.toString();
    }, []);

    const updateFilters = useCallback(
        async (newFilters: IPeopleFilter) => {
            setCurrentFilters(newFilters);
            // Only include the search term in the URL
            const queryString = createQueryString(newFilters.search ?? "");
            router.push(pathname + (queryString ? "?" + queryString : ""));
        },
        [createQueryString, pathname, router]
    );

    const onSearch = useCallback(
        (value: string) => {
            updateFilters({ ...currentFilters, search: value });
        },
        [currentFilters, updateFilters]
    );

    const handleSearch = useCallback(
        (event: React.ChangeEvent<HTMLInputElement>) => {
            const searchTerm = event.target.value;
            onSearch(searchTerm);
        },
        [onSearch]
    );

    // end of filter code section

    const tabs = [
        { id: "members", label: t("Members") },
        {
            id: "pending",
            label: (
                <div className="flex items-center gap-2">
                    <span>{t("Pending Invitation")}</span>
                    <div
                        data-testid="pending-count-badge"
                        className="bg-[#F4E1FE] text-primary font-primary h-[18px] w-[18px] rounded-[6px] text-2.5 font-semibold flex items-center justify-center"
                    >
                        {pendingCount}
                    </div>
                </div>
            ),
        },
    ];

    // Memoize the handler to prevent unnecessary re-renders
    const handleInviteTeamMemberModal = useCallback(
        (memberInvited?: boolean) => {
            // If the modal is already open, we're closing it
            if (isInviteTeamMemberModalOpen) {
                setIsInviteTeamMemberModalOpen(false);

                // Only refresh data if a member was actually invited
                if (memberInvited) {
                    refreshData("pendingInvites");
                }
                return;
            }

            // We're opening the modal
            // Special handling for the pending tab
            if (activeTab === "pending") {
                // For opening the modal, use a more robust approach
                // First, ensure roles data is available
                if (!rolesData) {
                    // Fetch roles data first using our centralized function
                    refreshData("roles");
                }
            }

            // Open the modal
            setIsInviteTeamMemberModalOpen(true);
        },
        [activeTab, isInviteTeamMemberModalOpen, rolesData, refreshData]
    );

    return (
        <section data-testid="people-component">
            <h1 className="font-bold text-xl text-[#2E335B] mb-11">{t("People")}</h1>

            <div className="w-fit mb-8">
                <TabSwitch tabs={tabs} activeTab={activeTab} onChange={setActiveTab} />
            </div>

            <div className="w-full mb-8">
                <div className="flex justify-between items-center mb-1">
                    <PeopleFilter currentFilters={currentFilters} onSearch={handleSearch} activeTab={activeTab} />

                    <Button
                        color="primary"
                        size="medium"
                        leftIcon={<UserIcon baseColor="white" />}
                        data-testid="invite-button"
                        onClick={() => handleInviteTeamMemberModal()}
                    >
                        Invite team member
                    </Button>
                </div>
                {/* <CurrentFilterItems currentFilters={currentFilters} onClearAll={onClearAll} /> */}
            </div>

            <div className="w-full">
                {activeTab === "members" && (
                    <TabMembers
                        data-testid="members-content"
                        rolesData={rolesData}
                        refreshRolesData={() => refreshData("roles")}
                        refreshTeamMembersData={() => refreshData("teamMembers")}
                        refreshPermissionsData={() => refreshData("permissions")}
                        permissionsData={permissionsData || undefined}
                        searchTerm={currentFilters.search}
                    />
                )}
                {activeTab === "pending" && (
                    <TabPendingInvitation
                        data-testid="pending-content"
                        rolesData={rolesData}
                        refreshRolesData={() => refreshData("roles")}
                        refreshPendingInvites={() => refreshData("pendingInvites")}
                        searchTerm={currentFilters.search}
                    />
                )}
            </div>

            {/* Use the same modal for both tabs */}
            {isInviteTeamMemberModalOpen && (
                <InviteTeamMemberModal
                    open={isInviteTeamMemberModalOpen}
                    onClose={handleInviteTeamMemberModal}
                    activeTab={activeTab}
                    rolesData={rolesData}
                />
            )}
        </section>
    );
};

// export default PeopleComponent;

interface CurrentFilterItemsProps {
    currentFilters: IPeopleFilter;
    onClearAll: () => void;
}

const CurrentFilterItems: React.FC<CurrentFilterItemsProps> = ({ currentFilters, onClearAll }) => {
    const { page, size, ...others } = currentFilters;
    const hasFilters = Object.entries(others).some(([key, value]) => key !== "search" && value);

    if (!hasFilters) {
        return null;
    }

    return (
        <div className="flex items-center gap-[26px] !mt-6">
            <div className="flex flex-wrap gap-2">
                {Object.entries(others).map(([key, value]) => {
                    if (key !== "search" && value) {
                        let displayText = `${convertCamelCaseToWords(key)}: ${value}`;

                        if (key === "startDate" || key === "endDate") {
                            displayText = `${key === "startDate" ? "From" : "To"}: ${formatDate(value)}`;
                        }

                        return <FilterItem key={key} text={displayText} />;
                    }
                    return null;
                })}
            </div>
            <Button variant="text-destructive" leftIcon={<X color="#D92D20" size={16} />} onClick={onClearAll}>
                Clear Filter
            </Button>
        </div>
    );
};

const FilterItem = ({ text }: { text: string }) => (
    <span className="flex items-center rounded-full py-[9px] px-3 border border-[#DBDBE1] bg-[#F9F9FA] text-sm leading-[18px] font-medium text-subText h-8 w-max">
        {text}
    </span>
);

// const ProtectedPage = withPermissionCheck(PeopleComponent, {
//     // Specify the permissions required to access this page
//     requiredPermissions: ["View team members"],
//     // User needs any of these permissions (not all)
//     requireAll: false,
//     // Where to redirect if user doesn't have permissions
//     redirectTo: PATH_PROTECTED.root,
// });

import { withDynamicPermissionCheck } from "@/components/hoc/withDynamicPermissionCheck";

// Create protected component with dynamic permissions
const ProtectedPage = withDynamicPermissionCheck(PeopleComponent, {
    // Use dynamic permission checker that automatically updates when backend permissions change
    permissions: (P) => [P.TEAM_MANAGEMENT.VIEW_TEAM_MEMBERS],
    // User needs any of these permissions (not all)
    requireAll: false,
    // Where to redirect if user doesn't have permissions
    redirectTo: PATH_PROTECTED.root,
    // Fallback permissions for safety (optional)
    fallbackPermissions: ["View team members"],
    // Show loading state while permissions are being resolved
    showLoadingState: true,
});

export default ProtectedPage;
