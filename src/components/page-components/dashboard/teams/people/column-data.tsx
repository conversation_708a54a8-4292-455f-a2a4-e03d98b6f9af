import { type ColumnDef, TableMeta } from "@tanstack/react-table";
import Badge from "@/components/common/badge";
import Checkbox from "@/components/common/checkbox";
import TableMoreAction from "@/components/common/table/table-more-action";
import { menuItemType } from "@/components/common/table/types";
import { DeleteIcon, EditIcon } from "@/components/icons/team";
import { getNameInitials, capitalizeUserName } from "@/functions/stringManipulations";

// EditTeamMemberDrawer is now moved to the parent component
import { TeamMembersData } from "@/redux/types/teamMembers";

export interface ITeamMember {
    id: string;
    name: string;
    email: string;
    role: string;
    lastLogin: string;
    twoFaStatus: "Enabled" | "Disabled";
    initials: string;
    phoneNumber: string;
}

export interface ITableMetaWithSetIsOpen extends TableMeta<TeamMembersData> {
    setIsOpen: (value: boolean) => void;
    setMemberId: (value: string) => void;
    setIsRoleEditOpen: (value: boolean) => void;
    setMemberToEdit: (member: TeamMembersData | null) => void;
    setIsRemoveModalOpen: (value: boolean) => void;
    setMemberToRemove: (member: TeamMembersData | null) => void;
    isRoleEditOpen: boolean;
    memberToEdit: TeamMembersData | null;
    setPhoneNumber: (value: string) => void;
    // New properties for role mapping
    roleMap: Record<number, string>;
    rolesLoading: boolean;
}

export const columns: ColumnDef<TeamMembersData>[] = [
    {
        id: "select",
        header: ({ table }) => (
            <Checkbox
                checked={table.getIsAllPageRowsSelected()}
                onCheckedChange={(checked) => table.toggleAllPageRowsSelected(checked)}
                aria-label="Select all"
                data-testid="checkbox-select-all"
                size="sm"
                indeterminate={table.getIsSomePageRowsSelected()}
            />
        ),
        cell: ({ row }) => (
            <Checkbox
                checked={row.getIsSelected()}
                onCheckedChange={(checked) => row.toggleSelected(checked)}
                aria-label="Select row"
                data-testid="checkbox-select-row"
                size="sm"
                role="checkbox-row"
            />
        ),
        enableSorting: false,
        enableHiding: false,
    },
    {
        accessorKey: "name",
        header: "Name",
        cell: ({ row }) => {
            const member = row.original;
            const initials = getNameInitials(member.firstName + " " + member.lastName);
            return (
                <div className="flex items-center gap-2">
                    <div className=" rounded-full py-3 px-3 h-[3rem] w-[3rem]  bg-[#F9F0FE]  flex items-center justify-center ">
                        <span
                            data-testid="member-initials"
                            className="text-[#3A3A41] text-[16px] font-medium uppercase"
                        >
                            {initials}
                        </span>
                    </div>

                    <div className="flex flex-col items-start gap-1">
                        <div className="font-[600] text-[0.875rem] leading-[1.125rem] text-[#151519] ">
                            {capitalizeUserName(member.firstName) + " " + capitalizeUserName(member.lastName)}
                        </div>
                        <div className="text-[#3A3A41] text-[0.875rem] font-normal tracking-[0.0175rem] leading-[1.125rem]">
                            {member?.email}
                        </div>
                    </div>
                </div>
            );
        },
    },
    {
        accessorKey: "role",
        header: "Role",
        cell: ({ row, table }) => {
            const member = row.original;
            const meta = table.options.meta as ITableMetaWithSetIsOpen;
            const { roleMap, rolesLoading } = meta;

            // Get role name from the role map, if roleId is 0, then it is a super admin role, otherwise use the role map to get the role name, if roleId is not in the role map, then use "Unknown Role"
            let roleName;
            if (member.roleId === 0) {
                roleName = "Super Admin";
            } else {
                roleName = roleMap?.[member.roleId] ?? "Unknown Role";
            }

            // Render role with appropriate styling, if roles are loading, show a loading indicator
            return (
                <p className="text-[0.875rem] font-normal tracking-[0.0175rem] leading-[1.125rem] text-[#3A3A41]">
                    {rolesLoading ? <span className="inline-block animate-pulse">Loading...</span> : roleName}
                </p>
            );
        },
    },
    {
        accessorKey: "lastLogin",
        header: "Last login",
        cell: ({ row }) => {
            const formatDate = (dateString: string) => {
                try {
                    // Check if the string is empty or invalid
                    if (!dateString) return "N/A";

                    // Parse the date string as UTC
                    // The API sends dates in format: "2025-05-19T16:42:49.208137"
                    // We need to ensure it's treated as UTC and then converted to local time

                    // First, append 'Z' to the timestamp if it doesn't have timezone info
                    // This explicitly marks it as UTC
                    const utcDateString = dateString.endsWith("Z") ? dateString : `${dateString}Z`;

                    // Create a date object (which will be in local time)
                    const date = new Date(utcDateString);

                    // Check if date is valid
                    if (isNaN(date.getTime())) return "N/A";

                    // Get day (no leading zero)
                    const day = date.getDate();

                    // Get month (short name)
                    const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
                    const month = months[date.getMonth()];

                    // Get year
                    const year = date.getFullYear();

                    // Get time (hours and minutes with leading zeros)
                    const hours = date.getHours().toString().padStart(2, "0");
                    const minutes = date.getMinutes().toString().padStart(2, "0");

                    // Format: "8 Nov 2024, 15:06"
                    return `${day} ${month} ${year}, ${hours}:${minutes}`;
                } catch (error) {
                    console.error("Error formatting date:", error);
                    return "N/A";
                }
            };

            return (
                <p className="text-[#3A3A41] text-[0.875rem] font-normal tracking-[0.0175rem] leading-[1.125rem]">
                    {typeof row.getValue("lastLogin") === "string" ? formatDate(row.getValue("lastLogin")) : "N/A"}
                </p>
            );
        },
    },
    {
        accessorKey: "mfaStatus",
        header: "2FA status",
        cell: ({ row }) => {
            const status = row.getValue("mfaStatus");
            const statusText = status ? "Enabled" : "Disabled";

            return <Badge text={statusText as string} size="md" color={status ? "success" : "error"} />;
        },
    },
    {
        id: "actions",
        cell: ({ row, table }) => {
            const member = row.original;
            const meta = table.options.meta as ITableMetaWithSetIsOpen;
            // Drawer is now handled in the parent component

            const menuItems: menuItemType<TeamMembersData>[] = [
                {
                    label: "Edit team member",
                    icon: <EditIcon baseColor="#151519" />,
                    onClick: (data: TeamMembersData) => {
                        if (data) {
                            meta.setIsRoleEditOpen(true);
                            // @ts-ignore
                            meta.setMemberToEdit(data);
                        }
                    },
                },
                {
                    label: "Remove team member",
                    style: { color: "#D92D20", whiteSpace: "nowrap", width: "fit-content" },
                    icon: <DeleteIcon />,
                    onClick: (data: TeamMembersData) => {
                        if (data) {
                            // @ts-ignore
                            meta.setMemberToRemove(data);
                            meta.setIsRemoveModalOpen(true);
                        }
                    },
                },
            ];
            if (member.roleId !== 0) {
                return (
                    <div>
                        <TableMoreAction
                            data-testid="more-actions"
                            data={{ ...member, id: member?.id }}
                            menuItems={menuItems}
                        />
                    </div>
                );
            }
        },
    },
];
