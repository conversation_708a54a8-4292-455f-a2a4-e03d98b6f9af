import CountryCode from "@/components/common/country-code";
import SideDrawer from "@/components/common/drawer";
import Dropdown from "@/components/common/dropdown";
import LabelInput from "@/components/common/label-input";
import { RoleInfo, RolePermission } from "@/components/icons/team";
import { X } from "lucide-react";
import { ITableMetaWithSetIsOpen } from "../column-data";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { useEffect, useMemo, useState, useRef } from "react";
import { sendCatchFeedback, sendFeedback } from "@/functions/feedback";
import { capitalizeUserName } from "@/functions/stringManipulations";
import { getRoleById, getAllPermissions } from "@/redux/actions/rolesActions";
import { editTeamMember, getTeamMembers } from "@/redux/actions/teamMembersActions";
import { PermissionData, RoleData } from "@/redux/types/roles";
import { EditTeamMemberPayload } from "@/redux/types/teamMembers";
import { Button } from "@/components/common/buttonv3";
import { useFormik } from "formik";
import * as Yup from "yup";
import { clearEditSuccess, clearEditError } from "@/redux/slices/teamMembersSlice";

interface IEditMemberDrawerProps {
    handleClose: () => void;
    meta: ITableMetaWithSetIsOpen;
    refreshTeamMembers?: () => void;
    refreshRolesData?: () => void;
    refreshPermissionsData?: () => void;
    permissionsData?: PermissionData[];
}

// Helper function to group permissions by module
const groupPermissionsByModule = (permissions: PermissionData[]) => {
    const grouped: Record<string, PermissionData[]> = {};

    permissions.forEach((permission) => {
        const moduleName = permission.appModule || "Other";
        if (!grouped[moduleName]) {
            grouped[moduleName] = [];
        }
        grouped[moduleName].push(permission);
    });

    return grouped;
};

const EditTeamMemberDrawer = ({
    handleClose,
    meta,
    refreshTeamMembers,
    refreshRolesData,
    refreshPermissionsData,
    permissionsData: propPermissionsData,
}: IEditMemberDrawerProps) => {
    const dispatch = useAppDispatch();
    const [isSaving, setIsSaving] = useState(false);
    const [rolePermissions, setRolePermissions] = useState<PermissionData[]>([]);
    const [isLoadingPermissions, setIsLoadingPermissions] = useState(false);
    const [permissionsError, setPermissionsError] = useState<string | null>(null);

    // Store the original member details that won't change until a successful edit
    const [displayedMember, setDisplayedMember] = useState(meta.memberToEdit);

    // Get roles data from Redux store
    // The parent component (tab-members.tsx) already dispatches getAllRoles()
    const { data: rolesData, loading: rolesLoading } = useAppSelector((state) => state.roles.getAllRoles);
    // We don't need roleByIdData directly since we're using unwrap() in the useEffect
    const { data: allPermissionsData } = useAppSelector((state) => state.roles.getAllPermissions);
    // Get corporate ID from Redux store

    const {
        loading: isEditLoading,
        success: isEditSuccess,
        error: editError,
    } = useAppSelector((state) => state.teamMembers.editTeamMember);

    // Define validation schema with Yup
    const validationSchema = Yup.object({
        firstName: Yup.string().required("First name is required"),
        lastName: Yup.string().required("Last name is required"),
        email: Yup.string().email("Invalid email address").required("Email is required"),
        phoneNumber: Yup.string()
            .matches(/^\d{10}$/, "Phone number must be 10 digits")
            .nullable(),
        roleId: Yup.number().required("Role is required"),
    });

    // Initialize Formik
    const formik = useFormik({
        initialValues: {
            firstName: meta?.memberToEdit?.firstName ?? "",
            lastName: meta?.memberToEdit?.lastName ?? "",
            email: meta?.memberToEdit?.email ?? "",
            phoneNumber: meta?.memberToEdit?.phoneNumber ?? "",
            roleId: meta?.memberToEdit?.roleId ?? undefined,
            preferredMfaMethod: meta?.memberToEdit?.preferredMfaMethod ?? "",
            mfaStatus: meta?.memberToEdit?.mfaStatus ?? false,
        },
        validationSchema,
        enableReinitialize: true,
        onSubmit: (values) => {
            setIsSaving(true);

            // Prepare the payload for updating the team member
            const payload: EditTeamMemberPayload & { phoneNumber?: string } = {
                // corporateId: parseInt(corporateId),
                firstName: values.firstName,
                lastName: values.lastName,
                email: values.email,
                roleId: values.roleId ?? 0, // Fallback to 0 if undefined
                preferredMfaMethod: values.preferredMfaMethod,
                mfaStatus: values.mfaStatus,
                // Add phoneNumber to the payload
                phoneNumber: values.phoneNumber || "",
            };

            // Dispatch the action to update the team member
            dispatch(
                editTeamMember({
                    data: payload,
                    teamMemberId: meta.memberToEdit?.id ?? 0,
                })
            );
        },
    });

    // Initialize displayedMember when meta.memberToEdit changes
    useEffect(() => {
        if (meta.memberToEdit) {
            setDisplayedMember(meta.memberToEdit);
        }
    }, [meta.memberToEdit]);

    // No need to fetch permissions when the component mounts
    // It's now handled by the parent component

    // Handle edit team member success and error with data refresh
    const successHandledRef = useRef(false);

    useEffect(() => {
        // Only handle success once per edit operation
        if (isEditSuccess && !isEditLoading && !successHandledRef.current) {
            successHandledRef.current = true;

            // First, reset the loading state
            setIsSaving(false);

            // Update the displayed member with the current form values
            if (meta.memberToEdit) {
                const updatedMember = {
                    ...meta.memberToEdit,
                    firstName: formik.values.firstName,
                    lastName: formik.values.lastName,
                    email: formik.values.email,
                    phoneNumber: formik.values.phoneNumber,
                    roleId: formik.values.roleId ?? meta.memberToEdit.roleId,
                };

                // Update both the displayed member and the meta.memberToEdit
                setDisplayedMember(updatedMember);
                meta.setMemberToEdit(updatedMember);
            }

            // Show success message
            sendFeedback("Team member updated successfully", "success");

            // Close the drawer immediately
            handleClose();

            // Dispatch an action to clear the edit success state
            dispatch(clearEditSuccess());

            // Refresh team members list after the drawer is closed
            setTimeout(() => {
                if (refreshTeamMembers) {
                    refreshTeamMembers();
                } else {
                    dispatch(getTeamMembers());
                }
            }, 300);
        } else if (editError && !isEditLoading) {
            sendCatchFeedback(editError);
            setIsSaving(false);

            // Clear the error state
            dispatch(clearEditError());
        }

        // Reset the success handled flag when the edit state changes
        if (!isEditSuccess && !isEditLoading) {
            successHandledRef.current = false;
        }
    }, [isEditSuccess, isEditLoading, editError, handleClose, dispatch, formik.values]);

    // Format roles data for dropdown
    const roleOptions = useMemo(() => {
        if (!rolesData || !Array.isArray(rolesData)) {
            return [
                { value: "1", label: "Admin" },
                { value: "2", label: "Super Admin" },
                { value: "3", label: "Staff" },
            ];
        }

        // Map roles to dropdown options format
        return rolesData.map((role) => ({
            value: role.id.toString(),
            label: role.name,
        }));
    }, [rolesData]);

    // Helper function to find role name by ID
    const getRoleNameById = (roleId: number | undefined) => {
        if (roleId === undefined) return "";

        // Find the matching role in roleOptions
        const matchingRole = roleOptions.find((role) => role.value === roleId.toString());

        return matchingRole?.label ?? `Role ${roleId}`;
    };

    // Helper function to get initials from first and last name
    const getInitials = (firstName?: string, lastName?: string): string => {
        if (!firstName || !lastName) return "";

        // Get first letter of first name
        const firstInitial = firstName.charAt(0);

        // Get first letter of last name
        const lastInitial = lastName.charAt(0);

        // Combine and convert to uppercase
        return (firstInitial + lastInitial).toUpperCase();
    };

    // Helper function to get full name from first and last name
    const getFullName = (firstName?: string, lastName?: string): string => {
        if (!firstName || !lastName) return "";
        return `${capitalizeUserName(firstName)} ${capitalizeUserName(lastName)}`;
    };

    // Group permissions by module
    const groupedPermissions = useMemo(() => groupPermissionsByModule(rolePermissions), [rolePermissions]);

    // Function to render permissions content based on loading state, errors, and data
    const renderPermissionsContent = () => {
        // If permissions are loading, show loading indicator
        if (isLoadingPermissions) {
            return (
                <div className="flex justify-center items-center py-8">
                    <div className="animate-pulse text-[#5C068C]">Loading permissions...</div>
                </div>
            );
        }

        // If there's an error, show error message
        if (permissionsError) {
            return <div className="text-red-500 py-4">{permissionsError}</div>;
        }

        // If there are no permissions, show empty state
        if (rolePermissions.length === 0) {
            return <div className="text-gray-500 py-4">No permissions found for this role</div>;
        }

        // Otherwise, render the grouped permissions
        return Object.entries(groupedPermissions).map(([moduleName, permissions]) => (
            <div key={moduleName} className="flex flex-col gap-[8px] w-full mb-4">
                <p className="font-[600] text-[12px] leading-[18px] text-[#151519] capitalize">{moduleName}</p>
                <ul className="flex flex-col gap-4">
                    {permissions.map((permission) => (
                        <li key={permission.id} className="font-[400] text-[14px] leading-[18px] text-[#151519]">
                            • {permission.name}
                        </li>
                    ))}
                </ul>
            </div>
        ));
    };

    // Helper function to filter permissions for a role
    const filterPermissionsForRole = (permissionsData: PermissionData[], rolePermissionIds: number[]) =>
        permissionsData.filter((permission) => rolePermissionIds.includes(permission.id));

    // Helper function to fetch all permissions and filter for the role
    const fetchAndFilterAllPermissions = async (roleData: RoleData) => {
        try {
            // Use refreshPermissionsData if available
            if (refreshPermissionsData) {
                refreshPermissionsData();
            }

            // Fetch permissions data
            const permissionsData = await dispatch(getAllPermissions()).unwrap();

            if (permissionsData && Array.isArray(permissionsData)) {
                return filterPermissionsForRole(permissionsData, roleData.permissions);
            }

            return [];
        } catch (error) {
            setPermissionsError("Could not load permission details");
            sendCatchFeedback(error);
            return [];
        }
    };

    // Helper function to handle the role data after fetching
    const handleRoleData = async (roleData: RoleData) => {
        try {
            // If we already have permissions data, filter it
            if (allPermissionsData && Array.isArray(allPermissionsData)) {
                const rolePermissionsData = filterPermissionsForRole(allPermissionsData, roleData.permissions);
                setRolePermissions(rolePermissionsData);
            } else {
                // Otherwise fetch all permissions and filter
                const rolePermissionsData = await fetchAndFilterAllPermissions(roleData);
                setRolePermissions(rolePermissionsData);
            }

            setIsLoadingPermissions(false);
        } catch (error) {
            setPermissionsError("Failed to process role permissions");
            setIsLoadingPermissions(false);
            sendCatchFeedback(error);
        }
    };

    // Fetch role permissions when roleId changes
    const rolePermissionsRef = useRef<number | undefined>(undefined);

    // Helper function to check if roleId is valid and has changed
    const shouldFetchRolePermissions = (roleId: number | undefined) =>
        roleId !== undefined && roleId !== null && roleId >= 0 && roleId !== rolePermissionsRef.current;

    useEffect(() => {
        const roleId = formik.values.roleId;

        // Only fetch permissions if the roleId is valid and has changed
        if (shouldFetchRolePermissions(roleId)) {
            rolePermissionsRef.current = roleId;
            setIsLoadingPermissions(true);
            setPermissionsError(null);

            // Ensure roleId is a number (convert from string if needed)
            const validRoleId = typeof roleId === "string" ? parseInt(roleId, 10) : (roleId as number);

            dispatch(getRoleById(validRoleId))
                .unwrap()
                .then(handleRoleData)
                .catch((error) => {
                    setPermissionsError("Failed to load role permissions");
                    setIsLoadingPermissions(false);
                    sendCatchFeedback(error);
                });
        }
    }, [dispatch, formik.values.roleId, allPermissionsData]);

    return (
        <SideDrawer isOpen={meta.isRoleEditOpen} className="">
            <div className="flex flex-col h-full font-primary">
                {/* Fixed Header */}
                <div className="flex-shrink-0 border-b border-[#E3E5E8] px-6 py-[20px] w-full">
                    <button className="ml-auto text-right w-fit flex justify-end" onClick={handleClose}>
                        <X color="#90909D" />
                    </button>

                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2 py-[2-5px] mt-5">
                            <div className="rounded-full py-3 px-3 h-[3rem] w-[3rem] bg-[#F9F9FA] flex items-center justify-center">
                                <span data-testid="member-initials" className="text-[#3A3A41] text-[20px] font-medium">
                                    {getInitials(displayedMember?.firstName, displayedMember?.lastName)}
                                </span>
                            </div>

                            <div className="flex flex-col items-start gap-1">
                                <div className="font-[600] text-[0.875rem] leading-[1.125rem] text-[#151519]">
                                    {getFullName(displayedMember?.firstName, displayedMember?.lastName)}
                                    <span className="bg-[#F9F0FE] px-2 py-[6px] rounded-2xl text-xs font-medium text-[#5C068C] ml-2">
                                        {getRoleNameById(displayedMember?.roleId)}
                                    </span>
                                </div>
                                <div className="text-[#3A3A41] text-[0.875rem] font-normal tracking-[0.0175rem] leading-[1.125rem]">
                                    {displayedMember?.email}
                                </div>
                            </div>
                        </div>

                        <Button
                            variant="outline"
                            size="medium"
                            onClick={() => {
                                if (meta.memberToEdit) {
                                    meta.setMemberToRemove(meta.memberToEdit);
                                    meta.setIsRemoveModalOpen(true);
                                    handleClose(); // Close the edit drawer
                                }
                            }}
                        >
                            Remove
                        </Button>
                    </div>
                </div>

                {/* Scrollable Content */}
                <div className="flex-grow overflow-auto px-6 py-6">
                    <form
                        id="edit-team-member-form"
                        className="flex flex-col gap-[20px]"
                        autoComplete="on"
                        onSubmit={formik.handleSubmit}
                    >
                        <h3 className="font-[600] text-[14px] leading-[18px]">Details</h3>

                        <div className="flex gap-3 items-center w-full">
                            <LabelInput
                                className="w-full"
                                name="firstName"
                                type="text"
                                label="First Name"
                                useFormik={true}
                                formik={formik}
                                required
                            />

                            <LabelInput
                                className="w-full"
                                name="lastName"
                                type="text"
                                label="Last Name"
                                useFormik={true}
                                formik={formik}
                                required
                            />
                        </div>

                        <LabelInput
                            className="w-full"
                            name="email"
                            type="email"
                            label="Email address"
                            useFormik={true}
                            formik={formik}
                            required
                        />

                        <div className="">
                            <p className="text-[12px] font-semibold leading-[16px] text-[#151519] mb-2.5">
                                Phone number (optional)
                            </p>

                            <div className="flex gap-2 items-center w-full">
                                <CountryCode
                                    country="NG"
                                    className="h-11 bg-slate-600"
                                    aria-label="Country code"
                                    data-testid="country-code"
                                    aria-disabled={true}
                                />
                                <LabelInput
                                    className="w-full h-11"
                                    name="phoneNumber"
                                    type="tel"
                                    pattern="[0-9]*" // HTML5 pattern to only allow numbers
                                    inputMode="numeric" // Show numeric keyboard on mobile devices
                                    useFormik={true}
                                    formik={formik}
                                    placeholder="Enter 10 digit phone number"
                                    maxLength={10} // Hard limit on input length
                                    autoComplete="tel"
                                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                                        // Only allow digits
                                        const value = e.target.value.replace(/\D/g, "");

                                        // Set the value directly on the input element to enforce the limit
                                        e.target.value = value;

                                        // Update formik value using setFieldValue to ensure dirty state is tracked
                                        formik.setFieldValue("phoneNumber", value, false);
                                        // Mark as touched to show we've interacted with the field
                                        formik.setFieldTouched("phoneNumber", true, false);
                                    }}
                                    onKeyDown={(e: React.KeyboardEvent<HTMLInputElement>) => {
                                        // Prevent non-numeric keys (except for control keys like Backspace, Delete, etc.)
                                        const isNumericKey = /^\d$/.test(e.key);
                                        const isControlKey = [
                                            "Backspace",
                                            "Delete",
                                            "ArrowLeft",
                                            "ArrowRight",
                                            "Tab",
                                            "Home",
                                            "End",
                                        ].includes(e.key);

                                        if (!isNumericKey && !isControlKey) {
                                            e.preventDefault();
                                        }
                                    }}
                                    onInput={(e: React.FormEvent<HTMLInputElement>) => {
                                        // Additional check to ensure max length and digits only
                                        const input = e.target as HTMLInputElement;
                                        const digitsOnly = input.value.replace(/\D/g, "");

                                        // If the value changed after removing non-digits, update it
                                        if (digitsOnly !== input.value) {
                                            input.value = digitsOnly;
                                        }

                                        // Enforce max length
                                        if (input.value.length > 10) {
                                            input.value = input.value.slice(0, 10);
                                        }

                                        // Update formik value
                                        formik.setFieldValue("phoneNumber", input.value, false);
                                    }}
                                />
                            </div>
                        </div>

                        <div>
                            <Dropdown
                                className=""
                                label="Role"
                                name="roleId"
                                value={
                                    formik.values.roleId !== undefined
                                        ? {
                                              value: formik.values.roleId.toString(),
                                              label: getRoleNameById(formik.values.roleId),
                                          }
                                        : null
                                }
                                options={[
                                    {
                                        label: "Roles",
                                        options: roleOptions,
                                    },
                                ]}
                                isLoading={rolesLoading}
                                useFormik={false} // Set to false to handle the onChange manually
                                required
                                onChange={(selectedOption) => {
                                    if (!selectedOption) return;

                                    // Extract the value from the selected option
                                    const selectedValue =
                                        typeof selectedOption === "object" && selectedOption !== null
                                            ? selectedOption.value
                                            : selectedOption;

                                    // Convert the selected value to a number
                                    const roleId = parseInt(selectedValue, 10);

                                    // Update formik value
                                    formik.setFieldValue("roleId", roleId);
                                }}
                            />
                        </div>

                        <div className="mt-[32px] flex flex-col items-start gap-4">
                            <p className="font-[500] text-base leading-[20px] flex items-center">
                                <span className="mr-2">
                                    <RolePermission />
                                </span>
                                Role permissions{/* */}
                            </p>

                            <div className="p-5 flex gap-6 flex-col bg-[#F9F9FA] rounded-[8px] w-full">
                                {renderPermissionsContent()}
                            </div>
                        </div>
                    </form>
                </div>

                {/* Fixed Footer */}
                <div className="flex-shrink-0 px-6 py-4 border-t border-[#E3E5E8] flex flex-col gap-8">
                    <div className="w-full flex items-center gap-3 rounded-[8px] bg-[#F9F0FE] p-4">
                        <p className="font-[400] text-[14px] leading-[18px] text-[#7707B6] flex items-center">
                            <span className="mr-2">
                                <RoleInfo />
                            </span>
                            {/* */}
                            Permission changes will take effect immediately, and the team member will be notified of
                            these changes.
                        </p>
                    </div>

                    <div className="flex items-center gap-3 ml-auto w-fit pb-4">
                        <Button
                            variant="outline"
                            onClick={handleClose}
                            className="border border-[#DBDBE1] px-[14px] py-2 rounded-[8px] text-[14px] text-[#151519] leading-[18px] font-[600]"
                        >
                            Cancel
                        </Button>
                        <Button
                            variant="primary"
                            size="medium"
                            type="submit"
                            form="edit-team-member-form"
                            disabled={isSaving || isEditLoading || !formik.isValid}
                            loading={isSaving || isEditLoading}
                        >
                            Save changes
                        </Button>
                    </div>
                </div>
            </div>
        </SideDrawer>
    );
};

export default EditTeamMemberDrawer;
