"use client";

import React, { type FC, useState, useRef, useEffect, useMemo, useCallback } from "react";
import CountryCode from "@/components/common/country-code";
import Dropdown from "@/components/common/dropdown";
import FullScreenDrawer from "@/components/common/full-screen-drawer";
import LabelInput from "@/components/common/label-input";
import { Formik, Form, type FormikProps } from "formik";
import type { SingleValue } from "react-select";
import * as Yup from "yup";
import { useSuccessErrorHandler } from "@/hooks/useSuccessErrorHandler";
import { sendFeedback, sendCatchFeedback } from "@/functions/feedback";
import { capitalizeUserName } from "@/functions/stringManipulations";
import RolePermissionContent from "../../roles/role-permission-content";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import type { RoleData } from "@/redux/types/roles";
import { inviteTeamMember } from "@/redux/actions/teamMembersActions";
import { clearState } from "@/redux/slices/teamMembersSlice";
import { Button } from "@/components/common/buttonv3";

interface IFormValues {
    firstName: string;
    lastName: string;
    email: string;
    phoneNumber: string;
    role: string;
    roleId: number;
}

interface IInviteTeamMemberModalProps {
    onClose: (memberInvited?: boolean) => void;
    open: boolean;
    activeTab?: string;
    rolesData?: RoleData[] | null;
}

const InviteTeamMemberModal: FC<IInviteTeamMemberModalProps> = ({
    onClose,
    open,
    activeTab,
    rolesData: propRolesData,
}: IInviteTeamMemberModalProps) => {
    // Use ref for form access
    const formikRef = useRef<FormikProps<IFormValues>>(null);

    // State management
    const [isSideDrawerOpen, setIsSideDrawerOpen] = useState(false);
    const [selectedRole, setSelectedRole] = useState<RoleData>({} as RoleData);
    const [memberInvited, setMemberInvited] = useState(false);
    const [formKey, setFormKey] = useState(Date.now());

    // Redux
    const dispatch = useAppDispatch();
    const { data, loading, error, message, success } = useAppSelector((state) => state.teamMembers.inviteTeamMember);
    const corporateId = useAppSelector((state) => state.corporate.corporateId);
    const { data: reduxRoleData, loading: roleLoading } = useAppSelector((state) => state.roles.getAllRoles);

    // Use props data if available, otherwise use Redux data
    const roleData = propRolesData || reduxRoleData;

    // Initial form values - stable reference, no need for useMemo
    const initialValues: IFormValues = {
        firstName: "",
        lastName: "",
        email: "",
        phoneNumber: "",
        role: "",
        roleId: 0, // Set to 0 to ensure no role is selected by default
    };

    // Validation schema - stable reference, no need for useMemo
    const validationSchema = Yup.object({
        firstName: Yup.string().required("First name is required"),
        lastName: Yup.string().required("Last name is required"),
        email: Yup.string()
            .email("Please enter a valid email address")
            .matches(/^[^\s@]+@[^\s@]+\.[^\s@]+$/, "Enter a valid email address")
            .test("email-format", "Please enter a valid email address", (value) => {
                if (!value) return true; // Skip validation if empty (handled by required)

                // Additional validation checks
                if (value.length > 254) return false; // Too long
                if (value.indexOf("@") <= 0) return false; // No local part

                const parts = value.split("@");
                if (parts.length !== 2) return false; // Multiple @ symbols

                const [local, domain] = parts;
                if (local.length > 64) return false; // Local part too long
                if (domain.length < 3) return false; // Domain too short

                // Check for consecutive dots
                if (value.includes("..")) return false;

                // Check domain has at least one dot
                if (!domain.includes(".")) return false;

                // Check TLD is at least 2 characters
                const tld = domain.split(".").pop() ?? "";
                if (tld.length < 2) return false;

                return true;
            })

            .required("Email is required"),
        phoneNumber: Yup.string()
            .matches(/^\d{0,10}$/, "Phone number must be at most 10 digits")
            .test("len", "Phone number must be exactly 10 digits if provided", (val) => {
                if (!val) return true; // Allow empty
                return val.length === 0 || val.length === 10; // Must be empty or exactly 10 digits
            })
            .nullable(),
        role: Yup.string().required("Role is required"),
        roleId: Yup.number().min(1, "Please select a role"),
    });

    // Properly memoized callback functions
    const handleCloseDrawer = useCallback(() => {
        setIsSideDrawerOpen(false);
    }, []);

    // Reset form - memoized with stable reference
    const resetFormCompletely = useCallback(() => {
        // Reset the selectedRole
        setSelectedRole({} as RoleData);

        // Change the key to force a complete remount of Formik
        setFormKey((prevKey) => prevKey + 1);
    }, []);

    // Helper function to ensure form is dirty - stable reference
    const ensureFormIsDirty = useCallback(() => {
        if (formikRef.current) {
            // This is a workaround to force Formik to recognize the form as dirty
            const currentEmail = formikRef.current.values.email;

            // Make a temporary change
            formikRef.current.setFieldValue("email", currentEmail + " ", false);

            // Then change it back
            setTimeout(() => {
                if (formikRef.current) {
                    formikRef.current.setFieldValue("email", currentEmail, false);
                }
            }, 0);
        }
    }, []);

    // Form submission handler - memoized with minimal dependencies
    const handleSubmit = useCallback(() => {
        // Get the latest form values
        if (formikRef.current) {
            // Update payload with current form values
            const currentValues = formikRef.current.values;
            const updatedPayload = {
                corporateId: Number(corporateId),
                firstName: capitalizeUserName(currentValues.firstName || ""),
                lastName: capitalizeUserName(currentValues.lastName || ""),
                email: currentValues.email || "",
                phoneNumber: currentValues.phoneNumber || "",
                roleId: currentValues.roleId || 1,
            };

            // Dispatch the action with the latest values
            dispatch(inviteTeamMember(updatedPayload))
                .unwrap()
                .then(() => {
                    // Open the side drawer to show permissions
                    setIsSideDrawerOpen(true);
                })
                .catch((err) => {
                    // Handle errors from the API response
                    if (err?.message) {
                        sendFeedback(err.message, "error");
                    } else {
                        sendCatchFeedback(err);
                    }

                    // Keep the form open for corrections
                    if (formikRef.current) {
                        formikRef.current.setTouched({
                            firstName: true,
                            lastName: true,
                            email: true,
                            phoneNumber: true,
                            role: true,
                            roleId: true,
                        });

                        // Set specific error for duplicate email
                        if (err?.message?.toLowerCase().includes("email")) {
                            formikRef.current.setFieldError("email", "This email has already been invited");
                        }
                    }
                });
        }
    }, [dispatch, corporateId]);

    // Effect to clear Redux state when modal is opened or closed
    useEffect(() => {
        if (open) {
            // Clear the Redux state when the modal is opened
            dispatch(clearState("inviteTeamMember"));
        }

        // Clean up function to clear state when modal is closed
        return () => {
            dispatch(clearState("inviteTeamMember"));
        };
    }, [open, dispatch]);

    // Effect to clear Redux state when component mounts - no cleanup needed
    useEffect(() => {
        dispatch(clearState("inviteTeamMember"));
    }, [dispatch]);

    // Use custom hook to handle success and error responses
    useSuccessErrorHandler({
        data,
        success,
        error,
        message,
        loading,
        preventDuplicateHandling: true,
        successMessage: "Team member invited successfully", // Default success message
        onSuccess: () => {
            // Set successful invite flag
            setMemberInvited(true);

            // Close the drawer
            handleCloseDrawer();

            // Use our complete reset function
            resetFormCompletely();
        },
        onError: () => {
            // Close side drawer but keep modal open
            handleCloseDrawer();

            // Update form for errors
            if (formikRef.current) {
                // Touch all fields to show validation errors
                formikRef.current.setTouched(
                    {
                        firstName: true,
                        lastName: true,
                        email: true,
                        phoneNumber: true,
                        role: true,
                        roleId: true,
                    },
                    false
                );

                // Special handling for email errors
                if (error?.message?.toLowerCase().includes("email")) {
                    formikRef.current.setFieldError("email", "This email has already been invited");
                }

                // Ensure form is marked as dirty for editing
                ensureFormIsDirty();
            }
        },
        clearStateAction: () => dispatch(clearState("inviteTeamMember")),
        clearStateDelay: 500,
    });

    // Convert roles data to dropdown options - properly memoized
    const roleOptions = useMemo(() => {
        if (!roleData) return [];

        // Map role data to dropdown options format
        return roleData.map((role: RoleData) => ({
            value: role.id.toString(),
            label: role.name,
        }));
    }, [roleData]);

    // Define the type for role options
    interface RoleOption {
        value: string;
        label: string;
    }

    // Role selection handler - properly memoized with exact dependencies
    const handleRoleSelection = useCallback(
        (option: SingleValue<RoleOption> | string) => {
            if (!option || !formikRef.current) return;

            // Handle different types of option (object or string)
            let roleId: number;
            let roleName: string;

            if (typeof option === "string") {
                // Find the corresponding option if it's a string
                const selectedOption = roleOptions.find((opt) => opt.value === option);
                if (!selectedOption) return;

                roleId = Number(selectedOption.value);
                roleName = selectedOption.label;
            } else {
                // It's an object from react-select
                roleId = Number(option.value);
                roleName = option.label;
            }

            // Update form values
            formikRef.current.setFieldValue("role", roleName, true);
            formikRef.current.setFieldValue("roleId", roleId, true);

            // Mark fields as touched
            formikRef.current.setFieldTouched("role", true, true);
            formikRef.current.setFieldTouched("roleId", true, true);

            // Find and set the selected role
            if (roleData) {
                const role = roleData.find((r: RoleData) => r.id === roleId);
                if (role) {
                    setSelectedRole(role);
                }
            }
        },
        [roleOptions, roleData]
    );

    return (
        <FullScreenDrawer isOpen={open} onClose={() => onClose(memberInvited)} showSupport title="Invite Team Member">
            <Formik
                key={formKey} // Key forces remount when it changes
                initialValues={initialValues}
                validationSchema={validationSchema}
                onSubmit={handleSubmit}
                innerRef={formikRef}
                validateOnChange={true} // Enable validation on change
                validateOnBlur={true} // Enable validation on blur
                validateOnMount={false}
            >
                {(formik: FormikProps<IFormValues>) => {
                    // Get current selected option for display
                    const selectedOption =
                        formik.values.roleId > 0
                            ? roleOptions.find((option) => option.value === formik.values.roleId?.toString())
                            : undefined;

                    return (
                        <Form>
                            <div className="flex-grow px-6 py-6 max-w-[470px] w-full mx-auto">
                                <h1 className="font-[600] text-[24px] leading-[30px] mt-8 mb-12 flex justify-center">
                                    Team members details
                                </h1>
                                <div className="flex flex-col gap-[20px]">
                                    {/* Basic input fields */}
                                    <div className="flex gap-3 items-start w-full">
                                        <LabelInput
                                            formik={formik}
                                            className="w-full"
                                            name="firstName"
                                            type="text"
                                            label="First Name"
                                            useFormik={true}
                                            required
                                            autoComplete="given-name"
                                            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                                                formik.setFieldValue("firstName", e.target.value, true);
                                                formik.setFieldTouched("firstName", true, true);
                                            }}
                                        />

                                        <LabelInput
                                            formik={formik}
                                            className="w-full"
                                            name="lastName"
                                            type="text"
                                            label="Last Name"
                                            useFormik={true}
                                            required
                                            autoComplete="family-name"
                                            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                                                formik.setFieldValue("lastName", e.target.value, true);
                                                formik.setFieldTouched("lastName", true, true);
                                            }}
                                        />
                                    </div>

                                    <LabelInput
                                        formik={formik}
                                        className="w-full"
                                        name="email"
                                        type="email"
                                        label="Email address"
                                        useFormik={true}
                                        required
                                        autoComplete="email"
                                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                                            formik.setFieldValue("email", e.target.value, true);
                                            formik.setFieldTouched("email", true, true);
                                        }}
                                    />

                                    <div className="">
                                        <p className="text-[12px] font-semibold leading-[16px] text-[#151519] mb-2.5">
                                            Phone number (optional)
                                        </p>

                                        <div className="flex gap-2 items-center w-full">
                                            <CountryCode country="NG" className="h-11" />
                                            <LabelInput
                                                formik={formik}
                                                className="w-full h-11"
                                                name="phoneNumber"
                                                type="tel"
                                                pattern="[0-9]*"
                                                inputMode="numeric"
                                                placeholder="Enter phone number (10 digits)"
                                                useFormik={true}
                                                maxLength={10}
                                                autoComplete="tel"
                                                onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                                                    // Only allow digits
                                                    const value = e.target.value.replace(/\D/g, "");
                                                    e.target.value = value;
                                                    formik.setFieldValue("phoneNumber", value, true);
                                                    formik.setFieldTouched("phoneNumber", true, true);
                                                }}
                                                onKeyDown={(e: React.KeyboardEvent<HTMLInputElement>) => {
                                                    // Prevent non-numeric keys
                                                    const isNumericKey = /^\d$/.test(e.key);
                                                    const isControlKey = [
                                                        "Backspace",
                                                        "Delete",
                                                        "ArrowLeft",
                                                        "ArrowRight",
                                                        "Tab",
                                                        "Home",
                                                        "End",
                                                    ].includes(e.key);

                                                    if (!isNumericKey && !isControlKey) {
                                                        e.preventDefault();
                                                    }
                                                }}
                                                onInput={(e: React.FormEvent<HTMLInputElement>) => {
                                                    // Additional validation
                                                    const input = e.target as HTMLInputElement;
                                                    const digitsOnly = input.value.replace(/\D/g, "");

                                                    if (digitsOnly !== input.value) {
                                                        input.value = digitsOnly;
                                                    }

                                                    if (input.value.length > 10) {
                                                        input.value = input.value.slice(0, 10);
                                                    }

                                                    formik.setFieldValue("phoneNumber", input.value, false);
                                                }}
                                            />
                                        </div>
                                    </div>

                                    <div>
                                        {/* Role dropdown */}
                                        <Dropdown
                                            formik={formik}
                                            className=""
                                            label="Assign a role"
                                            name="role"
                                            placeholder="Select a role"
                                            value={selectedOption}
                                            options={roleOptions}
                                            useFormik={false}
                                            onChange={handleRoleSelection}
                                            required
                                            isLoading={roleLoading}
                                            isDisabled={roleLoading}
                                            isClearable={false}
                                        />
                                    </div>
                                </div>
                                {/* Form buttons */}
                                <div className="flex items-center gap-3 ml-auto w-fit pb-4 mt-8">
                                    <Button variant="outline" onClick={() => onClose(false)}>
                                        Cancel
                                    </Button>
                                    <Button
                                        variant="primary"
                                        loading={loading}
                                        onClick={() => {
                                            // Validate form before submission
                                            formik.validateForm().then((errors) => {
                                                if (Object.keys(errors).length === 0) {
                                                    // Handle form based on active tab
                                                    if (activeTab === "pending") {
                                                        // Special handling for pending tab
                                                        const currentValues = formik.values;
                                                        const updatedPayload = {
                                                            corporateId: Number(corporateId),
                                                            firstName: currentValues.firstName || "",
                                                            lastName: currentValues.lastName || "",
                                                            email: currentValues.email || "",
                                                            phoneNumber: currentValues.phoneNumber || "",
                                                            roleId: currentValues.roleId || 0,
                                                        };

                                                        // Dispatch with error handling
                                                        dispatch(inviteTeamMember(updatedPayload))
                                                            .unwrap()
                                                            .then((response) => {
                                                                // For pending tab, show success feedback directly
                                                                // since it bypasses the normal Redux flow
                                                                sendFeedback(
                                                                    response.message ??
                                                                        "Team member invited successfully",
                                                                    "success"
                                                                );
                                                                setMemberInvited(true);
                                                                setIsSideDrawerOpen(true);
                                                                dispatch(clearState("inviteTeamMember"));
                                                            })
                                                            .catch((err) => {
                                                                // Error handling
                                                                if (err?.message) {
                                                                    sendFeedback(err.message, "error");
                                                                } else {
                                                                    sendCatchFeedback(err);
                                                                }

                                                                // Show validation errors
                                                                formik.setTouched({
                                                                    firstName: true,
                                                                    lastName: true,
                                                                    email: true,
                                                                    phoneNumber: true,
                                                                    role: true,
                                                                    roleId: true,
                                                                });

                                                                // Special handling for duplicate email
                                                                if (err?.message?.toLowerCase().includes("email")) {
                                                                    formik.setFieldError(
                                                                        "email",
                                                                        "This email has already been invited"
                                                                    );
                                                                }
                                                            });
                                                    } else {
                                                        // Standard form submission
                                                        formik.submitForm();
                                                    }
                                                } else {
                                                    // Show validation errors
                                                    formik.setTouched({
                                                        firstName: true,
                                                        lastName: true,
                                                        email: true,
                                                        phoneNumber: true,
                                                        role: true,
                                                        roleId: true,
                                                    });
                                                }
                                            });
                                        }}
                                        disabled={
                                            !(
                                                formik.values.firstName &&
                                                formik.values.lastName &&
                                                formik.values.email &&
                                                formik.values.role &&
                                                !loading
                                            )
                                        }
                                        className={`border border-[#5C068C] bg-[#5C068C] text-[14px] text-white leading-[18px] font-[600] px-[14px] py-2 rounded-[8px] ${
                                            !(
                                                formik.values.firstName &&
                                                formik.values.lastName &&
                                                formik.values.email &&
                                                formik.values.role &&
                                                !loading
                                            )
                                                ? "opacity-50 cursor-not-allowed bg-[#F7F7F8] border-[#F7F7F8] text-[#9D9DAC]"
                                                : ""
                                        }`}
                                    >
                                        Continue
                                    </Button>
                                </div>
                            </div>
                        </Form>
                    );
                }}
            </Formik>

            {/* Role permission content drawer */}
            <RolePermissionContent
                data-testid="side-drawer"
                role={selectedRole?.name ?? ""}
                roleData={selectedRole}
                handleCloseDrawer={handleCloseDrawer}
                isOpen={isSideDrawerOpen}
            />
        </FullScreenDrawer>
    );
};

export default InviteTeamMemberModal;
