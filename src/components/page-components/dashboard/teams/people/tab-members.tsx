"use client";

import { useEffect, useState, useRef, useMemo } from "react";
import { ColumnDef, getCoreRowModel, useReactTable } from "@tanstack/react-table";
import { DataTable } from "@/components/common/table/DataTable";
import { columns, ITableMetaWithSetIsOpen } from "./column-data";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { deleteTeamMember, getTeamMembers } from "@/redux/actions/teamMembersActions";
import { TeamMembersData } from "@/redux/types/teamMembers";
import { PermissionData, RoleData } from "@/redux/types/roles";
import { sendCatchFeedback, sendFeedback } from "@/functions/feedback";
import { capitalizeUserName } from "@/functions/stringManipulations";
import EditTeamMemberDrawer from "./modals/edit-team-member-drawer";
import { Button } from "@/components/common/buttonv3";
import { clearDeleteSuccess } from "@/redux/slices/teamMembersSlice";
// RemoveMemberModal is defined in this file

// Using ITableMetaWithSetIsOpen from column-data-optimized.tsx

interface TabMembersProps {
    rolesData?: RoleData[] | null;
    "data-testid"?: string;
    refreshRolesData?: () => void;
    refreshTeamMembersData?: () => void;
    refreshPermissionsData?: () => void;
    permissionsData?: PermissionData[];
    searchTerm?: string;
}

const TabMembers: React.FC<TabMembersProps> = ({
    rolesData: propRolesData,
    refreshRolesData,
    refreshTeamMembersData,
    refreshPermissionsData,
    permissionsData: propPermissionsData,
    searchTerm = "",
}) => {
    const [rowSelection, setRowSelection] = useState({});
    // These state variables are needed for the table meta
    const setIsOpen = () => {}; // Dummy function for compatibility
    const [isRoleEditOpen, setIsRoleEditOpen] = useState(false);
    const setMemberId = () => {}; // Dummy function for compatibility
    const [isRemoveModalOpen, setIsRemoveModalOpen] = useState(false);
    const [memberToRemove, setMemberToRemove] = useState<TeamMembersData | null>(null);
    const [memberToEdit, setMemberToEdit] = useState<TeamMembersData | null>(null);
    const [phoneNumber, setPhoneNumber] = useState("");
    // Dynamic empty table messages based on whether we're searching or not
    const emptyTabledescription = searchTerm
        ? `No team members found matching "${searchTerm}". Try a different search term.`
        : "You have no team members yet. Invite your team members to start using Corporate Internet banking.";
    const emptyTabletitle = searchTerm ? "No search results" : "No team members yet";
    const dispatch = useAppDispatch();

    const {
        data: teamMembersData,
        success,
        error,
        loading,
    } = useAppSelector((state) => state.teamMembers.getTeamMembers);
    const {
        success: deletedMemberSuccess,
        error: deletedMemberError,
        loading: deletedMemberLoading,
    } = useAppSelector((state) => state.teamMembers.deleteTeamMember);

    const handleRemoveMember = () => {
        // Implement your remove logic here
        if (memberToRemove) {
            dispatch(deleteTeamMember({ teamMemberId: memberToRemove.id }));
        }
        if (!deletedMemberLoading) {
            setIsRemoveModalOpen(false);
            setMemberToRemove(null);
        }
    };

    // Handle delete team member success and error
    const deleteSuccessHandledRef = useRef(false);

    useEffect(() => {
        // Only handle success once per delete operation
        if (deletedMemberSuccess && !deletedMemberLoading && !deleteSuccessHandledRef.current) {
            deleteSuccessHandledRef.current = true;

            // Show success message for user actions
            sendFeedback("Team member removed successfully", "success");

            // Dispatch an action to clear the delete success state
            dispatch(clearDeleteSuccess());

            // Refresh team members list
            setTimeout(() => {
                // Set flag to false since this is an automatic refresh after deletion
                shouldShowFeedbackRef.current = false;
                refreshTeamMembers();
            }, 300);
        } else if (deletedMemberError && !deletedMemberLoading) {
            // Use sendCatchFeedback for errors
            sendCatchFeedback(deletedMemberError);

            // Clear the error state
            dispatch(clearDeleteSuccess());
        }

        // Reset the success handled flag when the delete state changes
        if (!deletedMemberSuccess && !deletedMemberLoading) {
            deleteSuccessHandledRef.current = false;
        }
    }, [deletedMemberSuccess, deletedMemberLoading, deletedMemberError, dispatch]);

    // Use roles data from props if available, otherwise get from Redux store
    const { data: reduxRolesData, loading: rolesLoading } = useAppSelector((state) => state.roles.getAllRoles);
    // Use props data if available, otherwise use Redux data
    const rolesData = propRolesData || reduxRolesData;

    // Create a role mapping for efficient lookups
    const roleMap = useMemo(() => {
        const map: Record<number, string> = {};

        if (rolesData && Array.isArray(rolesData)) {
            rolesData.forEach((role) => {
                map[role.id] = role.name;
            });
        }

        return map;
    }, [rolesData]);

    // Filter team members based on search term
    const filteredTeamMembers = useMemo(() => {
        if (!teamMembersData || !Array.isArray(teamMembersData)) {
            return [];
        }

        // If no search term, return all data
        if (!searchTerm) {
            return teamMembersData;
        }

        // Convert search term to lowercase for case-insensitive comparison
        const lowerSearchTerm = searchTerm.toLowerCase();

        // Filter by name or email
        return teamMembersData?.filter((member) => {
            const fullName =
                `${capitalizeUserName(member.firstName)} ${capitalizeUserName(member.lastName)}`.toLowerCase();
            const email = member.email.toLowerCase();

            return fullName.includes(lowerSearchTerm) || email.includes(lowerSearchTerm);
        });
    }, [teamMembersData, searchTerm]);

    const table = useReactTable<TeamMembersData>({
        data: filteredTeamMembers,
        columns: columns as ColumnDef<TeamMembersData, unknown>[],
        getCoreRowModel: getCoreRowModel(),
        onRowSelectionChange: setRowSelection,
        state: {
            rowSelection,
        },
        meta: {
            setIsOpen,
            setMemberId,
            setIsRoleEditOpen,
            setIsRemoveModalOpen,
            setMemberToRemove,
            isRoleEditOpen,
            setMemberToEdit,
            memberToEdit,
            phoneNumber,
            setPhoneNumber,
            // Add role mapping for efficient lookups
            roleMap,
            rolesLoading,
        } as unknown as ITableMetaWithSetIsOpen,
    });

    // Track data fetching state
    const dataFetchedRef = useRef(false);
    const shouldShowFeedbackRef = useRef(false);

    // Function to explicitly refresh data when needed
    const refreshTeamMembers = () => {
        shouldShowFeedbackRef.current = true; // Show feedback on manual refresh

        // Use the prop function if available, otherwise dispatch directly
        if (refreshTeamMembersData) {
            refreshTeamMembersData();
        } else {
            dispatch(getTeamMembers());
        }
    };

    // No need to fetch permissions data anymore
    useEffect(() => {
        // We don't need to fetch team members data here anymore
        // It's now handled by the parent component

        // We don't need to fetch permissions data here anymore
        // It's now handled by the parent component

        // Mark as fetched to prevent future fetches
        dataFetchedRef.current = true;
    }, []);

    // We don't need to reset dataFetchedRef on unmount anymore
    // The parent component now manages the data fetching state

    // Handle success and error states
    const prevLoadingRef = useRef(loading);

    useEffect(() => {
        // Only run this effect when loading transitions from true to false (request completed)
        const loadingComplete = prevLoadingRef.current && !loading;

        if (loadingComplete) {
            if (success) {
                // Only show feedback when explicitly requested (after user action)
                if (shouldShowFeedbackRef.current) {
                    sendFeedback("Team members fetched successfully", "success");
                    shouldShowFeedbackRef.current = false;
                }
            } else if (error) {
                // Always show error feedback
                sendCatchFeedback(error);
                shouldShowFeedbackRef.current = false;
            }
        }

        // Update loading ref for next render
        prevLoadingRef.current = loading;
    }, [loading, success, error]);

    return (
        <main data-testid="members-content" className="">
            <DataTable<TeamMembersData, unknown>
                columns={columns}
                table={table}
                loading={loading}
                emptyTabledescription={emptyTabledescription}
                emptyTabletitle={emptyTabletitle}
            />

            {/* Remove Member Modal */}

            <RemoveMemberModal
                isOpen={isRemoveModalOpen}
                onClose={() => {
                    setIsRemoveModalOpen(false);
                    setMemberToRemove(null);
                }}
                onConfirm={handleRemoveMember}
                memberName={
                    capitalizeUserName(memberToRemove?.firstName || "") +
                    " " +
                    capitalizeUserName(memberToRemove?.lastName || "")
                }
                isLoading={deletedMemberLoading}
            />

            {/* Edit Team Member Drawer - Now at parent level for better performance */}
            <EditTeamMemberDrawer
                handleClose={() => {
                    setIsRoleEditOpen(false);
                    setMemberToEdit(null);
                }}
                refreshTeamMembers={refreshTeamMembers}
                refreshRolesData={refreshRolesData}
                refreshPermissionsData={refreshPermissionsData}
                permissionsData={propPermissionsData}
                meta={
                    {
                        setIsOpen,
                        setMemberId,
                        setIsRoleEditOpen,
                        setIsRemoveModalOpen,
                        setMemberToRemove,
                        isRoleEditOpen,
                        setMemberToEdit,
                        memberToEdit,
                        phoneNumber,
                        setPhoneNumber,
                        roleMap,
                        rolesLoading,
                    } as ITableMetaWithSetIsOpen
                }
            />
        </main>
    );
};

export default TabMembers;

interface IRemoveMemberModalProps {
    isOpen: boolean;
    onClose: () => void;
    onConfirm: () => void;
    memberName: string;
    isLoading?: boolean;
}

const RemoveMemberModal: React.FC<IRemoveMemberModalProps> = ({
    isOpen,
    onClose,
    onConfirm,
    memberName,
    isLoading = false,
}) => {
    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-50">
            <div className="bg-white rounded-xl shadow-lg w-full max-w-[464px] font-primary p-6">
                <div className="flex items-center justify-between mb-4">
                    <h2 className="text-xl font-semibold text-[#151519]">Remove "{memberName}"?</h2>
                    <button onClick={onClose} className="text-[#90909D] hover:text-[#90909D]">
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M15 5L5 15M5 5L15 15"
                                stroke="#90909D"
                                strokeWidth="1.5"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                            />
                        </svg>
                    </button>
                </div>

                <p className="text-[#3A3A41] text-[400] text-[14px] leading-[18px] mb-6">
                    Are you sure you want to remove this team member? They will no longer have access to your
                    organization's account.
                </p>

                <div className="flex justify-end gap-3">
                    <Button onClick={onClose} size="medium" variant="outline">
                        Cancel
                    </Button>

                    <Button
                        variant="destructive"
                        size="medium"
                        onClick={onConfirm}
                        loading={isLoading}
                        disabled={isLoading}
                    >
                        Yes, remove
                    </Button>
                </div>
            </div>
        </div>
    );
};

export { RemoveMemberModal };
