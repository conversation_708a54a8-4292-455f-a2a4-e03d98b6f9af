import { type ColumnDef, TableMeta, Row, Table } from "@tanstack/react-table";
import Badge from "@/components/common/badge";
import Checkbox from "@/components/common/checkbox";
import TableMoreAction from "@/components/common/table/table-more-action";
import { menuItemType } from "@/components/common/table/types";
import { DeleteIcon, DisableIcon, EditIcon } from "@/components/icons/team";
import { getNameInitials, capitalizeUserName } from "@/functions/stringManipulations";
import { SuccessResponse } from "@/redux/types/approvalRule";
import { data } from "../../transactions/data";
import { useAppSelector } from "@/redux/hooks";
import { useMemo } from "react";

export interface IRuleCreator {
    name: string;
    role: string;
}

export type IApprovalRuleRow = {
    original: SuccessResponse;
    getValue: <T extends keyof SuccessResponse>(key: T) => SuccessResponse[T];
    getIsSelected: () => boolean;
    toggleSelected: (value?: boolean) => void;
};

export interface IApprovalRulesTableMeta extends TableMeta<SuccessResponse> {
    setIsDisableRuleModalOpen: (value: boolean) => void;
    setIsDeleteRuleModalOpen: (value: boolean) => void;
    setSelectedRule: (ruleData: SuccessResponse) => void;
    setActionToDo: (action: string) => void;
    setIsRuleEditClicked: (value: boolean) => void;
    setRuleToEdit: (ruleData: SuccessResponse) => void;
    isRuleEditClicked: boolean;
    toggleCreateRuleModal: () => void;
}

// Utility function to render text-based cells
const renderTextCell =
    (key: keyof SuccessResponse) =>
    ({ row }: { row: Row<SuccessResponse> }) => (
        <p className="text-[#3A3A41] text-[0.875rem] font-semibold tracking-[0.0175rem] leading-[1.125rem]">
            {row.getValue(key)}
        </p>
    );

// Component to display creator name
export const CreatedByCell = ({ creatorId }: { creatorId: string }) => {
    // Use a single selector to get both team members and roles data
    // This is more efficient as it reduces the number of subscriptions
    const { teamMembers, roles } = useAppSelector(
        (state) => ({
            teamMembers: state.teamMembers.getTeamMembers.data,
            roles: state.roles.getAllRoles.data,
        }),
        (prev, next) =>
            // Use a custom equality function to prevent unnecessary rerenders
            prev.teamMembers === next.teamMembers && prev.roles === next.roles
    );

    // Combine creator lookup and role name resolution in a single useMemo
    // This is more efficient as it avoids the dependency chain
    const { creator, roleName, fullName, initials } = useMemo(() => {
        // Default values
        let creator = null;
        let roleName = "Unknown Role";
        let fullName = "";
        let initials = getNameInitials(creatorId || "Unknown User");

        // Try to find the creator
        if (teamMembers && Array.isArray(teamMembers) && creatorId) {
            const creatorIdNum = parseInt(creatorId, 10);
            if (!isNaN(creatorIdNum)) {
                creator = teamMembers.find((member) => member.id === creatorIdNum);
            }
        }

        // If creator found, get their name and role
        if (creator) {
            fullName = `${capitalizeUserName(creator.firstName)} ${capitalizeUserName(creator.lastName)}`;
            initials = getNameInitials(fullName);

            // Handle role name
            if (creator.roleId === 0) {
                roleName = "Super Admin";
            } else if (roles && Array.isArray(roles)) {
                const role = roles.find((r) => r.id === creator.roleId);
                if (role) {
                    roleName = role.name;
                }
            }
        }

        return { creator, roleName, fullName, initials };
    }, [teamMembers, roles, creatorId]);

    // Render based on whether creator was found
    if (creator) {
        return (
            <div className="flex items-center gap-2">
                <div className="rounded-full py-3 px-3 h-[3rem] w-[3rem] bg-[#F9F0FE] flex items-center justify-center">
                    <span data-testid="member-initials" className="text-[#3A3A41] text-[16px] font-medium">
                        {initials}
                    </span>
                </div>
                <div className="flex flex-col items-start gap-1">
                    <div className="font-[600] text-[0.875rem] leading-[1.125rem] text-[#151519]">{fullName}</div>
                    <div className="text-[#3A3A41] text-[0.875rem] font-normal tracking-[0.0175rem] leading-[1.125rem]">
                        {roleName}
                    </div>
                </div>
            </div>
        );
    }

    // If creator is not found, display the ID with default initials
    return (
        <div className="flex items-center gap-2">
            <div className="rounded-full py-3 px-3 h-[3rem] w-[3rem] bg-[#F9F0FE] flex items-center justify-center">
                <span data-testid="member-initials" className="text-[#3A3A41] text-[16px] font-medium">
                    {initials}
                </span>
            </div>
            <div className="flex flex-col items-start gap-1">
                <div className="font-[600] text-[0.875rem] leading-[1.125rem] text-[#151519]">
                    {creatorId || "Unknown"}
                </div>
                <div className="text-[#3A3A41] text-[0.875rem] font-normal tracking-[0.0175rem] leading-[1.125rem]">
                    User ID: {creatorId}
                </div>
            </div>
        </div>
    );
};

// Utility function to render the status badge
const renderBadge = ({ row }: { row: Row<SuccessResponse> }) => {
    const status = row.getValue("status");

    return (
        <Badge
            text={status as string}
            color={status === "ACTIVE" ? "success" : "error"}
            size="sm"
            className=" capitalize  "
        />
    );
};

// Utility function to render action buttons
const renderActionCell = ({ row, table }: { row: Row<SuccessResponse>; table: Table<SuccessResponse> }) => {
    const rule = row.original;
    const meta = table.options.meta as IApprovalRulesTableMeta;

    const menuItems: menuItemType<SuccessResponse>[] = [
        {
            label: "Edit rule",
            icon: <EditIcon baseColor="#151519" />,
            onClick: (data) => {
                if (meta.setIsRuleEditClicked && meta.setRuleToEdit) {
                    meta.setIsRuleEditClicked(true);
                    meta.setRuleToEdit(data);
                    meta.toggleCreateRuleModal();
                    // For debugging purposes, you can log the data to the console
                    // This will help you understand what data is being passed
                    // to the setRuleToEdit function
                }
            },
        },
        {
            label: rule.status === "ACTIVE" ? "Disable rule" : "Enable rule",
            style: { color: "#151519", whiteSpace: "nowrap", width: "fit-full" },
            icon: <DisableIcon />,
            onClick: (data) => {
                if (meta.setIsDisableRuleModalOpen && meta.setSelectedRule && meta.setActionToDo) {
                    meta.setIsDisableRuleModalOpen(true);
                    meta.setSelectedRule(data);
                    meta.setActionToDo("disable");
                }
            },
        },
        {
            label: "Delete rule",
            style: { color: "#D92D20", whiteSpace: "nowrap", width: "fit-full" },
            icon: <DeleteIcon />,
            onClick: (data) => {
                if (meta.setIsDeleteRuleModalOpen && meta.setSelectedRule && meta.setActionToDo) {
                    meta.setIsDeleteRuleModalOpen(true);
                    meta.setSelectedRule(data);
                    meta.setActionToDo("delete");
                }
            },
        },
    ];

    return (
        <TableMoreAction
            data={{ ...rule, id: rule.id }}
            menuItems={menuItems}
            // Using proper HTML attributes
            aria-label="More actions"
            data-testid="more-actions"
        />
    );
};

export const columns: ColumnDef<SuccessResponse>[] = [
    {
        id: "select",
        header: ({ table }) => (
            <Checkbox
                checked={table.getIsAllPageRowsSelected()}
                onCheckedChange={() => table.toggleAllPageRowsSelected(!table.getIsAllPageRowsSelected())}
                aria-label="Select all"
                data-testid="checkbox-select-all"
                size="sm"
                indeterminate={table.getIsSomePageRowsSelected()}
            />
        ),
        cell: ({ row }) => (
            <Checkbox
                checked={row.getIsSelected()}
                onCheckedChange={() => row.toggleSelected(!row.getIsSelected())}
                aria-label="Select row"
                data-testid="checkbox-select-row"
                size="sm"
                role="checkbox-row"
            />
        ),
        enableSorting: false,
        enableHiding: false,
    },
    { accessorKey: "name", header: "Rule Name", cell: renderTextCell("name") },
    {
        accessorKey: "createdBy",
        header: "Created By",
        cell: ({ row }) => {
            const creatorId = row.original.createdBy;
            return <CreatedByCell creatorId={creatorId} />;
        },
    },
    { accessorKey: "applicableTo", header: "Approval Type", cell: renderTextCell("applicableTo") },
    { accessorKey: "status", header: "Status", cell: renderBadge },
    { id: "actions", cell: renderActionCell },
];
