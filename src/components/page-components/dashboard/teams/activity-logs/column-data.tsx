import { type ColumnDef, TableMeta, Row } from "@tanstack/react-table";
import { useMemo } from "react";
import Checkbox from "@/components/common/checkbox";

import { getNameInitials, capitalizeUserName } from "@/functions/stringManipulations";
import { useAppSelector } from "@/redux/hooks";
import { ActivityLogsData } from "@/redux/types/activityLogs";
import { TeamMembersData } from "@/redux/types/teamMembers";
import { RoleData } from "@/redux/types/roles";

export interface IActivityLogsTableMeta extends TableMeta<ActivityLogsData> {
    setSelectedLog: (logData: ActivityLogsData) => void;
}

// Utility function to get user display data
const getUserDisplayData = (creator: TeamMembersData | null, roles: RoleData[] | null, performerRole: string) => {
    if (!creator) {
        return { fullName: "", initials: "", roleName: performerRole };
    }

    const fullName = `${capitalizeUserName(creator.firstName)} ${capitalizeUserName(creator.lastName)}`;
    const initials = getNameInitials(fullName);

    let roleName = performerRole; // Default fallback

    if (creator.roleId === 0) {
        roleName = "Super Admin";
    } else if (roles && Array.isArray(roles)) {
        const role = roles.find((r) => r.id === creator.roleId);
        if (role) {
            roleName = role.name;
        }
    }

    return { fullName, initials, roleName };
};

// Utility function to format date - memoized
const formatTimestamp = (timestamp: string): string => {
    const date = new Date(timestamp);
    return (
        date.toLocaleDateString("en-GB", {
            day: "2-digit",
            month: "short",
            year: "numeric",
        }) +
        ", " +
        date.toLocaleTimeString("en-GB", {
            hour: "2-digit",
            minute: "2-digit",
            hour12: false,
        })
    );
};

// Stable cell renderers
const TextCell = ({ row, accessorKey }: { row: Row<ActivityLogsData>; accessorKey: keyof ActivityLogsData }) => (
    <p className="text-[#3A3A41] text-[0.875rem] font-normal tracking-[0.0175rem] leading-[1.125rem]">
        {row.getValue(accessorKey as string)}
    </p>
);

const LocationCell = ({ row }: { row: Row<ActivityLogsData> }) => {
    const location = row.original.location;
    const ipAddress = row.original.ipAddress;
    return (
        <div className="flex flex-col items-start gap-1">
            <div className="font-[600] text-[0.875rem] leading-[1.125rem] text-[#151519]">{location}</div>
            <div className="text-[#3A3A41] text-[0.875rem] font-normal tracking-[0.0175rem] leading-[1.125rem]">
                {ipAddress}
            </div>
        </div>
    );
};

const PerformedByCell = ({ row }: { row: Row<ActivityLogsData> }) => {
    const performedBy = row.original.performedBy;
    const id = typeof performedBy === "object" && performedBy.id ? performedBy.id : null;
    const performerFullName = typeof performedBy === "object" ? performedBy.name : performedBy;
    const performerInitials = useMemo(() => getNameInitials(performerFullName), [performerFullName]);
    const performerRole = row.original.role;

    // Use a single selector to get both team members and roles data
    // This is more efficient as it reduces the number of subscriptions
    const { teamMembers, roles } = useAppSelector(
        (state) => ({
            teamMembers: state.teamMembers.getTeamMembers.data,
            roles: state.roles.getAllRoles.data,
        }),
        (prev, next) =>
            // Use a custom equality function to prevent unnecessary rerenders
            prev.teamMembers === next.teamMembers && prev.roles === next.roles
    );

    // Combine creator lookup and role name resolution in a single useMemo
    // This is more efficient as it avoids the dependency chain
    const { creator, roleName, fullName, initials } = useMemo(() => {
        // Default values
        let creator: TeamMembersData | null = null;

        // Try to find the creator
        if (teamMembers && Array.isArray(teamMembers) && id) {
            const creatorIdNum = parseInt(String(id), 10);
            if (!isNaN(creatorIdNum)) {
                creator = teamMembers.find((member) => member.id === creatorIdNum) || null;
            }
        }

        // Use utility function to get display data
        const { fullName, initials, roleName } = getUserDisplayData(creator, roles, performerRole);

        return { creator, roleName, fullName, initials };
    }, [teamMembers, roles, id, performerRole]);

    // Render based on whether creator was found
    // Determine which data to use based on whether creator exists
    const displayInitials = id && creator ? initials : performerInitials;
    const displayName = id && creator ? fullName : performerFullName;

    return (
        <div className="flex items-center gap-2">
            <div className="rounded-full py-3 px-3 h-[3rem] w-[3rem] bg-[#F9F0FE] flex items-center justify-center">
                <span data-testid="member-initials" className="text-[#3A3A41] text-[16px] font-medium">
                    {displayInitials}
                </span>
            </div>
            <div className="flex flex-col items-start gap-1">
                <div className="font-[600] text-[0.875rem] leading-[1.125rem] text-[#151519]">{displayName}</div>
                <div className="text-[#3A3A41] text-[0.875rem] font-normal tracking-[0.0175rem] leading-[1.125rem]">
                    {roleName}
                </div>
            </div>
        </div>
    );
};

const TimestampCell = ({ row }: { row: Row<ActivityLogsData> }) => {
    const formattedDate = useMemo(() => formatTimestamp(row.original.timestamp), [row.original.timestamp]);

    return (
        <p className="text-[#3A3A41] text-[0.875rem] font-normal tracking-[0.0175rem] leading-[1.125rem]">
            {formattedDate}
        </p>
    );
};

// const ActionCell = ({ row }: { row: Row<IActivityLogsData> }) => {
//     const rule = row.original;

//     return (
//         <TableMoreAction
//             data={{ ...rule, id: rule.id }}
//             menuItems={MENU_ITEMS}
//             aria-label="More actions"
//             data-testid="more-actions"
//         />
//     );
// };

export const columns: ColumnDef<ActivityLogsData>[] = [
    {
        id: "select",
        header: ({ table }) => (
            <Checkbox
                checked={table.getIsAllPageRowsSelected()}
                onCheckedChange={() => table.toggleAllPageRowsSelected(!table.getIsAllPageRowsSelected())}
                aria-label="Select all"
                data-testid="checkbox-select-all"
                size="sm"
                indeterminate={table.getIsSomePageRowsSelected()}
            />
        ),
        cell: ({ row }) => (
            <Checkbox
                checked={row.getIsSelected()}
                onCheckedChange={() => row.toggleSelected(!row.getIsSelected())}
                aria-label="Select row"
                data-testid="checkbox-select-row"
                size="sm"
                role="checkbox-row"
            />
        ),
        enableSorting: false,
        enableHiding: false,
    },
    {
        accessorKey: "performedBy",
        header: "Performed By",
        cell: PerformedByCell,
    },
    {
        accessorKey: "action",
        header: "Action",
        cell: ({ row }) => <TextCell row={row} accessorKey="action" />,
    },
    {
        accessorKey: "timestamp",
        header: "Date",
        cell: TimestampCell,
    },
    {
        accessorKey: "location",
        header: "Location",
        cell: LocationCell,
    },
    // {
    //     id: "actions",
    //     // cell: ActionCell,
    // },
];

// Alternative approach: Create columns with useMemo in the component
export const createOptimizedColumns = (): ColumnDef<ActivityLogsData>[] => columns;
