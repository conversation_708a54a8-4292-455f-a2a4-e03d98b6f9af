import { userAxios } from "@/api/axios";
import { Button } from "@/components/common/buttonv3";
import LabelInput from "@/components/common/label-input";
import { Skeleton } from "@/components/ui/skeleton";
import { sendCatchFeedback, sendFeedback } from "@/functions/feedback";
import { handleError } from "@/lib/utils";
import { validateUserSecurityQuestion } from "@/redux/actions/auth/signinActions";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { signinActions } from "@/redux/slices/auth/signinSlice";
import { useFormik } from "formik";
import { useEffect, useState } from "react";
import * as yup from "yup";

type SecurityQuestionProps = {
    email: string;
    onVerified: (token: string) => void;
};

// Skeleton loader for security questions form
const SecurityQuestionSkeleton = () => (
    <div className="space-y-4 max-w-[50%] mx-auto">
        <div className="text-center mb-[40px]">
            <Skeleton className="h-8 w-64 mx-auto mb-3" />
            <Skeleton className="h-5 w-80 mx-auto" />
        </div>

        <div className="grid gap-8">
            {/* Question label skeleton */}
            <div className="space-y-2">
                <Skeleton className="h-5 w-48" />
                <Skeleton className="h-12 w-full rounded-lg" />
            </div>

            {/* Button skeleton */}
            <Skeleton className="h-12 w-full rounded-lg" />
        </div>
    </div>
);

const SecurityQuestion = ({ email, onVerified }: SecurityQuestionProps) => {
    const [question, setQuestion] = useState("");
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const {
        error: validateUserSecurityQuestionError,
        success: validateUserSecurityQuestionSuccess,
        loading: validateUserSecurityQuestionLoading,
        token,
    } = useAppSelector((state) => state.signin?.validateUserSecurityQuestion || {});

    const dispatch = useAppDispatch();

    const formik = useFormik({
        initialValues: {
            question,
            answer: "",
        },
        enableReinitialize: true,
        onSubmit: () => {
            handleFormSubmit();
        },
        validationSchema: yup.object({
            question: yup.string().required("Question is required"),
            answer: yup.string().required("Answer is required"),
        }),
    });

    const handleFormSubmit = () => {
        dispatch(
            validateUserSecurityQuestion({
                userEmail: email,
                question: question, // Using the state question which now matches formik
                // Normalize answer to lowercase for case-insensitive validation
                answer: formik.values.answer.trim().toLowerCase(),
                // loginFlag: true,
            })
        );
    };

    const getRandomIndex = (length: number): number => Math.floor(Math.random() * length);

    useEffect(() => {
        const fetchQuestions = async () => {
            setLoading(true);
            try {
                const response = await userAxios(`/v1/user-security-questions?email=${email}`);

                // Generate the random index once
                const randomIndex = getRandomIndex(response.data.length);
                const selectedQuestion = response.data[randomIndex]?.question;

                // Use the same question for both state and formik
                setQuestion(selectedQuestion);
                formik.setFieldValue("question", selectedQuestion);

                setError(null);
            } catch (error) {
                console.error("Error fetching question:", error);
                setError(handleError(error));
            } finally {
                setLoading(false);
            }
        };

        fetchQuestions();

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [email]);

    useEffect(() => {
        if (error) {
            sendCatchFeedback(error);
        }

        if (validateUserSecurityQuestionError) {
            sendCatchFeedback(validateUserSecurityQuestionError, () =>
                dispatch(signinActions.clearState("validateUserSecurityQuestion"))
            );
        }

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [error, validateUserSecurityQuestionError]);

    useEffect(() => {
        if (validateUserSecurityQuestionSuccess && token) {
            onVerified(token);
            sendFeedback("Security question validated successfully", "success");
            dispatch(signinActions.clearState("validateUserSecurityQuestion"));
        }

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [validateUserSecurityQuestionSuccess, token]);

    if (loading) {
        return <SecurityQuestionSkeleton />;
    }

    if (!question) {
        return <div>No question available.</div>;
    }

    return (
        <div className="space-y-4 max-w-[50%] mx-auto">
            <div className="text-center mb-[40px]">
                <h2 className="text-2xl font-bold text-black">Let's make sure it's you</h2>
                <p className="text-base font-normal text-subText mt-[12px]">
                    Provide the answer to your security question below
                </p>
            </div>

            <form className="grid gap-8" onSubmit={formik.handleSubmit}>
                <LabelInput
                    name="answer"
                    label={question}
                    placeholder="Enter your answer"
                    formik={formik}
                    masked={true}
                    hint="Answer is case-insensitive"
                />
                <Button
                    loading={validateUserSecurityQuestionLoading}
                    disabled={validateUserSecurityQuestionLoading}
                    type="submit"
                    fullWidth
                    size="lg"
                >
                    Continue
                </Button>
            </form>
        </div>
    );
};

export default SecurityQuestion;
