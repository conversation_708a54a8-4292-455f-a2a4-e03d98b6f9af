"use client";

// LIBRARY IMPORTS
import React, { useEffect, useMemo, useState } from "react";
import { Area, AreaChart, CartesianGrid, ResponsiveContainer, Tooltip, XAxis, YAxis } from "recharts";

// PROJECT IMPORTS
import Dropdown from "@/components/common/dropdown";
import LoadingIndicator from "@/components/common/loading-indicator";
import { dateFilterValues } from "@/components/page-components/dashboard/accounts/data";
import { fDateNumeric } from "@/functions/date";
import { getTransactions } from "@/redux/actions/dashboardActions";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { format } from "date-fns";
import ChartTooltip from "./chart-tooltip";

const AccountChart: React.FC = () => {
    const dispatch = useAppDispatch();
    const { loading, data } = useAppSelector((state) => state.dashboard.getTransactions);
    const { selectedAccount } = useAppSelector((state) => state.account);
    const [dateOption, setDateOption] = useState<{ label: string; value: string }>({
        label: "Last 30 days",
        value: "Last 30 days",
    });

    const dates = useMemo(
        () => ({
            startDate: dateFilterValues.find((item) => item.label === dateOption.label)?.startDate || new Date(),
            endDate: dateFilterValues.find((item) => item.label === dateOption.label)?.endDate || new Date(),
        }),
        [dateOption.label]
    );

    useEffect(() => {
        const getData = async () => {
            await dispatch(
                getTransactions({
                    startDate: fDateNumeric(dates.startDate.toString(), "yyyy-MM-dd"),
                    endDate: fDateNumeric(dates.endDate.toString(), "yyyy-MM-dd"),
                    accountNumber: selectedAccount?.accountNumber,
                })
            );
        };

        if (selectedAccount) getData();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [dates.endDate, dates.startDate, selectedAccount]);

    const chartData = useMemo(() => {
        if (data) {
            return data.map((item) => ({
                name: format(new Date(item.createdDate), "MMM d"),
                value: item.amount,
            }));
        } else return [];
    }, [data]);

    return (
        <div className="bg-[#F9F9FA] pb-[33px] rounded-lg pt-[22px] px-4">
            <div className="w-60  ml-auto mt-1 z-[100px] mb-[15px]">
                <Dropdown
                    name="status"
                    value={dateOption}
                    options={dateFilterValues
                        .filter((item) => item.label !== "Custom date")
                        .map((item) => ({
                            label: item.label,
                            value: item.label,
                        }))}
                    useFormik={false}
                    onChange={(value) => {
                        setDateOption(value as { label: string; value: string });
                    }}
                    isLoading={loading}
                />
            </div>

            {loading ? (
                <LoadingIndicator />
            ) : (
                <ResponsiveContainer height={319} width="100%">
                    <AreaChart data={chartData.length > 0 ? chartData : [{ name: "", value: 0 }]}>
                        <defs>
                            <linearGradient id="colorUv" x1="0" y1="0" x2="0" y2="1">
                                <stop offset="100%" stopColor="#494384" stopOpacity={1} />
                            </linearGradient>
                        </defs>
                        <XAxis dataKey="name" tickLine={false} stroke="#3A3A41" tick={{ fontSize: 12 }} dy={5} />
                        <YAxis
                            tickLine={false}
                            axisLine={false}
                            tickFormatter={(value) =>
                                value === 0
                                    ? ""
                                    : `₦${new Intl.NumberFormat("en-US", {
                                          notation: "compact",
                                          compactDisplay: "short",
                                      }).format(value)}`
                            }
                            stroke="#3A3A41"
                            tick={{ fontSize: 12 }}
                        />
                        <CartesianGrid vertical={false} stroke="#EBEAEB" />
                        <Tooltip content={<ChartTooltip />} />
                        <Area type="monotone" dataKey="value" fillOpacity={1} fill="url(#colorUv)" stroke="" />
                    </AreaChart>
                </ResponsiveContainer>
            )}
        </div>
    );
};

export default AccountChart;
