"use client";
import { Button } from "@/components/common/buttonv3";
import CustomModal from "@/components/common/custom-modal";
import DatePicker from "@/components/common/date-picker";
import Dropdown from "@/components/common/dropdown";
import LabelInput from "@/components/common/label-input";
import { sendFeedback } from "@/functions/feedback";
import { getAccountStatement } from "@/redux/actions/dashboardActions";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { FormEvent, useEffect, useState } from "react";
import { dateFilterValues } from "./data";

const DownloadStatementModal = ({ onClose, open }: { open: boolean; onClose: () => void }) => {
    const dispatch = useAppDispatch();
    const { loading, success } = useAppSelector((state) => state.dashboard.getAccountStatement);
    const [emailAddress, setEmailAddress] = useState<string>("");
    const selectedAccount = useAppSelector((state) => state.account?.selectedAccount);

    const [dateOption, setDateOption] = useState<{ label: string; value: string }>({
        label: "Custom date",
        value: "Custom date",
    });
    const [dates, setDates] = useState<{ startDate: Date; endDate: Date }>({
        startDate: dateFilterValues.find((item) => item.label === dateOption.label)?.startDate ?? new Date(),
        endDate: dateFilterValues.find((item) => item.label === dateOption.label)?.endDate ?? new Date(),
    });

    useEffect(() => {
        const selectedOption = dateFilterValues.find((item) => item.label === dateOption.label);

        if (selectedOption && selectedOption.startDate && selectedOption.endDate) {
            setDates({
                startDate: selectedOption.startDate,
                endDate: selectedOption.endDate,
            });
        }
    }, [dateOption]);

    useEffect(() => {
        if (success) {
            sendFeedback("Account statement request submitted successfully", "success");
            onClose();
        }
    }, [onClose, success]);

    const requestStatement = async (e: FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        await dispatch(
            getAccountStatement({
                startDate: new Date(dates.startDate).toISOString(),
                endDate: new Date(dates.endDate).toISOString(),
                accountNumber: selectedAccount?.accountNumber || "",
                emailAddress,
            })
        );
    };
    return (
        <CustomModal isOpen={open} onRequestClose={onClose} title="Get account statement" width="522px">
            <form onSubmit={(e) => requestStatement(e)}>
                <div className="text-[14px]">
                    <div className="flex flex-col justify-between h-full w-full">
                        <div className="grid gap-[20px]">
                            <div>
                                <Dropdown
                                    label="Date"
                                    name="date"
                                    value={dateOption}
                                    options={dateFilterValues.map((item) => ({
                                        label: item.label,
                                        value: item.label,
                                    }))}
                                    useFormik={false}
                                    onChange={(value) => {
                                        setDateOption(value as { label: string; value: string });
                                    }}
                                />
                            </div>
                            <div className="flex flex-col gap-[20px] md:flex-row">
                                <DatePicker
                                    data-testid="start-date"
                                    label="Start date"
                                    value={dates.startDate}
                                    onChange={(value) => {
                                        if (dateOption.value === "Custom date" && value) {
                                            setDates((prev) => ({ ...prev, startDate: value }));
                                        }
                                    }}
                                    disable={dateOption.value !== "Custom date"}
                                />
                                <DatePicker
                                    label="End date"
                                    position="bottom right"
                                    value={dates.endDate}
                                    onChange={(value) => {
                                        if (dateOption.value === "Custom date" && value) {
                                            setDates((prev) => ({ ...prev, endDate: value }));
                                        }
                                    }}
                                    disable={dateOption.value !== "Custom date"}
                                />
                            </div>
                            <div>
                                <LabelInput
                                    label="Email address"
                                    name="emailAddress"
                                    value={emailAddress}
                                    onChange={(e) => setEmailAddress((e.target as HTMLInputElement).value)}
                                    useFormik={false}
                                    data-testid="email-address"
                                />
                            </div>
                        </div>
                    </div>
                </div>
                <div className="flex justify-end items-center gap-3 mt-6">
                    <Button type="button" variant="outline" onClick={onClose}>
                        Cancel
                    </Button>
                    <Button type="submit" loading={loading} disabled={loading || !emailAddress}>
                        Get Statement
                    </Button>
                </div>
            </form>
        </CustomModal>
    );
};

export default DownloadStatementModal;
