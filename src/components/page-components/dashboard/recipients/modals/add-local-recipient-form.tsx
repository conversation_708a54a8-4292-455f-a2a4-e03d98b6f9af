"use client";

import { But<PERSON> } from "@/components/common/buttonv3";
import CountryCurrencyDropdown from "@/components/common/country-dropdown";
import Dropdown from "@/components/common/dropdown";
import LabelInput from "@/components/common/label-input";
import { sendCatchFeedback, sendFeedback } from "@/functions/feedback";
import {
    addLocalRecipient,
    fetchRecipientDetails,
    getLocalBanksAction,
} from "@/redux/actions/recipients/local-recipient";
import { recipientActions } from "@/redux/slices/recipientsSlice";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { useFormik } from "formik";
import { useEffect, useState } from "react";
import * as yup from "yup";
import { AlertCircle, Loader2 } from "lucide-react";

interface AddLocalRecipientFormProps {
    closeModal: () => void;
}

const AddLocalRecipientForm = ({ closeModal }: AddLocalRecipientFormProps) => {
    const dispatch = useAppDispatch();

    // Redux state
    const { loading, success } = useAppSelector((state) => state.recipient.addLocalRecipient || {});
    const { banks, loading: fetchingBanks } = useAppSelector((state) => state.recipient.getLocalBanks || {});
    const {
        details,
        loading: fetchingDetails,
        error: fetchDetailsError,
    } = useAppSelector((state) => state.recipient.getRecipientDetails || {});

    // Local state for account verification
    const [accountVerified, setAccountVerified] = useState(false);
    const [accountVerifiedError, setAccountVerifiedError] = useState(false);
    const [shouldVerify, setShouldVerify] = useState(false);

    // Format banks for dropdown
    const allBanks = banks?.map((bank) => ({ label: bank.bankName, value: bank.bankCode }));

    // Initialize form with Formik
    const formik = useFormik({
        initialValues: {
            accountNumber: "",
            accountName: "",
            bank: "",
            currencyCode: "",
        },
        onSubmit: (values) => {
            // Store form values in localStorage before submitting
            localStorage.setItem("localRecipientFormValues", JSON.stringify({ name: values.accountName }));
            submitValues(values);
        },
        validationSchema: yup.object({
            accountNumber: yup
                .string()
                .matches(/^\d+$/, "Account number must contain only numbers")
                .matches(/^\d{10}$/, "Account number must be exactly 10 digits")
                .required("Account number is required"),
            accountName: yup.string().required(""),
            bank: yup.string().required("Bank is required"),
            currencyCode: yup.string().required("Currency is required"),
        }),
        enableReinitialize: true,
    });

    // Submit form values
    const submitValues = async (values: {
        accountNumber: string;
        accountName: string;
        bank: string;
        currencyCode: string;
    }) => {
        if (!accountVerified) {
            sendFeedback("Please complete all fields to verify account details", "error");
            return;
        }

        const selectedBank = banks?.find((bank) => `${bank.bankCode}` === `${values.bank}`);

        await dispatch(
            addLocalRecipient({
                accountNumber: values.accountNumber,
                accountName: values.accountName,
                bank: selectedBank?.bankName as string,
                currencyCode: values.currencyCode,
            })
        );
    };

    // Verify account details when all required fields are provided
    const verifyAccountDetails = async () => {
        if (
            !formik.values.bank ||
            !formik.values.accountNumber ||
            formik.values.accountNumber.length !== 10 ||
            !formik.values.currencyCode
        ) {
            return;
        }

        setAccountVerified(false);

        try {
            await dispatch(
                fetchRecipientDetails({
                    destinationInstitutionCode: formik.values.bank,
                    accountNumber: formik.values.accountNumber,
                    channelCode: "1",
                })
            );
        } catch (error) {
            console.error("Error verifying account:", error);
        } finally {
            setShouldVerify(false);
        }
    };

    const handleCloseModal = () => {
        closeModal();
        formik.resetForm();
        setAccountVerified(false);
        localStorage.removeItem("localRecipientFormValues");
        dispatch(recipientActions.clearState("getRecipientDetails"));
        dispatch(recipientActions.clearState("addLocalRecipient"));
    };

    // Handle successful recipient addition
    useEffect(() => {
        if (success) {
            sendFeedback("Local recipient added successfully", "success");
            formik.resetForm();
            setAccountVerified(false);
            handleCloseModal();
        }
    }, [success, formik, closeModal]);

    // Fetch banks on component mount
    useEffect(() => {
        dispatch(getLocalBanksAction());

        // Clean up recipient details state when component unmounts
        return () => {
            dispatch(recipientActions.clearState("getRecipientDetails"));
        };
    }, [dispatch]);

    // Update account name when details are fetched
    useEffect(() => {
        if (details && details.accountName) {
            formik.setFieldValue("accountName", details.accountName);
            setAccountVerified(true);
            sendFeedback("Account details verified successfully", "success");
        }
    }, [details]);

    // Handle fetch details error
    useEffect(() => {
        if (fetchDetailsError) {
            sendCatchFeedback(fetchDetailsError, () => {
                dispatch(recipientActions.clearState("getRecipientDetails"));
            });
            formik.setFieldValue("accountName", "");
            setAccountVerified(false);
            setAccountVerifiedError(true);
        }
    }, [fetchDetailsError, dispatch]);

    // Reset error state when verification is attempted again
    useEffect(() => {
        if (shouldVerify) {
            setAccountVerifiedError(false);
        }
    }, [shouldVerify]);

    // Trigger verification when all required fields are filled
    useEffect(() => {
        if (
            formik.values.bank &&
            formik.values.accountNumber &&
            formik.values.accountNumber.length === 10 &&
            formik.values.currencyCode &&
            !accountVerified
        ) {
            setShouldVerify(true);
        } else if (
            !formik.values.bank ||
            !formik.values.accountNumber ||
            formik.values.accountNumber.length !== 10 ||
            !formik.values.currencyCode
        ) {
            setAccountVerified(false);
            formik.setFieldValue("accountName", "");
        }
    }, [formik.values.bank, formik.values.accountNumber, formik.values.currencyCode, accountVerified]);

    // Verify account with a slight delay to avoid excessive API calls
    useEffect(() => {
        if (shouldVerify) {
            const timer = setTimeout(() => {
                verifyAccountDetails();
            }, 500);

            return () => clearTimeout(timer);
        }
    }, [shouldVerify]);

    return (
        <div className="flex-col flex items-center w-full">
            <div className="flex flex-col w-full max-w-[470px] items-center">
                <h3 className="font-semibold text-black text-2xl text-center" data-testid="heading">
                    Add a new recipient
                </h3>
            </div>
            <form onSubmit={formik.handleSubmit} className="w-full max-w-[470px] mt-12" data-testid="add-local-form">
                <CountryCurrencyDropdown
                    name="currencyCode"
                    label="Recipient country"
                    formik={formik}
                    className="mb-8"
                />

                <LabelInput
                    formik={formik}
                    name="accountNumber"
                    label="Account number"
                    className="mb-5"
                    maxLength={10}
                />

                <Dropdown
                    options={allBanks}
                    name="bank"
                    label="Bank"
                    size="sm"
                    className="mb-5"
                    formik={formik}
                    isLoading={fetchingBanks}
                    loadingMessage={() => <span>Loading...</span>}
                />

                <div className="relative mb-5">
                    <LabelInput
                        formik={formik}
                        name="accountName"
                        label="Recipient name"
                        disabled={true}
                        readOnly={true}
                        locked
                        placeholder={
                            fetchingDetails
                                ? "Verifying account..."
                                : "Complete all fields above to fetch recipient name"
                        }
                    />
                    {fetchingDetails && (
                        <div className="absolute right-9 top-[55%]">
                            <Loader2 className="h-4 w-4 animate-spin text-primary" />
                        </div>
                    )}
                    {accountVerified && (
                        <div className="absolute right-9 top-[55%]">
                            <svg
                                width="16"
                                height="16"
                                viewBox="0 0 16 16"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                                className="text-primary"
                            >
                                <path
                                    d="M8 0C3.6 0 0 3.6 0 8C0 12.4 3.6 16 8 16C12.4 16 16 12.4 16 8C16 3.6 12.4 0 8 0ZM7 11.4L3.6 8L5 6.6L7 8.6L11 4.6L12.4 6L7 11.4Z"
                                    fill="currentColor"
                                />
                            </svg>
                        </div>
                    )}

                    {accountVerifiedError && (
                        <div className="absolute right-9 top-[55%]">
                            <AlertCircle className="h-5 w-5 text-red-500" />
                        </div>
                    )}
                </div>

                <div className="flex items-center justify-end w-full gap-3 mt-8">
                    <Button type="button" disabled={loading} onClick={handleCloseModal} variant="outline">
                        Go back
                    </Button>
                    <Button type="submit" loading={loading} disabled={!accountVerified || loading}>
                        Add recipient
                    </Button>
                </div>
            </form>
        </div>
    );
};

export default AddLocalRecipientForm;
