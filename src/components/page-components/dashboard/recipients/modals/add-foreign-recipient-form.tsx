"use client";

import { Button } from "@/components/common/buttonv3";
import CountryCurrencyDropdown from "@/components/common/country-dropdown";
import Dropdown from "@/components/common/dropdown";
import LabelInput from "@/components/common/label-input";
import { sendFeedback } from "@/functions/feedback";
import { addForeignRecipient } from "@/redux/actions/recipients/foreign-recipient";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { useFormik } from "formik";
import Image from "next/image";
import { useEffect, useMemo } from "react";
import * as yup from "yup";

const AddForeignRecipientForm = ({ closeModal }: { closeModal: () => void }) => {
    const { loading, success } = useAppSelector((state) => state.recipient.addForeignRecipient || {});
    const dispatch = useAppDispatch();
    const { countries } = useAppSelector((state) => state.countries);

    const formik = useFormik({
        initialValues: useMemo(
            () => ({
                accountNumber: "",
                accountName: "",
                bank: "",
                currencyCode: "",
                country: "",
                city: "",
                address: "",
                swiftCode: "",
                bankCode: "",
                bankAddress: "",
            }),
            []
        ),
        onSubmit: (values) => {
            localStorage.setItem("foreignRecipientFormValues", JSON.stringify({ name: values.accountName }));
            submitValues();
        },
        validationSchema: yup.object({
            accountName: yup.string().required("Account name required"),
            bank: yup.string().required("Bank is required"),
            country: yup.string().required("Country is required"),
            city: yup.string().required("City is required"),
            address: yup.string().required("Address is required"),
            swiftCode: yup.string().required("Swift code is required"),
            bankCode: yup.string().required("Bank code is required"),
            bankAddress: yup.string().required("Bank address is required"),
            accountNumber: yup
                .string()
                .matches(/^\d+$/, "Account number must contain only numbers")
                .matches(/^\d{10}$/, "Account number must be exactly 10 digits")
                .required("Account number is required"),
            currencyCode: yup.string().required("Recipient country is required"),
        }),
    });

    const submitValues = async () => {
        await dispatch(
            addForeignRecipient({
                accountNumber: formik.values.accountNumber,
                accountName: formik.values.accountName,
                bank: formik.values.bank,
                currencyCode: formik.values.currencyCode,
                country: formik.values.country,
                city: formik.values.city,
                address: formik.values.address,
                swiftCode: formik.values.swiftCode,
                bankCode: formik.values.bankCode,
                bankAddress: formik.values.bankAddress,
            })
        );
    };

    useEffect(() => {
        if (success) {
            sendFeedback("Foreign recipient added", "success");
            formik.resetForm();
        }
    }, [formik, success]);

    return (
        <div className="flex-col flex items-center w-full">
            <div className="flex flex-col w-full max-w-[470px] items-center">
                <h3 className="font-semibold text-black text-2xl text-center" data-testid="heading">
                    Recipient information
                </h3>
            </div>
            <form onSubmit={formik.handleSubmit} className="w-full max-w-[470px] mt-12">
                <CountryCurrencyDropdown
                    name="currencyCode"
                    label="Recipient country"
                    formik={formik}
                    className="mb-8"
                />

                <LabelInput formik={formik} name="accountNumber" label="Account number" className="mb-5" />
                <Dropdown
                    options={[{ label: "Bank of America", value: "Bank of America" }]}
                    name="bank"
                    label="Bank"
                    size="sm"
                    className="mb-5"
                    formik={formik}
                />
                <LabelInput formik={formik} name="accountName" label="Recipient full name" className="mb-5" />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-5">
                    <Dropdown
                        options={
                            countries?.map((country) => ({
                                label: (
                                    <div className="flex items-center gap-2">
                                        <Image
                                            src={country.flags?.svg || country.flags?.png || country.flags?.alt}
                                            alt="country flag"
                                            width={28}
                                            height={28}
                                            className="w-5 h-5 object-cover rounded-full"
                                        />
                                        <span className="font-medium text-black">{country.name.common}</span>
                                    </div>
                                ),
                                value: country.name.common,
                            })) || []
                        }
                        name="country"
                        label="Country"
                        size="sm"
                        formik={formik}
                    />
                    <Dropdown
                        options={[{ label: "Los Angeles", value: "Los Angeles" }]}
                        name="city"
                        label="City"
                        size="sm"
                        formik={formik}
                    />
                </div>
                <LabelInput formik={formik} name="address" label="Recipient address" className="mb-5" />
                <LabelInput formik={formik} name="swiftCode" label="Bank SWIFT ID/BIC" className="mb-5" />
                <LabelInput formik={formik} name="bankCode" label="Bank code" className="mb-5" />
                <LabelInput formik={formik} name="bankAddress" label="Bank address" className="mb-8" />

                <div className="flex items-center justify-end w-full gap-3">
                    <Button type="button" disabled={loading} onClick={closeModal} variant="outline">
                        Go back
                    </Button>
                    <Button type="submit" loading={loading} disabled={!formik.isValid || loading}>
                        Add recipient
                    </Button>
                </div>
            </form>
        </div>
    );
};

export default AddForeignRecipientForm;
