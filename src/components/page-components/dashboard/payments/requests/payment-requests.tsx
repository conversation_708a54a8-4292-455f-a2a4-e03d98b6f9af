"use client";

import React, { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import {
    ColumnFiltersState,
    getCoreRowModel,
    getFilteredRowModel,
    getSortedRowModel,
    SortingState,
    useReactTable,
} from "@tanstack/react-table";
import { DataTable } from "@/components/common/table/DataTable";
import { useSearchParams, useRouter, usePathname } from "next/navigation";
import { Pagination } from "@/components/common/pagination";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { PaymentRequestColumns } from "@/components/page-components/dashboard/payments/requests/payment-request-column-data";
import dynamic from "next/dynamic";
import { getPaymentRequest } from "@/redux/actions/paymentRequestActions";
import { paymentRequestActions } from "@/redux/slices/payments/requestSlice";
import { IPaymentRequest } from "@/redux/types/payment/request";

interface ITransactionFilter {
    size?: number | null;
    page?: number | null;
}

const PaymentRequestDetails = dynamic(
    () => import("./payment-request-details").then((mod) => mod.PaymentRequestDetails),
    {
        ssr: false,
    }
);

const RequestReview = dynamic(() => import("./request-review"), {
    ssr: false,
});

const DeclineRequest = dynamic(() => import("./decline-request"), {
    ssr: false,
});

const PaymentRequests = () => {
    const { t } = useTranslation();
    const params = useSearchParams();
    const router = useRouter();
    const pathname = usePathname();
    const [isOpen, setIsOpen] = useState<boolean>(false);
    const [sorting, setSorting] = useState<SortingState>([]);
    const [rowSelection, setRowSelection] = useState({});
    const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
    const [requestId, setRequestId] = useState<number | null>(null);
    const [requestApprovalModal, setRequestApprovalModal] = useState<boolean>(false);
    const [declineModal, setDeclineModal] = useState<boolean>(false);

    const dispatch = useAppDispatch();
    const { list, requests, approveOrDecline } = useAppSelector((state) => state.paymentRequest);
    const data: IPaymentRequest[] = requests;
    const { loading, count, isAvailable } = list;

    const [currentFilters, setCurrentFilters] = useState<ITransactionFilter>({
        size: Number(params.get("size")) || list.size,
        page: Number(params.get("page")) || list.currentPage,
    });

    const createQueryString = useCallback((filters: ITransactionFilter) => {
        const newParams = new URLSearchParams();
        Object.entries(filters).forEach(([key, value]) => {
            if (value) {
                newParams.set(key, value);
            }
        });
        return newParams.toString();
    }, []);

    const updateFilters = useCallback(
        async (newFilters: ITransactionFilter) => {
            setCurrentFilters(newFilters);
            const queryString = createQueryString(newFilters);
            router.push(pathname + (queryString ? "?" + queryString : ""));
        },
        [createQueryString, pathname, router]
    );

    async function onChangePage(newPage: number) {
        await updateFilters({
            ...currentFilters,
            page: newPage,
        });
    }

    async function onChangeRowsPerPage(value: number) {
        await updateFilters({
            ...currentFilters,
            size: value,
        });
    }

    const onGetPaymentRequest = useCallback(async () => {
        const { page, size } = currentFilters;
        const pageNo = page ? Math.max(0, page - 1) : list.currentPage - 1;
        dispatch(
            getPaymentRequest({
                pageNo,
                pageSize: size ?? list.size,
            })
        );

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [currentFilters, getPaymentRequest, list.currentPage, list.size]);

    const handleCloseDetails = () => {
        setIsOpen(false);
        setRequestId(null);
    };

    const handleApproveRequest = (requestId: number) => {
        setRequestApprovalModal(true);
        setIsOpen(false);
        setRequestId(requestId);
    };

    const handleDeclineRequest = (requestId: number) => {
        setDeclineModal(true);
        setIsOpen(false);
        setRequestId(requestId);
    };

    const handleCloseApprovalModal = () => {
        setRequestApprovalModal(false);
        setRequestId(null);
        dispatch(paymentRequestActions.clearState("approveOrDecline"));
    };

    const handleCloseDeclineModal = () => {
        setDeclineModal(false);
        handleCloseApprovalModal();
        setRequestId(null);
    };

    useEffect(() => {
        onGetPaymentRequest();
        dispatch(paymentRequestActions.clearState("approveOrDecline"));

        // eslint-disable-next-line
    }, []);

    useEffect(() => {
        if (isAvailable) {
            onGetPaymentRequest();
        }

        // eslint-disable-next-line
    }, [currentFilters.size, currentFilters.page]);

    useEffect(() => {
        if (approveOrDecline.success) {
            onGetPaymentRequest();
        }

        // eslint-disable-next-line
    }, [approveOrDecline.success]);

    const table = useReactTable({
        data,
        columns: PaymentRequestColumns,
        getCoreRowModel: getCoreRowModel(),
        onSortingChange: setSorting,
        getSortedRowModel: getSortedRowModel(),
        onRowSelectionChange: setRowSelection,
        onColumnFiltersChange: setColumnFilters,
        getFilteredRowModel: getFilteredRowModel(),
        getRowId: (row) => row.id.toString(), // Add explicit row ID function
        state: {
            sorting,
            rowSelection,
            columnFilters,
        },
        meta: {
            setIsOpen: (value: boolean) => setIsOpen(value),
            setRequestApprovalModal: (value: boolean) => setRequestApprovalModal(value),
            setDeclineModal: (value: boolean) => setDeclineModal(value),
            setRequestId: (value: number) => setRequestId(value),
        },
    });

    return (
        <>
            <div>
                <div className="flex items-center bg-white flex-wrap gap-[26px] pt-8 pb-6">
                    <h1 className="font-bold text-xl text-[#2E335B]">{t("Payment requests")}</h1>
                </div>
            </div>

            <div className="!mt-8 flex-1 flex flex-col justify-between">
                <DataTable
                    columns={PaymentRequestColumns}
                    table={table}
                    loading={loading}
                    emptyTabletitle="No payment requests yet"
                    emptyTabledescription="When you have payments that require your approval, they’ll show up there."
                    width="1400px"
                />
                <div className="absolute inset-0 top-auto bottom-5">
                    <Pagination
                        totalItems={count}
                        onPageChange={(page) => onChangePage(page)}
                        onItemsPerPageChange={(count) => onChangeRowsPerPage(count)}
                        initialItemsPerPage={currentFilters.size || list.size}
                        initialPage={currentFilters.page || list.currentPage}
                    />
                </div>
            </div>

            <PaymentRequestDetails
                requestId={requestId as number}
                handleCloseDetails={handleCloseDetails}
                isOpen={isOpen}
                handleApproveRequest={handleApproveRequest}
                handleDeclineRequest={handleDeclineRequest}
            />

            <RequestReview
                isOpen={requestApprovalModal}
                onClose={handleCloseApprovalModal}
                requestId={requestId as number}
                handleDeclineRequest={handleDeclineRequest}
            />

            <DeclineRequest
                isOpen={declineModal}
                closeModal={handleCloseDeclineModal}
                requestId={requestId as number}
            />
        </>
    );
};

export default PaymentRequests;
