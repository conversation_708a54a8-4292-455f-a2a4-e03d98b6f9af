import { CopyIcon } from "@/components/icons/auth";
import { convertCamelCaseToWords, copyToClipboard, formatNumberToNaira } from "@/functions/stringManipulations";
import { Button } from "@/components/common/buttonv3";
import { ChevronRight } from "lucide-react";
import { IPaymentRequest } from "@/redux/types/payment/request";
import { useRouter } from "next/navigation";
import { PATH_DASHBOARD, PATH_PROTECTED } from "@/routes/path";

type TabDetailsProps = {
    request: IPaymentRequest;
};

export default function TabDetails({ request }: Readonly<TabDetailsProps>) {
    const { createdDate, id, corporateId, requestId, numberOfApprovalsGotten, requiredNumberOfApprovals, ...others } =
        request;
    const router = useRouter();
    const transactionDetailsList = Object.entries(others).map(([item, detail]) =>
        item === "amount"
            ? {
                  item,
                  detail: formatNumberToNaira(detail as number),
              }
            : {
                  item,
                  detail,
              }
    );

    return (
        <div className="h-full flex flex-col justify-between">
            <div className="flex flex-col gap-8">
                {transactionDetailsList.map(({ item, detail }) => (
                    <div key={item} className="flex items-center justify-between text-sm leading-[18px]">
                        <p className="font-normal text-subText">{convertCamelCaseToWords(item)}</p>
                        <span className="flex items-center gap-[6px]">
                            <p className="font-medium text-sm leading-[18px] text-black">{detail || "--"}</p>
                            {item === "reference" ? (
                                <button
                                    aria-label="Copy authenticator code"
                                    onClick={async () => await copyToClipboard(detail as string)}
                                    data-testid="copy-authenticator"
                                >
                                    <CopyIcon />
                                </button>
                            ) : null}
                        </span>
                    </div>
                ))}
            </div>

            <div className="bg-[#F9F9FA] rounded-lg p-4">
                <div className="mb-3">
                    <p className="font-medium text-sm leading-[18px] text-black">
                        <span>{request.numberOfApprovalsGotten}</span> of{" "}
                        <span>{request.requiredNumberOfApprovals}</span> approvals granted
                    </p>
                    <p className="font-normal text-sm leading-[18px] text-subText">
                        This payment will be sent once all approvals are completed.
                    </p>
                </div>

                <div>
                    <Button
                        variant="text-primary"
                        size="sm"
                        rightIcon={<ChevronRight className="h-4 w-4" />}
                        onClick={() => {
                            // Update the current URL to switch to the "People involved" tab (index 1)
                            const currentParams = new URLSearchParams(window.location.search);
                            currentParams.set("tab", "1");
                            router.push(`${window.location.pathname}?${currentParams.toString()}`);
                        }}
                    >
                        See approvers
                    </Button>
                </div>
            </div>
        </div>
    );
}
