import Checkbox from "@/components/common/checkbox";
import { ColumnDef, TableMeta } from "@tanstack/react-table";
import { ArrowDownIcon } from "@/components/icons/table";
import TableMoreAction from "@/components/common/table/table-more-action";
import { formatDate } from "@/functions/date";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/common/Avatar";
import { getNameInitials, formatNumberToNaira } from "@/functions/stringManipulations";
import { IPaymentRequest } from "@/redux/types/payment/request";
import { Check, Eye } from "lucide-react";
import { RemoveCircle } from "@/components/icons/paymnet-request";

interface TableMetaWithSetIsOpen extends TableMeta<IPaymentRequest> {
    setIsOpen: (value: boolean) => void;
    setRequestApprovalModal: (value: boolean) => void;
    setDeclineModal: (value: boolean) => void;
    setRequestId: (value: number) => void;
}

export const PaymentRequestColumns: ColumnDef<IPaymentRequest>[] = [
    {
        id: "select",
        header: ({ table }) => (
            <Checkbox
                checked={table.getIsAllPageRowsSelected()}
                onCheckedChange={(checked) => table.toggleAllPageRowsSelected(checked)}
                aria-label="Select all"
                size="sm"
                indeterminate={table.getIsSomePageRowsSelected()}
            />
        ),
        cell: ({ row }) => (
            <Checkbox
                checked={row.getIsSelected()}
                onCheckedChange={(checked) => row.toggleSelected(checked)}
                aria-label="Select row"
                size="sm"
            />
        ),
        enableSorting: false,
        enableHiding: false,
    },
    {
        accessorKey: "createdDate",
        header: ({ column }) => (
            <button
                className="flex items-center gap-1"
                onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            >
                <span>Date</span>
                <ArrowDownIcon />
            </button>
        ),
        cell: ({ row }) => {
            const payment = row.original;

            return <span className="font-semibold text-black">{formatDate(payment.createdDate)}</span>;
        },
    },
    {
        accessorKey: "initiator",
        header: "Initiator",
        cell: ({ row }) => {
            const payment = row.original;

            return (
                <div className="flex items-center gap-2">
                    <div>
                        <Avatar className="w-[32px] h-[32px] static z-0">
                            <AvatarImage src="" alt="" />
                            <AvatarFallback className="text-xs leading-[26px] font-medium text-subText">
                                {getNameInitials(payment.createdBy)}
                            </AvatarFallback>
                        </Avatar>
                    </div>
                    <p className="text-sm leading-[18px] font-medium text-subText capitalize">{payment.createdBy}</p>
                </div>
            );
        },
    },
    {
        accessorKey: "approvals",
        header: "Approvals",
        cell: ({ row }) => (
            <div className="font-medium text-black">
                {row.original.numberOfApprovalsGotten}/{row.original.requiredNumberOfApprovals}
            </div>
        ),
    },
    {
        accessorKey: "counterparty",
        header: "Counterparty",
        cell: ({ row }) => {
            const payment = row.original;

            return (
                <div className="flex items-center gap-2">
                    <div>
                        <Avatar className="w-[32px] h-[32px]  static z-0">
                            <AvatarImage src="" alt="" />
                            <AvatarFallback className="text-xs leading-[26px] font-medium text-subText">
                                {getNameInitials(payment.counterpartyTitle)}
                            </AvatarFallback>
                        </Avatar>
                    </div>
                    <div>
                        <h4 className="font-medium text-black">{row.original.counterpartyTitle}</h4>
                        <p>{row.original.counterpartyDetails}</p>
                    </div>
                </div>
            );
        },
    },

    {
        accessorKey: "narration",
        header: "Narration",
    },

    {
        accessorKey: "amount",
        header: ({ column }) => (
            <button
                className="flex items-center justify-end gap-1 text-right w-full"
                onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            >
                <ArrowDownIcon />
                <span className="text-right">Amount</span>
            </button>
        ),
        cell: ({ row }) => {
            const amount = parseFloat(row.getValue("amount"));
            return <div className={"text-right font-semibold text-black"}>{formatNumberToNaira(amount)}</div>;
        },
    },
    {
        id: "actions",
        cell: ({ row, table }) => {
            const payment = row.original;
            const setIsOpen = (table.options.meta as TableMetaWithSetIsOpen).setIsOpen;
            const setRequestId = (table.options.meta as TableMetaWithSetIsOpen).setRequestId;

            const setRequestApprovalModal = (table.options.meta as TableMetaWithSetIsOpen).setRequestApprovalModal;

            const setDeclineModal = (table.options.meta as TableMetaWithSetIsOpen).setDeclineModal;

            const updatedMenuItems = [
                {
                    label: <RenderMenuItem icon={<Eye className="w-4 h-4" />} title="View" />,
                    onClick: (data: IPaymentRequest) => {
                        if (setIsOpen) {
                            setRequestId(data.id);
                            setIsOpen(true);
                        }
                    },
                },
                {
                    label: <RenderMenuItem icon={<Check className="w-4 h-4" />} title="Approve" />,
                    onClick: (data: IPaymentRequest) => {
                        if (setRequestApprovalModal) {
                            setRequestId(data.id);
                            setRequestApprovalModal(true);
                        }
                    },
                },
                {
                    label: <RenderMenuItem icon={<RemoveCircle />} title="Decline" className="text-[#D92D20]" />,
                    onClick: (data: IPaymentRequest) => {
                        if (setDeclineModal) {
                            setRequestId(data.id);
                            setDeclineModal(true);
                        }
                    },
                },
            ];

            return <TableMoreAction data={{ ...payment, id: payment.id }} menuItems={updatedMenuItems} />;
        },
    },
];

export const RenderMenuItem = ({
    icon,
    title,
    className = "",
}: {
    icon: React.ReactNode;
    title: string;
    className?: string;
}) => (
    <div data-testid="menu-item" className={`flex items-center gap-2 ${className}`}>
        <span>{icon}</span> <span>{title}</span>
    </div>
);
