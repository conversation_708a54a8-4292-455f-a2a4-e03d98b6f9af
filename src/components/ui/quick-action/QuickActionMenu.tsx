"use client";

import { AnimatePresence, motion } from "framer-motion";
import { Dispatch, SetStateAction } from "react";
import ClickAwayListener from "react-click-away-listener";
import { getMenuItems } from "./data";
import MenuCard from "./menu-card";

const containerVariants = {
    hidden: { y: -20, opacity: 0 },
    visible: { y: 0, opacity: 1 },
};

const QuickActionMenu = ({
    setOpenMenu,
    openMenu,
}: {
    openMenu: boolean;
    setOpenMenu: Dispatch<SetStateAction<boolean>>;
}) => {
    const closeMenu = () => {
        setOpenMenu(false);
    };
    return (
        <AnimatePresence>
            {openMenu ? (
                <motion.div
                    variants={containerVariants}
                    initial="hidden"
                    animate="visible"
                    exit="hidden"
                    transition={{ type: "tween", duration: 0.2 }}
                    className="rounded-[14px] border border-[#E3E5E8] bg-white min-w-[205px] absolute top-[43px] right-0 p-2 z-30"
                    style={{
                        boxShadow: "0px 0px 7px 0px rgba(0, 0, 0, 0.07)",
                    }}
                >
                    <ClickAwayListener onClickAway={closeMenu}>
                        <div className="flex flex-col w-full gap-3">
                            {getMenuItems().map((item) => (
                                <MenuCard key={item.label} menu={item} closeMenu={closeMenu} />
                            ))}
                        </div>
                    </ClickAwayListener>
                </motion.div>
            ) : null}
        </AnimatePresence>
    );
};

export default QuickActionMenu;
