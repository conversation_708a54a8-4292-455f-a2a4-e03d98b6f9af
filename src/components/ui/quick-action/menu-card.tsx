import { useRouter } from "next/navigation";
import { MenuItemType } from "./data";

const MenuCard = ({ menu, closeMenu }: { menu: MenuItemType; closeMenu: () => void }) => {
    const router = useRouter();
    return (
        <button
            className="flex p-2 items-center gap-[6px] bg-white hover:bg-[#F9F9FA] rounded-lg duration-300"
            onClick={() => {
                if (menu.onClick) {
                    menu.onClick();
                    closeMenu();
                }
                if (menu.destination) {
                    router.push(menu.destination);
                    closeMenu();
                }
            }}
        >
            {menu.icon}
            <span className="text-black text-sm font-medium">{menu.label}</span>
        </button>
    );
};

export default MenuCard;
