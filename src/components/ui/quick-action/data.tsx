import { store } from "@/redux";
import { openSendMoneyDialog } from "@/redux/features/sendMoneyDialog";
import { PATH_PROTECTED } from "@/routes/path";
import { ReactNode } from "react";
import { PayBillsIcon, PayRemitaIcon, SendIcon } from "../icons";

export interface MenuItemType {
    icon: ReactNode;
    label: string;
    onClick?: () => void;
    destination?: string;
}

export const getMenuItems: () => MenuItemType[] = () =>
    // This was made a function so that other links can
    // have their onclick handlers passed here if required
    [
        {
            label: "Send NGN",
            icon: <SendIcon />,
            onClick: () => store.dispatch(openSendMoneyDialog()),
        },
        // {
        //     label: "Send FX",
        //     icon: <SendFXIcon />,
        //     onClick: () => null,
        // },
        {
            label: "Pay Bills",
            icon: <PayBillsIcon />,
            destination: PATH_PROTECTED.billPayments.root,
        },
        {
            label: "Pay Remita",
            icon: <PayRemitaIcon />,
            destination: PATH_PROTECTED.billPayments.root,
        },
    ];
