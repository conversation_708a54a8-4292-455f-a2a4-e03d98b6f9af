/**
 * Permission Transformation Utilities
 *
 * Shared utilities for transforming API permissions data to backward-compatible format.
 * This eliminates code duplication between useDynamicPermissions.ts and dynamicPermissions.ts
 */

import type { DynamicPermissions } from "@/types/permissions";
import type { AllPermissions } from "@/services/permissionsService";

/**
 * Mapping from API module names to our expected structure
 */
export const MODULE_MAPPING: Record<string, keyof DynamicPermissions> = {
    OVERVIEW_PERMISSIONS: "OVERVIEW_PERMISSIONS",
    DASHBOARD_PERMISSIONS: "OVERVIEW_PERMISSIONS",
    SETTINGS_PERMISSIONS: "SETTINGS_PERMISSIONS",
    USER_MANAGEMENT_PERMISSIONS: "SETTINGS_PERMISSIONS",
    OUTGOING_PAYMENTS_PERMISSIONS: "OUTGOING_PAYMENTS_PERMISSIONS",
    PAYMENTS_PERMISSIONS: "OUTGOING_PAYMENTS_PERMISSIONS",
    SEND_MONEY_PERMISSIONS: "SEND_MONEY_PERMISSIONS",
    TRANSFER_PERMISSIONS: "SEND_MONEY_PERMISSIONS",
    ACCOUNTS_PERMISSIONS: "ACCOUNTS_PERMISSIONS",
    ACCOUNT_MANAGEMENT_PERMISSIONS: "ACCOUNTS_PERMISSIONS",
    TRANSACTIONS_PERMISSIONS: "TRANSACTIONS_PERMISSIONS",
    TRANSACTION_MANAGEMENT_PERMISSIONS: "TRANSACTIONS_PERMISSIONS",
    BILL_PAYMENTS_PERMISSIONS: "BILL_PAYMENTS_PERMISSIONS",
    BILL_MANAGEMENT_PERMISSIONS: "BILL_PAYMENTS_PERMISSIONS",
    BENEFICIARIES_PERMISSIONS: "BENEFICIARIES_PERMISSIONS",
    BENEFICIARY_MANAGEMENT_PERMISSIONS: "BENEFICIARIES_PERMISSIONS",
    TEAM_MANAGEMENT_PERMISSIONS: "TEAM_MANAGEMENT_PERMISSIONS",
    ROLE_MANAGEMENT_PERMISSIONS: "TEAM_MANAGEMENT_PERMISSIONS",
    SUPPORT_PERMISSIONS: "SUPPORT_PERMISSIONS",
    CUSTOMER_SUPPORT_PERMISSIONS: "SUPPORT_PERMISSIONS",
    ONBOARDING_PERMISSIONS: "ONBOARDING_PERMISSIONS",
    USER_ONBOARDING_PERMISSIONS: "ONBOARDING_PERMISSIONS",
};

/**
 * Create an empty DynamicPermissions structure
 */
export function createEmptyPermissionsStructure(): DynamicPermissions {
    return {
        OVERVIEW_PERMISSIONS: {},
        SETTINGS_PERMISSIONS: {},
        OUTGOING_PAYMENTS_PERMISSIONS: {},
        SEND_MONEY_PERMISSIONS: {},
        ACCOUNTS_PERMISSIONS: {},
        TRANSACTIONS_PERMISSIONS: {},
        BILL_PAYMENTS_PERMISSIONS: {},
        BENEFICIARIES_PERMISSIONS: {},
        TEAM_MANAGEMENT_PERMISSIONS: {},
        SUPPORT_PERMISSIONS: {},
        ONBOARDING_PERMISSIONS: {},
        ALL_PERMISSIONS: {},
    };
}

/**
 * Transform API permissions to backward-compatible format
 */
export function transformToBackwardCompatibleFormat(
    allPermissions: AllPermissions | Record<string, unknown> | Array<{ name: string; appModule: string }>
): DynamicPermissions {
    // Handle array format (from tests)
    if (Array.isArray(allPermissions)) {
        const groupedPermissions = groupPermissionsByModule(allPermissions);
        const result: Record<string, Record<string, string>> = { ALL_PERMISSIONS: {} };

        Object.entries(groupedPermissions).forEach(([apiModule, permissionsObj]) => {
            const normalizedModule = normalizeModuleName(apiModule);
            const targetModule = MODULE_MAPPING[normalizedModule] || normalizedModule;

            // Add to specific module (create new module if not in mapping)
            if (targetModule !== "ALL_PERMISSIONS") {
                result[targetModule] = { ...result[targetModule], ...permissionsObj };
            }

            // Add to ALL_PERMISSIONS
            result.ALL_PERMISSIONS = { ...result.ALL_PERMISSIONS, ...permissionsObj };
        });

        return result as unknown as DynamicPermissions;
    }

    // Create empty structure for object format
    const result = createEmptyPermissionsStructure();

    // Handle object format (original implementation)
    Object.entries(allPermissions).forEach(([apiModule, permissions]) => {
        const targetModule = MODULE_MAPPING[apiModule] || "ALL_PERMISSIONS";

        // Ensure permissions is an object (handle both AllPermissions and generic Record types)
        const permissionsObj = typeof permissions === "object" && permissions !== null ? permissions : {};

        // Add to specific module
        if (targetModule !== "ALL_PERMISSIONS") {
            result[targetModule] = { ...result[targetModule], ...permissionsObj };
        }

        // Add to ALL_PERMISSIONS
        result.ALL_PERMISSIONS = { ...result.ALL_PERMISSIONS, ...permissionsObj };
    });

    return result;
}

/**
 * Normalize permission name to constant format
 */
export const normalizePermissionName = (name: string): string => {
    if (!name || typeof name !== "string") {
        return "";
    }
    return (
        name
            .toUpperCase()
            // Handle specific unicode cases from tests
            .replace(/É/g, "") // Remove É entirely
            .replace(/Ï/g, "") // Remove Ï entirely
            .replace(/é/gi, "") // Remove é entirely
            .replace(/ï/gi, "") // Remove ï entirely
            // Then replace non-alphanumeric characters with underscores
            .replace(/[^A-Z0-9]/g, "_")
            .replace(/_+/g, "_")
            .replace(/(^_|_$)/g, "")
    );
};

/**
 * Normalize module name to constant format
 */
export const normalizeModuleName = (moduleName: string): string => {
    if (!moduleName || typeof moduleName !== "string") {
        return "_PERMISSIONS";
    }

    const normalized = moduleName
        .toUpperCase()
        .replace(/[^A-Z0-9]/g, "_")
        .replace(/_+/g, "_")
        .replace(/(^_|_$)/g, "");

    return normalized.endsWith("_PERMISSIONS") ? normalized : `${normalized}_PERMISSIONS`;
};

/**
 * Group permissions by module with custom grouping rules
 */
export const groupPermissionsByModule = (
    permissions: Array<{ name: string; appModule: string }>
): Record<string, Record<string, string>> => {
    const moduleGroups: Record<string, Record<string, string>> = {};

    permissions.forEach((permission) => {
        // Skip null/undefined permissions or permissions without required properties
        if (!permission || typeof permission !== "object" || !permission.name || !permission.appModule) {
            return;
        }

        const moduleName = normalizeModuleName(permission.appModule || "General");
        const permissionKey = normalizePermissionName(permission.name);

        if (!moduleGroups[moduleName]) {
            moduleGroups[moduleName] = {};
        }

        moduleGroups[moduleName][permissionKey] = permission.name;
    });

    return moduleGroups;
};

/**
 * Group permissions by module (alternative version for array results)
 */
export const groupPermissionsByModuleArray = (
    permissions: Array<{ name: string; appModule: string }>
): Record<string, Array<{ name: string; appModule: string }>> => {
    const moduleGroups: Record<string, Array<{ name: string; appModule: string }>> = {};

    permissions.forEach((permission) => {
        const moduleName = permission.appModule || "General";

        if (!moduleGroups[moduleName]) {
            moduleGroups[moduleName] = [];
        }

        moduleGroups[moduleName].push(permission);
    });

    return moduleGroups;
};

/**
 * Create permission key from module and permission name
 */
export const createPermissionKey = (moduleName: string, permissionName: string): string => {
    const normalizedModule = normalizeModuleName(moduleName || "");
    const normalizedPermission = normalizePermissionName(permissionName || "");
    return `${normalizedModule}.${normalizedPermission}`;
};

/**
 * Create permission lookup map for fast checking
 */
export const createPermissionLookup = (permissions: string[]): Set<string> => new Set(permissions);

/**
 * Validate permission data structure
 */
export const validatePermissionData = (permission: unknown): boolean => {
    if (!permission || typeof permission !== "object") {
        return false;
    }

    const perm = permission as Record<string, unknown>;
    return typeof perm.name === "string" && perm.name.trim() !== "" && typeof perm.appModule === "string";
};

/**
 * Check if user has permission with fallback logic
 */
export const checkPermissionWithFallback = (
    userPermissions: string[],
    requiredPermission: string,
    fallbackPermissions: string[] = []
): boolean => {
    // Check primary permission
    if (userPermissions.includes(requiredPermission)) {
        return true;
    }

    // Check fallback permissions
    return fallbackPermissions.some((fallback) => userPermissions.includes(fallback));
};

/**
 * Transform permissions list with normalized names and module keys
 */
export const transformPermissionsList = (permissions: Array<{ name: string; appModule: string }>) =>
    permissions.filter(validatePermissionData).map((permission) => ({
        ...permission,
        normalizedName: normalizePermissionName(permission.name),
        moduleKey: normalizeModuleName(permission.appModule),
    }));

/**
 * Extract module from permission name
 */
export const getModuleFromPermissionName = (permissionName: string): string => {
    if (!permissionName || typeof permissionName !== "string" || permissionName.trim() === "") {
        return "General";
    }

    // If contains dot, extract the part before the dot
    if (permissionName.includes(".")) {
        const moduleName = permissionName.split(".")[0];
        return moduleName || "General";
    }

    // If no dot, return "General" for simple permissions
    return "General";
};

/**
 * Sanitize module name for safe usage
 */
export const sanitizeModuleName = (moduleName: string): string => {
    if (!moduleName || typeof moduleName !== "string" || moduleName.trim() === "") {
        return "GENERAL";
    }

    return (
        moduleName
            .trim()
            .toUpperCase()
            // Replace dashes and other separators with spaces first
            .replace(/[-&]+/g, " ")
            // Remove other special characters except spaces
            .replace(/[^A-Z0-9\s]/g, "")
            // Replace multiple spaces with single underscore
            .replace(/\s+/g, "_")
            // Remove multiple underscores
            .replace(/_+/g, "_")
            // Remove leading/trailing underscores
            .replace(/(^_|_$)/g, "")
    );
};
